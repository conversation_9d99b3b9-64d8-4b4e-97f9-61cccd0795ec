# -*- coding: utf-8 -*-
"""
简化打包脚本 - 使用PyInstaller直接打包
适用于没有Visual C++编译器的环境
"""

import os
import sys
import shutil
import subprocess
import time
from pathlib import Path

def log(message):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_dependencies():
    """检查必要的依赖"""
    log("🔍 检查依赖环境...")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        log(f"✅ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        log("❌ 未安装PyInstaller，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"])
        log("✅ PyInstaller 安装完成")
    
    # 检查主要模块
    required_files = [
        "kx7m.py",  # 主程序
        "qz4n.py",  # UI界面
        "mx9p.py",  # 工具类
        "bv8k.py",  # ColorOS解锁
        "wr3j.py",  # ColorOS15
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        log(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    log("✅ 所有必要文件都存在")
    return True

def create_pyinstaller_spec():
    """创建PyInstaller规格文件"""
    log("📝 创建PyInstaller规格文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['kx7m.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('ADBTools', 'ADBTools'),
        ('ico', 'ico'),
        ('tup', 'tup'),
    ],
    hiddenimports=[
        'mx9p',
        'qz4n', 
        'bv8k',
        'wr3j',
        'ly6h',
        'dt5c',
        'fn2w',
        'gp4r',
        'hs7v',
        'jt8x',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'unittest', 
        'test',
        'distutils',
        'email',
        'html',
        'http',
        'xml',
        'pydoc',
        'doctest',
        'pickle',
        'argparse',
    ],
    noarchive=False,
    optimize=2,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='益民固件刷写工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='ico/icon.ico' if os.path.exists('ico/icon.ico') else None,
)
'''
    
    with open('简化打包.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    log("✅ 规格文件创建完成: 简化打包.spec")
    return True

def build_executable():
    """构建可执行文件"""
    log("🚀 开始构建可执行文件...")
    
    # 清理之前的构建
    for dir_name in ['build', 'dist']:
        if os.path.exists(dir_name):
            log(f"🧹 清理目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 执行PyInstaller
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--clean",
        "--noconfirm",
        "简化打包.spec"
    ]
    
    log(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, timeout=600, capture_output=True, text=True)
        
        if result.returncode == 0:
            # 检查输出文件
            exe_path = os.path.join('dist', '益民固件刷写工具.exe')
            if os.path.exists(exe_path):
                size = os.path.getsize(exe_path)
                log(f"🎉 构建成功!")
                log(f"📁 输出文件: {exe_path}")
                log(f"📦 文件大小: {size:,} 字节 ({size/1024/1024:.1f} MB)")
                return True
            else:
                log("❌ 构建完成但未找到输出文件")
        else:
            log("❌ PyInstaller构建失败")
            if result.stderr:
                log(f"错误信息: {result.stderr[:500]}")
        
        return False
        
    except subprocess.TimeoutExpired:
        log("❌ 构建超时")
        return False
    except Exception as e:
        log(f"❌ 构建异常: {e}")
        return False

def main():
    """主函数"""
    log("🔧 简化打包工具")
    log("使用PyInstaller直接打包，无需编译器")
    log("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        log("❌ 依赖检查失败")
        return False
    
    # 创建规格文件
    if not create_pyinstaller_spec():
        log("❌ 规格文件创建失败")
        return False
    
    # 构建可执行文件
    if build_executable():
        log("\n🎉 打包完成!")
        log("📁 可执行文件位于: dist/益民固件刷写工具.exe")
        log("🔒 虽然没有Cython编译，但PyInstaller也提供了基本的代码保护")
        return True
    else:
        log("\n❌ 打包失败")
        return False

if __name__ == "__main__":
    main()
