from PyQt6.QtWidgets import <PERSON>Dialog, QVBoxLayout, QPushButton, QLabel, QWidget, QScrollArea, QTextEdit, QHBoxLayout
from PyQt6.QtCore import Qt, QPoint, QThread, pyqtSignal
from PyQt6.QtGui import QFont, QMouseEvent, QPixmap
import subprocess
import os
from utils import ADBTools

class FRPRemovalThread(QThread):
    finished = pyqtSignal(str)
    error = pyqtSignal(str)

    def __init__(self, fastboot_path='fastboot', parent=None):
        super().__init__(parent)
        self.fastboot_path = fastboot_path

    def run(self):
        try:
            # Windows下隐藏黑框
            startupinfo = None
            creationflags = 0
            if os.name == 'nt':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creationflags = subprocess.CREATE_NO_WINDOW

            # 检查设备是否在 fastboot 模式
            result = subprocess.run([self.fastboot_path, 'devices'],
                                 capture_output=True,
                                 text=True,
                                 startupinfo=startupinfo,
                                 creationflags=creationflags)
            
            if not result.stdout.strip():
                self.error.emit("设备未进入 fastboot 模式，请先进入 fastboot 模式！")
                return

            # 执行 fastboot erase frp 命令
            result = subprocess.run([self.fastboot_path, 'erase', 'frp'],
                                 capture_output=True,
                                 text=True,
                                 startupinfo=startupinfo,
                                 creationflags=creationflags)
            
            if result.returncode == 0:
                self.finished.emit("ok！")
            else:
                self.error.emit(f"no!：{result.stderr}")
        except Exception as e:
            self.error.emit(f"执行命令时出错：{str(e)}")

class MoreFeaturesDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("更多功能")
        self.setFixedSize(350, 560)
        # 设置为独立工具窗口和无边框
        self.setWindowFlags(Qt.WindowType.Tool | Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
                border-radius: 12px;
                border: 1px solid #ddd;
            }
            QLabel#titleLabel {
                color: #333333;
                font-size: 16px;
                font-weight: bold;
                padding-left: 10px;
                background-color: #ffffff;
            }
            QPushButton#closeButton {
                background-color: #ff3333;
                border: none;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
                min-width: 20px;
                max-width: 20px;
                min-height: 20px;
                max-height: 20px;
                margin-right: 10px;
            }
            QPushButton#closeButton:hover {
                background-color: #cc2929;
            }
            QWidget#titleBar {
                background-color: #ffffff;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
                border-bottom: 1px solid #ddd;
            }
            QScrollArea {
                background-color: #ffffff;
                border: none;
            }
            QWidget#scrollWidget {
                background-color: #ffffff;
            }
        """)

        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 添加标题栏
        title_bar = QWidget()
        title_bar.setObjectName("titleBar")
        title_bar.setFixedHeight(35)
        title_bar_layout = QHBoxLayout(title_bar)
        title_bar_layout.setContentsMargins(0, 0, 0, 0)
        
        title_label = QLabel("更多功能")
        title_label.setObjectName("titleLabel")
        
        title_bar_layout.addWidget(title_label)
        title_bar_layout.addStretch()
        
        main_layout.addWidget(title_bar)

        # 按钮样式
        button_styles = {
            "解锁bootloader": """
                QPushButton {
                    background-color: #2EA043;
                    border: 1px solid #278838;
                    color: white;
                    font-size: 16px;
                    font-weight: bold;
                    border-radius: 18px;
                    min-height: 45px;
                }
                QPushButton:hover { background-color: #278838; }
            """,
            "锁定bootloader": """
                QPushButton {
                    background-color: #F28C48;
                    border: 1px solid #CC763C;
                    color: white;
                    font-size: 16px;
                    font-weight: bold;
                    border-radius: 18px;
                    min-height: 45px;
                }
                QPushButton:hover { background-color: #CC763C; }
            """,
            "设备管理器": """
                QPushButton {
                    background-color: #F24983;
                    border: 1px solid #CC3E6E;
                    color: white;
                    font-size: 16px;
                    font-weight: bold;
                    border-radius: 18px;
                    min-height: 45px;
                }
                QPushButton:hover { background-color: #CC3E6E; }
            """,
            "字库提取": """
                QPushButton {
                    background-color: #8250DF;
                    border: 1px solid #6E44BC;
                    color: white;
                    font-size: 16px;
                    font-weight: bold;
                    border-radius: 18px;
                    min-height: 45px;
                }
                QPushButton:hover { background-color: #6E44BC; }
            """,
            "谷歌锁移除": """
                QPushButton {
                    background-color: #ff3333;
                    border: 1px solid #cc2929;
                    color: white;
                    font-size: 16px;
                    font-weight: bold;
                    border-radius: 18px;
                    min-height: 45px;
                }
                QPushButton:hover { background-color: #cc2929; }
            """,
            "待定": """
                QPushButton {
                    background-color: #FFD700;
                    border: 1px solid #DAA520;
                    color: #000000;
                    font-size: 16px;
                    font-weight: bold;
                    border-radius: 18px;
                    min-height: 45px;
                }
                QPushButton:hover { background-color: #DAA520; }
            """,
            "关闭": """
                QPushButton {
                    background-color: #ff3333;
                    border: 1px solid #cc2929;
                    color: white;
                    font-size: 16px;
                    font-weight: bold;
                    border-radius: 18px;
                    min-height: 45px;
                }
                QPushButton:hover { background-color: #cc2929; }
            """,
        }

        # 按钮顺序
        button_names = [
            "解锁bootloader",
            "锁定bootloader",
            "设备管理器",
            "字库提取",
            "谷歌锁移除",
            "待定",
            
        ]

        # 按钮区
        btn_widget = QWidget()
        btn_widget.setObjectName("scrollWidget")
        btn_layout = QVBoxLayout(btn_widget)
        btn_layout.setSpacing(7)
        btn_layout.setContentsMargins(0, 0, 0, 0)

        for name in button_names:
            btn = QPushButton(name)
            btn.setStyleSheet(button_styles[name])
            if name == "解锁bootloader":
                btn.clicked.connect(self.show_bootloader_unlock)
            elif name == "锁定bootloader":
                btn.clicked.connect(self.show_bootloader_lock)
            elif name == "设备管理器":
                btn.clicked.connect(self.open_device_manager)
            elif name == "字库提取":
                btn.clicked.connect(self.show_font_extractor)
            elif name == "谷歌锁移除":
                btn.clicked.connect(self.remove_frp)
            btn_layout.addWidget(btn)

        # 滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setWidget(btn_widget)
        scroll.setStyleSheet("QScrollArea { border: none; background: transparent; }")
        main_layout.addWidget(scroll)

        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.setStyleSheet(button_styles["关闭"])
        close_btn.clicked.connect(self.close)
        main_layout.addWidget(close_btn)

    def show_bootloader_unlock(self):
        from genduodakhd import BootloaderUnlockDialog
        dlg = BootloaderUnlockDialog(parent=self)
        dlg.exec()

    def show_bootloader_lock(self):
        from genduodakhd import BootloaderUnlockDialog
        dlg = BootloaderUnlockDialog(parent=self, mode="lock")
        dlg.exec()

    def open_device_manager(self):
        try:
            # Windows下隐藏黑框
            startupinfo = None
            creationflags = 0
            if os.name == 'nt':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creationflags = subprocess.CREATE_NO_WINDOW
                
            # 使用 start 命令打开设备管理器
            subprocess.Popen(['start', 'devmgmt.msc'], 
                           shell=True,
                           startupinfo=startupinfo,
                           creationflags=creationflags)
        except Exception as e:
            # 如果出错，尝试使用完整路径
            try:
                system32_path = os.path.join(os.environ['SystemRoot'], 'System32')
                devmgmt_path = os.path.join(system32_path, 'devmgmt.msc')
                subprocess.Popen(['start', devmgmt_path],
                               shell=True,
                               startupinfo=startupinfo,
                               creationflags=creationflags)
            except Exception as e2:
                # 如果还是失败，显示错误信息
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.critical(self, "错误", f"无法打开设备管理器：{str(e2)}")

    def show_font_extractor(self):
        from font_extractor import FontExtractorDialog
        adb_tools = ADBTools()
        devices = adb_tools.get_devices()
        if not devices:
            dlg = CustomErrorDialog("未检测到ADB设备，请连接设备并确保已开启USB调试！", parent=self)
            dlg.exec()
            return
        # 检查设备是否处于fastboot模式
        in_fastboot = any(mode == 'fastboot' for mode, _ in devices)
        if in_fastboot:
            dlg = CustomErrorDialog("字库提取功能仅支持ADB模式，请退出fastboot模式！", parent=self)
            dlg.exec()
            return
        dlg = FontExtractorDialog(parent=self)
        dlg.exec()

    def remove_frp(self):
        """移除谷歌锁"""
        # 检查设备是否连接
        adb_tools = ADBTools()
        devices = adb_tools.get_devices()
        if not devices:
            dlg = CustomErrorDialog("未检测到设备，请先进入fastboot模式！", parent=self)
            dlg.exec()
            return

        # 检查是否在 fastboot 模式
        in_fastboot = any(mode == 'fastboot' for mode, _ in devices)
        if not in_fastboot:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "警告", "设备不在 fastboot 模式")
            return

        # 获取 fastboot 路径
        _, fastboot_path = adb_tools.get_adb_path()

        # 创建并启动线程，传递 fastboot_path
        self.frp_thread = FRPRemovalThread(fastboot_path=fastboot_path)
        self.frp_thread.finished.connect(self.show_frp_success)
        self.frp_thread.error.connect(self.show_frp_error)
        self.frp_thread.start()

    def show_frp_success(self, message):
        """显示FRP移除成功消息"""
        dlg = CustomSuccessDialog(message, parent=self)
        dlg.exec()

    def show_frp_error(self, error_message):
        """显示FRP移除错误消息"""
        dlg = CustomFailedDialog(error_message, parent=self)
        dlg.exec()

    def mousePressEvent(self, event: QMouseEvent):
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event: QMouseEvent):
        if event.buttons() == Qt.MouseButton.LeftButton:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()

class CustomErrorDialog(QDialog):
    def __init__(self, message, parent=None):
        super().__init__(parent)
        self.setWindowTitle("未检测到设备")
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setFixedSize(400, 180)
        self.setStyleSheet("""
            QDialog {
                background-color: #fff;
                border-radius: 16px;
                border: 1px solid #8250DF;
            }
            QLabel#errorTitle {
                color: #d32f2f;
                font-size: 18px;
                font-weight: bold;
            }
            QLabel#errorMsg {
                color: #333;
                font-size: 15px;
            }
            QPushButton {
                background-color: #8250DF;
                border: none;
                color: white;
                font-size: 15px;
                font-weight: bold;
                border-radius: 10px;
                min-width: 80px;
                min-height: 32px;
                margin-top: 10px;
            }
            QPushButton:hover { background-color: #6E44BC; }
        """)
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 24, 30, 24)
        layout.setSpacing(12)
        icon_row = QHBoxLayout()
        icon_label = QLabel()
        icon_pix = QPixmap(48, 48)
        icon_pix.fill(Qt.GlobalColor.transparent)
        # 绘制红色圆圈带白色叉
        from PyQt6.QtGui import QPainter, QColor, QPen
        painter = QPainter(icon_pix)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setBrush(QColor('#d32f2f'))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(0, 0, 48, 48)
        pen = QPen(QColor('#fff'))
        pen.setWidth(6)
        painter.setPen(pen)
        painter.drawLine(14, 14, 34, 34)
        painter.drawLine(34, 14, 14, 34)
        painter.end()
        icon_label.setPixmap(icon_pix)
        icon_row.addWidget(icon_label)
        title = QLabel("未检测到ADB设备")
        title.setObjectName("errorTitle")
        icon_row.addWidget(title)
        icon_row.addStretch()
        layout.addLayout(icon_row)
        msg = QLabel(message)
        msg.setObjectName("errorMsg")
        msg.setWordWrap(True)
        layout.addWidget(msg)
        btn = QPushButton("OK")
        btn.clicked.connect(self.accept)
        btn.setCursor(Qt.CursorShape.PointingHandCursor)
        btn_row = QHBoxLayout()
        btn_row.addStretch()
        btn_row.addWidget(btn)
        layout.addLayout(btn_row)

class BootloaderUnlockDialog(QDialog):
    def __init__(self, parent=None, mode="unlock"):
        super().__init__(parent)
        self.mode = mode
        self.setWindowTitle("锁定 Bootloader" if mode == "lock" else "解锁 Bootloader")
        # 设置为独立工具窗口和无边框
        self.setWindowFlags(Qt.WindowType.Tool | Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setFixedSize(400, 350 if mode == "lock" else 300)
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
                border-radius: 12px;
                border: 1px solid #ddd;
            }
            QLabel#titleLabel {
                color: #333333;
                font-size: 16px;
                font-weight: bold;
                padding-left: 10px;
                background-color: #ffffff;
            }
            QPushButton#closeButton {
                background-color: #ff3333;
                border: none;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
                min-width: 20px;
                max-width: 20px;
                min-height: 20px;
                max-height: 20px;
                margin-right: 10px;
            }
            QPushButton#closeButton:hover {
                background-color: #cc2929;
            }
            QWidget#titleBar {
                background-color: #ffffff;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
                border-bottom: 1px solid #ddd;
            }
            QLabel {
                color: #dc3545;
                font-size: 18px;
                font-weight: bold;
                background-color: #ffffff;
            }
            QLabel#warningLabel {
                color: #dc3545;
                font-size: 14px;
                font-weight: bold;
                background-color: #fff3cd;
                border: 1px solid #ffeeba;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
                min-height: 40px;
            }
            QTextEdit {
                background-color: #ffffff;
                border: 1px solid #ddd;
                border-radius: 8px;
                font-size: 14px;
                color: #333333;
                padding: 6px;
                min-height: 120px;
            }
        """)
        self.adb_tools = ADBTools()
        layout = QVBoxLayout(self)
        layout.setSpacing(12)
        layout.setContentsMargins(20, 20, 20, 20)

        # 添加标题栏
        title_bar = QWidget()
        title_bar.setObjectName("titleBar")
        title_bar.setFixedHeight(35)
        title_bar_layout = QHBoxLayout(title_bar)
        title_bar_layout.setContentsMargins(0, 0, 0, 0)
        
        title_label = QLabel("锁定 Bootloader" if mode == "lock" else "解锁 Bootloader")
        title_label.setObjectName("titleLabel")
        
        title_bar_layout.addWidget(title_label)
        title_bar_layout.addStretch()
        
        layout.addWidget(title_bar)

        # 添加警告标签（仅在锁定模式下显示）
        if mode == "lock":
            warning_label = QLabel("⚠️ 警告：锁定 Bootloader 有风险，可能会导致数据丢失，设备损坏等，请谨慎操作！想回锁？等着变砖吧")
            warning_label.setObjectName("warningLabel")
            warning_label.setWordWrap(True)
            warning_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(warning_label)

        self.result_box = QTextEdit()
        self.result_box.setReadOnly(True)
        self.result_box.setMinimumHeight(120)
        layout.addWidget(self.result_box)

        self.unlock_btn = QPushButton("锁定" if mode == "lock" else "解锁")
        if mode == "lock":
            self.unlock_btn.setStyleSheet("""
                QPushButton {
                    background-color: #F28C48;
                    border: 1px solid #CC763C;
                    color: white;
                    font-size: 16px;
                    font-weight: bold;
                    border-radius: 12px;
                    min-height: 40px;
                }
                QPushButton:hover { background-color: #CC763C; }
            """)
        else:
            self.unlock_btn.setStyleSheet("""
                QPushButton {
                    background-color: #2EA043;
                    border: 1px solid #278838;
                    color: white;
                    font-size: 16px;
                    font-weight: bold;
                    border-radius: 12px;
                    min-height: 40px;
                }
                QPushButton:hover { background-color: #278838; }
            """)
        self.unlock_btn.clicked.connect(self.lock_bootloader if mode == "lock" else self.unlock_bootloader)
        layout.addWidget(self.unlock_btn)

        self.close_btn = QPushButton("关闭")
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff3333;
                border: 1px solid #cc2929;
                color: white;
                font-size: 16px;
                font-weight: bold;
                border-radius: 12px;
                min-height: 40px;
            }
            QPushButton:hover { background-color: #cc2929; }
        """)
        self.close_btn.clicked.connect(self.close)
        layout.addWidget(self.close_btn)

    def unlock_bootloader(self):
        self.result_box.clear()
        try:
            # 检查设备是否连接
            adb_path, fastboot_path = self.adb_tools.get_adb_path()
            devices = self.adb_tools.get_devices()
            if not devices:
                self.result_box.append("未检测到设备，请打开开发者oem解锁，usb调试！")
                return
            in_fastboot = any(mode == 'fastboot' for mode, _ in devices)
            if not in_fastboot:
                self.result_box.append("设备不在 bootloader 模式，重启到bootloader...\n")
                # Windows下隐藏黑框
                startupinfo = None
                creationflags = 0
                if os.name == 'nt':
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    startupinfo.wShowWindow = subprocess.SW_HIDE
                    creationflags = subprocess.CREATE_NO_WINDOW
                cmd = [adb_path, "reboot", "bootloader"]
                result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', timeout=15,
                                       startupinfo=startupinfo, creationflags=creationflags)
                if result.stdout:
                    self.result_box.append(result.stdout)
                if result.stderr:
                    self.result_box.append(result.stderr)
                self.result_box.append("请等待设备进入 bootloader 模式后再点击解锁！")
                return
            # 获取 fastboot 路径
            _, fastboot_path = self.adb_tools.get_adb_path()
            if not os.path.exists(fastboot_path):
                self.result_box.append("未找到 fastboot 工具！")
                return
            # Windows下隐藏黑框
            startupinfo = None
            creationflags = 0
            if os.name == 'nt':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creationflags = subprocess.CREATE_NO_WINDOW
            cmd = [fastboot_path, "flashing", "unlock"]
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', timeout=20,
                                   startupinfo=startupinfo, creationflags=creationflags)
            if result.stdout:
                self.result_box.append(result.stdout)
            if result.stderr:
                self.result_box.append(result.stderr)
            if result.returncode == 0:
                self.result_box.append("\n请在手机上确认解锁")
            else:
                self.result_box.append(f"\n解锁失败，返回码: {result.returncode}")
        except Exception as e:
            self.result_box.append(f"执行出错: {str(e)}")

    def lock_bootloader(self):
        self.result_box.clear()
        try:
            # 检查设备是否连接
            adb_path, fastboot_path = self.adb_tools.get_adb_path()
            devices = self.adb_tools.get_devices()
            if not devices:
                self.result_box.append("未检测到设备")
                return
            in_fastboot = any(mode == 'fastboot' for mode, _ in devices)
            if not in_fastboot:
                self.result_box.append("设备不在 bootloader 模式，重启到bootloader...\n")
                # Windows下隐藏黑框
                startupinfo = None
                creationflags = 0
                if os.name == 'nt':
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    startupinfo.wShowWindow = subprocess.SW_HIDE
                    creationflags = subprocess.CREATE_NO_WINDOW
                cmd = [adb_path, "reboot", "bootloader"]
                result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', timeout=15,
                                       startupinfo=startupinfo, creationflags=creationflags)
                if result.stdout:
                    self.result_box.append(result.stdout)
                if result.stderr:
                    self.result_box.append(result.stderr)
                self.result_box.append("请等待设备进入 bootloader 模式后再点击锁定！")
                return
            # 获取 fastboot 路径
            _, fastboot_path = self.adb_tools.get_adb_path()
            if not os.path.exists(fastboot_path):
                self.result_box.append("未找到 fastboot 工具！")
                return
            # Windows下隐藏黑框
            startupinfo = None
            creationflags = 0
            if os.name == 'nt':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creationflags = subprocess.CREATE_NO_WINDOW
            cmd = [fastboot_path, "flashing", "lock"]
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', timeout=20,
                                   startupinfo=startupinfo, creationflags=creationflags)
            if result.stdout:
                self.result_box.append(result.stdout)
            if result.stderr:
                self.result_box.append(result.stderr)
            if result.returncode == 0:
                self.result_box.append("\n请在手机上确认锁定")
            else:
                self.result_box.append(f"\n锁定失败，返回码: {result.returncode}")
        except Exception as e:
            self.result_box.append(f"执行出错: {str(e)}")

    def mousePressEvent(self, event: QMouseEvent):
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event: QMouseEvent):
        if event.buttons() == Qt.MouseButton.LeftButton:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()

class CustomSuccessDialog(QDialog):
    def __init__(self, message, parent=None):
        super().__init__(parent)
        self.setWindowTitle("成功")
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setFixedSize(400, 180)
        self.setStyleSheet("""
            QDialog {
                background-color: #fff;
                border-radius: 16px;
                border: 1px solid #2EA043;
            }
            QLabel#successTitle {
                color: #2EA043;
                font-size: 18px;
                font-weight: bold;
            }
            QLabel#successMsg {
                color: #333;
                font-size: 15px;
            }
            QPushButton {
                background-color: #2EA043;
                border: none;
                color: white;
                font-size: 15px;
                font-weight: bold;
                border-radius: 10px;
                min-width: 80px;
                min-height: 32px;
                margin-top: 10px;
            }
            QPushButton:hover { background-color: #278838; }
        """)
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 24, 30, 24)
        layout.setSpacing(12)
        icon_row = QHBoxLayout()
        icon_label = QLabel()
        icon_pix = QPixmap(48, 48)
        icon_pix.fill(Qt.GlobalColor.transparent)
        # 绘制绿色圆圈带白色对勾
        from PyQt6.QtGui import QPainter, QColor, QPen
        painter = QPainter(icon_pix)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setBrush(QColor('#2EA043'))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(0, 0, 48, 48)
        pen = QPen(QColor('#fff'))
        pen.setWidth(6)
        pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        painter.setPen(pen)
        # 对勾路径
        painter.drawLine(14, 28, 22, 38)
        painter.drawLine(22, 38, 36, 14)
        painter.end()
        icon_label.setPixmap(icon_pix)
        icon_row.addWidget(icon_label)
        title = QLabel("成功")
        title.setObjectName("successTitle")
        icon_row.addWidget(title)
        icon_row.addStretch()
        layout.addLayout(icon_row)
        msg = QLabel(message)
        msg.setObjectName("successMsg")
        msg.setWordWrap(True)
        layout.addWidget(msg)
        btn = QPushButton("OK")
        btn.clicked.connect(self.accept)
        btn.setCursor(Qt.CursorShape.PointingHandCursor)
        btn_row = QHBoxLayout()
        btn_row.addStretch()
        btn_row.addWidget(btn)
        layout.addLayout(btn_row)

class CustomFailedDialog(QDialog):
    def __init__(self, message, parent=None):
        super().__init__(parent)
        self.setWindowTitle("错误")
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setFixedSize(480, 200)
        self.setStyleSheet("""
            QDialog {
                background-color: #fff;
                border-radius: 16px;
                border: 1px solid #d32f2f;
            }
            QLabel#errorTitle {
                color: #d32f2f;
                font-size: 18px;
                font-weight: bold;
            }
            QLabel#errorMsg {
                color: #333;
                font-size: 15px;
            }
            QPushButton {
                background-color: #d32f2f;
                border: none;
                color: white;
                font-size: 15px;
                font-weight: bold;
                border-radius: 10px;
                min-width: 80px;
                min-height: 32px;
                margin-top: 10px;
            }
            QPushButton:hover { background-color: #b71c1c; }
        """)
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 24, 30, 24)
        layout.setSpacing(12)
        icon_row = QHBoxLayout()
        icon_label = QLabel()
        icon_pix = QPixmap(48, 48)
        icon_pix.fill(Qt.GlobalColor.transparent)
        # 绘制红色圆圈带白色叉
        from PyQt6.QtGui import QPainter, QColor, QPen
        painter = QPainter(icon_pix)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setBrush(QColor('#d32f2f'))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(0, 0, 48, 48)
        pen = QPen(QColor('#fff'))
        pen.setWidth(6)
        pen.setCapStyle(Qt.PenCapStyle.RoundCap)
        painter.setPen(pen)
        painter.drawLine(14, 14, 34, 34)
        painter.drawLine(34, 14, 14, 34)
        painter.end()
        icon_label.setPixmap(icon_pix)
        icon_row.addWidget(icon_label)
        title = QLabel("错误")
        title.setObjectName("errorTitle")
        icon_row.addWidget(title)
        icon_row.addStretch()
        layout.addLayout(icon_row)
        msg = QLabel(message)
        msg.setObjectName("errorMsg")
        msg.setWordWrap(True)
        layout.addWidget(msg)
        btn = QPushButton("OK")
        btn.clicked.connect(self.accept)
        btn.setCursor(Qt.CursorShape.PointingHandCursor)
        btn_row = QHBoxLayout()
        btn_row.addStretch()
        btn_row.addWidget(btn)
        layout.addLayout(btn_row)
