// qbluetoothservicediscoveryagent.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QBluetoothServiceDiscoveryAgent : public QObject
{
%TypeHeaderCode
#include <qbluetoothservicediscoveryagent.h>
%End

public:
    enum Error
    {
        NoError,
        InputOutputError,
        PoweredOffError,
        InvalidBluetoothAdapterError,
%If (Qt_6_4_0 -)
        MissingPermissionsError,
%End
        UnknownError,
    };

    enum DiscoveryMode
    {
        MinimalDiscovery,
        FullDiscovery,
    };

    explicit QBluetoothServiceDiscoveryAgent(QObject *parent /TransferThis/ = 0);
    QBluetoothServiceDiscoveryAgent(const QBluetoothAddress &deviceAdapter, QObject *parent /TransferThis/ = 0);
    virtual ~QBluetoothServiceDiscoveryAgent();
    bool isActive() const;
    QBluetoothServiceDiscoveryAgent::Error error() const;
    QString errorString() const;
    QList<QBluetoothServiceInfo> discoveredServices() const;
    void setUuidFilter(const QList<QBluetoothUuid> &uuids);
    void setUuidFilter(const QBluetoothUuid &uuid);
    QList<QBluetoothUuid> uuidFilter() const;
    bool setRemoteAddress(const QBluetoothAddress &address);
    QBluetoothAddress remoteAddress() const;

public slots:
    void start(QBluetoothServiceDiscoveryAgent::DiscoveryMode mode = QBluetoothServiceDiscoveryAgent::MinimalDiscovery);
    void stop();
    void clear();

signals:
    void serviceDiscovered(const QBluetoothServiceInfo &info);
    void finished();
    void canceled();
    void errorOccurred(QBluetoothServiceDiscoveryAgent::Error error);
};

%End
