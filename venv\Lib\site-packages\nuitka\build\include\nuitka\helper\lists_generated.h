//     Copyright 2025, <PERSON>, mailto:<EMAIL> find license text at end of file

/* WARNING, this code is GENERATED. Modify the template CodeTemplateMakeListSmall.c.j2 instead! */

/* This file is included from another C file, help IDEs to still parse it on its own. */
#ifdef __IDE_ONLY__
#include "nuitka/prelude.h"
#endif

extern PyObject *MAKE_LIST1(PyThreadState *tstate, PyObject *arg0);
extern PyObject *MAKE_LIST2(PyThreadState *tstate, PyObject *arg0, PyObject *arg1);
extern PyObject *MAKE_LIST3(PyThreadState *tstate, PyObject *arg0, PyObject *arg1, PyObject *arg2);
extern PyObject *MAKE_LIST4(PyThreadState *tstate, PyObject *list);
extern PyObject *MAKE_LIST5(PyThreadState *tstate, PyObject *list);
extern PyObject *MAKE_LIST6(PyThreadState *tstate, PyObject *list);
extern PyObject *MAKE_LIST7(PyThreadState *tstate, PyObject *list);
extern PyObject *MAKE_LIST8(PyThreadState *tstate, PyObject *list);
extern PyObject *MAKE_LIST9(PyThreadState *tstate, PyObject *list);
extern PyObject *MAKE_LIST10(PyThreadState *tstate, PyObject *list);
extern PyObject *MAKE_LIST11(PyThreadState *tstate, PyObject *list);
extern PyObject *MAKE_LIST12(PyThreadState *tstate, PyObject *list);

//     Part of "Nuitka", an optimizing Python compiler that is compatible and
//     integrates with CPython, but also works on its own.
//
//     Licensed under the Apache License, Version 2.0 (the "License");
//     you may not use this file except in compliance with the License.
//     You may obtain a copy of the License at
//
//        http://www.apache.org/licenses/LICENSE-2.0
//
//     Unless required by applicable law or agreed to in writing, software
//     distributed under the License is distributed on an "AS IS" BASIS,
//     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//     See the License for the specific language governing permissions and
//     limitations under the License.
