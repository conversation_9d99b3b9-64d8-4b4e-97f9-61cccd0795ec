// qcamera.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QCamera : public QObject
{
%TypeHeaderCode
#include <qcamera.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QAudioDecoder, &sipType_QAudioDecoder, -1, 1},
        {sipName_QAudioInput, &sipType_QAudioInput, -1, 2},
        {sipName_QAudioOutput, &sipType_QAudioOutput, -1, 3},
        {sipName_QAudioSink, &sipType_QAudioSink, -1, 4},
        {sipName_QAudioSource, &sipType_QAudioSource, -1, 5},
        {sipName_QCamera, &sipType_QCamera, -1, 6},
        {sipName_QImageCapture, &sipType_QImageCapture, -1, 7},
        {sipName_QMediaCaptureSession, &sipType_QMediaCaptureSession, -1, 8},
        {sipName_QMediaDevices, &sipType_QMediaDevices, -1, 9},
        {sipName_QMediaPlayer, &sipType_QMediaPlayer, -1, 10},
        {sipName_QMediaRecorder, &sipType_QMediaRecorder, -1, 11},
    #if QT_VERSION >= 0x060500
        {sipName_QScreenCapture, &sipType_QScreenCapture, -1, 12},
    #else
        {0, 0, -1, 12},
    #endif
        {sipName_QSoundEffect, &sipType_QSoundEffect, -1, 13},
        {sipName_QVideoSink, &sipType_QVideoSink, -1, 14},
    #if QT_VERSION >= 0x060600
        {sipName_QWindowCapture, &sipType_QWindowCapture, -1, -1},
    #else
        {0, 0, -1, -1},
    #endif
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum Error
    {
        NoError,
        CameraError,
    };

    enum FocusMode
    {
        FocusModeAuto,
        FocusModeAutoNear,
        FocusModeAutoFar,
        FocusModeHyperfocal,
        FocusModeInfinity,
        FocusModeManual,
    };

    enum FlashMode
    {
        FlashOff,
        FlashOn,
        FlashAuto,
    };

    enum TorchMode
    {
        TorchOff,
        TorchOn,
        TorchAuto,
    };

    enum ExposureMode
    {
        ExposureAuto,
        ExposureManual,
        ExposurePortrait,
        ExposureNight,
        ExposureSports,
        ExposureSnow,
        ExposureBeach,
        ExposureAction,
        ExposureLandscape,
        ExposureNightPortrait,
        ExposureTheatre,
        ExposureSunset,
        ExposureSteadyPhoto,
        ExposureFireworks,
        ExposureParty,
        ExposureCandlelight,
        ExposureBarcode,
    };

    enum WhiteBalanceMode
    {
        WhiteBalanceAuto,
        WhiteBalanceManual,
        WhiteBalanceSunlight,
        WhiteBalanceCloudy,
        WhiteBalanceShade,
        WhiteBalanceTungsten,
        WhiteBalanceFluorescent,
        WhiteBalanceFlash,
        WhiteBalanceSunset,
    };

    enum class Feature
    {
        ColorTemperature,
        ExposureCompensation,
        IsoSensitivity,
        ManualExposureTime,
        CustomFocusPoint,
        FocusDistance,
    };

    typedef QFlags<QCamera::Feature> Features;
    QCamera(const QCameraDevice &cameraDevice, QObject *parent /TransferThis/ = 0);
    QCamera(QCameraDevice::Position position, QObject *parent /TransferThis/ = 0);
    explicit QCamera(QObject *parent /TransferThis/ = 0);
    virtual ~QCamera();
    bool isAvailable() const;
    bool isActive() const;
    QMediaCaptureSession *captureSession() const;
    QCameraDevice cameraDevice() const;
    void setCameraDevice(const QCameraDevice &cameraDevice);
    QCameraFormat cameraFormat() const;
    void setCameraFormat(const QCameraFormat &format);
    QCamera::Error error() const;
    QString errorString() const;
    QCamera::Features supportedFeatures() const;
    QCamera::FocusMode focusMode() const;
    void setFocusMode(QCamera::FocusMode mode);
    bool isFocusModeSupported(QCamera::FocusMode mode) const;
    QPointF focusPoint() const;
    QPointF customFocusPoint() const;
    void setCustomFocusPoint(const QPointF &point);
    void setFocusDistance(float d);
    float focusDistance() const;
    float minimumZoomFactor() const;
    float maximumZoomFactor() const;
    float zoomFactor() const;
    void setZoomFactor(float factor);
    QCamera::FlashMode flashMode() const;
    bool isFlashModeSupported(QCamera::FlashMode mode) const;
    bool isFlashReady() const;
    QCamera::TorchMode torchMode() const;
    bool isTorchModeSupported(QCamera::TorchMode mode) const;
    QCamera::ExposureMode exposureMode() const;
    bool isExposureModeSupported(QCamera::ExposureMode mode) const;
    float exposureCompensation() const;
    int isoSensitivity() const;
    int manualIsoSensitivity() const;
    float exposureTime() const;
    float manualExposureTime() const;
    int minimumIsoSensitivity() const;
    int maximumIsoSensitivity() const;
    float minimumExposureTime() const;
    float maximumExposureTime() const;
    QCamera::WhiteBalanceMode whiteBalanceMode() const;
    bool isWhiteBalanceModeSupported(QCamera::WhiteBalanceMode mode) const;
    int colorTemperature() const;

public slots:
    void setActive(bool active);
    void start();
    void stop();
    void zoomTo(float zoom, float rate);
    void setFlashMode(QCamera::FlashMode mode);
    void setTorchMode(QCamera::TorchMode mode);
    void setExposureMode(QCamera::ExposureMode mode);
    void setExposureCompensation(float ev);
    void setManualIsoSensitivity(int iso);
    void setAutoIsoSensitivity();
    void setManualExposureTime(float seconds);
    void setAutoExposureTime();
    void setWhiteBalanceMode(QCamera::WhiteBalanceMode mode);
    void setColorTemperature(int colorTemperature);

signals:
    void activeChanged(bool);
    void errorChanged();
    void errorOccurred(QCamera::Error error, const QString &errorString);
    void cameraDeviceChanged();
    void cameraFormatChanged();
    void supportedFeaturesChanged();
    void focusModeChanged();
    void zoomFactorChanged(float);
    void minimumZoomFactorChanged(float);
    void maximumZoomFactorChanged(float);
    void focusDistanceChanged(float);
    void customFocusPointChanged();
    void flashReady(bool);
    void flashModeChanged();
    void torchModeChanged();
    void exposureTimeChanged(float speed);
    void isoSensitivityChanged(int);
    void exposureCompensationChanged(float);
    void exposureModeChanged();
    void whiteBalanceModeChanged() const;
    void colorTemperatureChanged() const;
    void focusPointChanged();
    void manualExposureTimeChanged(float speed);
    void manualIsoSensitivityChanged(int);
};

%End
