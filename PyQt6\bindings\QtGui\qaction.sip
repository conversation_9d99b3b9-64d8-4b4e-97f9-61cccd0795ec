// qaction.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMenu /External/;

class QAction : public QObject
{
%TypeHeaderCode
#include <qaction.h>
%End

public:
    explicit QAction(QObject *parent /TransferThis/ = 0);
    QAction(const QString &text, QObject *parent /TransferThis/ = 0);
    QAction(const QIcon &icon, const QString &text, QObject *parent /TransferThis/ = 0);
    virtual ~QAction();
    void setActionGroup(QActionGroup *group /KeepReference/);
    QActionGroup *actionGroup() const;
    void setIcon(const QIcon &icon);
    QIcon icon() const;
    void setText(const QString &text);
    QString text() const;
    void setIconText(const QString &text);
    QString iconText() const;
    void setToolTip(const QString &tip);
    QString toolTip() const;
    void setStatusTip(const QString &statusTip);
    QString statusTip() const;
    void setWhatsThis(const QString &what);
    QString whatsThis() const;
    void setSeparator(bool b);
    bool isSeparator() const;
    void setShortcut(const QKeySequence &shortcut);
    QKeySequence shortcut() const;
    void setShortcutContext(Qt::ShortcutContext context);
    Qt::ShortcutContext shortcutContext() const;
    void setFont(const QFont &font);
    QFont font() const;
    void setCheckable(bool);
    bool isCheckable() const;
    QVariant data() const;
    void setData(const QVariant &var);
    bool isChecked() const;
    bool isEnabled() const;
    bool isVisible() const;

    enum ActionEvent
    {
        Trigger,
        Hover,
    };

    void activate(QAction::ActionEvent event);
    bool showStatusText(QObject *object = 0);

protected:
    virtual bool event(QEvent *);

public slots:
    void trigger();
    void hover();
    void setChecked(bool);
    void toggle();
    void setEnabled(bool);
    void setDisabled(bool b);
    void setVisible(bool);

signals:
    void changed();
    void triggered(bool checked = false);
    void hovered();
    void toggled(bool);

public:
    enum MenuRole
    {
        NoRole,
        TextHeuristicRole,
        ApplicationSpecificRole,
        AboutQtRole,
        AboutRole,
        PreferencesRole,
        QuitRole,
    };

    void setShortcuts(const QList<QKeySequence> &shortcuts);
    void setShortcuts(QKeySequence::StandardKey);
    QList<QKeySequence> shortcuts() const;
    void setAutoRepeat(bool);
    bool autoRepeat() const;
    void setMenuRole(QAction::MenuRole menuRole);
    QAction::MenuRole menuRole() const;
    QMenu *menu() const;
%MethodCode
        typedef QMenu *(*pyqt6_qtgui_qaction_menu_t)(const QAction *);
        
        pyqt6_qtgui_qaction_menu_t pyqt6_qtgui_qaction_menu = (pyqt6_qtgui_qaction_menu_t)sipImportSymbol("pyqt6_qaction_menu");
        
        sipRes = (pyqt6_qtgui_qaction_menu ? pyqt6_qtgui_qaction_menu(sipCpp) : SIP_NULLPTR);
%End

    void setMenu(QMenu *menu);
%MethodCode
        typedef void *(*pyqt6_qtgui_qaction_set_menu_t)(QAction *, QMenu *);
        
        pyqt6_qtgui_qaction_set_menu_t pyqt6_qtgui_qaction_set_menu = (pyqt6_qtgui_qaction_set_menu_t)sipImportSymbol("pyqt6_qaction_set_menu");
        
        if (pyqt6_qtgui_qaction_set_menu)
            pyqt6_qtgui_qaction_set_menu(sipCpp, a0);
%End

    void setIconVisibleInMenu(bool visible);
    bool isIconVisibleInMenu() const;

    enum Priority
    {
        LowPriority,
        NormalPriority,
        HighPriority,
    };

    void setPriority(QAction::Priority priority);
    QAction::Priority priority() const;
    void setShortcutVisibleInContextMenu(bool show);
    bool isShortcutVisibleInContextMenu() const;
    QList<QObject *> associatedObjects() const;

public slots:
    void resetEnabled();

signals:
    void enabledChanged(bool enabled);
    void checkableChanged(bool checkable);
    void visibleChanged();
};
