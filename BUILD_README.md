# 🔒 三合一加密打包工具

## 概述

本项目采用了先进的三层加密保护方案，为Android刷机工具提供最高级别的代码保护。

## 🛡️ 分层保护架构

### 外层保护：Nuitka + VMProtect + 自签名证书
- **Nuitka编译**: 将Python代码编译为原生机器码
- **VMProtect加壳**: 虚拟化保护，防止逆向工程
- **自签名证书**: 数字签名验证，确保文件完整性

### 中层保护：PyArmor混淆
- **代码混淆**: 使用PyArmor对非核心模块进行混淆
- **字符串加密**: 加密字符串常量
- **控制流混淆**: 打乱程序执行流程

### 内层保护：Cython编译
- **核心算法保护**: 将关键算法编译为C扩展
- **性能优化**: 提升执行效率
- **源码隐藏**: 核心逻辑完全隐藏

## 📁 模块分类

### 核心模块 (Cython编译)
- `coloros.py` - ColorOS解锁核心算法
- `coloros15.py` - ColorOS 15核心算法  
- `utils.py` - 工具类核心算法

### 非核心模块 (PyArmor混淆)
- `flash_tool.py` - UI界面
- `main.py` - 主程序
- `jiebao.py` - 捷宝功能
- `zhidinyishuaxie.py` - 自定义刷写
- `config.py` - 配置文件
- `payload_extractor.py` - 解包工具
- `fastboodt.py` - Fastboot工具
- `custom_messagebox.py` - 自定义消息框

## 🚀 使用方法

### 方法一：使用批处理文件 (推荐)
```bash
# 双击运行
build.bat
```

### 方法二：使用Python脚本
```bash
# 完整构建
python build.py

# 仅Cython编译
python build.py --cython-only

# 仅PyArmor混淆
python build.py --pyarmor-only

# 仅Nuitka编译
python build.py --nuitka-only

# 清理构建文件
python build.py --clean

# 查看帮助
python build.py --help
```

## 📋 系统要求

### 必需工具
- Python 3.8+
- Visual Studio Build Tools (Windows)
- Git

### 自动安装的依赖
- PyQt6 >= 6.4.0
- Nuitka >= 1.8.0
- Cython >= 3.0.0
- PyArmor >= 8.0.0
- pywin32 >= 305

### 可选工具
- VMProtect (商业软件，需单独购买)
- 代码签名证书

## 🔧 构建流程

1. **环境检查** - 验证构建工具可用性
2. **依赖安装** - 自动安装所需Python包
3. **文件准备** - 复制资源文件和依赖
4. **Cython编译** - 编译核心算法模块
5. **PyArmor混淆** - 混淆非核心代码
6. **模块整合** - 合并编译后的模块
7. **Nuitka编译** - 生成最终可执行文件
8. **加壳保护** - 应用VMProtect保护 (可选)
9. **数字签名** - 添加代码签名 (可选)

## 📊 构建输出

```
项目根目录/
├── build_output/           # 最终输出目录
├── nuitka_build/          # Nuitka编译目录
├── cython_build/          # Cython编译目录
├── pyarmor_build/         # PyArmor混淆目录
└── temp_build/            # 临时构建目录
```

## ⚠️ 注意事项

1. **首次构建时间较长** - 需要下载和编译大量依赖
2. **磁盘空间要求** - 建议至少5GB可用空间
3. **网络连接** - 需要稳定的网络下载依赖包
4. **VMProtect许可** - 需要有效的VMProtect许可证
5. **证书配置** - 代码签名需要配置有效证书

## 🐛 故障排除

### 常见问题

**Q: Cython编译失败**
A: 确保已安装Visual Studio Build Tools

**Q: PyArmor混淆失败**  
A: 检查PyArmor版本，建议使用最新版本

**Q: Nuitka编译超时**
A: 增加超时时间或使用更强的硬件配置

**Q: 缺少依赖包**
A: 运行 `python build.py --clean` 后重新构建

### 日志分析
构建过程会生成详细日志，包含时间戳和状态信息，便于问题定位。

## 📞 技术支持

如遇到构建问题，请提供：
1. 完整的错误日志
2. 系统环境信息
3. Python版本信息
4. 构建命令和参数

## 🔄 版本历史

- v1.0.0 - 初始版本，实现三合一加密保护
