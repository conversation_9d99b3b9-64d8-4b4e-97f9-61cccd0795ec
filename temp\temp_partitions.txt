frp
recovery_b
userdata
mdm_oem_stanvbk
xbl_config_b
vbmeta_system_b
qupfw_b
fw_ufs5_b
dtbo_b
aging
imagefv_b
fw_ufs1_b
op1
devcfg_b
storsec_a
vbmeta_b
fw_ufs4_b
multiimgoem_b
rawdump
uefisecapp_b
recovery_a
aging_mod
fw_ufs3_a
fw_ufs5_a
aop_b
boot_a
LOGO_b
vbmeta_system_a
fw_ufs2_a
misc
devinfo
oem_dycnvbk
fw_ufs4_a
config
keystore
multiimgoem_a
xbl_b
opproduct_b
tz_b
secdata
sdc
mdm1oemnvbktmp
apdp
catecontentfv
devcfg_a
msadp
vbmeta_a
reserve2
core_nhlos
xbl_a
uefisecapp_a
reserve4
mdmddr
dip
modem_b
fsc
catefv
cmnlib64_a
fw_ufs3_b
qupfw_a
reserve1
dtbo_a
reserve3
tz_a
keymaster_a
imagefv_a
fw_ufs2_b
cmnlib_a
sdd
fw_ufs1_a
ALIGN_TO_128K_2
mdm1m9kefs2
mdm1m9kefs3
modemst2
opproduct_a
ssd
mdm_oem_dycnvbk
param
splash
modem_a
oem_stanvbk
toolsfv
mdtpsecapp_a
cateloader
mdtp_b
abl_a
dsp_b
LOGO_a
storsec_b
boot_b
spunvm
hyp_a
aop_a
limits
bluetooth_a
mdtpsecapp_b
logfs
mdtp_a
abl_b
logdump
dsp_a
keymaster_b
uefivarstore
hyp_b
cmnlib_b
bluetooth_b
sde
cmnlib64_b
sdb
xbl_config_a
ddr
cdt
ALIGN_TO_128K_1
modemst1
fsg
mdm1m9kefsc
mdm1m9kefs1
sdf
super
metadata
ADB
adl
my_carrier
1
my_heytap
my_bigball_a
my_carrier_a
my_company_a
my_engineering_a
my_heytap_a
my_manifest_a
my_preload_a
my_product_a
my_region_a
my_stock_a
odm_a
product_a
system_a
system_ext_a
vendor_a