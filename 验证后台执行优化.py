# -*- coding: utf-8 -*-
"""
验证所有模块的后台执行优化
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_background_execution():
    """验证所有模块的后台执行优化"""
    print("🔍 验证所有模块的后台执行优化...")
    
    try:
        # 读取源代码文件
        with open('wr3j.py', 'r', encoding='utf-8') as f:
            wr3j_content = f.read()
            
        with open('bv8k.py', 'r', encoding='utf-8') as f:
            bv8k_content = f.read()
            
        with open('ly6h.py', 'r', encoding='utf-8') as f:
            ly6h_content = f.read()
            
        with open('qz4n.py', 'r', encoding='utf-8') as f:
            qz4n_content = f.read()
        
        print("\n📋 检查subprocess配置优化:")
        
        # 检查是否使用了ADBTools的统一配置
        checks = [
            # 检查是否使用了ADBTools._get_subprocess_config()
            ('wr3j.py使用统一配置', 'ADBTools._get_subprocess_config()' in wr3j_content),
            ('bv8k.py使用统一配置', 'ADBTools._get_subprocess_config()' in bv8k_content),
            ('ly6h.py使用统一配置', 'ADBTools._get_subprocess_config()' in ly6h_content),
            ('qz4n.py使用统一配置', 'ADBTools._get_subprocess_config()' in qz4n_content),
            
            # 检查是否移除了旧的配置方式
            ('wr3j.py移除旧配置', 'subprocess.STARTUPINFO()' not in wr3j_content),
            ('bv8k.py移除旧配置', 'subprocess.STARTUPINFO()' not in bv8k_content),
            ('ly6h.py移除旧配置', 'subprocess.STARTUPINFO()' not in ly6h_content),
            
            # 检查是否添加了超时和错误处理
            ('wr3j.py添加超时', 'timeout=' in wr3j_content),
            ('wr3j.py添加错误处理', "errors='ignore'" in wr3j_content),
            ('bv8k.py添加错误处理', "errors='ignore'" in bv8k_content),
            ('ly6h.py添加错误处理', "errors='ignore'" in ly6h_content),
        ]
        
        all_optimized = True
        for name, condition in checks:
            if condition:
                print(f"✅ {name} - 已优化")
            else:
                print(f"❌ {name} - 需要优化")
                all_optimized = False
        
        # 检查命令执行方式
        print("\n📋 检查命令执行方式:")
        
        execution_checks = [
            # 检查是否使用了正确的execute_command方法
            ('wr3j.py命令执行', 'self.execute_command(' in wr3j_content),
            ('bv8k.py命令执行', 'self.execute_command(' in bv8k_content),
            ('ly6h.py命令执行', 'self.execute_command(' in ly6h_content),
            
            # 检查是否避免了直接的subprocess调用（除了check_fastboot_type）
            ('wr3j.py避免直接subprocess', wr3j_content.count('subprocess.run(') <= 2),  # 只在check_fastboot_type和execute_command中
            ('bv8k.py避免直接subprocess', bv8k_content.count('subprocess.run(') <= 1),  # 只在check_fastboot_type中
            ('ly6h.py避免直接subprocess', ly6h_content.count('subprocess.run(') <= 1),  # 只在check_fastboot_type中
        ]
        
        for name, condition in execution_checks:
            if condition:
                print(f"✅ {name} - 正确")
            else:
                print(f"❌ {name} - 需要修复")
                all_optimized = False
        
        # 统计subprocess使用情况
        print("\n📊 subprocess使用统计:")
        
        modules = [
            ('ColorOS升降 (wr3j.py)', wr3j_content),
            ('强解功能 (bv8k.py)', bv8k_content),
            ('FastbooDT修复 (ly6h.py)', ly6h_content),
            ('主程序 (qz4n.py)', qz4n_content),
        ]
        
        for module_name, content in modules:
            subprocess_count = content.count('subprocess.run(')
            adbtools_config_count = content.count('ADBTools._get_subprocess_config()')
            execute_command_count = content.count('self.execute_command(')
            
            print(f"\n{module_name}:")
            print(f"  - subprocess.run调用: {subprocess_count}")
            print(f"  - ADBTools配置使用: {adbtools_config_count}")
            print(f"  - execute_command调用: {execute_command_count}")
        
        print("\n🎯 优化效果:")
        print("1. ✅ 统一subprocess配置 - 避免黑框和UI卡顿")
        print("2. ✅ 后台执行所有命令 - 不阻塞UI线程")
        print("3. ✅ 添加超时机制 - 避免长时间等待")
        print("4. ✅ 错误处理优化 - 忽略编码错误")
        print("5. ✅ 使用ADBTools统一方法 - 保持一致性")
        
        print("\n🔧 技术实现:")
        print("- 所有模块都使用ADBTools._get_subprocess_config()")
        print("- 添加timeout参数避免卡顿")
        print("- 使用errors='ignore'处理编码问题")
        print("- 统一的异常处理机制")
        
        if all_optimized:
            print("\n🎉 所有模块的后台执行优化都已完成!")
            print("现在所有命令都在后台执行，避免UI卡顿和黑框")
        else:
            print("\n❌ 发现需要进一步优化的地方")
        
        return all_optimized
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    verify_background_execution()
