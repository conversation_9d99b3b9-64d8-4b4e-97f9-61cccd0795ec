
import sys
import os
import ctypes
import time
import random

# 设置环境变量
os.environ["PYTHONIOENCODING"] = "utf-8"

# 反调试检查
def anti_debug():
    try:
        # 检测调试器
        if ctypes.windll.kernel32.IsDebuggerPresent():
            sys.exit(1)
            
        # 检测虚拟机
        try:
            ctypes.windll.kernel32.GetModuleHandleW('SbieDll.dll')
            sys.exit(1)
        except:
            pass
            
        # 检测时间差异
        start = time.time()
        [random.random() for _ in range(10000)]
        if time.time() - start > 0.5:
            sys.exit(1)
    except:
        sys.exit(1)

# 执行检查
anti_debug()

# 设置默认编码
if hasattr(sys, "setdefaultencoding"):
    sys.setdefaultencoding("utf-8")
