# -*- coding: utf-8 -*-
"""
步骤2: Nuitka打包
使用Cython编译的模块进行最终打包
"""

import os
import sys
import shutil
import subprocess
import time
import json

def log(message, level="INFO"):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    if level == "SUCCESS":
        print(f"[{timestamp}] ✅ {message}")
    elif level == "ERROR":
        print(f"[{timestamp}] ❌ {message}")
    elif level == "WARNING":
        print(f"[{timestamp}] ⚠️ {message}")
    elif level == "STEP":
        print(f"[{timestamp}] 🔧 {message}")
    else:
        print(f"[{timestamp}] {message}")

def check_cython_build():
    """检查Cython编译结果"""
    log("检查Cython编译结果", "STEP")
    
    build_dir = "cython_compiled_modules"
    mapping_file = os.path.join(build_dir, "cython_mapping.json")
    
    if not os.path.exists(build_dir):
        log("Cython编译目录不存在", "ERROR")
        log("请先运行 step1_cython_compile.py", "ERROR")
        return False, None, {}
    
    if not os.path.exists(mapping_file):
        log("Cython映射文件不存在", "ERROR")
        return False, None, {}
    
    try:
        with open(mapping_file, 'r', encoding='utf-8') as f:
            mapping_data = json.load(f)
        
        compiled_modules = mapping_data.get("modules", {})
        success_rate = mapping_data.get("success_rate", "0%")
        
        log(f"找到Cython编译结果: {len(compiled_modules)} 个模块")
        log(f"编译成功率: {success_rate}")
        
        # 验证编译文件存在
        valid_modules = {}
        for original, compiled in compiled_modules.items():
            compiled_path = os.path.join(build_dir, compiled)
            if os.path.exists(compiled_path):
                size = os.path.getsize(compiled_path)
                valid_modules[original] = compiled
                log(f"验证模块: {compiled} ({size:,} 字节)")
            else:
                log(f"编译文件不存在: {compiled}", "WARNING")
        
        if valid_modules:
            log(f"有效的Cython模块: {len(valid_modules)} 个", "SUCCESS")
            return True, build_dir, valid_modules
        else:
            log("没有有效的Cython模块", "ERROR")
            return False, None, {}
            
    except Exception as e:
        log(f"读取映射文件失败: {e}", "ERROR")
        return False, None, {}

def check_nuitka_environment():
    """检查Nuitka环境"""
    log("检查Nuitka环境", "STEP")
    
    try:
        import nuitka
        log("Nuitka - 已安装", "SUCCESS")
    except ImportError:
        log("安装Nuitka...", "STEP")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "nuitka", "--quiet"])
            log("Nuitka 安装成功", "SUCCESS")
        except subprocess.CalledProcessError:
            log("Nuitka 安装失败", "ERROR")
            return False
    
    # 检查必需文件
    required_files = ["main.py"]
    for file in required_files:
        if os.path.exists(file):
            log(f"找到必需文件: {file}", "SUCCESS")
        else:
            log(f"缺少必需文件: {file}", "ERROR")
            return False
    
    return True

def create_nuitka_package(build_dir, compiled_modules):
    """创建Nuitka包"""
    log("开始Nuitka打包", "STEP")
    log("=" * 60)
    
    # 构建Nuitka命令
    nuitka_cmd = [
        sys.executable, "-m", "nuitka",
        "--standalone",
        "--onefile",
        "--onefile-tempdir-spec=\\syiming\\yimin_{PID}_{TIME}",
        "--windows-console-mode=disable",
        "--enable-plugin=pyqt6",
        "--assume-yes-for-downloads",
        "--show-progress",
        "--show-memory",
        "--remove-output",
        "--output-filename=益民工具箱.exe",
    ]
    
    # 添加图标
    if os.path.exists("ico/icon.ico"):
        nuitka_cmd.append("--windows-icon-from-ico=ico/icon.ico")
        log("添加图标文件", "SUCCESS")
    
    # 添加所有Cython编译模块
    log("添加Cython编译模块:")
    for original, compiled in compiled_modules.items():
        compiled_path = os.path.join(build_dir, compiled)
        if os.path.exists(compiled_path):
            nuitka_cmd.append(f"--include-data-file={compiled_path}={compiled}")
            size = os.path.getsize(compiled_path)
            log(f"  {compiled} ({size:,} 字节)")
    
    # 添加Cython映射文件
    mapping_file = os.path.join(build_dir, "cython_mapping.json")
    if os.path.exists(mapping_file):
        nuitka_cmd.append(f"--include-data-file={mapping_file}=cython_mapping.json")
        log("添加Cython映射文件", "SUCCESS")
    
    # 添加Cython加载器
    loader_file = os.path.join(build_dir, "cython_loader.py")
    if os.path.exists(loader_file):
        nuitka_cmd.append(f"--include-data-file={loader_file}=cython_loader.py")
        log("添加Cython加载器", "SUCCESS")
    
    # 添加ADBTools（逐个文件确保包含）
    if os.path.exists("ADBTools"):
        adb_files = [f for f in os.listdir("ADBTools") if os.path.isfile(os.path.join("ADBTools", f))]
        for file in adb_files:
            file_path = os.path.join("ADBTools", file)
            nuitka_cmd.append(f"--include-data-file={file_path}=ADBTools/{file}")
        log(f"添加ADBTools目录 ({len(adb_files)} 个文件)", "SUCCESS")
    
    # 添加其他资源目录
    resource_dirs = ["ico", "tup"]
    for dir_name in resource_dirs:
        if os.path.exists(dir_name):
            nuitka_cmd.append(f"--include-data-dir={dir_name}={dir_name}")
            files_count = len([f for f in os.listdir(dir_name) if os.path.isfile(os.path.join(dir_name, f))])
            log(f"添加目录: {dir_name} ({files_count} 个文件)")
    
    # 添加主脚本
    nuitka_cmd.append("main.py")
    
    log(f"\nNuitka命令参数数量: {len(nuitka_cmd)}")
    log("开始Nuitka编译...", "STEP")
    
    try:
        start_time = time.time()
        result = subprocess.run(nuitka_cmd, timeout=2400)  # 40分钟超时
        
        if result.returncode == 0:
            output_file = "益民工具箱.exe"
            if os.path.exists(output_file):
                size = os.path.getsize(output_file)
                build_time = time.time() - start_time
                minutes = int(build_time // 60)
                seconds = int(build_time % 60)
                
                log("Nuitka编译成功！", "SUCCESS")
                log(f"输出文件: {output_file}")
                log(f"文件大小: {size:,} 字节 ({size/1024/1024:.1f} MB)")
                log(f"编译时间: {minutes}分{seconds}秒")
                
                # 移动到最终输出目录
                final_dir = "advanced_build/final_output"
                os.makedirs(final_dir, exist_ok=True)
                final_path = os.path.join(final_dir, output_file)
                shutil.move(output_file, final_path)
                log(f"已移动到: {final_path}", "SUCCESS")
                
                return True, final_path
            else:
                log("Nuitka编译完成但未找到输出文件", "ERROR")
        else:
            log(f"Nuitka编译失败，返回码: {result.returncode}", "ERROR")
        
        return False, None
        
    except subprocess.TimeoutExpired:
        log("Nuitka编译超时", "ERROR")
        return False, None
    except Exception as e:
        log(f"Nuitka编译异常: {e}", "ERROR")
        return False, None

def create_vmprotect_config(output_file):
    """创建VMProtect配置文件"""
    log("创建VMProtect配置文件", "STEP")
    
    if not output_file or not os.path.exists(output_file):
        log("输出文件不存在，跳过VMProtect配置", "WARNING")
        return
    
    # VMProtect项目文件
    vmprotect_config = f'''<?xml version="1.0" encoding="UTF-8"?>
<Document Version="1.0">
    <Options>
        <InputFileName>{os.path.basename(output_file)}</InputFileName>
        <OutputFileName>{os.path.basename(output_file).replace('.exe', '_VMProtect.exe')}</OutputFileName>
        <Watermark>益民工具箱</Watermark>
        <CheckKernelDebugger>true</CheckKernelDebugger>
        <CheckDebugger>true</CheckDebugger>
        <CheckVirtualMachine>true</CheckVirtualMachine>
        <StripDebugInfo>true</StripDebugInfo>
        <StripRelocations>true</StripRelocations>
        <PackOutputFile>true</PackOutputFile>
        <ImportProtection>true</ImportProtection>
        <ResourceProtection>true</ResourceProtection>
        <MemoryProtection>true</MemoryProtection>
    </Options>
    <Procedures>
        <!-- 核心算法保护配置 -->
    </Procedures>
</Document>'''
    
    config_dir = os.path.dirname(output_file)
    vmprotect_file = os.path.join(config_dir, "vmprotect_config.vmp")
    
    with open(vmprotect_file, 'w', encoding='utf-8') as f:
        f.write(vmprotect_config)
    
    log(f"VMProtect配置已保存: {vmprotect_file}", "SUCCESS")

def create_build_report(build_dir, compiled_modules, output_file):
    """创建构建报告"""
    log("生成构建报告", "STEP")
    
    if not output_file:
        return
    
    file_size = os.path.getsize(output_file) if os.path.exists(output_file) else 0
    
    report = {
        "build_info": {
            "build_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "output_file": output_file,
            "file_size": file_size,
            "file_size_mb": round(file_size / 1024 / 1024, 2)
        },
        "protection_layers": {
            "inner_layer": "Cython编译核心算法",
            "outer_layer": "Nuitka编译 + VMProtect加壳准备"
        },
        "cython_modules": {
            "total_modules": len(compiled_modules),
            "modules": compiled_modules
        },
        "next_steps": [
            "使用VMProtect对exe文件进行加壳保护",
            "创建自签名证书并对exe文件签名",
            "测试所有功能确保正常运行"
        ]
    }
    
    report_file = os.path.join(os.path.dirname(output_file), "cython_build_report.json")
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    log(f"构建报告已保存: {report_file}", "SUCCESS")

def main():
    """主函数"""
    log("步骤2: Nuitka打包", "STEP")
    log("=" * 60)
    
    # 检查Cython编译结果
    success, build_dir, compiled_modules = check_cython_build()
    if not success:
        return False
    
    # 检查Nuitka环境
    if not check_nuitka_environment():
        return False
    
    # 执行Nuitka打包
    success, output_file = create_nuitka_package(build_dir, compiled_modules)
    
    if success:
        log("\n" + "=" * 60)
        log("Cython分层保护版构建完成！", "SUCCESS")
        log("保护层级:")
        log(f"  内层: {len(compiled_modules)} 个模块Cython编译")
        log("  外层: Nuitka编译完成")
        log("  准备: VMProtect加壳配置")
        log("=" * 60)
        
        # 创建VMProtect配置
        create_vmprotect_config(output_file)
        
        # 生成构建报告
        create_build_report(build_dir, compiled_modules, output_file)
        
        log("\n🎉 分层保护构建成功完成！")
        log("下一步: 使用VMProtect进行最终加壳保护")
        
        return True
    else:
        log("Nuitka打包失败", "ERROR")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
