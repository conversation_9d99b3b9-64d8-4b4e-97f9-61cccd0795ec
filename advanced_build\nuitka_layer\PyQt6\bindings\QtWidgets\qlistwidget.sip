// qlistwidget.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QListWidgetItem /Supertype=PyQt6.sip.wrapper/
{
%TypeHeaderCode
#include <qlistwidget.h>
%End

public:
    enum ItemType /BaseType=IntEnum/
    {
        Type,
        UserType,
    };

    QListWidgetItem(QListWidget *parent /TransferThis/ = 0, int type = QListWidgetItem::Type);
    QListWidgetItem(const QString &text, QListWidget *parent /TransferThis/ = 0, int type = QListWidgetItem::Type);
    QListWidgetItem(const QIcon &icon, const QString &text, QListWidget *parent /TransferThis/ = 0, int type = QListWidgetItem::Type);
    QListWidgetItem(const QListWidgetItem &other);
    virtual ~QListWidgetItem();
    virtual QListWidgetItem *clone() const /Factory/;
    QListWidget *listWidget() const;
    Qt::ItemFlags flags() const;
    QString text() const;
    QIcon icon() const;
    QString statusTip() const;
    QString toolTip() const;
    QString whatsThis() const;
    QFont font() const;
    int textAlignment() const;
%If (Qt_6_4_0 -)
    void setTextAlignment(Qt::Alignment alignment);
%End
    void setTextAlignment(int alignment);
    Qt::CheckState checkState() const;
    void setCheckState(Qt::CheckState state);
    QSize sizeHint() const;
    void setSizeHint(const QSize &size);
    virtual QVariant data(int role) const;
    virtual void setData(int role, const QVariant &value);
    virtual bool operator<(const QListWidgetItem &other /NoCopy/) const;
    virtual void read(QDataStream &in) /ReleaseGIL/;
    virtual void write(QDataStream &out) const /ReleaseGIL/;
    int type() const;
    void setFlags(Qt::ItemFlags aflags);
    void setText(const QString &atext);
    void setIcon(const QIcon &aicon);
    void setStatusTip(const QString &astatusTip);
    void setToolTip(const QString &atoolTip);
    void setWhatsThis(const QString &awhatsThis);
    void setFont(const QFont &afont);
    QBrush background() const;
    void setBackground(const QBrush &brush);
    QBrush foreground() const;
    void setForeground(const QBrush &brush);
    void setSelected(bool aselect);
    bool isSelected() const;
    void setHidden(bool ahide);
    bool isHidden() const;

private:
    QListWidgetItem &operator=(const QListWidgetItem &);
};

QDataStream &operator<<(QDataStream &out, const QListWidgetItem &item) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &in, QListWidgetItem &item /Constrained/) /ReleaseGIL/;

class QListWidget : public QListView
{
%TypeHeaderCode
#include <qlistwidget.h>
%End

public:
    explicit QListWidget(QWidget *parent /TransferThis/ = 0);
    virtual ~QListWidget();
    QListWidgetItem *item(int row) const;
    int row(const QListWidgetItem *item) const;
    void insertItem(int row, QListWidgetItem *item /Transfer/);
    void insertItem(int row, const QString &label);
    void insertItems(int row, const QStringList &labels);
    void addItem(QListWidgetItem *aitem /Transfer/);
    void addItem(const QString &label);
    void addItems(const QStringList &labels);
    QListWidgetItem *takeItem(int row) /TransferBack/;
    int count() const /__len__/;
    QListWidgetItem *currentItem() const;
    void setCurrentItem(QListWidgetItem *item);
    void setCurrentItem(QListWidgetItem *item, QItemSelectionModel::SelectionFlags command);
    int currentRow() const;
    void setCurrentRow(int row);
    void setCurrentRow(int row, QItemSelectionModel::SelectionFlags command);
    QListWidgetItem *itemAt(const QPoint &p) const;
    QListWidgetItem *itemAt(int ax, int ay) const;
    QWidget *itemWidget(QListWidgetItem *item) const;
    void setItemWidget(QListWidgetItem *item, QWidget *widget /Transfer/);
%MethodCode
        // We have to break the association with any existing widget.
        QWidget *w = sipCpp->itemWidget(a0);
        
        if (w)
        {
            PyObject *wo = sipGetPyObject(w, sipType_QWidget);
        
            if (wo)
                sipTransferTo(wo, 0);
        }
        
        Py_BEGIN_ALLOW_THREADS
        sipCpp->setItemWidget(a0, a1);
        Py_END_ALLOW_THREADS
%End

    QRect visualItemRect(const QListWidgetItem *item) const;
    void sortItems(Qt::SortOrder order = Qt::AscendingOrder);
    void editItem(QListWidgetItem *item);
    void openPersistentEditor(QListWidgetItem *item);
    void closePersistentEditor(QListWidgetItem *item);
    QList<QListWidgetItem *> selectedItems() const;
    QList<QListWidgetItem *> findItems(const QString &text, Qt::MatchFlags flags) const;

public slots:
    void clear();
    void scrollToItem(const QListWidgetItem *item, QAbstractItemView::ScrollHint hint = QAbstractItemView::EnsureVisible);

signals:
    void itemPressed(QListWidgetItem *item);
    void itemClicked(QListWidgetItem *item);
    void itemDoubleClicked(QListWidgetItem *item);
    void itemActivated(QListWidgetItem *item);
    void itemEntered(QListWidgetItem *item);
    void itemChanged(QListWidgetItem *item);
    void currentItemChanged(QListWidgetItem *current, QListWidgetItem *previous);
    void currentTextChanged(const QString &currentText);
    void currentRowChanged(int currentRow);
    void itemSelectionChanged();

protected:
    virtual QStringList mimeTypes() const;
    virtual QMimeData *mimeData(const QList<QListWidgetItem *> &items) const /TransferBack/;
    virtual bool dropMimeData(int index, const QMimeData *data, Qt::DropAction action);
    virtual Qt::DropActions supportedDropActions() const;
    virtual bool event(QEvent *e);
    virtual void dropEvent(QDropEvent *event);

public:
    QList<QListWidgetItem *> items(const QMimeData *data) const;
    QModelIndex indexFromItem(const QListWidgetItem *item) const;
    QListWidgetItem *itemFromIndex(const QModelIndex &index) const;
    void setSortingEnabled(bool enable);
    bool isSortingEnabled() const;
    void removeItemWidget(QListWidgetItem *aItem);
%MethodCode
        // We have to break the association with any existing widget.
        QWidget *w = sipCpp->itemWidget(a0);
        
        if (w)
        {
            PyObject *wo = sipGetPyObject(w, sipType_QWidget);
        
            if (wo)
                sipTransferTo(wo, 0);
        }
        
        Py_BEGIN_ALLOW_THREADS
        sipCpp->removeItemWidget(a0);
        Py_END_ALLOW_THREADS
%End

    virtual void setSelectionModel(QItemSelectionModel *selectionModel);
    bool isPersistentEditorOpen(QListWidgetItem *item) const;

private:
    virtual void setModel(QAbstractItemModel *model /KeepReference/);
};
