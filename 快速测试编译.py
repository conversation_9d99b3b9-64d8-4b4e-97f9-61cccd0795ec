# -*- coding: utf-8 -*-
"""
快速测试编译 - 测试修复后的导入问题
"""

import os
import sys
import shutil
import subprocess
import time

def log(message):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def quick_compile_test():
    """快速编译测试"""
    log("🧪 快速编译测试 - 验证导入修复")
    
    # 测试模块列表（选择几个关键模块）
    test_modules = [
        "hs7v.py",  # 更多功能模块
        "qz4n.py",  # 主界面（包含导入修复）
        "fn2w.py",  # 小模块测试
    ]
    
    # 创建输出目录
    output_dir = "quick_test_compiled"
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    os.makedirs(output_dir)
    
    compiled_count = 0
    failed_count = 0
    
    for module_file in test_modules:
        log(f"\n📦 测试编译: {module_file}")
        
        if not os.path.exists(module_file):
            log(f"❌ 文件不存在: {module_file}")
            failed_count += 1
            continue
        
        # 模块名（短名称）
        module_name = module_file.replace('.py', '')
        short_name = f"{module_name}.pyd"
        
        # 创建setup.py
        setup_content = f'''
from setuptools import setup
from Cython.Build import cythonize

setup(
    ext_modules = cythonize("{module_file}", language_level=3),
    zip_safe=False,
)
'''
        
        setup_file = f"setup_{module_name}_quick.py"
        with open(setup_file, "w", encoding="utf-8") as f:
            f.write(setup_content)
        
        try:
            # 执行编译
            cmd = [sys.executable, setup_file, "build_ext", "--inplace"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=90)
            
            # 清理setup文件
            if os.path.exists(setup_file):
                os.remove(setup_file)
            
            if result.returncode == 0:
                # 查找生成的.pyd文件
                pyd_files = [f for f in os.listdir('.') if f.startswith(module_name) and f.endswith('.pyd')]
                
                if pyd_files:
                    original_pyd = pyd_files[0]
                    size = os.path.getsize(original_pyd)
                    
                    # 重命名为短名称
                    os.rename(original_pyd, short_name)
                    
                    # 移动到输出目录
                    output_path = os.path.join(output_dir, short_name)
                    shutil.move(short_name, output_path)
                    
                    log(f"✅ 编译成功: {original_pyd} → {short_name} ({size:,} 字节)")
                    compiled_count += 1
                    
                    # 清理.c文件
                    c_files = [f for f in os.listdir('.') if f.startswith(module_name) and f.endswith('.c')]
                    for c_file in c_files:
                        try:
                            os.remove(c_file)
                        except:
                            pass
                else:
                    log(f"⚠️ 编译完成但未找到.pyd文件")
                    failed_count += 1
            else:
                log(f"❌ 编译失败:")
                if result.stderr:
                    log(f"错误: {result.stderr[:200]}")
                failed_count += 1
            
        except Exception as e:
            log(f"❌ 编译异常: {e}")
            failed_count += 1
        finally:
            # 清理临时文件
            if os.path.exists(setup_file):
                try:
                    os.remove(setup_file)
                except:
                    pass
    
    # 总结
    log(f"\n📊 快速测试结果:")
    log(f"总模块数: {len(test_modules)}")
    log(f"编译成功: {compiled_count}")
    log(f"编译失败: {failed_count}")
    log(f"成功率: {compiled_count/len(test_modules)*100:.1f}%")
    
    if compiled_count > 0:
        log(f"\n✅ 编译文件保存在: {output_dir}/")
        return True
    else:
        log(f"\n❌ 所有模块编译失败")
        return False

if __name__ == "__main__":
    quick_compile_test()
