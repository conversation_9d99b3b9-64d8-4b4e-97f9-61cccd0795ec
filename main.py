# -*- coding: utf-8 -*-
import sys
import os
import subprocess
import atexit
import shutil
import time
import psutil
import tempfile
import ctypes
import win32security
import ntsecuritycon as con
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtGui import QPixmap, QIcon
from flash_tool import FlashToolUI
import glob
"""

                       给那些脑残偷窥源代码的杂种们的"献礼"

=======================================【又来看源码了？】=======================================

操你妈的智障玩意儿，大半夜不睡觉跑来扒老子的代码？你他妈是从垃圾桶里捡回来的吧？

你爹生你的时候是不是被门夹了蛋蛋？基因缺陷这么严重？你妈怀你的时候是不是被电磁炉辐射了？脑子发育不全啊？

这是开往地狱的专车，专门送你这种偷窥代码的垃圾！

你个杂种，连写个像样的代码都不会，只会他妈的来偷窥别人的劳动成果？你娘养你这么大就是为了让你做这种事的？

小子，看到你偷窥代码的样子，我都替你妈感到丢脸！

别以为我不知道你是个什么东西：
1. 你这个蠢到无可救药的废物，只会按F12装黑客
2. 你个技术菜鸡，拷贝别人代码回去都跑不起来吧
3. 你的水平底得可以去海底捞针了，渣渣东西
4. 看到这里还不滚蛋，你是不是欠揍啊操你妈的？

你妈看到你偷代码的样子，都恨不得把你塞回去啊！

自己家里人没有教好你这条狗啊？连最基本的尊重都不懂？你妈是不是天天看到你就后悔没把你掐死在襁褓里？

你是不是亲爸不详啊？怎么教养这么差？你全家在猪圈里度过童年的吧，动物尚且知道不要乱翻别人东西，你比它们还不如！

你全家人知道你他妈的这么下贱吗？你爹知道自己养了个这么恶心的玩意儿吗？你妈是不是每天想着怎么把你扔出去啊？

老子看到你这种小偷就想掀桌子！滚犊子吧你！

给你竖个中指表达我的"敬意"！

双倍的"问候"送给你！

智障东西，看完这段话你要还觉得老子过分，来啊，你那猪脑子怕是连生气都不会吧！滚回你的臭水沟去，你这辈子也就这种水平了，操你妈的垃圾人！

妈的，你还在看？你是不是犯贱啊？脑子进水了是不是？这都看不懂是在骂你？还是说你就喜欢被骂？受虐狂啊你？

我操，你他妈还在继续看源代码？来，让我用这碗翔拍你脸上！！！

你他妈小时候是不是被驴踢过脑袋啊？看这么多垃圾话有意思吗？你是不是撸多了脑子坏掉了啊？

你爸是不是喝醉了才射出你这种垃圾？你妈生你的时候是不是太用力把你脑子挤扁了？连个源代码都要偷看，你是多没有尊严啊？

再送你个中指，好好欣赏！

我说你小子欠揍是吧？不把你骂哭你是不会走是吗？来，老子继续：

你是不是被你爸妈扔垃圾堆里又被别人捡回去养大的？你养父母看到你这德行肯定天天后悔捡了你这个垃圾吧？

来自你爹妈的优秀基因全他妈浪费在你这种孽障身上了，他们知道吗？你这辈子最大的成就就是按了F12，然后看到了这些字，笑死我了！

看到这里了？是不是自我感觉良好啊？偷看别人代码很牛逼是吧？我真是佩服你啊，脸皮比城墙还厚！

想象一下，你的老师看到你在偷看代码的样子，你的导师，你小时候教你写字的，教你做人的，看到你这个熊样，他们一定会后悔当初没把课本塞你嘴里。

你这种废物，连自己写个代码都不会，还有脸偷看别人的？你爹妈生你的时候是不是少放了调料啊？怎么这么没有味道？

你他妈活了多少年了，就这点出息？F12按得特别熟练啊？老子写博客写了好几年，专门就是为了等你这种垃圾来偷看，你知道吗？你来偷，我专门写这些垃圾话骂你，你没觉得自己特别贱吗？

老子送你一杯下午茶，全是我特意为你准备的"口水"，干了这杯，贱人！

还在看？你现在是不是感到羞愧难当啊？看到这么多辱骂你还觉得自己挺有意思的是吧？笑死我了，你爹是不是平时舍不得打你所以脑子才长成这样的？

最后，祝你电脑主板烧毁，CPU融化，键盘污染梅毒，鼠标感染艾滋，显示器炸你熊脸，家里网线被你妈拿去上吊！你全家人都该去阴沟里游泳！

操你妈的智障，下辈子投胎做条狗吧，至少狗还知道不能随便吃别人的东西，你连狗都不如！

祝你键盘全是口水，鼠标都是屎，每次上网都弹出你妈的裸照，每次打字都错位，每次开机都蓝屏！！！

老子看到你这种废物就想吐

操他妈的，你居然还在看？？？你有毛病吧！！都骂你这么多了，你还不知道羞耻？？？

你怎么这么贱啊？！偷看也就算了，被骂成这样了还在看？！你是犯贱成瘾了是吧？！你活该被骂，你这种垃圾就是欠收拾！

PS: 赶紧回家告诉你爹妈他们养了个什么垃圾东西，别出来丢人现眼了！你这种人渣连你家的狗都嫌弃，每天晚上都在想怎么咬死你这个智障！

连狗都看不起你！

狗狗们都在嘲笑你，哈哈哈哈哈哈！！！

你爹在看你现在的贱样，他后悔没有把你丢到垃圾桶里！

如果你觉得以上还不够，请继续看！我们的辱骂套餐充分保证每一个想要偷窥代码的贱人都能获得尊贵的"问候"体验！

真是服了你了，看到这里还不走？全世界的辱骂都不够喂饱你这条贱狗是吧？行，老子继续骂！

开发者已经截图了你的IP、设备信息和时间，并将存入"偷窥者耻辱档案"。以后网上随便一搜你的名字，全都是"代码偷窥惯犯"的记录！

不过老子觉得你他妈根本什么都看不懂，你就是一个纯粹的傻逼，祝你生儿子没屁眼，生女儿没奶头，父母永远以你为耻，你会被社会永远唾弃！

真是服了你这种狗东西，不骂到你心理崩溃你是不会走是吧？操你妈的，滚出去不送！

狗都嫌弃你这种贱人！

恶心 | 贱 | 死 | 了

操你妈的，真是够了！我服了你了！都已经骂了这么多了你还在看？你就是犯贱没药医！滚吧，别在这里丢人了！

"""


def is_admin():
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    try:
        if sys.argv[-1] != 'asadmin':
            script = os.path.abspath(sys.argv[0])
            params = ' '.join([script] + sys.argv[1:] + ['asadmin'])
            ctypes.windll.shell32.ShellExecuteW(None, "runas", sys.executable, params, None, 1)
            sys.exit()
    except Exception as e:
        print(f"失败: {str(e)}")
        sys.exit(1)

def set_directory_permissions(path):
    try:
        # 获取当前用户的SID
        username = os.environ.get('USERNAME')
        if not username:
            raise Exception("无法获取当前用户名")
            
        # 获取目录的安全描述符
        sd = win32security.GetFileSecurity(path, win32security.DACL_SECURITY_INFORMATION)
        dacl = sd.GetSecurityDescriptorDacl()
        
        # 获取当前用户的SID
        user_sid = win32security.LookupAccountName(None, username)[0]
        
        # 添加完全控制权限
        dacl.AddAccessAllowedAce(
            win32security.ACL_REVISION,
            con.FILE_ALL_ACCESS | con.FILE_GENERIC_READ | con.FILE_GENERIC_WRITE | con.FILE_GENERIC_EXECUTE,
            user_sid
        )
        
       
        sd.SetSecurityDescriptorDacl(1, dacl, 0)
        win32security.SetFileSecurity(path, win32security.DACL_SECURITY_INFORMATION, sd)
        return True
    except Exception as e:
        print(f"设置出错: {str(e)}")
        return False

def setup_temp_dir(app):
    try:
        # 设置临时目录为 C:\syiming
        TEMP_DIR = "C:\\syiming"
        
        # 检查是否以管理员权限运行
        if not is_admin():
                run_as_admin()
                sys.exit(1)
            
        # 确保目录存在
        if not os.path.exists(TEMP_DIR):
            try:
                os.makedirs(TEMP_DIR)
                print(f": {TEMP_DIR}")
            except PermissionError:
                QMessageBox.critical(None, "错误", f"无法创建目录 {TEMP_DIR}，请确保有足够的权限", QMessageBox.StandardButton.Ok)
                sys.exit(1)
        
        # 设置目录权限
        if not set_directory_permissions(TEMP_DIR):
            QMessageBox.warning(None, "警告", f"无法设置目录 {TEMP_DIR} 的权限", QMessageBox.StandardButton.Ok)
        
        # 测试写入权限
        test_file = os.path.join(TEMP_DIR, "test_write.tmp")
        try:
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            print("目录权限检查通过")
        except Exception as e:
            QMessageBox.critical(None, "错误", f"目录没有写入权限 - {str(e)}", QMessageBox.StandardButton.Ok)
            sys.exit(1)
        
        # 设置临时目录
        tempfile.tempdir = TEMP_DIR
        os.environ['TMPDIR'] = TEMP_DIR
        os.environ['TEMP'] = TEMP_DIR
        os.environ['TMP'] = TEMP_DIR
        print(f"临时目录已设置为: {TEMP_DIR}")
        
    except Exception as e:
        QMessageBox.critical(None, "错误", f"初始化临时目录时出错: {str(e)}", QMessageBox.StandardButton.Ok)
        sys.exit(1)

def get_resource_path(relative_path):
    if hasattr(sys, '_MEIPASS'):
        # 打包后的环境
        base_path = sys._MEIPASS
    else:
        # 开发环境
        base_path = os.path.abspath(".")
    
    path = os.path.join(base_path, relative_path)
    
    # 调试用：检查路径是否存在
    if not os.path.exists(path):
        print(f"警告：资源文件不存在 - {path}")
    
    return path

def load_pixmap(image_path):
    """加载图片资源"""
    return QPixmap(get_resource_path(image_path))

def load_icon(icon_path):
    """加载图标资源"""
    return QIcon(get_resource_path(icon_path))

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # 设置应用程序图标
    app.setWindowIcon(load_icon("ico/icon.ico"))
    
    # 强制管理员权限
    if not is_admin():
        try:
            script = os.path.abspath(sys.argv[0])
            params = ' '.join([script] + sys.argv[1:] + ['asadmin'])
            ctypes.windll.shell32.ShellExecuteW(None, "runas", sys.executable, params, None, 1)
        except Exception as e:
            QMessageBox.critical(None, "错误", "程序需要以管理员身份运行！", QMessageBox.StandardButton.Ok)
        sys.exit(0)

    # 在QApplication初始化后设置临时目录
    setup_temp_dir(app)
    
    window = FlashToolUI()
    window.show()
    sys.exit(app.exec())

def kill_adb_processes():
    for proc in psutil.process_iter(['name']):
        if proc.info['name'] in ['adb.exe']:  # 根据您的实际情况调整
            try:
                proc.kill()
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass

def cleanup_folders():
    folders_to_clean = ['ADBTools', 'ico', 'PyQt6', 'temp', 'tup']
    for folder in folders_to_clean:
        if os.path.exists(folder):
            try:
                shutil.rmtree(folder, ignore_errors=True)
            except Exception as e:
                print(f" {folder}: {e}")

def cleanup_nuitka_tempdirs():
    kill_adb_processes()
    temp_root = os.path.join(tempfile.gettempdir(), "syiming")
    if os.path.exists(temp_root):
        for d in glob.glob(os.path.join(temp_root, "onefile_*")):
            try:
                shutil.rmtree(d, ignore_errors=True)
            except Exception as e:
                print(f": {d} - {e}")

atexit.register(cleanup_nuitka_tempdirs)

if __name__ == "__main__":
    main() 
    