import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qmlaccelerometer_p.h"
        name: "QmlAccelerometer"
        accessSemantics: "reference"
        prototype: "QmlSensor"
        exports: [
            "QtSensors/Accelerometer 5.0",
            "QtSensors/Accelerometer 6.0",
            "QtSensors/Accelerometer 6.7"
        ]
        exportMetaObjectRevisions: [1280, 1536, 1543]
        Enum {
            name: "AccelerationMode"
            values: ["Combined", "Gravity", "User"]
        }
        Property {
            name: "accelerationMode"
            revision: 65281
            type: "AccelerationMode"
            read: "accelerationMode"
            write: "setAccelerationMode"
            notify: "accelerationModeChanged"
            index: 0
        }
        Signal {
            name: "accelerationModeChanged"
            revision: 65281
            Parameter { name: "accelerationMode"; type: "AccelerationMode" }
        }
    }
    Component {
        file: "private/qmlaccelerometer_p.h"
        name: "QmlAccelerometerReading"
        accessSemantics: "reference"
        prototype: "QmlSensorReading"
        exports: [
            "QtSensors/AccelerometerReading 5.0",
            "QtSensors/AccelerometerReading 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536]
        Property {
            name: "x"
            type: "double"
            bindable: "bindableX"
            read: "x"
            notify: "xChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "y"
            type: "double"
            bindable: "bindableY"
            read: "y"
            notify: "yChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "z"
            type: "double"
            bindable: "bindableZ"
            read: "z"
            notify: "zChanged"
            index: 2
            isReadonly: true
        }
        Signal { name: "xChanged" }
        Signal { name: "yChanged" }
        Signal { name: "zChanged" }
    }
    Component {
        file: "private/qmlambientlightsensor_p.h"
        name: "QmlAmbientLightSensor"
        accessSemantics: "reference"
        prototype: "QmlSensor"
        exports: [
            "QtSensors/AmbientLightSensor 5.0",
            "QtSensors/AmbientLightSensor 6.0",
            "QtSensors/AmbientLightSensor 6.7"
        ]
        exportMetaObjectRevisions: [1280, 1536, 1543]
    }
    Component {
        file: "private/qmlambientlightsensor_p.h"
        name: "QmlAmbientLightSensorReading"
        accessSemantics: "reference"
        prototype: "QmlSensorReading"
        exports: [
            "QtSensors/AmbientLightReading 5.0",
            "QtSensors/AmbientLightReading 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536]
        Property {
            name: "lightLevel"
            type: "QAmbientLightReading::LightLevel"
            bindable: "bindableLightLevel"
            read: "lightLevel"
            notify: "lightLevelChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "lightLevelChanged" }
    }
    Component {
        file: "private/qmlambienttemperaturesensor_p.h"
        name: "QmlAmbientTemperatureReading"
        accessSemantics: "reference"
        prototype: "QmlSensorReading"
        exports: [
            "QtSensors/AmbientTemperatureReading 5.1",
            "QtSensors/AmbientTemperatureReading 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1281, 1536]
        Property {
            name: "temperature"
            type: "double"
            bindable: "bindableTemperature"
            read: "temperature"
            notify: "temperatureChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "temperatureChanged" }
    }
    Component {
        file: "private/qmlambienttemperaturesensor_p.h"
        name: "QmlAmbientTemperatureSensor"
        accessSemantics: "reference"
        prototype: "QmlSensor"
        exports: [
            "QtSensors/AmbientTemperatureSensor 5.1",
            "QtSensors/AmbientTemperatureSensor 6.0",
            "QtSensors/AmbientTemperatureSensor 6.7"
        ]
        exportMetaObjectRevisions: [1281, 1536, 1543]
    }
    Component {
        file: "private/qmlcompass_p.h"
        name: "QmlCompass"
        accessSemantics: "reference"
        prototype: "QmlSensor"
        exports: [
            "QtSensors/Compass 5.0",
            "QtSensors/Compass 6.0",
            "QtSensors/Compass 6.7"
        ]
        exportMetaObjectRevisions: [1280, 1536, 1543]
    }
    Component {
        file: "private/qmlcompass_p.h"
        name: "QmlCompassReading"
        accessSemantics: "reference"
        prototype: "QmlSensorReading"
        exports: [
            "QtSensors/CompassReading 5.0",
            "QtSensors/CompassReading 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536]
        Property {
            name: "azimuth"
            type: "double"
            bindable: "bindableAzimuth"
            read: "azimuth"
            notify: "azimuthChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "calibrationLevel"
            type: "double"
            bindable: "bindableCalibrationLevel"
            read: "calibrationLevel"
            notify: "calibrationLevelChanged"
            index: 1
            isReadonly: true
        }
        Signal { name: "azimuthChanged" }
        Signal { name: "calibrationLevelChanged" }
    }
    Component {
        file: "private/qmlgyroscope_p.h"
        name: "QmlGyroscope"
        accessSemantics: "reference"
        prototype: "QmlSensor"
        exports: [
            "QtSensors/Gyroscope 5.0",
            "QtSensors/Gyroscope 6.0",
            "QtSensors/Gyroscope 6.7"
        ]
        exportMetaObjectRevisions: [1280, 1536, 1543]
    }
    Component {
        file: "private/qmlgyroscope_p.h"
        name: "QmlGyroscopeReading"
        accessSemantics: "reference"
        prototype: "QmlSensorReading"
        exports: [
            "QtSensors/GyroscopeReading 5.0",
            "QtSensors/GyroscopeReading 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536]
        Property {
            name: "x"
            type: "double"
            bindable: "bindableX"
            read: "x"
            notify: "xChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "y"
            type: "double"
            bindable: "bindableY"
            read: "y"
            notify: "yChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "z"
            type: "double"
            bindable: "bindableZ"
            read: "z"
            notify: "zChanged"
            index: 2
            isReadonly: true
        }
        Signal { name: "xChanged" }
        Signal { name: "yChanged" }
        Signal { name: "zChanged" }
    }
    Component {
        file: "private/qmlhumiditysensor_p.h"
        name: "QmlHumidityReading"
        accessSemantics: "reference"
        prototype: "QmlSensorReading"
        exports: [
            "QtSensors/HumidityReading 5.9",
            "QtSensors/HumidityReading 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1289, 1536]
        Property {
            name: "relativeHumidity"
            type: "double"
            bindable: "bindableRelativeHumidity"
            read: "relativeHumidity"
            notify: "relativeHumidityChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "absoluteHumidity"
            type: "double"
            bindable: "bindableAbsoluteHumidity"
            read: "absoluteHumidity"
            notify: "absoluteHumidityChanged"
            index: 1
            isReadonly: true
        }
        Signal { name: "relativeHumidityChanged" }
        Signal { name: "absoluteHumidityChanged" }
    }
    Component {
        file: "private/qmlhumiditysensor_p.h"
        name: "QmlHumiditySensor"
        accessSemantics: "reference"
        prototype: "QmlSensor"
        exports: [
            "QtSensors/HumiditySensor 5.9",
            "QtSensors/HumiditySensor 6.0",
            "QtSensors/HumiditySensor 6.7"
        ]
        exportMetaObjectRevisions: [1289, 1536, 1543]
    }
    Component {
        file: "private/qmlirproximitysensor_p.h"
        name: "QmlIRProximitySensor"
        accessSemantics: "reference"
        prototype: "QmlSensor"
        exports: [
            "QtSensors/IRProximitySensor 5.0",
            "QtSensors/IRProximitySensor 6.0",
            "QtSensors/IRProximitySensor 6.7"
        ]
        exportMetaObjectRevisions: [1280, 1536, 1543]
    }
    Component {
        file: "private/qmlirproximitysensor_p.h"
        name: "QmlIRProximitySensorReading"
        accessSemantics: "reference"
        prototype: "QmlSensorReading"
        exports: [
            "QtSensors/IRProximityReading 5.0",
            "QtSensors/IRProximityReading 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536]
        Property {
            name: "reflectance"
            type: "double"
            bindable: "bindableReflectance"
            read: "reflectance"
            notify: "reflectanceChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "reflectanceChanged" }
    }
    Component {
        file: "private/qmllidsensor_p.h"
        name: "QmlLidReading"
        accessSemantics: "reference"
        prototype: "QmlSensorReading"
        exports: ["QtSensors/LidReading 5.9", "QtSensors/LidReading 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1289, 1536]
        Property {
            name: "backLidClosed"
            type: "bool"
            bindable: "bindableBackLidClosed"
            read: "backLidClosed"
            notify: "backLidChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "frontLidClosed"
            type: "bool"
            bindable: "bindableFrontLidClosed"
            read: "frontLidClosed"
            notify: "frontLidChanged"
            index: 1
            isReadonly: true
        }
        Signal {
            name: "backLidChanged"
            Parameter { name: "closed"; type: "bool" }
        }
        Signal {
            name: "frontLidChanged"
            type: "bool"
            Parameter { name: "closed"; type: "bool" }
        }
    }
    Component {
        file: "private/qmllidsensor_p.h"
        name: "QmlLidSensor"
        accessSemantics: "reference"
        prototype: "QmlSensor"
        exports: [
            "QtSensors/LidSensor 5.9",
            "QtSensors/LidSensor 6.0",
            "QtSensors/LidSensor 6.7"
        ]
        exportMetaObjectRevisions: [1289, 1536, 1543]
    }
    Component {
        file: "private/qmllightsensor_p.h"
        name: "QmlLightSensor"
        accessSemantics: "reference"
        prototype: "QmlSensor"
        exports: [
            "QtSensors/LightSensor 5.0",
            "QtSensors/LightSensor 6.0",
            "QtSensors/LightSensor 6.7"
        ]
        exportMetaObjectRevisions: [1280, 1536, 1543]
        Property {
            name: "fieldOfView"
            type: "double"
            read: "fieldOfView"
            notify: "fieldOfViewChanged"
            index: 0
            isReadonly: true
        }
        Signal {
            name: "fieldOfViewChanged"
            Parameter { name: "fieldOfView"; type: "double" }
        }
    }
    Component {
        file: "private/qmllightsensor_p.h"
        name: "QmlLightSensorReading"
        accessSemantics: "reference"
        prototype: "QmlSensorReading"
        exports: ["QtSensors/LightReading 5.0", "QtSensors/LightReading 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536]
        Property {
            name: "illuminance"
            type: "double"
            bindable: "bindableIlluminance"
            read: "illuminance"
            notify: "illuminanceChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "illuminanceChanged" }
    }
    Component {
        file: "private/qmlmagnetometer_p.h"
        name: "QmlMagnetometer"
        accessSemantics: "reference"
        prototype: "QmlSensor"
        exports: [
            "QtSensors/Magnetometer 5.0",
            "QtSensors/Magnetometer 6.0",
            "QtSensors/Magnetometer 6.7"
        ]
        exportMetaObjectRevisions: [1280, 1536, 1543]
        Property {
            name: "returnGeoValues"
            type: "bool"
            read: "returnGeoValues"
            write: "setReturnGeoValues"
            notify: "returnGeoValuesChanged"
            index: 0
        }
        Signal {
            name: "returnGeoValuesChanged"
            Parameter { name: "returnGeoValues"; type: "bool" }
        }
    }
    Component {
        file: "private/qmlmagnetometer_p.h"
        name: "QmlMagnetometerReading"
        accessSemantics: "reference"
        prototype: "QmlSensorReading"
        exports: [
            "QtSensors/MagnetometerReading 5.0",
            "QtSensors/MagnetometerReading 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536]
        Property {
            name: "x"
            type: "double"
            bindable: "bindableX"
            read: "x"
            notify: "xChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "y"
            type: "double"
            bindable: "bindableY"
            read: "y"
            notify: "yChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "z"
            type: "double"
            bindable: "bindableZ"
            read: "z"
            notify: "zChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "calibrationLevel"
            type: "double"
            bindable: "bindableCalibrationLevel"
            read: "calibrationLevel"
            notify: "calibrationLevelChanged"
            index: 3
            isReadonly: true
        }
        Signal { name: "xChanged" }
        Signal { name: "yChanged" }
        Signal { name: "zChanged" }
        Signal { name: "calibrationLevelChanged" }
    }
    Component {
        file: "private/qmlorientationsensor_p.h"
        name: "QmlOrientationSensor"
        accessSemantics: "reference"
        prototype: "QmlSensor"
        exports: [
            "QtSensors/OrientationSensor 5.0",
            "QtSensors/OrientationSensor 6.0",
            "QtSensors/OrientationSensor 6.7"
        ]
        exportMetaObjectRevisions: [1280, 1536, 1543]
    }
    Component {
        file: "private/qmlorientationsensor_p.h"
        name: "QmlOrientationSensorReading"
        accessSemantics: "reference"
        prototype: "QmlSensorReading"
        exports: [
            "QtSensors/OrientationReading 5.0",
            "QtSensors/OrientationReading 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536]
        Property {
            name: "orientation"
            type: "QOrientationReading::Orientation"
            bindable: "bindableOrientation"
            read: "orientation"
            notify: "orientationChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "orientationChanged" }
    }
    Component {
        file: "private/qmlpressuresensor_p.h"
        name: "QmlPressureReading"
        accessSemantics: "reference"
        prototype: "QmlSensorReading"
        exports: [
            "QtSensors/PressureReading 5.1",
            "QtSensors/PressureReading 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1281, 1536]
        Property {
            name: "pressure"
            type: "double"
            bindable: "bindablePressure"
            read: "pressure"
            notify: "pressureChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "temperature"
            revision: 65281
            type: "double"
            bindable: "bindableTemperature"
            read: "temperature"
            notify: "temperatureChanged"
            index: 1
            isReadonly: true
        }
        Signal { name: "pressureChanged" }
        Signal { name: "temperatureChanged"; revision: 65281 }
    }
    Component {
        file: "private/qmlpressuresensor_p.h"
        name: "QmlPressureSensor"
        accessSemantics: "reference"
        prototype: "QmlSensor"
        exports: [
            "QtSensors/PressureSensor 5.1",
            "QtSensors/PressureSensor 6.0",
            "QtSensors/PressureSensor 6.7"
        ]
        exportMetaObjectRevisions: [1281, 1536, 1543]
    }
    Component {
        file: "private/qmlproximitysensor_p.h"
        name: "QmlProximitySensor"
        accessSemantics: "reference"
        prototype: "QmlSensor"
        exports: [
            "QtSensors/ProximitySensor 5.0",
            "QtSensors/ProximitySensor 6.0",
            "QtSensors/ProximitySensor 6.7"
        ]
        exportMetaObjectRevisions: [1280, 1536, 1543]
    }
    Component {
        file: "private/qmlproximitysensor_p.h"
        name: "QmlProximitySensorReading"
        accessSemantics: "reference"
        prototype: "QmlSensorReading"
        exports: [
            "QtSensors/ProximityReading 5.0",
            "QtSensors/ProximityReading 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536]
        Property {
            name: "near"
            type: "bool"
            bindable: "bindableNear"
            read: "near"
            notify: "nearChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "nearChanged" }
    }
    Component {
        file: "private/qmlrotationsensor_p.h"
        name: "QmlRotationSensor"
        accessSemantics: "reference"
        prototype: "QmlSensor"
        exports: [
            "QtSensors/RotationSensor 5.0",
            "QtSensors/RotationSensor 6.0",
            "QtSensors/RotationSensor 6.7"
        ]
        exportMetaObjectRevisions: [1280, 1536, 1543]
        Property {
            name: "hasZ"
            type: "bool"
            read: "hasZ"
            notify: "hasZChanged"
            index: 0
            isReadonly: true
        }
        Signal {
            name: "hasZChanged"
            Parameter { name: "hasZ"; type: "bool" }
        }
    }
    Component {
        file: "private/qmlrotationsensor_p.h"
        name: "QmlRotationSensorReading"
        accessSemantics: "reference"
        prototype: "QmlSensorReading"
        exports: [
            "QtSensors/RotationReading 5.0",
            "QtSensors/RotationReading 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536]
        Property {
            name: "x"
            type: "double"
            bindable: "bindableX"
            read: "x"
            notify: "xChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "y"
            type: "double"
            bindable: "bindableY"
            read: "y"
            notify: "yChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "z"
            type: "double"
            bindable: "bindableZ"
            read: "z"
            notify: "zChanged"
            index: 2
            isReadonly: true
        }
        Signal { name: "xChanged" }
        Signal { name: "yChanged" }
        Signal { name: "zChanged" }
    }
    Component {
        file: "private/qmlsensor_p.h"
        name: "QmlSensor"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtSensors/Sensor 5.0",
            "QtSensors/Sensor 6.0",
            "QtSensors/Sensor 6.7"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536, 1543]
        Enum {
            name: "Feature"
            type: "int"
            values: [
                "Buffering",
                "AlwaysOn",
                "GeoValues",
                "FieldOfView",
                "AccelerationMode",
                "SkipDuplicates",
                "AxesOrientation",
                "PressureSensorTemperature"
            ]
        }
        Enum {
            name: "AxesOrientationMode"
            values: [
                "FixedOrientation",
                "AutomaticOrientation",
                "UserOrientation"
            ]
        }
        Property {
            name: "identifier"
            type: "QByteArray"
            read: "identifier"
            write: "setIdentifier"
            notify: "identifierChanged"
            index: 0
        }
        Property {
            name: "type"
            type: "QByteArray"
            read: "type"
            index: 1
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "connectedToBackend"
            type: "bool"
            read: "isConnectedToBackend"
            notify: "connectedToBackendChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "availableDataRates"
            type: "QmlSensorRange"
            isList: true
            read: "availableDataRates"
            notify: "availableDataRatesChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "dataRate"
            type: "int"
            read: "dataRate"
            write: "setDataRate"
            notify: "dataRateChanged"
            index: 4
        }
        Property {
            name: "reading"
            type: "QmlSensorReading"
            isPointer: true
            bindable: "bindableReading"
            read: "reading"
            notify: "readingChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "busy"
            type: "bool"
            read: "isBusy"
            notify: "busyChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "active"
            type: "bool"
            read: "isActive"
            write: "setActive"
            notify: "activeChanged"
            index: 7
        }
        Property {
            name: "outputRanges"
            type: "QmlSensorOutputRange"
            isList: true
            read: "outputRanges"
            notify: "outputRangesChanged"
            index: 8
            isReadonly: true
        }
        Property {
            name: "outputRange"
            type: "int"
            read: "outputRange"
            write: "setOutputRange"
            notify: "outputRangeChanged"
            index: 9
        }
        Property {
            name: "description"
            type: "QString"
            read: "description"
            notify: "descriptionChanged"
            index: 10
            isReadonly: true
        }
        Property {
            name: "error"
            type: "int"
            read: "error"
            notify: "errorChanged"
            index: 11
            isReadonly: true
        }
        Property {
            name: "alwaysOn"
            type: "bool"
            read: "isAlwaysOn"
            write: "setAlwaysOn"
            notify: "alwaysOnChanged"
            index: 12
        }
        Property {
            name: "skipDuplicates"
            revision: 65281
            type: "bool"
            read: "skipDuplicates"
            write: "setSkipDuplicates"
            notify: "skipDuplicatesChanged"
            index: 13
        }
        Property {
            name: "axesOrientationMode"
            revision: 65281
            type: "AxesOrientationMode"
            read: "axesOrientationMode"
            write: "setAxesOrientationMode"
            notify: "axesOrientationModeChanged"
            index: 14
        }
        Property {
            name: "currentOrientation"
            revision: 65281
            type: "int"
            read: "currentOrientation"
            notify: "currentOrientationChanged"
            index: 15
            isReadonly: true
        }
        Property {
            name: "userOrientation"
            revision: 65281
            type: "int"
            read: "userOrientation"
            write: "setUserOrientation"
            notify: "userOrientationChanged"
            index: 16
        }
        Property {
            name: "maxBufferSize"
            revision: 65281
            type: "int"
            read: "maxBufferSize"
            notify: "maxBufferSizeChanged"
            index: 17
            isReadonly: true
        }
        Property {
            name: "efficientBufferSize"
            revision: 65281
            type: "int"
            read: "efficientBufferSize"
            notify: "efficientBufferSizeChanged"
            index: 18
            isReadonly: true
        }
        Property {
            name: "bufferSize"
            revision: 65281
            type: "int"
            read: "bufferSize"
            write: "setBufferSize"
            notify: "bufferSizeChanged"
            index: 19
        }
        Signal { name: "identifierChanged" }
        Signal { name: "connectedToBackendChanged" }
        Signal { name: "availableDataRatesChanged" }
        Signal { name: "dataRateChanged" }
        Signal { name: "readingChanged" }
        Signal { name: "activeChanged" }
        Signal { name: "outputRangesChanged" }
        Signal { name: "outputRangeChanged" }
        Signal { name: "descriptionChanged" }
        Signal { name: "errorChanged" }
        Signal { name: "alwaysOnChanged" }
        Signal { name: "busyChanged" }
        Signal {
            name: "skipDuplicatesChanged"
            revision: 65281
            Parameter { name: "skipDuplicates"; type: "bool" }
        }
        Signal {
            name: "axesOrientationModeChanged"
            revision: 65281
            Parameter { name: "axesOrientationMode"; type: "AxesOrientationMode" }
        }
        Signal {
            name: "currentOrientationChanged"
            revision: 65281
            Parameter { name: "currentOrientation"; type: "int" }
        }
        Signal {
            name: "userOrientationChanged"
            revision: 65281
            Parameter { name: "userOrientation"; type: "int" }
        }
        Signal {
            name: "maxBufferSizeChanged"
            revision: 65281
            Parameter { name: "maxBufferSize"; type: "int" }
        }
        Signal {
            name: "efficientBufferSizeChanged"
            revision: 65281
            Parameter { name: "efficientBufferSize"; type: "int" }
        }
        Signal {
            name: "bufferSizeChanged"
            revision: 65281
            Parameter { name: "bufferSize"; type: "int" }
        }
        Method { name: "start"; type: "bool" }
        Method { name: "stop" }
        Method { name: "updateReading" }
        Method {
            name: "isFeatureSupported"
            revision: 1543
            type: "bool"
            Parameter { name: "feature"; type: "Feature" }
        }
    }
    Component {
        file: "private/qmlsensorglobal_p.h"
        name: "QmlSensorGlobal"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtSensors/QmlSensors 5.0", "QtSensors/QmlSensors 6.0"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [1280, 1536]
        Signal { name: "availableSensorsChanged" }
        Method { name: "sensorTypes"; type: "QStringList" }
        Method {
            name: "sensorsForType"
            type: "QStringList"
            Parameter { name: "type"; type: "QString" }
        }
        Method {
            name: "defaultSensorForType"
            type: "QString"
            Parameter { name: "type"; type: "QString" }
        }
    }
    Component {
        file: "private/qmlsensorrange_p.h"
        name: "QmlSensorOutputRange"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtSensors/OutputRange 5.0", "QtSensors/OutputRange 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536]
        Property { name: "minimum"; type: "double"; read: "minimum"; index: 0; isReadonly: true }
        Property { name: "maximum"; type: "double"; read: "maximum"; index: 1; isReadonly: true }
        Property { name: "accuracy"; type: "double"; read: "accuracy"; index: 2; isReadonly: true }
    }
    Component {
        file: "private/qmlsensorrange_p.h"
        name: "QmlSensorRange"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtSensors/Range 5.0", "QtSensors/Range 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536]
        Property { name: "minimum"; type: "int"; read: "minimum"; index: 0; isReadonly: true }
        Property { name: "maximum"; type: "int"; read: "maximum"; index: 1; isReadonly: true }
    }
    Component {
        file: "private/qmlsensor_p.h"
        name: "QmlSensorReading"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtSensors/SensorReading 5.0",
            "QtSensors/SensorReading 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536]
        Property {
            name: "timestamp"
            type: "qulonglong"
            bindable: "bindableTimestamp"
            read: "timestamp"
            notify: "timestampChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "timestampChanged" }
    }
    Component {
        file: "private/qmltapsensor_p.h"
        name: "QmlTapSensor"
        accessSemantics: "reference"
        prototype: "QmlSensor"
        exports: [
            "QtSensors/TapSensor 5.0",
            "QtSensors/TapSensor 6.0",
            "QtSensors/TapSensor 6.7"
        ]
        exportMetaObjectRevisions: [1280, 1536, 1543]
        Property {
            name: "returnDoubleTapEvents"
            type: "bool"
            read: "returnDoubleTapEvents"
            write: "setReturnDoubleTapEvents"
            notify: "returnDoubleTapEventsChanged"
            index: 0
        }
        Signal {
            name: "returnDoubleTapEventsChanged"
            Parameter { name: "returnDoubleTapEvents"; type: "bool" }
        }
    }
    Component {
        file: "private/qmltapsensor_p.h"
        name: "QmlTapSensorReading"
        accessSemantics: "reference"
        prototype: "QmlSensorReading"
        exports: ["QtSensors/TapReading 5.0", "QtSensors/TapReading 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536]
        Property {
            name: "tapDirection"
            type: "QTapReading::TapDirection"
            bindable: "bindableTapDirection"
            read: "tapDirection"
            notify: "tapDirectionChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "doubleTap"
            type: "bool"
            bindable: "bindableDoubleTap"
            read: "isDoubleTap"
            notify: "isDoubleTapChanged"
            index: 1
            isReadonly: true
        }
        Signal { name: "tapDirectionChanged" }
        Signal { name: "isDoubleTapChanged" }
    }
    Component {
        file: "private/qmltiltsensor_p.h"
        name: "QmlTiltSensor"
        accessSemantics: "reference"
        prototype: "QmlSensor"
        exports: [
            "QtSensors/TiltSensor 5.0",
            "QtSensors/TiltSensor 6.0",
            "QtSensors/TiltSensor 6.7"
        ]
        exportMetaObjectRevisions: [1280, 1536, 1543]
        Method { name: "calibrate" }
    }
    Component {
        file: "private/qmltiltsensor_p.h"
        name: "QmlTiltSensorReading"
        accessSemantics: "reference"
        prototype: "QmlSensorReading"
        exports: ["QtSensors/TiltReading 5.0", "QtSensors/TiltReading 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1280, 1536]
        Property {
            name: "yRotation"
            type: "double"
            bindable: "bindableYRotation"
            read: "yRotation"
            notify: "yRotationChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "xRotation"
            type: "double"
            bindable: "bindableXRotation"
            read: "xRotation"
            notify: "xRotationChanged"
            index: 1
            isReadonly: true
        }
        Signal { name: "yRotationChanged" }
        Signal { name: "xRotationChanged" }
    }
}
