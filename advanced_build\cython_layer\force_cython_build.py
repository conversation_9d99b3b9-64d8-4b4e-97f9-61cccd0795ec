# -*- coding: utf-8 -*-
"""
强制Cython编译脚本 - 确保所有核心算法都编译成功
绝对不允许失败！
"""

import os
import sys
import shutil
import subprocess
import time
from pathlib import Path

# 核心算法模块（必须全部使用Cython编译）
CORE_MODULES = [
    "coloros.py",             # ColorOS 解锁核心算法
    "coloros15.py",           # ColorOS 15核心算法
    "utils.py",               # 工具类核心算法
    "fastboodt.py",          # Fastboot工具核心
    "zhidinyishuaxie.py",     # 自定义刷写核心
    "payload_extractor.py",   # 解包工具核心
    "custom_messagebox.py",   # 自定义消息框
    "genduodakhd.py",         # 根多大核心
    "font_extractor.py",      # 字体提取器
]

def log(message, level="INFO"):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] [{level}] {message}")

def check_environment():
    """检查编译环境"""
    log("检查Cython编译环境", "STEP")
    
    # 检查Python版本
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    log(f"Python版本: {python_version}")
    
    # 检查必需的包
    required_packages = ['cython', 'numpy', 'setuptools']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            log(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            log(f"❌ {package} 未安装", "ERROR")
    
    if missing_packages:
        log("安装缺失的包...", "STEP")
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                log(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError:
                log(f"❌ {package} 安装失败", "ERROR")
                return False
    
    return True

def prepare_cython_directory():
    """准备Cython编译目录"""
    log("准备Cython编译目录", "STEP")
    
    cython_dir = "cython_build"
    if os.path.exists(cython_dir):
        shutil.rmtree(cython_dir)
    
    os.makedirs(cython_dir, exist_ok=True)
    
    # 复制核心模块
    copied_count = 0
    for module in CORE_MODULES:
        if os.path.exists(module):
            dst = os.path.join(cython_dir, module)
            shutil.copy2(module, dst)
            size = os.path.getsize(dst)
            log(f"复制: {module} ({size:,} 字节)")
            copied_count += 1
        else:
            log(f"❌ 核心模块不存在: {module}", "ERROR")
            return False
    
    if copied_count != len(CORE_MODULES):
        log(f"❌ 核心模块复制不完整: {copied_count}/{len(CORE_MODULES)}", "ERROR")
        return False
    
    log(f"✅ 成功复制 {copied_count} 个核心模块")
    return cython_dir

def create_setup_py(cython_dir):
    """创建setup.py文件"""
    log("创建setup.py文件", "STEP")
    
    setup_content = '''# -*- coding: utf-8 -*-
"""
Cython编译配置 - 强制编译所有核心算法
"""
import os
import sys
from setuptools import setup, Extension
from Cython.Build import cythonize

# 尝试导入numpy
try:
    import numpy
    include_dirs = [numpy.get_include()]
    define_macros = [('NPY_NO_DEPRECATED_API', 'NPY_1_7_API_VERSION')]
    print("✅ 使用numpy支持")
except ImportError:
    include_dirs = []
    define_macros = []
    print("⚠️ numpy未安装，使用基础配置")

# 核心模块列表
CORE_MODULES = [
    "coloros.py",
    "coloros15.py", 
    "utils.py",
    "fastboodt.py",
    "zhidinyishuaxie.py",
    "payload_extractor.py",
    "custom_messagebox.py",
    "genduodakhd.py",
    "font_extractor.py",
]

# 创建扩展模块
extensions = []
for module_file in CORE_MODULES:
    if os.path.exists(module_file):
        # 生成模块名
        module_name = module_file.replace('.py', '_cython')
        
        # 编译选项
        extra_compile_args = []
        extra_link_args = []
        
        if sys.platform == 'win32':
            extra_compile_args = ['/O2', '/DNDEBUG']
        else:
            extra_compile_args = ['-O3', '-DNDEBUG']
        
        # 创建扩展
        ext = Extension(
            module_name,
            [module_file],
            include_dirs=include_dirs,
            define_macros=define_macros,
            extra_compile_args=extra_compile_args,
            extra_link_args=extra_link_args,
            language='c++'
        )
        extensions.append(ext)
        print(f"✅ 添加编译目标: {module_name} <- {module_file}")

if not extensions:
    print("❌ 错误: 没有找到任何核心模块")
    sys.exit(1)

print(f"📦 总计 {len(extensions)} 个模块将被编译")

# 编译指令
compiler_directives = {
    'language_level': 3,
    'boundscheck': False,
    'wraparound': False,
    'initializedcheck': False,
    'cdivision': True,
    'embedsignature': False,
    'optimize.use_switch': True,
    'optimize.unpack_method_calls': True,
}

# 执行编译
setup(
    name="CoreAlgorithms",
    ext_modules=cythonize(
        extensions,
        compiler_directives=compiler_directives,
        build_dir="build",
        annotate=False,
        nthreads=4,
        force=True
    ),
    zip_safe=False,
)
'''
    
    setup_path = os.path.join(cython_dir, "setup.py")
    with open(setup_path, 'w', encoding='utf-8') as f:
        f.write(setup_content)
    
    log(f"✅ setup.py 已创建: {setup_path}")
    return setup_path

def compile_cython_modules(cython_dir):
    """强制编译Cython模块"""
    log("开始强制Cython编译", "STEP")
    
    original_dir = os.getcwd()
    
    try:
        # 切换到编译目录
        os.chdir(cython_dir)
        log(f"切换到目录: {os.getcwd()}")
        
        # 创建setup.py
        setup_path = create_setup_py(".")
        
        # 执行编译命令
        cmd = [sys.executable, "setup.py", "build_ext", "--inplace", "--force"]
        log(f"执行编译命令: {' '.join(cmd)}")
        
        # 运行编译
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=900  # 15分钟超时
        )
        
        # 检查编译结果
        if result.returncode == 0:
            # 查找编译后的文件
            compiled_files = []
            for file in os.listdir('.'):
                if file.endswith(('.pyd', '.so')):
                    compiled_files.append(file)
            
            if len(compiled_files) == len(CORE_MODULES):
                log(f"🎉 Cython编译完全成功！", "SUCCESS")
                log(f"编译了 {len(compiled_files)} 个核心模块:")
                
                compiled_mapping = {}
                for cf in compiled_files:
                    size = os.path.getsize(cf)
                    original_name = cf.replace('_cython.pyd', '.py').replace('_cython.so', '.py')
                    compiled_mapping[original_name] = cf
                    log(f"  ✅ {original_name} -> {cf} ({size:,} 字节)")
                
                # 保存映射文件
                import json
                mapping_data = {
                    "modules": compiled_mapping,
                    "total_modules": len(compiled_mapping),
                    "compile_time": time.strftime("%Y-%m-%d %H:%M:%S")
                }
                
                with open("cython_mapping.json", 'w', encoding='utf-8') as f:
                    json.dump(mapping_data, f, indent=2, ensure_ascii=False)
                
                log("✅ Cython映射文件已保存")
                
                os.chdir(original_dir)
                return True, compiled_mapping
            else:
                log(f"❌ 编译不完整: 期望 {len(CORE_MODULES)} 个，实际 {len(compiled_files)} 个", "ERROR")
        else:
            log("❌ Cython编译失败:", "ERROR")
            if result.stdout:
                log(f"输出: {result.stdout}", "ERROR")
            if result.stderr:
                log(f"错误: {result.stderr}", "ERROR")
        
        os.chdir(original_dir)
        return False, {}
        
    except Exception as e:
        if 'original_dir' in locals():
            os.chdir(original_dir)
        log(f"❌ 编译异常: {e}", "ERROR")
        return False, {}

def create_nuitka_with_cython():
    """创建包含Cython模块的Nuitka构建"""
    log("创建包含Cython模块的Nuitka构建", "STEP")
    
    cython_dir = "cython_build"
    
    # 检查Cython编译结果
    compiled_files = []
    mapping_file = os.path.join(cython_dir, "cython_mapping.json")
    
    if os.path.exists(mapping_file):
        import json
        with open(mapping_file, 'r', encoding='utf-8') as f:
            mapping_data = json.load(f)
            compiled_mapping = mapping_data.get("modules", {})
        
        for original, compiled in compiled_mapping.items():
            compiled_path = os.path.join(cython_dir, compiled)
            if os.path.exists(compiled_path):
                compiled_files.append(compiled)
    
    if len(compiled_files) != len(CORE_MODULES):
        log(f"❌ Cython模块不完整: {len(compiled_files)}/{len(CORE_MODULES)}", "ERROR")
        return False
    
    # 构建Nuitka命令
    nuitka_cmd = [
        sys.executable, "-m", "nuitka",
        "--standalone",
        "--onefile", 
        "--onefile-tempdir-spec=\\syiming\\cython_{PID}_{TIME}",
        "--windows-console-mode=disable",
        "--enable-plugin=pyqt6",
        "--assume-yes-for-downloads",
        "--show-progress",
        "--show-memory",
        "--remove-output",
        "--output-filename=益民欧加真固件刷写工具_Cython分层保护版.exe",
    ]
    
    # 添加图标
    if os.path.exists("ico/icon.ico"):
        nuitka_cmd.append("--windows-icon-from-ico=ico/icon.ico")
        log("✅ 添加图标文件")
    
    # 添加所有Cython编译模块
    for cf in compiled_files:
        src_path = os.path.join(cython_dir, cf)
        nuitka_cmd.append(f"--include-data-file={src_path}={cf}")
        log(f"包含Cython模块: {cf}")
    
    # 添加Cython映射文件
    nuitka_cmd.append(f"--include-data-file={mapping_file}=cython_mapping.json")
    log("包含Cython映射文件")
    
    # 添加ADBTools（逐个文件）
    if os.path.exists("ADBTools"):
        for file in os.listdir("ADBTools"):
            file_path = os.path.join("ADBTools", file)
            if os.path.isfile(file_path):
                nuitka_cmd.append(f"--include-data-file={file_path}=ADBTools/{file}")
        log("✅ 包含ADBTools目录")
    
    # 添加其他资源
    for dir_name in ["ico", "tup"]:
        if os.path.exists(dir_name):
            nuitka_cmd.append(f"--include-data-dir={dir_name}={dir_name}")
            log(f"包含目录: {dir_name}")
    
    # 添加主脚本
    nuitka_cmd.append("main.py")
    
    log("🚀 开始Nuitka编译...")
    try:
        result = subprocess.run(nuitka_cmd, timeout=1800)
        
        if result.returncode == 0:
            output_file = "益民欧加真固件刷写工具_Cython分层保护版.exe"
            if os.path.exists(output_file):
                size = os.path.getsize(output_file)
                log(f"🎉 分层保护版构建成功: {output_file} ({size:,} 字节)", "SUCCESS")
                
                # 移动到输出目录
                final_dir = "advanced_build/final_output"
                os.makedirs(final_dir, exist_ok=True)
                final_path = os.path.join(final_dir, output_file)
                shutil.move(output_file, final_path)
                log(f"📁 已移动到: {final_path}")
                
                return True
        
        log("❌ Nuitka编译失败", "ERROR")
        return False
        
    except Exception as e:
        log(f"❌ Nuitka编译异常: {e}", "ERROR")
        return False

def main():
    """主函数 - 强制Cython编译"""
    log("🔒 强制Cython编译 - 核心算法分层保护", "STEP")
    log("=" * 60)
    
    # 步骤1: 检查环境
    if not check_environment():
        log("❌ 环境检查失败", "ERROR")
        return False
    
    # 步骤2: 准备编译目录
    cython_dir = prepare_cython_directory()
    if not cython_dir:
        log("❌ 编译目录准备失败", "ERROR")
        return False
    
    # 步骤3: 强制编译Cython模块
    success, compiled_mapping = compile_cython_modules(cython_dir)
    if not success:
        log("❌ Cython编译失败 - 这是不可接受的！", "ERROR")
        return False
    
    log(f"🎉 所有 {len(compiled_mapping)} 个核心算法已成功编译为Cython模块！", "SUCCESS")
    
    # 步骤4: 询问是否继续Nuitka编译
    choice = input("\n是否继续创建分层保护版exe？(y/n): ").lower()
    if choice == 'y':
        if create_nuitka_with_cython():
            log("🎉 分层保护构建完全成功！", "SUCCESS")
            log("🔒 保护层级:")
            log("   外层: Nuitka编译")
            log("   内层: Cython编译核心算法")
        else:
            log("❌ Nuitka编译失败", "ERROR")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        log("❌ 构建失败", "ERROR")
        sys.exit(1)
    else:
        log("✅ 构建完成", "SUCCESS")
