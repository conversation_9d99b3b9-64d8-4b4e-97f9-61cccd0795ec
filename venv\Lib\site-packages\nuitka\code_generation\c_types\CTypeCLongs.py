#     Copyright 2025, <PERSON>, mailto:<EMAIL> find license text at end of file


""" CType classes for C "long", and C "digit" (used in conjunction with PyLongObject *)

"""

from .CTypeBases import CTypeBase


class CTypeCLongMixin(CTypeBase):
    @classmethod
    def emitAssignmentCodeFromConstant(
        cls, to_name, constant, may_escape, emit, context
    ):
        # No context needed, pylint: disable=unused-argument
        emit("%s = %s;" % (to_name, constant))


class CTypeCLong(CTypeCLongMixin, CTypeBase):
    c_type = "long"

    helper_code = "CLONG"


class CTypeCLongDigit(CTypeCLongMixin, CTypeBase):
    c_type = "nuitka_digit"

    helper_code = "DIGIT"


#     Part of "Nuitka", an optimizing Python compiler that is compatible and
#     integrates with CPython, but also works on its own.
#
#     Licensed under the Apache License, Version 2.0 (the "License");
#     you may not use this file except in compliance with the License.
#     You may obtain a copy of the License at
#
#        http://www.apache.org/licenses/LICENSE-2.0
#
#     Unless required by applicable law or agreed to in writing, software
#     distributed under the License is distributed on an "AS IS" BASIS,
#     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#     See the License for the specific language governing permissions and
#     limitations under the License.
