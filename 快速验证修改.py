# -*- coding: utf-8 -*-
"""
快速验证修改是否生效
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_modifications():
    """检查修改是否生效"""
    print("🔍 检查修改是否生效...")
    
    try:
        # 读取源代码文件
        with open('qz4n.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修改
        checks = [
            ('start_flashing方法', 'def start_flashing(self):'),
            ('finish_flashing方法', 'def finish_flashing(self, success=True):'),
            ('is_flashing标志', 'self.is_flashing = False'),
            ('刷机状态保护', 'and not self.is_flashing'),
            ('载入刷机状态', 'self.device_status_label.setText("载入刷机")'),
        ]
        
        print("\n📋 检查结果:")
        all_good = True
        for name, pattern in checks:
            if pattern in content:
                print(f"✅ {name} - 已添加")
            else:
                print(f"❌ {name} - 缺失")
                all_good = False
        
        # 检查调用是否正确
        call_checks = [
            ('ColorOS调用start_flashing', 'self.start_flashing()'),
            ('强解调用start_flashing', 'self.start_flashing()'),
            ('完成回调调用finish_flashing', 'self.finish_flashing(success)'),
        ]
        
        print("\n📞 检查方法调用:")
        for name, pattern in call_checks:
            count = content.count(pattern)
            if count > 0:
                print(f"✅ {name} - 找到 {count} 处调用")
            else:
                print(f"❌ {name} - 未找到调用")
                all_good = False
        
        if all_good:
            print("\n🎉 所有修改都已正确应用!")
            print("\n💡 如果设备状态仍未变化，可能的原因:")
            print("1. 需要重新启动程序")
            print("2. 设备检查线程仍在运行")
            print("3. 需要重新编译.pyd文件")
        else:
            print("\n❌ 发现缺失的修改，请检查代码")
        
        return all_good
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

if __name__ == "__main__":
    check_modifications()
