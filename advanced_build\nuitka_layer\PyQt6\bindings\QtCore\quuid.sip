// quuid.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QUuid
{
%TypeHeaderCode
#include <quuid.h>
%End

public:
    enum Variant
    {
        VarUnknown,
        NCS,
        DCE,
        Microsoft,
        Reserved,
    };

    enum Version
    {
        VerUnknown,
        Time,
        EmbeddedPOSIX,
        Md5,
        Name,
        Random,
        Sha1,
    };

    enum StringFormat
    {
        WithBraces,
        WithoutBraces,
        Id128,
    };

    QUuid();
%If (Qt_6_6_0 -)
    QUuid(QUuid::Id128Bytes id128, QSysInfo::Endian order = QSysInfo::BigEndian);
%End
    QUuid(uint l, ushort w1, ushort w2, uchar b1 /PyInt/, uchar b2 /PyInt/, uchar b3 /PyInt/, uchar b4 /PyInt/, uchar b5 /PyInt/, uchar b6 /PyInt/, uchar b7 /PyInt/, uchar b8 /PyInt/);
%If (Qt_6_4_0 -)
    explicit QUuid(QAnyStringView string);
%End
%If (- Qt_6_4_0)
    explicit QUuid(const QString &);
%End
%If (- Qt_6_4_0)
    explicit QUuid(const QByteArray &);
%End
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        PyObject *uni = qpycore_PyObject_FromQString(sipCpp->toString());
        
        if (uni)
        {
            sipRes = PyUnicode_FromFormat("PyQt6.QtCore.QUuid(%R)", uni);
        
            Py_DECREF(uni);
        }
%End

    QString toString(QUuid::StringFormat mode = QUuid::WithBraces) const;
    bool isNull() const;
    bool operator==(const QUuid &orig) const;
    bool operator!=(const QUuid &orig) const;
    bool operator<(const QUuid &other) const;
    bool operator>(const QUuid &other) const;
    static QUuid createUuid();
    static QUuid createUuidV3(const QUuid &ns, const QByteArray &baseData);
    static QUuid createUuidV5(const QUuid &ns, const QByteArray &baseData);
    static QUuid createUuidV3(const QUuid &ns, const QString &baseData);
    static QUuid createUuidV5(const QUuid &ns, const QString &baseData);
    QUuid::Variant variant() const;
    QUuid::Version version() const;
    QByteArray toByteArray(QUuid::StringFormat mode = QUuid::WithBraces) const;
    QByteArray toRfc4122() const;
%If (Qt_6_3_0 -)
    static QUuid fromRfc4122(QByteArrayView);
%End
%If (- Qt_6_3_0)
    static QUuid fromRfc4122(const QByteArray &);
%End
%If (Qt_6_4_0 -)
    static QUuid fromString(QAnyStringView string);
%End
%If (- Qt_6_4_0)
    static QUuid fromString(QStringView string);
%End
%If (Qt_6_6_0 -)

    struct Id128Bytes
    {
%TypeHeaderCode
#include <quuid.h>
%End
    };

%End
};

QDataStream &operator<<(QDataStream &, const QUuid & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QUuid & /Constrained/) /ReleaseGIL/;
bool operator<=(const QUuid &lhs, const QUuid &rhs);
bool operator>=(const QUuid &lhs, const QUuid &rhs);
