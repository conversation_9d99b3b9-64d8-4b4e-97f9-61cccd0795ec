# -*- coding: utf-8 -*-
"""
测试刷机日志显示
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_flash_log_display():
    """测试刷机日志显示"""
    print("🔍 测试刷机日志显示...")
    
    try:
        # 读取源代码文件
        with open('qz4n.py', 'r', encoding='utf-8') as f:
            qz4n_content = f.read()
        
        with open('wr3j.py', 'r', encoding='utf-8') as f:
            wr3j_content = f.read()
        
        # 检查日志连接
        checks = [
            # 检查FlashThread的日志信号连接
            ('FlashThread日志信号连接', 'self.flash_thread.log_signal.connect(self.add_log)' in qz4n_content),
            
            # 检查FlashThread的log_handler设置
            ('FlashThread日志处理器', 'self.flash_function.add_log = log_handler' in qz4n_content),
            
            # 检查刷机功能模块的日志调用
            ('ColorOS模块日志调用', 'self.add_log(' in wr3j_content),
            
            # 检查日志方法存在
            ('add_log方法存在', 'def add_log(self, message, level="info"' in qz4n_content),
        ]
        
        print("\n📋 检查结果:")
        all_good = True
        for name, condition in checks:
            if condition:
                print(f"✅ {name} - 正确")
            else:
                print(f"❌ {name} - 有问题")
                all_good = False
        
        # 检查日志流程
        print("\n🔄 日志显示流程:")
        print("1. 刷机功能模块调用 self.add_log(message, level)")
        print("2. FlashThread的log_handler捕获日志")
        print("3. 通过log_signal发送到UI")
        print("4. UI的add_log方法显示在日志区域")
        
        # 检查可能的问题
        print("\n🔍 可能的问题:")
        if 'flash_function.add_log = self.add_log' in qz4n_content:
            print("⚠️ 发现重复的日志函数设置，可能被FlashThread覆盖")
        
        # 统计日志调用次数
        log_calls = wr3j_content.count('self.add_log(')
        print(f"📊 ColorOS模块中的日志调用次数: {log_calls}")
        
        if log_calls > 0:
            print("✅ 刷机功能模块有日志输出")
        else:
            print("❌ 刷机功能模块没有日志输出")
        
        if all_good and log_calls > 0:
            print("\n🎉 日志显示应该正常工作!")
            print("如果日志仍不显示，可能需要检查:")
            print("- 刷机功能模块是否正确调用add_log")
            print("- FlashThread是否正确设置log_handler")
            print("- UI的add_log方法是否正常工作")
        else:
            print("\n❌ 发现日志显示问题")
        
        return all_good and log_calls > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_flash_log_display()
