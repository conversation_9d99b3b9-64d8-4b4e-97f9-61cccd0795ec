# -*- coding: utf-8 -*-
"""
测试日志信号是否正确发送
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_log_signal():
    """测试日志信号"""
    print("🔍 测试日志信号...")
    
    try:
        from qz4n import FlashThread
        from wr3j import ColorOS15Function
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建刷机功能实例
        flash_function = ColorOS15Function()
        
        # 创建FlashThread
        flash_thread = FlashThread(flash_function, "test_folder")
        
        # 测试日志处理器是否设置
        print(f"日志处理器类型: {type(flash_function.add_log)}")
        
        # 创建日志接收器
        received_logs = []
        def log_receiver(message, level):
            received_logs.append((message, level))
            print(f"📝 接收到日志: [{level}] {message}")
        
        # 连接信号
        flash_thread.log_signal.connect(log_receiver)
        
        # 测试发送日志
        print("\n🧪 测试发送日志...")
        flash_function.add_log("测试日志消息", "info")
        flash_function.add_log("测试成功消息", "success")
        flash_function.add_log("测试错误消息", "error")
        
        # 等待信号处理
        app.processEvents()
        
        print(f"\n📊 接收到的日志数量: {len(received_logs)}")
        for i, (msg, level) in enumerate(received_logs):
            print(f"  {i+1}. [{level}] {msg}")
        
        if len(received_logs) > 0:
            print("\n✅ 日志信号正常工作!")
        else:
            print("\n❌ 日志信号没有工作")
            print("可能的原因:")
            print("- log_handler没有正确设置")
            print("- log_signal没有正确发送")
            print("- 信号连接有问题")
        
        app.quit()
        return len(received_logs) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_log_signal()
