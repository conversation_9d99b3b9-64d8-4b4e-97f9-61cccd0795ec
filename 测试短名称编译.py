# -*- coding: utf-8 -*-
"""
测试短名称编译 - 验证新的命名方案
"""

import os
import sys
import shutil
import subprocess
import time

def log(message):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_single_compile():
    """测试单个模块编译"""
    log("🧪 测试短名称编译")
    
    # 选择一个小模块测试
    test_module = "fn2w.py"  # 自定义消息框，文件较小
    
    if not os.path.exists(test_module):
        log(f"❌ 测试文件不存在: {test_module}")
        return False
    
    # 创建输出目录
    output_dir = "test_short_names"
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    os.makedirs(output_dir)
    
    # 模块名（短名称）
    module_name = test_module.replace('.py', '')
    short_name = f"{module_name}.pyd"  # 简化后的名称
    
    # 创建setup.py
    setup_content = f'''
from setuptools import setup
from Cython.Build import cythonize

setup(
    ext_modules = cythonize("{test_module}", language_level=3),
    zip_safe=False,
)
'''
    
    setup_file = f"setup_{module_name}_test.py"
    with open(setup_file, "w", encoding="utf-8") as f:
        f.write(setup_content)
    
    try:
        # 执行编译
        cmd = [sys.executable, setup_file, "build_ext", "--inplace"]
        log(f"执行: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        # 清理setup文件
        if os.path.exists(setup_file):
            os.remove(setup_file)
        
        if result.returncode == 0:
            # 查找生成的.pyd文件
            pyd_files = [f for f in os.listdir('.') if f.startswith(module_name) and f.endswith('.pyd')]
            
            if pyd_files:
                original_pyd = pyd_files[0]
                size = os.path.getsize(original_pyd)
                
                log(f"📦 原始文件名: {original_pyd}")
                log(f"📦 文件大小: {size:,} 字节")
                
                # 重命名为短名称
                os.rename(original_pyd, short_name)
                log(f"✅ 重命名为: {short_name}")
                
                # 移动到输出目录
                output_path = os.path.join(output_dir, short_name)
                shutil.move(short_name, output_path)
                log(f"📁 移动到: {output_path}")
                
                # 清理.c文件
                c_files = [f for f in os.listdir('.') if f.startswith(module_name) and f.endswith('.c')]
                for c_file in c_files:
                    try:
                        os.remove(c_file)
                        log(f"🧹 清理: {c_file}")
                    except:
                        pass
                
                log(f"\n🎉 短名称编译测试成功!")
                log(f"原始名称: {original_pyd}")
                log(f"短名称: {short_name}")
                log(f"长度对比: {len(original_pyd)} → {len(short_name)} 字符")
                
                return True
            else:
                log(f"⚠️ 编译完成但未找到.pyd文件")
        else:
            log(f"❌ 编译失败:")
            if result.stderr:
                log(f"错误: {result.stderr[:300]}")
        
        return False
        
    except Exception as e:
        log(f"❌ 编译异常: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(setup_file):
            try:
                os.remove(setup_file)
            except:
                pass

if __name__ == "__main__":
    test_single_compile()
