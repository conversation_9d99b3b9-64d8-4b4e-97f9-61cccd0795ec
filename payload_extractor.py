import os
import subprocess
import sys
from pathlib import Path
import locale
import shutil
import time
"""
Payload解包工具模块
支持Android OTA包的解包和提取功能
"""


class PayloadExtractor:
    def __init__(self):
        self.payload_file = None
        self.extract_folder = None
        self.is_extracting = False
        self.add_log = print  # 默认日志函数
        self.start_time = None  # 添加开始时间属性

    @staticmethod
    def _get_subprocess_config():
        """获取subprocess的配置，用于隐藏命令行窗口"""
        startupinfo = None
        creationflags = 0
        if os.name == 'nt':  # Windows系统
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            creationflags = subprocess.CREATE_NO_WINDOW
        return startupinfo, creationflags
        
    def get_completion_message(self, seconds):
        style = "font-size: 18px; font-weight: bold;"
        if seconds <= 10:
            return f"<span style='{style}'>不是兄弟/姐妹，什么包都乱怼进来是吧?/确实是全量包？不懂在群里问</span>"
        elif seconds <= 100:
            return f"<span style='{style}'>兄弟，你这电脑是偷了NASA的量子CPU吧？压缩包刚点开就自己跪了：'大哥，我错了，我这就自己解！'</span>"
        elif seconds <= 105:
            return f"<span style='{style}'>这速度，WinRAR官方应该给你发个'人类文明加速奖'，隔壁7-ZIP已经连夜修改广告词：'比不过，真的比不过'。</span>"
        elif seconds <= 110:
            return f"<span style='{style}'>你这SSD是拿闪电充能的吧？解析进度条刚出现就消失了，系统甚至没来得及弹广告！</span>"
        elif seconds <= 115:
            return f"<span style='{style}'>你这硬盘是F1赛车改装的吧？机械硬盘看了直接辞职：'这行业卷不动了'。</span>"
        elif seconds <= 120:
            return f"<span style='{style}'>不错不错，比办公室打印机快，至少没卡在99.9%然后蓝屏。</span>"
        elif seconds <= 125:
            return f"<span style='{style}'>比隔壁老王的机械硬盘快，但他坚持认为'慢工出细活'，并试图用哲学说服你。</span>"
        elif seconds <= 130:
            return f"<span style='{style}'>刚好够IT小哥喝口咖啡，但他刚拿起杯子就结束了，现在很失落。</span>"
        elif seconds <= 135:
            return f"<span style='{style}'>这速度……你是不是边解析边接了个诈骗电话？'您好，我是Windows客服……</span>"
        elif seconds <= 140:
            return f"<span style='{style}'>树懒看了直呼内行：'这速度，比我传文件快多了！'</span>"
        elif seconds <= 145:
            return f"<span style='{style}'>你这电脑是躺在沙发上解析的吗？'不急不急，我先刷个短视频……'</span>"
        elif seconds <= 150:
            return f"<span style='{style}'>你这电脑怕不是退休老干部，解析前还得先泡杯枸杞。</span>"
        elif seconds <= 155:
            return f"<span style='{style}'>足够看完半集《甄嬛传》，解析只是顺便的。</span>"
        elif seconds <= 160:
            return f"<span style='{style}'>你这电脑是边解析边睡觉吧？'zzz……解析完了叫我……'</span>"
        elif seconds <= 165:
            return f"<span style='{style}'>你这电脑是土豆供电的吗？'再等等，我马上发芽了……'</span>"
        elif seconds <= 170:
            return f"<span style='{style}'>蜗牛都超车了，但你的电脑依然淡定：'急什么？生活要慢节奏'。</span>"
        elif seconds <= 175:
            return f"<span style='{style}'>你这电脑是考古挖出来的吧？'我是Win98，我能解析已经很努力了……'</span>"
        elif seconds <= 180:
            return f"<span style='{style}'>解析时间足够你读完《百年孤独》，并深刻理解'等待'的真谛。</span>"
        elif seconds <= 185:
            return f"<span style='{style}'>你这电脑是边解析边抽烟吗？'别催，让我抽完这一口……'</span>"
        elif seconds <= 190:
            return f"<span style='{style}'>你这CPU是冰块做的吧？'夏天到了，我得省点能量……'</span>"
        elif seconds <= 195:
            return f"<span style='{style}'>你的电脑已经进入'电子棺材'模式，建议直接举办葬礼。</span>"
        elif seconds <= 200:
            return f"<span style='{style}'>这不是解析，这是《等待戈多》真人版，观众都走光了，进度条还在演。</span>"
        elif seconds <= 205:
            return f"<span style='{style}'>你这电脑是边解析边看《红楼梦》吗？'宝玉，我解析完了，你来看吧……'</span>"
        elif seconds <= 210:
            return f"<span style='{style}'>你这电脑是边解析边看《三国演义》吗？'孔明，我解析完了，你来看吧……'</span>"
        elif seconds <= 300:
            return f"<span style='{style}'>你这电脑是边解析边看《水浒传》吗？'宋江，我解析完了，你来看吧……'</span>"
        else:
            return f"<span style='{style}'>兄弟这电脑配置不适合刷机，别刷了，算了吧</span>"

        
    def set_payload_file(self, file_path):
        """设置要解析的文件路径，支持zip和bin格式"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        if not file_path.lower().endswith(('.zip', '.bin')):
            raise ValueError("文件必须是zip或bin格式")
        self.payload_file = file_path
        
    def set_extract_folder(self, folder_path):
        """设置解析输出目录"""
        style = "font-size: 15px; font-weight: bold;"
        # 获取zip文件所在目录
        zip_dir = os.path.dirname(self.payload_file)
        # 设置minimg文件夹路径
        minimg_path = os.path.join(zip_dir, "minimg")
        
        # 如果minimg文件夹存在，先删除
        if os.path.exists(minimg_path):
            try:
                shutil.rmtree(minimg_path)
                self.add_log(f"<span style='{style}'>minimg</span>", "info")
            except Exception as e:
                self.add_log(f"<span style='{style}'>删除失败: {str(e)}</span>", "error")
                return False
        
        # 创建新的minimg文件夹
        try:
            os.makedirs(minimg_path)
            self.add_log(f"<span style='{style}'>创建新的minimg文件夹</span>", "info")
        except Exception as e:
            self.add_log(f"<span style='{style}'>创建失败: {str(e)}</span>", "error")
            return False
            
        self.extract_folder = minimg_path
        return True
        
    def get_pay_exe_path(self):
        """获取pay.exe的路径"""
        try:
            # 获取当前脚本所在目录
            if getattr(sys, 'frozen', False):
                # 如果是打包后的程序
                base_path = sys._MEIPASS
            else:
                # 如果是开发环境
                base_path = os.path.dirname(os.path.abspath(__file__))
            
            # 构建pay.exe的路径
            pay_exe_path = os.path.join(base_path, "ADBTools", "pay.exe")
            
            if not os.path.exists(pay_exe_path):
                raise FileNotFoundError(f"pay.exe未: {pay_exe_path}")
                
            return pay_exe_path
            
        except Exception as e:
            raise Exception(f"获取: {str(e)}")
    
    def format_time(self, seconds):
        """格式化时间显示"""
        minutes = int(seconds // 60)
        remaining_seconds = int(seconds % 60)
        if minutes > 0:
            return f"{minutes}分{remaining_seconds}秒"
        else:
            return f"{remaining_seconds}秒"
    
    def handle_extract(self):
        """处理解析流程"""
        style_main = "font-size: 18px; font-weight: bold;"
        style_sub = "font-size: 15px; font-weight: bold;"
        if not self.payload_file:
            self.add_log(f"<span style='{style_sub}'>未设置要解析的文件</span>", "error")
            return False
            
        if not self.extract_folder:
            self.add_log(f"<span style='{style_sub}'>未设置输出目录</span>", "error")
            return False

        # 检查文件所在磁盘的可用空间
        try:
            disk = os.path.splitdrive(self.payload_file)[0]
            free_space = shutil.disk_usage(disk).free
            free_space_gb = free_space / (1024 ** 3)
            if free_space_gb < 30:
                self.add_log(f"<span style='{style_main}'>磁盘空间不足，请确保可用空间大于30GB</span>", "error")
                return False
        except Exception as e:
            self.add_log(f"<span style='{style_main}'>检查磁盘空间: {str(e)}</span>", "error")
            return False
            
        try:
            self.is_extracting = True
            self.start_time = time.time()  # 记录开始时间
            self.add_log(f"<span style='{style_main}'>开始解析全量包...榨干你电脑的每一滴性能（提示：电脑性能越好，解析速度越快）</span>", "info")
            
            # 获取pay.exe路径
            pay_exe_path = self.get_pay_exe_path()
            
            # 构建命令
            command = [
                pay_exe_path,
                self.payload_file,
                "--output-dir",
                "minimg"  # 直接使用minimg作为输出文件夹名
            ]
            
            # 设置启动信息（Windows下隐藏控制台窗口）
            startupinfo, creationflags = self._get_subprocess_config()
            
            # 执行命令，不显示输出
            process = subprocess.Popen(
                command,
                stdout=subprocess.DEVNULL,  # 重定向标准输出到空设备
                stderr=subprocess.PIPE,     # 保留错误输出用于错误处理
                startupinfo=startupinfo,
                creationflags=creationflags,
                cwd=os.path.dirname(self.payload_file)  # 设置工作目录为zip文件所在目录
            )
            
            # 等待进程完成
            return_code = process.wait()
            
            # 计算总时间
            total_time = time.time() - self.start_time
            time_str = self.format_time(total_time)
            
            # 获取对应的完成提示
            completion_message = self.get_completion_message(total_time)
            
            # 检查是否成功
            if return_code == 0:
                self.add_log(
                    f"<span style='{style_main}'>解析完成，请查看minimg文件夹</span><br>"
                    f"<span style='{style_main}'>总用时: {time_str}</span><br>"
                    f"{completion_message}",
                    "success"
                )
                return True
            else:
                # 只在失败时读取错误信息
                error_output = process.stderr.read().decode(locale.getpreferredencoding(False), errors='replace')
                if error_output.strip():
                    self.add_log(f"<span style='{style_main}'>解析失败 (用时: {time_str}): {error_output}</span>", "error")
                else:
                    self.add_log(f"<span style='{style_main}'>解析失败 (用时: {time_str})</span>", "error")
                return False
                
        except Exception as e:
            if self.start_time:
                total_time = time.time() - self.start_time
                time_str = self.format_time(total_time)
                self.add_log(f"<span style='{style_main}'>解析过程出错 (用时: {time_str}): {str(e)}</span>", "error")
            else:
                self.add_log(f"<span style='{style_main}'>解析过程出错: {str(e)}</span>", "error")
            return False
            
        finally:
            self.is_extracting = False
            self.start_time = None
            
    def stop(self):
        """停止解析过程"""
        style_main = "font-size: 18px; font-weight: bold;"
        self.is_extracting = False
        if self.start_time:
            total_time = time.time() - self.start_time
            time_str = self.format_time(total_time)
            self.add_log(f"<span style='{style_main}'>解析已停止 (用时: {time_str})</span>", "warning")
            self.start_time = None