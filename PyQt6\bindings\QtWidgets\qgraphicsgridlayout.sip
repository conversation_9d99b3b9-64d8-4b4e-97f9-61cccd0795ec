// qgraphicsgridlayout.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QGraphicsGridLayout : public QGraphicsLayout
{
%TypeHeaderCode
#include <qgraphicsgridlayout.h>
%End

public:
    QGraphicsGridLayout(QGraphicsLayoutItem *parent /TransferThis/ = 0);
    virtual ~QGraphicsGridLayout();
    void addItem(QGraphicsLayoutItem *item /Transfer/, int row, int column, int rowSpan, int columnSpan, Qt::Alignment alignment = Qt::Alignment());
    void addItem(QGraphicsLayoutItem *item /Transfer/, int row, int column, Qt::Alignment alignment = Qt::Alignment());
    void setHorizontalSpacing(qreal spacing);
    qreal horizontalSpacing() const;
    void setVerticalSpacing(qreal spacing);
    qreal verticalSpacing() const;
    void setSpacing(qreal spacing);
    void setRowSpacing(int row, qreal spacing);
    qreal rowSpacing(int row) const;
    void setColumnSpacing(int column, qreal spacing);
    qreal columnSpacing(int column) const;
    void setRowStretchFactor(int row, int stretch);
    int rowStretchFactor(int row) const;
    void setColumnStretchFactor(int column, int stretch);
    int columnStretchFactor(int column) const;
    void setRowMinimumHeight(int row, qreal height);
    qreal rowMinimumHeight(int row) const;
    void setRowPreferredHeight(int row, qreal height);
    qreal rowPreferredHeight(int row) const;
    void setRowMaximumHeight(int row, qreal height);
    qreal rowMaximumHeight(int row) const;
    void setRowFixedHeight(int row, qreal height);
    void setColumnMinimumWidth(int column, qreal width);
    qreal columnMinimumWidth(int column) const;
    void setColumnPreferredWidth(int column, qreal width);
    qreal columnPreferredWidth(int column) const;
    void setColumnMaximumWidth(int column, qreal width);
    qreal columnMaximumWidth(int column) const;
    void setColumnFixedWidth(int column, qreal width);
    void setRowAlignment(int row, Qt::Alignment alignment);
    Qt::Alignment rowAlignment(int row) const;
    void setColumnAlignment(int column, Qt::Alignment alignment);
    Qt::Alignment columnAlignment(int column) const;
    void setAlignment(QGraphicsLayoutItem *item, Qt::Alignment alignment);
    Qt::Alignment alignment(QGraphicsLayoutItem *item) const;
    int rowCount() const;
    int columnCount() const;
    QGraphicsLayoutItem *itemAt(int row, int column) const;
    virtual int count() const;
    virtual QGraphicsLayoutItem *itemAt(int index) const;
    virtual void removeAt(int index);
%MethodCode
        // The ownership of any existing item must be passed back to Python.
        QGraphicsLayoutItem *itm;
        
        if (a0 < sipCpp->count())
            itm = sipCpp->itemAt(a0);
        else
            itm = 0;
        
        Py_BEGIN_ALLOW_THREADS
        sipSelfWasArg ? sipCpp->QGraphicsGridLayout::removeAt(a0)
                      : sipCpp->removeAt(a0);
        Py_END_ALLOW_THREADS
        
        if (itm)
        {
            PyObject *itmo = sipGetPyObject(itm, sipType_QGraphicsLayoutItem);
        
            if (itmo)
                sipTransferBack(itmo);
        }
%End

    virtual void invalidate();
    virtual void setGeometry(const QRectF &rect);
    virtual QSizeF sizeHint(Qt::SizeHint which, const QSizeF &constraint = QSizeF()) const;
    void removeItem(QGraphicsLayoutItem *item /TransferBack/);
};
