# -*- coding: utf-8 -*-
"""
ADBTools打包修复脚本
专门解决ADBTools文件夹打包问题
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_adbtools_in_exe():
    """检查exe文件中是否包含ADBTools"""
    exe_path = "advanced_build/final_output/益民欧加真固件刷写工具_加密版.exe"
    
    if not os.path.exists(exe_path):
        print("❌ exe文件不存在")
        return False
    
    # 运行exe文件并检查临时目录
    print("🔍 检查exe文件中的ADBTools...")
    
    # 这里可以添加更多检查逻辑
    file_size = os.path.getsize(exe_path)
    print(f"📦 exe文件大小: {file_size:,} 字节 ({file_size/1024/1024:.1f} MB)")
    
    return True

def rebuild_with_fixed_adbtools():
    """使用修复的ADBTools配置重新构建"""
    print("🔧 开始修复ADBTools打包问题...")
    
    # 检查ADBTools目录
    adbtools_path = "ADBTools"
    if not os.path.exists(adbtools_path):
        print("❌ ADBTools目录不存在")
        return False
    
    files = os.listdir(adbtools_path)
    print(f"✅ ADBTools包含 {len(files)} 个文件:")
    for file in files:
        file_path = os.path.join(adbtools_path, file)
        if os.path.isfile(file_path):
            size = os.path.getsize(file_path)
            print(f"  📄 {file} ({size:,} 字节)")
    
    # 创建专门的Nuitka命令
    nuitka_cmd = [
        sys.executable, "-m", "nuitka",
        "--standalone",
        "--onefile",
        "--onefile-tempdir-spec={TEMP}\\syiming\\onefile_{PID}_{TIME}",
        "--windows-console-mode=disable",
        "--enable-plugin=pyqt6",
        "--assume-yes-for-downloads",
        "--show-progress",
        "--show-memory",
        "--remove-output",
        "--output-filename=益民欧加真固件刷写工具_ADBTools修复版.exe",
    ]
    
    # 添加图标
    if os.path.exists("ico/icon.ico"):
        nuitka_cmd.append("--windows-icon-from-ico=ico/icon.ico")
    
    # 逐个添加ADBTools文件
    print("📦 添加ADBTools文件到Nuitka命令:")
    for file in files:
        file_path = os.path.join(adbtools_path, file)
        if os.path.isfile(file_path):
            nuitka_cmd.append(f"--include-data-file={file_path}=ADBTools/{file}")
            print(f"  ✅ 添加: {file}")
    
    # 添加其他资源目录
    other_dirs = ["ico", "tup"]
    for dir_name in other_dirs:
        if os.path.exists(dir_name):
            nuitka_cmd.append(f"--include-data-dir={dir_name}={dir_name}")
            print(f"  ✅ 添加目录: {dir_name}")
    
    # 添加主脚本
    nuitka_cmd.append("main.py")
    
    print("\n🚀 执行Nuitka编译...")
    print(f"命令: {' '.join(nuitka_cmd)}")
    
    try:
        result = subprocess.run(nuitka_cmd, timeout=1800)  # 30分钟超时
        
        if result.returncode == 0:
            output_file = "益民欧加真固件刷写工具_ADBTools修复版.exe"
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"\n✅ 修复版构建成功!")
                print(f"📁 输出文件: {output_file}")
                print(f"📦 文件大小: {file_size:,} 字节 ({file_size/1024/1024:.1f} MB)")
                
                # 移动到输出目录
                final_path = "advanced_build/final_output/益民欧加真固件刷写工具_ADBTools修复版.exe"
                os.makedirs(os.path.dirname(final_path), exist_ok=True)
                shutil.move(output_file, final_path)
                print(f"📁 已移动到: {final_path}")
                
                return True
            else:
                print("❌ 编译完成但未找到输出文件")
                return False
        else:
            print(f"❌ Nuitka编译失败，返回码: {result.returncode}")
            return False
    
    except Exception as e:
        print(f"❌ 编译过程异常: {e}")
        return False

def create_adbtools_test_script():
    """创建ADBTools测试脚本"""
    test_script = '''# -*- coding: utf-8 -*-
"""
ADBTools测试脚本
用于验证打包后的ADBTools是否可用
"""

import os
import sys
import subprocess

def get_resource_path(relative_path):
    """获取资源文件路径"""
    if hasattr(sys, '_MEIPASS'):
        # 打包后的环境
        base_path = sys._MEIPASS
    else:
        # 开发环境
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

def test_adbtools():
    """测试ADBTools"""
    print("🔍 测试ADBTools...")
    
    # 检查ADBTools目录
    adbtools_path = get_resource_path("ADBTools")
    print(f"ADBTools路径: {adbtools_path}")
    
    if os.path.exists(adbtools_path):
        print("✅ ADBTools目录存在")
        files = os.listdir(adbtools_path)
        print(f"包含 {len(files)} 个文件:")
        for file in files:
            file_path = os.path.join(adbtools_path, file)
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                print(f"  ✅ {file} ({size:,} 字节)")
            else:
                print(f"  ❌ {file} (不存在)")
    else:
        print("❌ ADBTools目录不存在")
        return False
    
    # 测试adb.exe
    adb_path = get_resource_path("ADBTools/adb.exe")
    if os.path.exists(adb_path):
        try:
            result = subprocess.run([adb_path, "version"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✅ adb.exe 可以正常运行")
                print(f"版本信息: {result.stdout.strip()}")
            else:
                print("⚠️ adb.exe 运行失败")
        except Exception as e:
            print(f"⚠️ adb.exe 测试异常: {e}")
    else:
        print("❌ adb.exe 不存在")
    
    return True

if __name__ == "__main__":
    test_adbtools()
    input("按回车键退出...")
'''
    
    with open("test_adbtools_in_exe.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print("✅ 已创建ADBTools测试脚本: test_adbtools_in_exe.py")

def main():
    """主函数"""
    print("🔧 ADBTools打包修复工具")
    print("=" * 50)
    
    # 检查当前exe
    check_adbtools_in_exe()
    
    # 创建测试脚本
    create_adbtools_test_script()
    
    # 询问是否重新构建
    choice = input("\n是否重新构建修复版？(y/n): ").lower()
    if choice == 'y':
        success = rebuild_with_fixed_adbtools()
        if success:
            print("\n🎉 修复版构建完成！")
            print("💡 建议运行生成的exe文件测试ADBTools是否正常工作")
        else:
            print("\n❌ 修复版构建失败")
    
    print("\n完成！")

if __name__ == "__main__":
    main()
