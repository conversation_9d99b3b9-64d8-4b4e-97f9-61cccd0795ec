# -*- coding: utf-8 -*-
"""
模块加载器 - 自动生成
处理Cython编译模块的动态加载
"""
import os
import sys
import json
import importlib.util

def load_cython_modules():
    """加载Cython编译的模块"""
    mapping_file = "cython_mapping.json"
    if not os.path.exists(mapping_file):
        return

    try:
        with open(mapping_file, 'r', encoding='utf-8') as f:
            mapping = json.load(f)

        for original_name, compiled_name in mapping.items():
            if os.path.exists(compiled_name):
                module_name = original_name.replace('.py', '')
                spec = importlib.util.spec_from_file_location(
                    module_name,
                    compiled_name
                )
                if spec and spec.loader:
                    module = importlib.util.module_from_spec(spec)
                    sys.modules[module_name] = module
                    spec.loader.exec_module(module)
                    print(f"已加载Cython模块: {module_name}")
    except Exception as e:
        print(f"加载Cython模块失败: {e}")

# 自动加载
load_cython_modules()
