// qimagecapture.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QImageCapture : public QObject
{
%TypeHeaderCode
#include <qimagecapture.h>
%End

public:
    enum Error
    {
        NoError,
        NotReadyError,
        ResourceError,
        OutOfSpaceError,
        NotSupportedFeatureError,
        FormatError,
    };

    enum Quality
    {
        VeryLowQuality,
        LowQuality,
        NormalQuality,
        HighQuality,
        VeryHighQuality,
    };

    enum FileFormat
    {
        UnspecifiedFormat,
        JPEG,
        PNG,
        WebP,
        Tiff,
    };

    explicit QImageCapture(QObject *parent /TransferThis/ = 0);
    virtual ~QImageCapture();
    bool isAvailable() const;
    QMediaCaptureSession *captureSession() const;
    QImageCapture::Error error() const;
    QString errorString() const;
    bool isReadyForCapture() const;
    QImageCapture::FileFormat fileFormat() const;
    void setFileFormat(QImageCapture::FileFormat format);
    static QList<QImageCapture::FileFormat> supportedFormats();
    static QString fileFormatName(QImageCapture::FileFormat c);
    static QString fileFormatDescription(QImageCapture::FileFormat c);
    QSize resolution() const;
    void setResolution(const QSize &);
    void setResolution(int width, int height);
    QImageCapture::Quality quality() const;
    void setQuality(QImageCapture::Quality quality);
    QMediaMetaData metaData() const;
    void setMetaData(const QMediaMetaData &metaData);
    void addMetaData(const QMediaMetaData &metaData);

public slots:
    int captureToFile(const QString &location = QString());
    int capture();

signals:
    void errorChanged();
    void errorOccurred(int id, QImageCapture::Error error, const QString &errorString);
    void readyForCaptureChanged(bool ready);
    void metaDataChanged();
    void fileFormatChanged();
    void qualityChanged();
    void resolutionChanged();
    void imageExposed(int id);
    void imageCaptured(int id, const QImage &preview);
    void imageAvailable(int id, const QVideoFrame &frame);
    void imageSaved(int id, const QString &fileName);
};

%End
