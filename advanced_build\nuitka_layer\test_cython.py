# -*- coding: utf-8 -*-
"""
测试Cython编译环境
"""

import os
import sys
import subprocess

def test_cython_environment():
    """测试Cython编译环境"""
    print("🔧 测试Cython编译环境")
    print("=" * 40)
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查必需的包
    packages = ['cython', 'numpy', 'setuptools']
    for package in packages:
        try:
            __import__(package)
            print(f"✅ {package} - 已安装")
        except ImportError:
            print(f"❌ {package} - 未安装")
            return False
    
    return True

def test_simple_cython():
    """测试简单的Cython编译"""
    print("\n🚀 测试简单Cython编译")
    print("-" * 40)
    
    # 创建简单的测试文件
    test_py = "test_module.py"
    test_content = '''
def hello_world():
    """简单的测试函数"""
    return "Hello from Cython!"

def add_numbers(a, b):
    """简单的加法函数"""
    return a + b
'''
    
    with open(test_py, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print(f"✅ 创建测试文件: {test_py}")
    
    # 创建setup.py
    setup_content = '''
from setuptools import setup
from Cython.Build import cythonize

setup(
    ext_modules = cythonize("test_module.py", language_level=3),
    zip_safe=False,
)
'''
    
    with open("setup.py", 'w', encoding='utf-8') as f:
        f.write(setup_content)
    
    print("✅ 创建setup.py")
    
    # 执行编译
    try:
        print("🔄 开始编译...")
        cmd = [sys.executable, "setup.py", "build_ext", "--inplace"]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            # 检查输出文件
            pyd_files = [f for f in os.listdir('.') if f.startswith('test_module') and f.endswith('.pyd')]
            if pyd_files:
                pyd_file = pyd_files[0]
                size = os.path.getsize(pyd_file)
                print(f"✅ 编译成功: {pyd_file} ({size:,} 字节)")
                
                # 测试导入
                try:
                    import test_module
                    result = test_module.hello_world()
                    print(f"✅ 模块测试成功: {result}")
                    
                    add_result = test_module.add_numbers(5, 3)
                    print(f"✅ 函数测试成功: 5 + 3 = {add_result}")
                    
                    return True
                except ImportError as e:
                    print(f"❌ 模块导入失败: {e}")
            else:
                print("❌ 未找到编译输出文件")
        else:
            print(f"❌ 编译失败:")
            print(f"stdout: {result.stdout}")
            print(f"stderr: {result.stderr}")
        
        return False
        
    except subprocess.TimeoutExpired:
        print("❌ 编译超时")
        return False
    except Exception as e:
        print(f"❌ 编译异常: {e}")
        return False
    finally:
        # 清理文件
        cleanup_files = ["test_module.py", "setup.py"]
        for f in cleanup_files:
            if os.path.exists(f):
                os.remove(f)
        
        # 清理编译产物
        import glob
        for pattern in ["test_module*.pyd", "test_module*.c", "build"]:
            for f in glob.glob(pattern):
                try:
                    if os.path.isdir(f):
                        import shutil
                        shutil.rmtree(f)
                    else:
                        os.remove(f)
                except:
                    pass

def main():
    """主函数"""
    print("🔒 Cython编译环境测试")
    print("=" * 50)
    
    # 测试环境
    if not test_cython_environment():
        print("\n❌ 环境检查失败")
        return False
    
    # 测试编译
    if test_simple_cython():
        print("\n🎉 Cython编译环境正常!")
        print("可以继续进行核心算法编译")
        return True
    else:
        print("\n❌ Cython编译测试失败")
        print("需要检查编译环境配置")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
