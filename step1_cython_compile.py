# -*- coding: utf-8 -*-
"""
步骤1: Cython编译所有核心模块
专门用于编译核心算法为C扩展
"""

import os
import sys
import shutil
import subprocess
import time
import json

# 所有需要编译的模块
ALL_MODULES = [
    "coloros.py",             # ColorOS 解锁核心算法
    "coloros15.py",           # ColorOS 15核心算法
    "utils.py",               # 工具类核心算法
    "fastboodt.py",          # Fastboot工具核心
    "zhidinyishuaxie.py",     # 自定义刷写核心
    "payload_extractor.py",   # 解包工具核心
    "custom_messagebox.py",   # 自定义消息框
    "genduodakhd.py",         # 根多大核心
    "font_extractor.py",      # 字体提取器
    "flash_tool.py",          # UI界面
    "main.py",                # 主程序（保留Python版本用于调试）
]

def log(message, level="INFO"):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    if level == "SUCCESS":
        print(f"[{timestamp}] ✅ {message}")
    elif level == "ERROR":
        print(f"[{timestamp}] ❌ {message}")
    elif level == "WARNING":
        print(f"[{timestamp}] ⚠️ {message}")
    elif level == "STEP":
        print(f"[{timestamp}] 🔧 {message}")
    else:
        print(f"[{timestamp}] {message}")

def check_cython_environment():
    """检查Cython编译环境"""
    log("检查Cython编译环境", "STEP")
    
    # 检查Python版本
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    log(f"Python版本: {python_version}")
    
    # 检查必需的包
    required_packages = ['cython', 'numpy', 'setuptools']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            log(f"{package} - 已安装", "SUCCESS")
        except ImportError:
            missing_packages.append(package)
            log(f"{package} - 未安装", "ERROR")
    
    if missing_packages:
        log("安装缺失的包...", "STEP")
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package, "--quiet"])
                log(f"{package} 安装成功", "SUCCESS")
            except subprocess.CalledProcessError:
                log(f"{package} 安装失败", "ERROR")
                return False
    
    return True

def prepare_modules():
    """准备要编译的模块"""
    log("检查要编译的模块", "STEP")
    
    available_modules = []
    missing_modules = []
    
    for module in ALL_MODULES:
        if os.path.exists(module):
            size = os.path.getsize(module)
            available_modules.append(module)
            log(f"找到模块: {module} ({size:,} 字节)")
        else:
            missing_modules.append(module)
            log(f"模块不存在: {module}", "WARNING")
    
    if not available_modules:
        log("没有找到任何可编译的模块", "ERROR")
        return []
    
    log(f"准备编译 {len(available_modules)} 个模块", "SUCCESS")
    return available_modules

def compile_single_module(module, build_dir):
    """编译单个模块"""
    log(f"编译模块: {module}", "STEP")
    
    # 复制模块到编译目录
    src_path = module
    dst_path = os.path.join(build_dir, module)
    shutil.copy2(src_path, dst_path)
    
    # 生成模块名
    module_name = module.replace('.py', '_cython')
    
    # 创建专用的setup.py
    setup_content = f'''# -*- coding: utf-8 -*-
from setuptools import setup
from Cython.Build import cythonize

# 编译选项
compiler_directives = {{
    'language_level': 3,
    'boundscheck': False,
    'wraparound': False,
    'initializedcheck': False,
    'cdivision': True,
}}

setup(
    ext_modules=cythonize(
        "{module}",
        compiler_directives=compiler_directives,
        annotate=False
    ),
    zip_safe=False,
)
'''
    
    setup_file = f"setup_{module_name}.py"
    setup_path = os.path.join(build_dir, setup_file)
    
    with open(setup_path, "w", encoding="utf-8") as f:
        f.write(setup_content)
    
    # 执行编译
    original_dir = os.getcwd()
    try:
        os.chdir(build_dir)
        
        cmd = [sys.executable, setup_file, "build_ext", "--inplace"]
        log(f"执行: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
        
        if result.returncode == 0:
            # 查找生成的.pyd文件
            pyd_files = [f for f in os.listdir('.') if f.startswith(module.replace('.py', '')) and f.endswith('.pyd')]
            
            if pyd_files:
                pyd_file = pyd_files[0]
                size = os.path.getsize(pyd_file)
                log(f"编译成功: {module} -> {pyd_file} ({size:,} 字节)", "SUCCESS")
                
                # 清理临时文件
                temp_files = [f for f in os.listdir('.') if f.endswith('.c') or f.startswith('setup_')]
                for temp_file in temp_files:
                    try:
                        os.remove(temp_file)
                    except:
                        pass
                
                os.chdir(original_dir)
                return True, pyd_file
            else:
                log(f"编译完成但未找到.pyd文件: {module}", "ERROR")
        else:
            log(f"编译失败: {module}", "ERROR")
            if result.stderr:
                log(f"错误信息: {result.stderr[:300]}")
        
        os.chdir(original_dir)
        return False, None
        
    except subprocess.TimeoutExpired:
        os.chdir(original_dir)
        log(f"编译超时: {module}", "ERROR")
        return False, None
    except Exception as e:
        os.chdir(original_dir)
        log(f"编译异常: {module} - {e}", "ERROR")
        return False, None

def compile_all_modules():
    """编译所有模块"""
    log("开始Cython编译所有模块", "STEP")
    log("=" * 60)
    
    # 准备编译目录
    build_dir = "cython_compiled_modules"
    if os.path.exists(build_dir):
        shutil.rmtree(build_dir)
    os.makedirs(build_dir)
    
    # 获取要编译的模块
    modules_to_compile = prepare_modules()
    if not modules_to_compile:
        return False, None, {}
    
    compiled_modules = {}
    failed_modules = []
    
    # 逐个编译模块
    for i, module in enumerate(modules_to_compile, 1):
        log(f"\n[{i}/{len(modules_to_compile)}] 处理模块: {module}")
        
        success, pyd_file = compile_single_module(module, build_dir)
        
        if success and pyd_file:
            compiled_modules[module] = pyd_file
        else:
            failed_modules.append(module)
        
        # 短暂暂停避免资源冲突
        time.sleep(0.5)
    
    # 生成编译报告
    log("\n" + "=" * 60)
    log("Cython编译结果统计", "STEP")
    log(f"总模块数: {len(modules_to_compile)}")
    log(f"成功编译: {len(compiled_modules)}")
    log(f"编译失败: {len(failed_modules)}")
    
    success_rate = len(compiled_modules) / len(modules_to_compile) * 100
    log(f"成功率: {success_rate:.1f}%")
    
    if compiled_modules:
        log("\n✅ 成功编译的模块:")
        total_size = 0
        for original, compiled in compiled_modules.items():
            compiled_path = os.path.join(build_dir, compiled)
            if os.path.exists(compiled_path):
                size = os.path.getsize(compiled_path)
                total_size += size
                log(f"  {original} -> {compiled} ({size:,} 字节)")
        
        log(f"\n总编译输出大小: {total_size:,} 字节 ({total_size/1024/1024:.1f} MB)")
    
    if failed_modules:
        log("\n❌ 编译失败的模块:")
        for module in failed_modules:
            log(f"  {module}")
    
    # 保存编译映射
    if compiled_modules:
        mapping_data = {
            "modules": compiled_modules,
            "total_modules": len(compiled_modules),
            "failed_modules": failed_modules,
            "success_rate": f"{success_rate:.1f}%",
            "compile_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "build_directory": build_dir
        }
        
        mapping_file = os.path.join(build_dir, "cython_mapping.json")
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(mapping_data, f, indent=2, ensure_ascii=False)
        
        log(f"\n编译映射已保存: {mapping_file}", "SUCCESS")
        
        # 创建模块加载器
        create_module_loader(build_dir, compiled_modules)
        
        return True, build_dir, compiled_modules
    else:
        log("没有成功编译任何模块", "ERROR")
        return False, None, {}

def create_module_loader(build_dir, compiled_modules):
    """创建Cython模块加载器"""
    log("创建Cython模块加载器", "STEP")
    
    loader_content = f'''# -*- coding: utf-8 -*-
"""
Cython模块动态加载器
自动加载所有Cython编译的模块
"""
import os
import sys
import json
import importlib.util

class CythonModuleLoader:
    def __init__(self):
        self.loaded_modules = {{}}
        self.mapping = {{}}
        self.load_mapping()
    
    def get_resource_path(self, relative_path):
        """获取资源文件路径"""
        if hasattr(sys, '_MEIPASS'):
            base_path = sys._MEIPASS
        else:
            base_path = os.path.abspath(".")
        return os.path.join(base_path, relative_path)
    
    def load_mapping(self):
        """加载模块映射"""
        mapping_file = self.get_resource_path("cython_mapping.json")
        if os.path.exists(mapping_file):
            try:
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.mapping = data.get("modules", {{}})
                    print(f"✅ 加载Cython映射: {{len(self.mapping)}} 个模块")
            except Exception as e:
                print(f"⚠️ 加载映射失败: {{e}}")
    
    def load_cython_module(self, module_name):
        """加载指定的Cython模块"""
        if module_name in self.loaded_modules:
            return self.loaded_modules[module_name]
        
        compiled_name = self.mapping.get(module_name)
        if not compiled_name:
            return None
        
        compiled_path = self.get_resource_path(compiled_name)
        if not os.path.exists(compiled_path):
            return None
        
        try:
            spec = importlib.util.spec_from_file_location(
                module_name.replace('.py', ''), 
                compiled_path
            )
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                sys.modules[module_name.replace('.py', '')] = module
                spec.loader.exec_module(module)
                self.loaded_modules[module_name] = module
                print(f"✅ 加载Cython模块: {{module_name}}")
                return module
        except Exception as e:
            print(f"❌ 加载模块失败 {{module_name}}: {{e}}")
        
        return None
    
    def load_all_modules(self):
        """加载所有Cython模块"""
        success_count = 0
        for module_name in self.mapping.keys():
            if self.load_cython_module(module_name):
                success_count += 1
        
        print(f"✅ 成功加载 {{success_count}}/{{len(self.mapping)}} 个Cython模块")
        return success_count == len(self.mapping)

# 全局加载器实例
_cython_loader = CythonModuleLoader()

def get_cython_module(module_name):
    """获取Cython模块"""
    return _cython_loader.load_cython_module(module_name)

def load_all_cython_modules():
    """加载所有Cython模块"""
    return _cython_loader.load_all_modules()

# 在模块导入时自动加载
if __name__ != "__main__":
    load_all_cython_modules()
'''
    
    loader_path = os.path.join(build_dir, "cython_loader.py")
    with open(loader_path, 'w', encoding='utf-8') as f:
        f.write(loader_content)
    
    log("Cython模块加载器已创建", "SUCCESS")

def main():
    """主函数"""
    log("步骤1: Cython编译所有模块", "STEP")
    log("=" * 60)
    
    start_time = time.time()
    
    # 检查环境
    if not check_cython_environment():
        log("Cython环境检查失败", "ERROR")
        return False
    
    # 编译所有模块
    success, build_dir, compiled_modules = compile_all_modules()
    
    if success:
        build_time = time.time() - start_time
        minutes = int(build_time // 60)
        seconds = int(build_time % 60)
        
        log("\n" + "=" * 60)
        log("Cython编译完成！", "SUCCESS")
        log(f"编译时间: {minutes}分{seconds}秒")
        log(f"成功编译: {len(compiled_modules)} 个模块")
        log(f"编译目录: {build_dir}")
        log("=" * 60)
        
        log("\n下一步: 运行 step2_nuitka_package.py 进行Nuitka打包")
        return True
    else:
        log("Cython编译失败", "ERROR")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
