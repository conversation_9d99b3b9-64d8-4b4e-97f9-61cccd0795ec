// qdatetime.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDate /TypeHintIn="Union[QDate, datetime.date]"/
{
%TypeHeaderCode
#include <qdatetime.h>
%End

%TypeCode
#include <QHash>
%End

%ConvertToTypeCode
// Allow a Python date object whenever a QDate is expected.

if (sipIsErr == NULL)
    return (sipGetDate(sipPy, 0) ||
            sipCanConvertToType(sipPy, sipType_QDate, SIP_NO_CONVERTORS));

sipDateDef py_date;

if (sipGetDate(sipPy, &py_date))
{
    *sipCppPtr = new QDate(py_date.pd_year,
                           py_date.pd_month,
                           py_date.pd_day);

    return sipGetState(sipTransferObj);
}

*sipCppPtr = reinterpret_cast<QDate *>(sipConvertToType(sipPy, sipType_QDate, sipTransferObj, SIP_NO_CONVERTORS, 0, sipIsErr));

return 0;
%End

%PickleCode
    sipRes = Py_BuildValue("iii", sipCpp->year(), sipCpp->month(), sipCpp->day());
%End

public:
    QDate();
    QDate(int y, int m, int d);
    QDate(int y, int m, int d, QCalendar cal);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        if (sipCpp->isNull())
        {
            sipRes = PyUnicode_FromString("PyQt6.QtCore.QDate()");
        }
        else
        {
            sipRes = PyUnicode_FromFormat(
                    "PyQt6.QtCore.QDate(%i, %i, %i)", sipCpp->year(),
                    sipCpp->month(), sipCpp->day());
        }
%End

    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(sipCpp->toString(Qt::ISODate));
%End

    SIP_PYOBJECT toPyDate() const /TypeHint="datetime.date"/;
%MethodCode
        // Convert to a Python date object.
        sipDateDef py_date;
        
        py_date.pd_year = sipCpp->year();
        py_date.pd_month = sipCpp->month();
        py_date.pd_day = sipCpp->day();
        
        sipRes = sipFromDate(&py_date);
%End

    bool isNull() const;
    int __bool__() const;
%MethodCode
        sipRes = !sipCpp->isNull();
%End

    bool isValid() const;
    int year() const;
    int year(QCalendar cal) const;
    int month() const;
    int month(QCalendar cal) const;
    int day() const;
    int day(QCalendar cal) const;
    int dayOfWeek() const;
    int dayOfWeek(QCalendar cal) const;
    int dayOfYear() const;
    int dayOfYear(QCalendar cal) const;
    int daysInMonth() const;
    int daysInMonth(QCalendar cal) const;
    int daysInYear() const;
    int daysInYear(QCalendar cal) const;
    int weekNumber(int *yearNumber = 0) const;
    // In Qt v6.7 this was replaced by two overloads bu twe need to retain the optional keyword argument.
    QString toString(const QString &format, QCalendar cal = QCalendar()) const;
    QString toString(Qt::DateFormat format = Qt::TextDate) const;
    QDate addDays(qint64 days) const;
    QDate addMonths(int months) const;
    QDate addMonths(int months, QCalendar cal) const;
    QDate addYears(int years) const;
    QDate addYears(int years, QCalendar cal) const;
    static QDate currentDate();
    static QDate fromString(const QString &string, Qt::DateFormat format = Qt::TextDate);
    // Qt v6.7 replaced this with two overloads but we need to retain the optional keyword argument.
    static QDate fromString(const QString &string, const QString &format, QCalendar cal = QCalendar());
%If (Qt_6_7_0 -)
    // This replaces two overloads added in Qy v6.7 designed to maintain compatibility regarding optional keyword arguments.
    static QDate fromString(const QString &string, const QString &format, int baseYear, QCalendar cal = QCalendar());
%End
    static bool isValid(int y, int m, int d);
    static bool isLeapYear(int year);
    static QDate fromJulianDay(qint64 jd);
    qint64 toJulianDay() const;
    bool setDate(int year, int month, int date);
    void getDate(int *year, int *month, int *day) const;
    // The arguments are marked as deprecated in Qt v6.9.
    QDateTime startOfDay(Qt::TimeSpec spec = Qt::LocalTime, int offsetSeconds = 0) const;
    QDateTime startOfDay(const QTimeZone &zone) const;
    // The arguments are marked as deprecated in Qt v6.9.
    QDateTime endOfDay(Qt::TimeSpec spec = Qt::LocalTime, int offsetSeconds = 0) const;
    QDateTime endOfDay(const QTimeZone &zone) const;
    bool setDate(int year, int month, int day, QCalendar cal);
    qint64 daysTo(QDate d) const;
};

class QTime /TypeHintIn="Union[QTime, datetime.time]"/
{
%TypeHeaderCode
#include <qdatetime.h>
%End

%TypeCode
#include <QHash>
%End

%ConvertToTypeCode
// Allow a Python time object whenever a QTime is expected.

if (sipIsErr == NULL)
    return (sipGetTime(sipPy, 0) ||
            sipCanConvertToType(sipPy, sipType_QTime, SIP_NO_CONVERTORS));

sipTimeDef py_time;

if (sipGetTime(sipPy, &py_time))
{
    *sipCppPtr = new QTime(py_time.pt_hour,
                           py_time.pt_minute,
                           py_time.pt_second,
                           py_time.pt_microsecond / 1000);

    return sipGetState(sipTransferObj);
}

*sipCppPtr = reinterpret_cast<QTime *>(sipConvertToType(sipPy, sipType_QTime, sipTransferObj, SIP_NO_CONVERTORS, 0, sipIsErr));

return 0;
%End

%PickleCode
    sipRes = Py_BuildValue("iiii", sipCpp->hour(), sipCpp->minute(), sipCpp->second(), sipCpp->msec());
%End

public:
    QTime();
    QTime(int h, int m, int second = 0, int msec = 0);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        if (sipCpp->isNull())
        {
            sipRes = PyUnicode_FromString("PyQt6.QtCore.QTime()");
        }
        else
        {
            sipRes = PyUnicode_FromFormat("PyQt6.QtCore.QTime(%i, %i", sipCpp->hour(),
                    sipCpp->minute());
        
            if (sipCpp->second() || sipCpp->msec())
            {
                qpycore_Unicode_ConcatAndDel(&sipRes,
                        PyUnicode_FromFormat(", %i", sipCpp->second()));
        
                if (sipCpp->msec())
                    qpycore_Unicode_ConcatAndDel(&sipRes,
                            PyUnicode_FromFormat(", %i", sipCpp->msec()));
            }
        
            qpycore_Unicode_ConcatAndDel(&sipRes, PyUnicode_FromString(")"));
        }
%End

    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(sipCpp->toString(Qt::ISODate));
%End

    SIP_PYOBJECT toPyTime() const /TypeHint="datetime.time"/;
%MethodCode
        // Convert to a Python time object.
        sipTimeDef py_time;
        
        py_time.pt_hour = sipCpp->hour();
        py_time.pt_minute = sipCpp->minute();
        py_time.pt_second = sipCpp->second();
        py_time.pt_microsecond = sipCpp->msec() * 1000;
        
        sipRes = sipFromTime(&py_time);
%End

    bool isNull() const;
    int __bool__() const;
%MethodCode
        sipRes = !sipCpp->isNull();
%End

    bool isValid() const;
    int hour() const;
    int minute() const;
    int second() const;
    int msec() const;
    QString toString(Qt::DateFormat format = Qt::TextDate) const;
    QString toString(const QString &format) const;
    bool setHMS(int h, int m, int s, int msec = 0);
    QTime addSecs(int secs) const;
    QTime addMSecs(int ms) const;
    static QTime currentTime();
    static QTime fromString(const QString &string, Qt::DateFormat format = Qt::TextDate);
    static QTime fromString(const QString &s, const QString &format);
    static bool isValid(int h, int m, int s, int msec = 0);
    static QTime fromMSecsSinceStartOfDay(int msecs);
    int msecsSinceStartOfDay() const;
    int secsTo(QTime t) const;
    int msecsTo(QTime t) const;
};

class QDateTime /TypeHintIn="Union[QDateTime, datetime.datetime]"/
{
%TypeHeaderCode
#include <qdatetime.h>
%End

%TypeCode
#include <QHash>
%End

%ConvertToTypeCode
// Allow a Python datetime object whenever a QDateTime is expected.

if (sipIsErr == NULL)
    return (sipGetDateTime(sipPy, 0, 0) ||
            sipCanConvertToType(sipPy, sipType_QDateTime, SIP_NO_CONVERTORS));

sipDateDef py_date;
sipTimeDef py_time;

if (sipGetDateTime(sipPy, &py_date, &py_time))
{
    QDate qdate(py_date.pd_year,
                py_date.pd_month,
                py_date.pd_day);

    QTime qtime(py_time.pt_hour,
                py_time.pt_minute,
                py_time.pt_second,
                py_time.pt_microsecond / 1000);

    QDateTime *qdt = new QDateTime(qdate, qtime);

    *sipCppPtr = qdt;

    return sipGetState(sipTransferObj);
}

*sipCppPtr = reinterpret_cast<QDateTime *>(sipConvertToType(sipPy, sipType_QDateTime, sipTransferObj, SIP_NO_CONVERTORS, 0, sipIsErr));

return 0;
%End

%PickleCode
    QDate qd = sipCpp->date();
    QTime qt = sipCpp->time();
    
    sipRes = Py_BuildValue("iiiiiiii", qd.year(), qd.month(), qd.day(),
            qt.hour(), qt.minute(), qt.second(), qt.msec(),
            (int)sipCpp->timeSpec());
%End

public:
%If (Qt_6_7_0 -)

    enum class TransitionResolution
    {
        Reject,
        RelativeToBefore,
        RelativeToAfter,
        PreferBefore,
        PreferAfter,
        PreferStandard,
        PreferDaylightSaving,
        LegacyBehavior,
    };

%End
    QDateTime();
    QDateTime(const QDateTime &other);
    QDateTime(int year, int month, int day, int hour, int minute, int second = 0, int msec = 0, int timeSpec = 0) /NoDerived/;
%MethodCode
        // This ctor is mainly supplied to allow pickling.
        QDate qd(a0, a1, a2);
        QTime qt(a3, a4, a5, a6);
        
        sipCpp = new QDateTime(qd, qt, (Qt::TimeSpec)a7);
%End

%If (Qt_6_7_0 -)
    // The resolve argument is not optional so that the overload with deprecated arguments continues to work.
    QDateTime(QDate date, QTime time, QDateTime::TransitionResolution resolve) [(QDate date, QTime time, QDateTime::TransitionResolution resolve = QDateTime::TransitionResolution::LegacyBehavior)];
%End
    // The optional arguments are marked as deprecated in Qt v6.9.
    QDateTime(QDate date, QTime time, Qt::TimeSpec spec = Qt::LocalTime, int offsetSeconds = 0);
%If (Qt_6_7_0 -)
    QDateTime(QDate date, QTime time, const QTimeZone &timeZone, QDateTime::TransitionResolution resolve = QDateTime::TransitionResolution::LegacyBehavior);
%End
%If (- Qt_6_7_0)
    QDateTime(QDate date, QTime time, const QTimeZone &timeZone);
%End
    ~QDateTime();
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        if (sipCpp->isNull())
        {
            sipRes = PyUnicode_FromString("PyQt6.QtCore.QDateTime()");
        }
        else
        {
            QDate qd = sipCpp->date();
            QTime qt = sipCpp->time();
        
            sipRes = PyUnicode_FromFormat(
                    "PyQt6.QtCore.QDateTime(%i, %i, %i, %i, %i",
                    qd.year(), qd.month(), qd.day(), qt.hour(), qt.minute());
        
            if (qt.second() || qt.msec() || sipCpp->timeSpec() != Qt::LocalTime)
            {
                qpycore_Unicode_ConcatAndDel(&sipRes,
                        PyUnicode_FromFormat(", %i", qt.second()));
        
                if (qt.msec() || sipCpp->timeSpec() != Qt::LocalTime)
                {
                    qpycore_Unicode_ConcatAndDel(&sipRes,
                            PyUnicode_FromFormat(", %i", qt.msec()));
        
                    if (sipCpp->timeSpec() != Qt::LocalTime)
                        qpycore_Unicode_ConcatAndDel(&sipRes,
                                PyUnicode_FromFormat(", PyQt6.QtCore.Qt.TimeSpec(%i)",
                                        (int)sipCpp->timeSpec()));
                }
            }
        
            qpycore_Unicode_ConcatAndDel(&sipRes, PyUnicode_FromString(")"));
        }
%End

    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(sipCpp->toString(Qt::ISODate));
%End

    SIP_PYOBJECT toPyDateTime() const /TypeHint="datetime.datetime"/;
%MethodCode
        // Convert to a Python datetime object.
        sipDateDef py_date;
        QDate qd = sipCpp->date();
        
        py_date.pd_year = qd.year();
        py_date.pd_month = qd.month();
        py_date.pd_day = qd.day();
        
        sipTimeDef py_time;
        QTime qt = sipCpp->time();
        
        py_time.pt_hour = qt.hour();
        py_time.pt_minute = qt.minute();
        py_time.pt_second = qt.second();
        py_time.pt_microsecond = qt.msec() * 1000;
        
        sipRes = sipFromDateTime(&py_date, &py_time);
%End

    bool isNull() const;
    int __bool__() const;
%MethodCode
        sipRes = !sipCpp->isNull();
%End

    bool isValid() const;
    QDate date() const;
    QTime time() const;
    Qt::TimeSpec timeSpec() const;
    void setTimeSpec(Qt::TimeSpec spec);
    // This was replaced with two overloads in Qt v6.7 but we need the optional keyword argument.
    QString toString(const QString &format, QCalendar cal = QCalendar()) const;
    QString toString(Qt::DateFormat format = Qt::TextDate) const;
    QDateTime addDays(qint64 days) const;
    QDateTime addMonths(int months) const;
    QDateTime addYears(int years) const;
    QDateTime addSecs(qint64 secs) const;
    QDateTime addMSecs(qint64 msecs) const;
    QDateTime toTimeSpec(Qt::TimeSpec spec) const;
    QDateTime toLocalTime() const;
    QDateTime toUTC() const;
    qint64 daysTo(const QDateTime &) const;
    qint64 secsTo(const QDateTime &) const;
    static QDateTime currentDateTime();
%If (Qt_6_5_0 -)
    static QDateTime currentDateTime(const QTimeZone &zone);
%End
    static QDateTime fromString(const QString &string, Qt::DateFormat format = Qt::TextDate);
    // Qt v6.7 replaced this with two overloads but we need to retain the optional keyword argument.
    static QDateTime fromString(const QString &string, const QString &format, QCalendar cal = QCalendar());
%If (Qt_6_7_0 -)
    // This replaces two overloads added in Qy v6.7 designed to maintain compatibility regarding optional keyword arguments.
    static QDateTime fromString(const QString &string, const QString &format, int baseYear, QCalendar cal = QCalendar());
%End
    qint64 toMSecsSinceEpoch() const;
    void setMSecsSinceEpoch(qint64 msecs);
    qint64 msecsTo(const QDateTime &) const;
    static QDateTime currentDateTimeUtc();
    static qint64 currentMSecsSinceEpoch();
    void swap(QDateTime &other /Constrained/);
    int offsetFromUtc() const;
    QTimeZone timeZone() const;
    QString timeZoneAbbreviation() const;
    bool isDaylightTime() const;
    void setOffsetFromUtc(int offsetSeconds);
%If (Qt_6_7_0 -)
    void setTimeZone(const QTimeZone &toZone, QDateTime::TransitionResolution resolve = QDateTime::TransitionResolution::LegacyBehavior);
%End
%If (- Qt_6_7_0)
    void setTimeZone(const QTimeZone &toZone);
%End
    QDateTime toOffsetFromUtc(int offsetSeconds) const;
    QDateTime toTimeZone(const QTimeZone &toZone) const;
    // The optional arguments are marked as deprecated in Qt v6.9.
    static QDateTime fromMSecsSinceEpoch(qint64 msecs, Qt::TimeSpec spec = Qt::LocalTime, int offsetSeconds = 0);
    static QDateTime fromMSecsSinceEpoch(qint64 msecs, const QTimeZone &timeZone);
    qint64 toSecsSinceEpoch() const;
    void setSecsSinceEpoch(qint64 secs);
    // The optional arguments are marked as deprecated in Qt v6.9.
    static QDateTime fromSecsSinceEpoch(qint64 secs, Qt::TimeSpec spec = Qt::LocalTime, int offsetSeconds = 0);
    static QDateTime fromSecsSinceEpoch(qint64 secs, const QTimeZone &timeZone);
    static qint64 currentSecsSinceEpoch();

    enum class YearRange
    {
        First,
        Last,
    };

%If (Qt_6_7_0 -)
    void setDate(QDate date, QDateTime::TransitionResolution resolve = QDateTime::TransitionResolution::LegacyBehavior);
%End
%If (- Qt_6_7_0)
    void setDate(QDate date);
%End
%If (Qt_6_7_0 -)
    void setTime(QTime time, QDateTime::TransitionResolution resolve = QDateTime::TransitionResolution::LegacyBehavior);
%End
%If (- Qt_6_7_0)
    void setTime(QTime time);
%End
%If (Qt_6_5_0 -)
    QTimeZone timeRepresentation() const;
%End
};

QDataStream &operator<<(QDataStream &, QDate) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QDate & /Constrained/) /ReleaseGIL/;
QDataStream &operator<<(QDataStream &, QTime) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QTime & /Constrained/) /ReleaseGIL/;
QDataStream &operator<<(QDataStream &, const QDateTime &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QDateTime & /Constrained/) /ReleaseGIL/;
%If (Qt_6_7_0 -)
bool operator==(const QDate &lhs, const QDate &rhs);
%End
%If (- Qt_6_7_0)
bool operator==(QDate lhs, QDate rhs);
%End
%If (Qt_6_7_0 -)
bool operator==(const QTime &lhs, const QTime &rhs);
%End
%If (- Qt_6_7_0)
bool operator==(QTime lhs, QTime rhs);
%End
bool operator==(const QDateTime &lhs, const QDateTime &rhs);
%If (Qt_6_7_0 -)
bool operator!=(const QDate &lhs, const QDate &rhs);
%End
%If (- Qt_6_7_0)
bool operator!=(QDate lhs, QDate rhs);
%End
%If (Qt_6_7_0 -)
bool operator!=(const QTime &lhs, const QTime &rhs);
%End
%If (- Qt_6_7_0)
bool operator!=(QTime lhs, QTime rhs);
%End
bool operator!=(const QDateTime &lhs, const QDateTime &rhs);
%If (Qt_6_7_0 -)
bool operator<(const QDate &lhs, const QDate &rhs);
%End
%If (- Qt_6_7_0)
bool operator<(QDate lhs, QDate rhs);
%End
%If (Qt_6_7_0 -)
bool operator<(const QTime &lhs, const QTime &rhs);
%End
%If (- Qt_6_7_0)
bool operator<(QTime lhs, QTime rhs);
%End
bool operator<(const QDateTime &lhs, const QDateTime &rhs);
%If (Qt_6_7_0 -)
bool operator<=(const QDate &lhs, const QDate &rhs);
%End
%If (- Qt_6_7_0)
bool operator<=(QDate lhs, QDate rhs);
%End
%If (Qt_6_7_0 -)
bool operator<=(const QTime &lhs, const QTime &rhs);
%End
%If (- Qt_6_7_0)
bool operator<=(QTime lhs, QTime rhs);
%End
bool operator<=(const QDateTime &lhs, const QDateTime &rhs);
%If (Qt_6_7_0 -)
bool operator>(const QDate &lhs, const QDate &rhs);
%End
%If (- Qt_6_7_0)
bool operator>(QDate lhs, QDate rhs);
%End
%If (Qt_6_7_0 -)
bool operator>(const QTime &lhs, const QTime &rhs);
%End
%If (- Qt_6_7_0)
bool operator>(QTime lhs, QTime rhs);
%End
bool operator>(const QDateTime &lhs, const QDateTime &rhs);
%If (Qt_6_7_0 -)
bool operator>=(const QDate &lhs, const QDate &rhs);
%End
%If (- Qt_6_7_0)
bool operator>=(QDate lhs, QDate rhs);
%End
%If (Qt_6_7_0 -)
bool operator>=(const QTime &lhs, const QTime &rhs);
%End
%If (- Qt_6_7_0)
bool operator>=(QTime lhs, QTime rhs);
%End
bool operator>=(const QDateTime &lhs, const QDateTime &rhs);
