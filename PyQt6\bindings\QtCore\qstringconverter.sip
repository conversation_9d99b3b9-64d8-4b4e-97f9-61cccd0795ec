// qstringconverter.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (- Qt_6_4_0)

class QStringConverterBase
{
%TypeHeaderCode
#include <qstringconverter.h>
%End

public:
    enum class Flag /BaseType=Flag/
    {
        Default,
        Stateless,
        ConvertInvalidToNull,
        WriteBom,
        ConvertInitialBom,
    };

    typedef QFlags<QStringConverterBase::Flag> Flags;
};

%End
%If (- Qt_6_4_0)

class QStringConverter : public QStringConverterBase
{
%TypeHeaderCode
#include <qstringconverter.h>
%End

public:
    enum Encoding
    {
        Utf8,
        Utf16,
        Utf16LE,
        Utf16BE,
        Utf32,
        Utf32LE,
        Utf32BE,
        Latin1,
        System,
    };

protected:
    QStringConverter();
    QStringConverter(QStringConverter::Encoding encoding, QStringConverterBase::Flags f);
    QStringConverter(const char *name, QStringConverterBase::Flags f);

public:
    bool isValid() const;
    void resetState();
    bool hasError() const;
    const char *name() const;

private:
    QStringConverter(const QStringConverter &);
};

%End

class QStringEncoder : public QStringConverter
{
%TypeHeaderCode
#include <qstringconverter.h>
%End

public:
    QStringEncoder();
    QStringEncoder(QStringConverter::Encoding encoding, QStringConverterBase::Flags flags = QStringConverterBase::Flag::Default);
    QStringEncoder(const char *name, QStringConverterBase::Flags flags = QStringConverterBase::Flag::Default);
    QByteArray operator()(QStringView in) [QStringEncoder::DecodedData<QStringView> (QStringView in)];
    QByteArray encode(QStringView in) [QStringEncoder::DecodedData<QStringView> (QStringView in)];
};

class QStringDecoder : public QStringConverter
{
%TypeHeaderCode
#include <qstringconverter.h>
%End

public:
    QStringDecoder(QStringConverter::Encoding encoding, QStringConverterBase::Flags flags = QStringConverterBase::Flag::Default);
    QStringDecoder();
    QStringDecoder(const char *name, QStringConverterBase::Flags flags = QStringConverterBase::Flag::Default);
    QString operator()(QByteArrayView ba) [QStringDecoder::EncodedData<QByteArrayView> (QByteArrayView ba)];
    QString decode(QByteArrayView ba) [QStringDecoder::EncodedData<QByteArrayView> (QByteArrayView ba)];
%If (Qt_6_4_0 -)
    static QStringDecoder decoderForHtml(QByteArrayView data);
%End
};
