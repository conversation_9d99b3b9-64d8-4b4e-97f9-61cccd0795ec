// qrunnable.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QRunnable /Supertype=PyQt6.sip.wrapper/
{
%TypeHeaderCode
#include <qrunnable.h>
%End

public:
    QRunnable();
    virtual ~QRunnable();
    virtual void run() = 0 /NewThread/;
    bool autoDelete() const;
    void setAutoDelete(bool _autoDelete);
    static QRunnable *create(SIP_PYCALLABLE functionToRun /KeepReference,TypeHint="Callable[[], None]"/) /Factory/;
%MethodCode
        sipRes = QRunnable::create([a0]() {
            SIP_BLOCK_THREADS
        
            PyObject *res;
        
            res = PyObject_CallObject(a0, NULL);
        
            if (res)
                Py_DECREF(res);
            else
                pyqt6_err_print();
        
            SIP_UNBLOCK_THREADS
        });
%End

private:
    QRunnable(const QRunnable &);
};
