# -*- coding: utf-8 -*-
"""
安静模式打包脚本 - 减少终端闪烁
专门用于已经完成Cython编译后的最终打包
"""

import os
import sys
import subprocess
import time

def log(message):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def create_quiet_build():
    """安静模式创建最终exe"""
    log("🔧 安静模式创建最终exe")
    log("减少输出信息，避免终端闪烁")
    log("-" * 40)
    
    # 检查Cython编译结果
    if not os.path.exists("cython_compiled"):
        log("❌ 未找到cython_compiled目录，请先运行Cython编译")
        return False
    
    # 构建Nuitka命令（安静模式）
    nuitka_cmd = [
        sys.executable, "-m", "nuitka",
        "--standalone",
        "--onefile",
        "--onefile-tempdir-spec=\\syiming\\cython_{PID}_{TIME}",
        "--windows-console-mode=disable",
        "--enable-plugin=pyqt6",
        "--assume-yes-for-downloads",
        "--quiet",  # 安静模式，减少输出
        "--remove-output",
        "--output-filename=yinming.exe",
    ]
    
    # 添加图标
    if os.path.exists("ico/icon.ico"):
        nuitka_cmd.append("--windows-icon-from-ico=ico/icon.ico")
        log("✅ 添加图标文件")
    
    # 添加所有Cython编译模块
    cython_files = [f for f in os.listdir("cython_compiled") if f.endswith('.pyd')]
    for pyd_file in cython_files:
        compiled_path = os.path.join("cython_compiled", pyd_file)
        nuitka_cmd.append(f"--include-data-file={compiled_path}={pyd_file}")
        log(f"包含Cython模块: {pyd_file}")
    
    # 添加映射文件
    mapping_file = "cython_compiled/cython_mapping.json"
    if os.path.exists(mapping_file):
        nuitka_cmd.append(f"--include-data-file={mapping_file}=cython_mapping.json")
        log("包含Cython映射文件")
    
    # 添加ADBTools
    if os.path.exists("ADBTools"):
        adb_files = os.listdir("ADBTools")
        for file in adb_files:
            file_path = os.path.join("ADBTools", file)
            if os.path.isfile(file_path):
                nuitka_cmd.append(f"--include-data-file={file_path}=ADBTools/{file}")
        log(f"✅ 包含ADBTools目录 ({len(adb_files)} 个文件)")
    
    # 添加其他资源
    for dir_name in ["ico", "tup"]:
        if os.path.exists(dir_name):
            nuitka_cmd.append(f"--include-data-dir={dir_name}={dir_name}")
            log(f"包含目录: {dir_name}")
    
    # 添加主脚本
    nuitka_cmd.append("kx7m.py")
    
    log("\n🚀 开始安静模式编译...")
    log("⏳ 编译过程中终端不会闪烁，请耐心等待...")
    
    try:
        # 使用安静模式运行，减少输出
        result = subprocess.run(nuitka_cmd, timeout=1800, capture_output=True, text=True)
        
        if result.returncode == 0:
            output_file = "yinming.exe"
            if os.path.exists(output_file):
                size = os.path.getsize(output_file)
                log(f"\n🎉 安静模式编译成功!")
                log(f"📁 输出文件: {output_file}")
                log(f"📦 文件大小: {size:,} 字节 ({size/1024/1024:.1f} MB)")
                return True
            else:
                log("❌ 编译完成但未找到输出文件")
        else:
            log("❌ 编译失败")
            if result.stderr:
                log(f"错误信息: {result.stderr[:500]}")
        
        return False
        
    except subprocess.TimeoutExpired:
        log("❌ 编译超时")
        return False
    except Exception as e:
        log(f"❌ 编译异常: {e}")
        return False

def main():
    """主函数"""
    log("🔇 安静模式打包工具")
    log("避免终端闪烁，适合已完成Cython编译的情况")
    log("=" * 50)
    
    if create_quiet_build():
        log("\n🎉 安静模式打包完成!")
        log("📁 可执行文件: yinming.exe")
        log("🔒 包含完整的Cython分层保护")
    else:
        log("\n❌ 安静模式打包失败")

if __name__ == "__main__":
    main()
