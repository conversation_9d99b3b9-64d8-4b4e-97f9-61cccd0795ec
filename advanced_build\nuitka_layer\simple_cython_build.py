# -*- coding: utf-8 -*-
"""
简化Cython编译脚本
专门用于编译核心算法模块
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

# 核心算法模块（必须全部使用Cython编译）
CORE_MODULES = [
    "coloros.py",             # ColorOS 解锁核心算法
    "coloros15.py",           # ColorOS 15核心算法
    "utils.py",               # 工具类核心算法
    "fastboodt.py",          # Fastboot工具核心
    "zhidinyishuaxie.py",     # 自定义刷写核心
    "payload_extractor.py",   # 解包工具核心
    "custom_messagebox.py",   # 自定义消息框
    "genduodakhd.py",         # 根多大核心
    "font_extractor.py",      # 字体提取器
]

def create_cython_setup():
    """创建Cython setup.py"""
    print("🔧 创建Cython setup.py...")
    
    setup_content = f'''# -*- coding: utf-8 -*-
import os
import sys
from setuptools import setup, Extension
from Cython.Build import cythonize
import numpy

# 核心模块列表
CORE_MODULES = {CORE_MODULES}

# 创建扩展模块
extensions = []
for module_file in CORE_MODULES:
    if os.path.exists(module_file):
        module_name = module_file.replace('.py', '_cython')
        ext = Extension(
            module_name,
            [module_file],
            include_dirs=[numpy.get_include()],
            define_macros=[('NPY_NO_DEPRECATED_API', 'NPY_1_7_API_VERSION')],
            extra_compile_args=['/O2'] if sys.platform == 'win32' else ['-O3'],
            language='c++'
        )
        extensions.append(ext)
        print(f"添加模块: {{module_name}} <- {{module_file}}")

# 编译选项
compiler_directives = {{
    'language_level': 3,
    'boundscheck': False,
    'wraparound': False,
    'initializedcheck': False,
    'cdivision': True,
    'embedsignature': False,
}}

setup(
    name="CoreModules",
    ext_modules=cythonize(
        extensions,
        compiler_directives=compiler_directives,
        build_dir="build",
        annotate=False,
        nthreads=4
    ),
    zip_safe=False,
)
'''
    
    with open("setup.py", "w", encoding="utf-8") as f:
        f.write(setup_content)
    
    print("✅ setup.py 已创建")

def compile_cython():
    """编译Cython模块"""
    print("🚀 开始Cython编译...")
    
    # 检查可用模块
    available_modules = []
    for module in CORE_MODULES:
        if os.path.exists(module):
            size = os.path.getsize(module)
            available_modules.append(module)
            print(f"✅ 找到: {module} ({size:,} 字节)")
        else:
            print(f"❌ 缺失: {module}")
    
    if not available_modules:
        print("❌ 没有可用的核心模块")
        return False
    
    # 创建setup.py
    create_cython_setup()
    
    # 执行编译
    try:
        cmd = [sys.executable, "setup.py", "build_ext", "--inplace"]
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            # 检查编译结果
            compiled_files = [f for f in os.listdir('.') if f.endswith(('.pyd', '.so'))]
            if compiled_files:
                print(f"✅ 编译成功，生成 {len(compiled_files)} 个文件:")
                for cf in compiled_files:
                    size = os.path.getsize(cf)
                    print(f"  📦 {cf} ({size:,} 字节)")
                return True
            else:
                print("⚠️ 编译完成但未生成文件")
        else:
            print(f"❌ 编译失败:")
            if result.stderr:
                print(f"错误: {result.stderr}")
            if result.stdout:
                print(f"输出: {result.stdout}")
        
        return False
        
    except Exception as e:
        print(f"❌ 编译异常: {e}")
        return False

def create_nuitka_build():
    """创建包含Cython模块的Nuitka构建"""
    print("🔧 创建Nuitka构建...")
    
    # 检查编译结果
    compiled_files = [f for f in os.listdir('.') if f.endswith(('.pyd', '.so'))]
    if not compiled_files:
        print("❌ 没有找到Cython编译文件")
        return False
    
    # 构建Nuitka命令
    nuitka_cmd = [
        sys.executable, "-m", "nuitka",
        "--standalone",
        "--onefile",
        "--onefile-tempdir-spec={TEMP}\\syiming\\cython_{PID}_{TIME}",
        "--windows-console-mode=disable",
        "--enable-plugin=pyqt6",
        "--assume-yes-for-downloads",
        "--show-progress",
        "--show-memory",
        "--remove-output",
        "--output-filename=益民欧加真固件刷写工具_Cython版.exe",
    ]
    
    # 添加图标
    if os.path.exists("ico/icon.ico"):
        nuitka_cmd.append("--windows-icon-from-ico=ico/icon.ico")
    
    # 添加Cython编译文件
    for cf in compiled_files:
        nuitka_cmd.append(f"--include-data-file={cf}={cf}")
        print(f"包含Cython模块: {cf}")
    
    # 添加ADBTools
    if os.path.exists("ADBTools"):
        for file in os.listdir("ADBTools"):
            file_path = os.path.join("ADBTools", file)
            if os.path.isfile(file_path):
                nuitka_cmd.append(f"--include-data-file={file_path}=ADBTools/{file}")
        print("包含ADBTools目录")
    
    # 添加其他资源
    for dir_name in ["ico", "tup"]:
        if os.path.exists(dir_name):
            nuitka_cmd.append(f"--include-data-dir={dir_name}={dir_name}")
            print(f"包含目录: {dir_name}")
    
    # 添加主脚本
    nuitka_cmd.append("main.py")
    
    print("🚀 开始Nuitka编译...")
    try:
        result = subprocess.run(nuitka_cmd, timeout=1800)
        
        if result.returncode == 0:
            output_file = "益民欧加真固件刷写工具_Cython版.exe"
            if os.path.exists(output_file):
                size = os.path.getsize(output_file)
                print(f"✅ Nuitka编译成功: {output_file} ({size:,} 字节)")
                return True
        
        print("❌ Nuitka编译失败")
        return False
        
    except Exception as e:
        print(f"❌ Nuitka编译异常: {e}")
        return False

def main():
    """主函数"""
    print("🔒 简化Cython编译工具")
    print("=" * 50)
    
    # 步骤1: 编译Cython模块
    if not compile_cython():
        print("❌ Cython编译失败")
        return False
    
    # 步骤2: 询问是否继续Nuitka编译
    choice = input("\n是否继续Nuitka编译？(y/n): ").lower()
    if choice == 'y':
        if create_nuitka_build():
            print("🎉 完整构建成功！")
        else:
            print("❌ Nuitka编译失败")
    
    print("完成！")
    return True

if __name__ == "__main__":
    main()
