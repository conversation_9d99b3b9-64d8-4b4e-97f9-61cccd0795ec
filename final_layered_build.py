# -*- coding: utf-8 -*-
"""
最终分层保护构建脚本
===================

分层保护方案：
- 外层：Nuitka打包 + VMProtect加壳（自签名证书）
- 内层：Cython编译核心算法（必须全部使用）

确保所有核心算法都通过Cython编译为C扩展
"""

import os
import sys
import shutil
import subprocess
import time
import json
from pathlib import Path

# 核心算法模块（必须全部编译）
CORE_MODULES = [
    "coloros.py",             # ColorOS 解锁核心算法
    "coloros15.py",           # ColorOS 15核心算法
    "utils.py",               # 工具类核心算法
    "fastboodt.py",          # Fastboot工具核心
    "zhidinyishuaxie.py",     # 自定义刷写核心
    "payload_extractor.py",   # 解包工具核心
    "custom_messagebox.py",   # 自定义消息框
    "genduodakhd.py",         # 根多大核心
    "font_extractor.py",      # 字体提取器
]

def log(message, level="INFO"):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    if level == "SUCCESS":
        print(f"[{timestamp}] ✅ {message}")
    elif level == "ERROR":
        print(f"[{timestamp}] ❌ {message}")
    elif level == "WARNING":
        print(f"[{timestamp}] ⚠️ {message}")
    elif level == "STEP":
        print(f"[{timestamp}] 🔧 {message}")
    else:
        print(f"[{timestamp}] {message}")

def check_environment():
    """检查构建环境"""
    log("检查构建环境", "STEP")
    
    # 检查Python版本
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    log(f"Python版本: {python_version}")
    
    # 检查必需的包
    required_packages = ['cython', 'numpy', 'setuptools', 'nuitka', 'PyQt6']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            log(f"{package} - 已安装", "SUCCESS")
        except ImportError:
            missing_packages.append(package)
            log(f"{package} - 未安装", "ERROR")
    
    if missing_packages:
        log("安装缺失的包...", "STEP")
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package, "--quiet"])
                log(f"{package} 安装成功", "SUCCESS")
            except subprocess.CalledProcessError:
                log(f"{package} 安装失败", "ERROR")
                return False
    
    # 检查核心模块
    missing_modules = []
    for module in CORE_MODULES:
        if os.path.exists(module):
            size = os.path.getsize(module)
            log(f"找到核心模块: {module} ({size:,} 字节)")
        else:
            missing_modules.append(module)
            log(f"缺少核心模块: {module}", "ERROR")
    
    if missing_modules:
        log(f"缺少 {len(missing_modules)} 个核心模块", "ERROR")
        return False
    
    log("环境检查完成", "SUCCESS")
    return True

def compile_cython_modules():
    """编译Cython核心模块"""
    log("开始Cython编译核心算法", "STEP")
    
    # 准备编译目录
    build_dir = "final_cython_build"
    if os.path.exists(build_dir):
        shutil.rmtree(build_dir)
    os.makedirs(build_dir)
    
    compiled_modules = {}
    failed_modules = []
    
    # 逐个编译模块
    for i, module in enumerate(CORE_MODULES, 1):
        log(f"[{i}/{len(CORE_MODULES)}] 编译模块: {module}", "STEP")
        
        # 复制模块到编译目录
        src_path = module
        dst_path = os.path.join(build_dir, module)
        shutil.copy2(src_path, dst_path)
        
        # 创建setup.py
        module_name = module.replace('.py', '_cython')
        setup_content = f'''
from setuptools import setup
from Cython.Build import cythonize

setup(
    ext_modules = cythonize("{module}", language_level=3, 
                           compiler_directives={{"boundscheck": False, "wraparound": False}}),
    zip_safe=False,
)
'''
        
        setup_path = os.path.join(build_dir, f"setup_{module_name}.py")
        with open(setup_path, "w", encoding="utf-8") as f:
            f.write(setup_content)
        
        # 执行编译
        original_dir = os.getcwd()
        try:
            os.chdir(build_dir)
            
            cmd = [sys.executable, f"setup_{module_name}.py", "build_ext", "--inplace"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                # 查找生成的.pyd文件
                pyd_files = [f for f in os.listdir('.') if f.startswith(module.replace('.py', '')) and f.endswith('.pyd')]
                
                if pyd_files:
                    pyd_file = pyd_files[0]
                    size = os.path.getsize(pyd_file)
                    compiled_modules[module] = pyd_file
                    log(f"编译成功: {module} -> {pyd_file} ({size:,} 字节)", "SUCCESS")
                    
                    # 清理临时文件
                    c_files = [f for f in os.listdir('.') if f.endswith('.c')]
                    for c_file in c_files:
                        try:
                            os.remove(c_file)
                        except:
                            pass
                else:
                    failed_modules.append(module)
                    log(f"编译失败: {module} (未生成.pyd文件)", "ERROR")
            else:
                failed_modules.append(module)
                log(f"编译失败: {module}", "ERROR")
                if result.stderr:
                    log(f"错误信息: {result.stderr[:200]}", "ERROR")
            
            os.chdir(original_dir)
            
        except Exception as e:
            os.chdir(original_dir)
            failed_modules.append(module)
            log(f"编译异常: {module} - {e}", "ERROR")
        
        # 清理setup文件
        if os.path.exists(setup_path):
            try:
                os.remove(setup_path)
            except:
                pass
    
    # 生成编译报告
    success_rate = len(compiled_modules) / len(CORE_MODULES) * 100
    log(f"Cython编译完成", "STEP")
    log(f"总模块数: {len(CORE_MODULES)}")
    log(f"成功编译: {len(compiled_modules)}")
    log(f"编译失败: {len(failed_modules)}")
    log(f"成功率: {success_rate:.1f}%")
    
    if len(compiled_modules) == len(CORE_MODULES):
        log("所有核心算法编译成功！", "SUCCESS")
    elif len(compiled_modules) >= len(CORE_MODULES) * 0.8:
        log(f"大部分核心算法编译成功 ({success_rate:.1f}%)", "WARNING")
    else:
        log("核心算法编译成功率过低", "ERROR")
        return False, None, {}
    
    # 保存编译映射
    if compiled_modules:
        mapping_data = {
            "modules": compiled_modules,
            "total_modules": len(compiled_modules),
            "failed_modules": failed_modules,
            "success_rate": f"{success_rate:.1f}%",
            "compile_time": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        mapping_file = os.path.join(build_dir, "cython_mapping.json")
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(mapping_data, f, indent=2, ensure_ascii=False)
        
        log("Cython编译映射已保存", "SUCCESS")
    
    return True, build_dir, compiled_modules

def create_final_executable(build_dir, compiled_modules):
    """创建最终的分层保护可执行文件"""
    log("创建分层保护可执行文件", "STEP")
    
    if not compiled_modules:
        log("没有可用的Cython模块", "ERROR")
        return False
    
    # 构建Nuitka命令
    nuitka_cmd = [
        sys.executable, "-m", "nuitka",
        "--standalone",
        "--onefile",
        "--onefile-tempdir-spec={TEMP}\\syiming\\layered_{PID}_{TIME}",
        "--windows-console-mode=disable",
        "--enable-plugin=pyqt6",
        "--assume-yes-for-downloads",
        "--show-progress",
        "--show-memory",
        "--remove-output",
        "--output-filename=益民欧加真固件刷写工具_分层保护终极版.exe",
    ]
    
    # 添加图标
    if os.path.exists("ico/icon.ico"):
        nuitka_cmd.append("--windows-icon-from-ico=ico/icon.ico")
        log("添加图标文件", "SUCCESS")
    
    # 添加所有Cython编译模块
    for original, compiled in compiled_modules.items():
        compiled_path = os.path.join(build_dir, compiled)
        if os.path.exists(compiled_path):
            nuitka_cmd.append(f"--include-data-file={compiled_path}={compiled}")
            log(f"包含Cython模块: {compiled}")
    
    # 添加映射文件
    mapping_file = os.path.join(build_dir, "cython_mapping.json")
    if os.path.exists(mapping_file):
        nuitka_cmd.append(f"--include-data-file={mapping_file}=cython_mapping.json")
        log("包含Cython映射文件")
    
    # 添加ADBTools（逐个文件确保包含）
    if os.path.exists("ADBTools"):
        adb_files = [f for f in os.listdir("ADBTools") if os.path.isfile(os.path.join("ADBTools", f))]
        for file in adb_files:
            file_path = os.path.join("ADBTools", file)
            nuitka_cmd.append(f"--include-data-file={file_path}=ADBTools/{file}")
        log(f"包含ADBTools目录 ({len(adb_files)} 个文件)", "SUCCESS")
    
    # 添加其他资源
    for dir_name in ["ico", "tup"]:
        if os.path.exists(dir_name):
            nuitka_cmd.append(f"--include-data-dir={dir_name}={dir_name}")
            log(f"包含目录: {dir_name}")
    
    # 添加主脚本
    nuitka_cmd.append("main.py")
    
    log("开始Nuitka编译...", "STEP")
    try:
        result = subprocess.run(nuitka_cmd, timeout=1800)
        
        if result.returncode == 0:
            output_file = "益民欧加真固件刷写工具_分层保护终极版.exe"
            if os.path.exists(output_file):
                size = os.path.getsize(output_file)
                log(f"分层保护版构建成功!", "SUCCESS")
                log(f"输出文件: {output_file}")
                log(f"文件大小: {size:,} 字节 ({size/1024/1024:.1f} MB)")
                
                # 移动到最终输出目录
                final_dir = "advanced_build/final_output"
                os.makedirs(final_dir, exist_ok=True)
                final_path = os.path.join(final_dir, output_file)
                shutil.move(output_file, final_path)
                log(f"已移动到: {final_path}", "SUCCESS")
                
                return True
        
        log("Nuitka编译失败", "ERROR")
        return False
        
    except Exception as e:
        log(f"Nuitka编译异常: {e}", "ERROR")
        return False

def main():
    """主函数"""
    log("分层保护构建工具 - 终极版", "STEP")
    log("=" * 60)
    log("保护方案:")
    log("  外层: Nuitka编译 + VMProtect加壳 + 自签名证书")
    log("  内层: Cython编译核心算法（必须全部使用）")
    log("=" * 60)
    
    start_time = time.time()
    
    # 步骤1: 环境检查
    if not check_environment():
        log("环境检查失败", "ERROR")
        return False
    
    # 步骤2: Cython编译核心算法
    success, build_dir, compiled_modules = compile_cython_modules()
    if not success:
        log("Cython编译失败 - 这是不可接受的！", "ERROR")
        return False
    
    # 步骤3: 创建最终可执行文件
    if create_final_executable(build_dir, compiled_modules):
        build_time = time.time() - start_time
        minutes = int(build_time // 60)
        seconds = int(build_time % 60)
        
        log("=" * 60)
        log("分层保护构建完成！", "SUCCESS")
        log(f"总用时: {minutes}分{seconds}秒")
        log("保护状态:")
        log(f"  内层: {len(compiled_modules)}/{len(CORE_MODULES)} 个核心算法Cython编译")
        log("  外层: Nuitka编译 + ADBTools完整包含")
        log("  准备: VMProtect加壳配置")
        log("输出目录: advanced_build/final_output/")
        log("=" * 60)
        return True
    else:
        log("最终构建失败", "ERROR")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
