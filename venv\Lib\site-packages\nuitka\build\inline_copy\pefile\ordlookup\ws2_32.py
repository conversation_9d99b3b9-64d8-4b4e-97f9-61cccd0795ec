
ord_names = {
    1: b'accept',
    2: b'bind',
    3: b'closesocket',
    4: b'connect',
    5: b'getpeername',
    6: b'getsockname',
    7: b'getsockopt',
    8: b'htonl',
    9: b'htons',
    10: b'ioctlsocket',
    11: b'inet_addr',
    12: b'inet_ntoa',
    13: b'listen',
    14: b'ntohl',
    15: b'ntohs',
    16: b'recv',
    17: b'recvfrom',
    18: b'select',
    19: b'send',
    20: b'sendto',
    21: b'setsockopt',
    22: b'shutdown',
    23: b'socket',
    24: b'GetAddrInfoW',
    25: b'GetNameInfoW',
    26: b'WSApSetPostRoutine',
    27: b'FreeAddrInfoW',
    28: b'WPUCompleteOverlappedRequest',
    29: b'WSAAccept',
    30: b'WSAAddressToStringA',
    31: b'WSAAddressToStringW',
    32: b'WSACloseEvent',
    33: b'WSAConnect',
    34: b'WSACreateEvent',
    35: b'WSADuplicateSocketA',
    36: b'WSADuplicateSocketW',
    37: b'WSAEnumNameSpaceProvidersA',
    38: b'WSAEnumNameSpaceProvidersW',
    39: b'WSAEnumNetworkEvents',
    40: b'WSAEnumProtocolsA',
    41: b'WSAEnumProtocolsW',
    42: b'WSAEventSelect',
    43: b'WSAGetOverlappedResult',
    44: b'WSAGetQOSByName',
    45: b'WSAGetServiceClassInfoA',
    46: b'WSAGetServiceClassInfoW',
    47: b'WSAGetServiceClassNameByClassIdA',
    48: b'WSAGetServiceClassNameByClassIdW',
    49: b'WSAHtonl',
    50: b'WSAHtons',
    51: b'gethostbyaddr',
    52: b'gethostbyname',
    53: b'getprotobyname',
    54: b'getprotobynumber',
    55: b'getservbyname',
    56: b'getservbyport',
    57: b'gethostname',
    58: b'WSAInstallServiceClassA',
    59: b'WSAInstallServiceClassW',
    60: b'WSAIoctl',
    61: b'WSAJoinLeaf',
    62: b'WSALookupServiceBeginA',
    63: b'WSALookupServiceBeginW',
    64: b'WSALookupServiceEnd',
    65: b'WSALookupServiceNextA',
    66: b'WSALookupServiceNextW',
    67: b'WSANSPIoctl',
    68: b'WSANtohl',
    69: b'WSANtohs',
    70: b'WSAProviderConfigChange',
    71: b'WSARecv',
    72: b'WSARecvDisconnect',
    73: b'WSARecvFrom',
    74: b'WSARemoveServiceClass',
    75: b'WSAResetEvent',
    76: b'WSASend',
    77: b'WSASendDisconnect',
    78: b'WSASendTo',
    79: b'WSASetEvent',
    80: b'WSASetServiceA',
    81: b'WSASetServiceW',
    82: b'WSASocketA',
    83: b'WSASocketW',
    84: b'WSAStringToAddressA',
    85: b'WSAStringToAddressW',
    86: b'WSAWaitForMultipleEvents',
    87: b'WSCDeinstallProvider',
    88: b'WSCEnableNSProvider',
    89: b'WSCEnumProtocols',
    90: b'WSCGetProviderPath',
    91: b'WSCInstallNameSpace',
    92: b'WSCInstallProvider',
    93: b'WSCUnInstallNameSpace',
    94: b'WSCUpdateProvider',
    95: b'WSCWriteNameSpaceOrder',
    96: b'WSCWriteProviderOrder',
    97: b'freeaddrinfo',
    98: b'getaddrinfo',
    99: b'getnameinfo',
    101: b'WSAAsyncSelect',
    102: b'WSAAsyncGetHostByAddr',
    103: b'WSAAsyncGetHostByName',
    104: b'WSAAsyncGetProtoByNumber',
    105: b'WSAAsyncGetProtoByName',
    106: b'WSAAsyncGetServByPort',
    107: b'WSAAsyncGetServByName',
    108: b'WSACancelAsyncRequest',
    109: b'WSASetBlockingHook',
    110: b'WSAUnhookBlockingHook',
    111: b'WSAGetLastError',
    112: b'WSASetLastError',
    113: b'WSACancelBlockingCall',
    114: b'WSAIsBlocking',
    115: b'WSAStartup',
    116: b'WSACleanup',
    151: b'__WSAFDIsSet',
    500: b'WEP',
}
