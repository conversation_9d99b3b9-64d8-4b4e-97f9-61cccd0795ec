// qmediaplayer.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QMediaPlayer : public QObject
{
%TypeHeaderCode
#include <qmediaplayer.h>
%End

public:
    enum PlaybackState
    {
        StoppedState,
        PlayingState,
        PausedState,
    };

    enum MediaStatus
    {
        NoMedia,
        LoadingMedia,
        LoadedMedia,
        StalledMedia,
        BufferingMedia,
        BufferedMedia,
        EndOfMedia,
        InvalidMedia,
    };

    enum Error
    {
        NoError,
        ResourceError,
        FormatError,
        NetworkError,
        AccessDeniedError,
    };

    explicit QMediaPlayer(QObject *parent /TransferThis/ = 0);
    virtual ~QMediaPlayer();
    QList<QMediaMetaData> audioTracks() const;
    QList<QMediaMetaData> videoTracks() const;
    QList<QMediaMetaData> subtitleTracks() const;
    int activeAudioTrack() const;
    int activeVideoTrack() const;
    int activeSubtitleTrack() const;
    void setActiveAudioTrack(int index);
    void setActiveVideoTrack(int index);
    void setActiveSubtitleTrack(int index);
    void setAudioOutput(QAudioOutput *output);
    QAudioOutput *audioOutput() const;
    void setVideoOutput(QObject *);
    QObject *videoOutput() const;
    void setVideoSink(QVideoSink *sink);
    QVideoSink *videoSink() const;
    QUrl source() const;
    const QIODevice *sourceDevice() const;
    QMediaPlayer::PlaybackState playbackState() const;
    QMediaPlayer::MediaStatus mediaStatus() const;
    qint64 duration() const;
    qint64 position() const;
    bool hasAudio() const;
    bool hasVideo() const;
    float bufferProgress() const;
    QMediaTimeRange bufferedTimeRange() const;
    bool isSeekable() const;
    qreal playbackRate() const;
    QMediaPlayer::Error error() const;
    QString errorString() const;
    bool isAvailable() const;
    QMediaMetaData metaData() const;

public slots:
    void play();
    void pause();
    void stop();
    void setPosition(qint64 position);
    void setPlaybackRate(qreal rate);
    void setSource(const QUrl &source) /ReleaseGIL/;
    void setSourceDevice(QIODevice *device, const QUrl &sourceUrl = QUrl()) /ReleaseGIL/;

signals:
    void sourceChanged(const QUrl &media);
    void playbackStateChanged(QMediaPlayer::PlaybackState newState);
    void mediaStatusChanged(QMediaPlayer::MediaStatus status);
    void durationChanged(qint64 duration);
    void positionChanged(qint64 position);
    void hasAudioChanged(bool available);
    void hasVideoChanged(bool videoAvailable);
    void bufferProgressChanged(float progress);
    void seekableChanged(bool seekable);
    void playbackRateChanged(qreal rate);
    void metaDataChanged();
    void videoOutputChanged();
    void audioOutputChanged();
    void tracksChanged();
    void activeTracksChanged();
    void errorChanged();
    void errorOccurred(QMediaPlayer::Error error, const QString &errorString);

public:
    enum Loops /BaseType=IntEnum/
    {
        Infinite,
        Once,
    };

    int loops() const;
    void setLoops(int loops);

signals:
    void loopsChanged();

public:
%If (Qt_6_5_0 -)
    bool isPlaying() const;
%End

signals:
%If (Qt_6_5_0 -)
    void playingChanged(bool playing);
%End
};

%End
