# Copyright (c) 2023 Riverbank Computing Limited.
# Copyright (c) 2006 <PERSON><PERSON>.
# All right reserved.
#
# This file is part of PyQt.
#
# You may use this file under the terms of the GPL v3 or the revised BSD
# license as follows:
#
# "Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are
# met:
#   * Redistributions of source code must retain the above copyright
#     notice, this list of conditions and the following disclaimer.
#   * Redistributions in binary form must reproduce the above copyright
#     notice, this list of conditions and the following disclaimer in
#     the documentation and/or other materials provided with the
#     distribution.
#   * Neither the name of the Riverbank Computing Limited nor the names
#     of its contributors may be used to endorse or promote products
#     derived from this software without specific prior written
#     permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
# "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
# LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
# A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
# OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
# SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
# LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
# DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
# THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE."


import sys

from ..properties import Properties
from ..uiparser import UIParser
from . import qtproxies
from .indenter import createCodeIndenter, getIndenter, write_code
from .qobjectcreator import CompilerCreatorPolicy


class UICompiler(UIParser):
    def __init__(self):
        UIParser.__init__(self, qtproxies.QtCore, qtproxies.QtGui,
                qtproxies.QtWidgets, CompilerCreatorPolicy())

    def reset(self):
        qtproxies.i18n_strings = []
        UIParser.reset(self)

    def setContext(self, context):
        qtproxies.i18n_context = context

    def createToplevelWidget(self, classname, widgetname):
        indenter = getIndenter()
        indenter.level = 0

        indenter.write("from PyQt6 import QtCore, QtGui, QtWidgets")
        indenter.write("")

        indenter.write("")
        indenter.write("class Ui_%s(object):" % self.uiname)
        indenter.indent()
        indenter.write("def setupUi(self, %s):" % widgetname)
        indenter.indent()
        w = self.factory.createQtObject(classname, widgetname,
                is_attribute=False, no_instantiation=True)
        w.baseclass = classname
        w.uiclass = "Ui_%s" % self.uiname
        return w

    def setDelayedProps(self):
        write_code("")
        write_code("self.retranslateUi(%s)" % self.toplevelWidget)
        UIParser.setDelayedProps(self)

    def finalize(self):
        indenter = getIndenter()
        indenter.level = 1
        indenter.write("")
        indenter.write("def retranslateUi(self, %s):" % self.toplevelWidget)

        indenter.indent()

        if qtproxies.i18n_strings:
            indenter.write("_translate = QtCore.QCoreApplication.translate")
            for s in qtproxies.i18n_strings:
                indenter.write(s)
        else:
            indenter.write("pass")

        indenter.dedent()
        indenter.dedent()

    def compileUi(self, input_stream, output_stream):
        createCodeIndenter(output_stream)
        w = self.parse(input_stream)

        self.factory._cpolicy._writeOutImports()

        return {"widgetname": str(w),
                "uiclass" : w.uiclass,
                "baseclass" : w.baseclass}
