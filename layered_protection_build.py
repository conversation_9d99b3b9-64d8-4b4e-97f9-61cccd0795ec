# -*- coding: utf-8 -*-
"""
分层保护打包脚本
================

分层保护方案：
- 外层：Nuitka打包 + VMProtect加壳（自签名证书）
- 内层：Cython编译核心算法（必须全部使用）

确保所有核心算法都通过Cython编译为C扩展
"""

import os
import sys
import shutil
import subprocess
import time
import json
from datetime import datetime
from pathlib import Path

# 项目配置
PROJECT_CONFIG = {
    "name": "益民欧加真固件刷写工具",
    "version": "3.0.0",
    "author": "益民工具箱",
    "description": "Android固件刷写工具 - 分层保护版",
    "main_script": "main.py",
    "icon_path": "ico/icon.ico",
    "output_name": "益民欧加真固件刷写工具_分层保护版.exe"
}

# 构建目录配置
BUILD_DIRS = {
    "base": "layered_build",
    "cython": "layered_build/cython_core",
    "nuitka": "layered_build/nuitka_final",
    "output": "layered_build/final_output",
    "temp": "layered_build/temp"
}

# 核心算法模块（必须全部使用Cython编译）
CORE_MODULES = [
    "coloros.py",             # ColorOS 解锁核心算法
    "coloros15.py",           # ColorOS 15核心算法
    "utils.py",               # 工具类核心算法
    "fastboodt.py",          # Fastboot工具核心
    "zhidinyishuaxie.py",     # 自定义刷写核心
    "payload_extractor.py",   # 解包工具核心
    "custom_messagebox.py",   # 自定义消息框
    "genduodakhd.py",         # 根多大核心
    "font_extractor.py",      # 字体提取器
]

# 非核心模块（保持Python格式）
NON_CORE_MODULES = [
    "flash_tool.py",          # UI界面
    "main.py",                # 主程序
    "config.py",              # 配置文件（如果存在）
]

# 必需的资源文件夹
REQUIRED_DIRS = [
    "ADBTools",               # ADB工具集（关键）
    "ico",                    # 图标文件
    "tup",                    # 图片资源
]

class LayeredProtectionBuilder:
    """分层保护构建器"""
    
    def __init__(self):
        self.start_time = time.time()
        self.log_file = os.path.join(BUILD_DIRS["base"], "layered_build.log")
        self.cython_modules = {}
        
    def log(self, message, level="INFO"):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"
        print(log_entry)
        
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry + '\n')
        except:
            pass
    
    def step(self, step_name, status="开始"):
        self.log(f"🔧 {step_name} - {status}", "STEP")
    
    def success(self, message):
        self.log(f"✅ {message}", "SUCCESS")
    
    def error(self, message):
        self.log(f"❌ {message}", "ERROR")
    
    def warning(self, message):
        self.log(f"⚠️ {message}", "WARNING")

    def cleanup_and_prepare(self):
        """清理并准备构建目录"""
        self.step("清理并准备构建目录")
        
        if os.path.exists(BUILD_DIRS["base"]):
            try:
                shutil.rmtree(BUILD_DIRS["base"])
                self.success("构建目录清理完成")
            except Exception as e:
                self.warning(f"清理构建目录失败: {e}")
        
        # 创建所有必需的目录
        for dir_name, dir_path in BUILD_DIRS.items():
            os.makedirs(dir_path, exist_ok=True)
            self.log(f"创建目录: {dir_path}")

    def check_core_modules(self):
        """检查核心模块"""
        self.step("检查核心模块")
        
        missing_modules = []
        existing_modules = []
        
        for module in CORE_MODULES:
            if os.path.exists(module):
                file_size = os.path.getsize(module)
                existing_modules.append(module)
                self.success(f"找到核心模块: {module} ({file_size:,} 字节)")
            else:
                missing_modules.append(module)
                self.warning(f"核心模块不存在: {module}")
        
        if missing_modules:
            self.error(f"缺少 {len(missing_modules)} 个核心模块: {missing_modules}")
            return False
        
        self.success(f"所有 {len(existing_modules)} 个核心模块检查完成")
        return True

    def create_cython_setup(self):
        """创建强制Cython编译配置"""
        self.step("创建强制Cython编译配置")
        
        setup_content = '''# -*- coding: utf-8 -*-
"""
强制Cython编译配置 - 分层保护版
所有核心算法必须编译为C扩展
"""
import os
import sys
from setuptools import setup, Extension
from Cython.Build import cythonize
from Cython.Compiler import Options

# 强制编译选项
Options.docstrings = False
Options.embed_pos_in_docstring = False

# 尝试导入numpy
try:
    import numpy
    include_dirs = [numpy.get_include()]
    define_macros = [('NPY_NO_DEPRECATED_API', 'NPY_1_7_API_VERSION')]
except ImportError:
    print("警告: numpy未安装，使用默认配置")
    include_dirs = []
    define_macros = []

# 核心模块列表（必须全部编译）
CORE_MODULES = {core_modules}

# 创建扩展模块
extensions = []
for module_file in CORE_MODULES:
    if os.path.exists(module_file):
        module_name = module_file.replace('.py', '_cython')
        
        # 强制编译选项
        extra_compile_args = []
        extra_link_args = []
        
        if sys.platform == 'win32':
            extra_compile_args = ['/O2', '/GL', '/DNDEBUG']
            extra_link_args = ['/LTCG']
        else:
            extra_compile_args = ['-O3', '-DNDEBUG', '-ffast-math']
        
        ext = Extension(
            module_name,
            [module_file],
            include_dirs=include_dirs,
            define_macros=define_macros + [('CYTHON_TRACE', '0')],
            extra_compile_args=extra_compile_args,
            extra_link_args=extra_link_args,
            language='c++'
        )
        extensions.append(ext)
        print(f"✅ 强制编译: {{module_name}} <- {{module_file}}")

if not extensions:
    print("❌ 错误: 没有找到任何核心模块进行编译")
    sys.exit(1)

print(f"📦 总计编译 {{len(extensions)}} 个核心模块")

# 强制编译指令
compiler_directives = {{
    'language_level': 3,
    'boundscheck': False,
    'wraparound': False,
    'initializedcheck': False,
    'cdivision': True,
    'embedsignature': False,
    'optimize.use_switch': True,
    'optimize.unpack_method_calls': True,
    'profile': False,
    'linetrace': False,
    'binding': False,
}}

setup(
    name="CoreAlgorithms",
    ext_modules=cythonize(
        extensions,
        compiler_directives=compiler_directives,
        build_dir="cython_build",
        annotate=False,
        nthreads=4,
        force=True,
        quiet=False
    ),
    zip_safe=False,
)
'''.format(core_modules=repr(CORE_MODULES))
        
        setup_path = os.path.join(BUILD_DIRS["cython"], "setup.py")
        with open(setup_path, 'w', encoding='utf-8') as f:
            f.write(setup_content)
        
        self.success(f"强制Cython配置已生成: {setup_path}")
        return True

    def copy_source_files(self):
        """复制源文件到构建目录"""
        self.step("复制源文件到构建目录")
        
        # 复制核心模块到Cython目录
        for module in CORE_MODULES:
            if os.path.exists(module):
                src = module
                dst = os.path.join(BUILD_DIRS["cython"], module)
                shutil.copy2(src, dst)
                self.log(f"复制核心模块: {module}")
        
        # 复制非核心模块到Nuitka目录
        for module in NON_CORE_MODULES:
            if os.path.exists(module):
                src = module
                dst = os.path.join(BUILD_DIRS["nuitka"], module)
                shutil.copy2(src, dst)
                self.log(f"复制非核心模块: {module}")
        
        # 复制资源目录到Nuitka目录
        for dir_name in REQUIRED_DIRS:
            if os.path.exists(dir_name):
                src = dir_name
                dst = os.path.join(BUILD_DIRS["nuitka"], dir_name)
                if os.path.exists(dst):
                    shutil.rmtree(dst)
                shutil.copytree(src, dst)
                file_count = sum([len(files) for _, _, files in os.walk(dst)])
                self.success(f"复制资源目录: {dir_name} ({file_count} 个文件)")
        
        self.success("源文件复制完成")
        return True

    def compile_cython_core(self):
        """强制编译Cython核心算法"""
        self.step("强制编译Cython核心算法")
        
        cython_dir = BUILD_DIRS["cython"]
        original_dir = os.getcwd()
        
        try:
            os.chdir(cython_dir)
            
            # 生成setup.py（在切换目录之前）
            self.create_cython_setup()
            
            # 强制执行Cython编译
            self.log("开始强制Cython编译...")
            cmd = [sys.executable, "setup.py", "build_ext", "--inplace", "--force"]
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=900  # 15分钟超时
            )
            
            if result.returncode == 0:
                # 检查编译结果
                compiled_files = [f for f in os.listdir('.') if f.endswith(('.pyd', '.so'))]
                if len(compiled_files) == len(CORE_MODULES):
                    self.success(f"✅ 所有 {len(compiled_files)} 个核心模块编译成功:")
                    for cf in compiled_files:
                        file_size = os.path.getsize(cf)
                        original_name = cf.replace('_cython.pyd', '.py').replace('_cython.so', '.py')
                        self.cython_modules[original_name] = cf
                        self.log(f"  ✓ {original_name} -> {cf} ({file_size:,} 字节)")
                    
                    # 创建模块映射
                    self.create_cython_mapping()
                    os.chdir(original_dir)
                    return True
                else:
                    self.error(f"编译不完整: 期望 {len(CORE_MODULES)} 个，实际 {len(compiled_files)} 个")
            else:
                self.error(f"Cython编译失败:")
                if result.stderr:
                    self.error(f"错误信息: {result.stderr[:1000]}")
            
            os.chdir(original_dir)
            return False
            
        except Exception as e:
            if 'original_dir' in locals():
                os.chdir(original_dir)
            self.error(f"Cython编译异常: {e}")
            return False

    def create_cython_mapping(self):
        """创建Cython模块映射"""
        mapping_data = {
            "modules": self.cython_modules,
            "build_time": datetime.now().isoformat(),
            "total_modules": len(self.cython_modules)
        }
        
        mapping_file = "cython_mapping.json"
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(mapping_data, f, indent=2, ensure_ascii=False)
        
        self.log(f"Cython模块映射已保存: {mapping_file}")

    def integrate_cython_modules(self):
        """整合Cython编译模块到Nuitka目录"""
        self.step("整合Cython编译模块")
        
        cython_dir = BUILD_DIRS["cython"]
        nuitka_dir = BUILD_DIRS["nuitka"]
        
        # 复制编译后的Cython模块
        for original_name, compiled_name in self.cython_modules.items():
            src = os.path.join(cython_dir, compiled_name)
            dst = os.path.join(nuitka_dir, compiled_name)
            if os.path.exists(src):
                shutil.copy2(src, dst)
                self.success(f"整合Cython模块: {compiled_name}")
        
        # 复制映射文件
        mapping_src = os.path.join(cython_dir, "cython_mapping.json")
        mapping_dst = os.path.join(nuitka_dir, "cython_mapping.json")
        if os.path.exists(mapping_src):
            shutil.copy2(mapping_src, mapping_dst)
            self.log("复制Cython映射文件")
        
        self.success("Cython模块整合完成")
        return True

    def create_module_loader(self):
        """创建Cython模块动态加载器"""
        self.step("创建Cython模块动态加载器")

        loader_content = f'''# -*- coding: utf-8 -*-
"""
Cython模块动态加载器 - 分层保护版
自动加载所有Cython编译的核心算法模块
"""
import os
import sys
import json
import importlib.util

class CythonModuleLoader:
    """Cython模块加载器"""

    def __init__(self):
        self.loaded_modules = {{}}
        self.mapping = {{}}
        self.load_mapping()

    def load_mapping(self):
        """加载模块映射"""
        mapping_file = self.get_resource_path("cython_mapping.json")
        if os.path.exists(mapping_file):
            try:
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.mapping = data.get("modules", {{}})
                    print(f"✅ 加载Cython映射: {{len(self.mapping)}} 个模块")
            except Exception as e:
                print(f"⚠️ 加载映射失败: {{e}}")

    def get_resource_path(self, relative_path):
        """获取资源文件路径"""
        if hasattr(sys, '_MEIPASS'):
            base_path = sys._MEIPASS
        else:
            base_path = os.path.abspath(".")
        return os.path.join(base_path, relative_path)

    def load_cython_module(self, module_name):
        """加载指定的Cython模块"""
        if module_name in self.loaded_modules:
            return self.loaded_modules[module_name]

        # 查找编译后的模块
        compiled_name = self.mapping.get(module_name)
        if not compiled_name:
            print(f"⚠️ 未找到模块映射: {{module_name}}")
            return None

        compiled_path = self.get_resource_path(compiled_name)
        if not os.path.exists(compiled_path):
            print(f"⚠️ 编译模块不存在: {{compiled_path}}")
            return None

        try:
            # 动态加载Cython模块
            spec = importlib.util.spec_from_file_location(
                module_name.replace('.py', ''),
                compiled_path
            )
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                sys.modules[module_name.replace('.py', '')] = module
                spec.loader.exec_module(module)
                self.loaded_modules[module_name] = module
                print(f"✅ 加载Cython模块: {{module_name}} -> {{compiled_name}}")
                return module
        except Exception as e:
            print(f"❌ 加载模块失败 {{module_name}}: {{e}}")

        return None

    def load_all_modules(self):
        """加载所有Cython模块"""
        success_count = 0
        for module_name in self.mapping.keys():
            if self.load_cython_module(module_name):
                success_count += 1

        print(f"✅ 成功加载 {{success_count}}/{{len(self.mapping)}} 个Cython模块")
        return success_count == len(self.mapping)

# 全局加载器实例
_cython_loader = CythonModuleLoader()

def get_cython_module(module_name):
    """获取Cython模块"""
    return _cython_loader.load_cython_module(module_name)

def load_all_cython_modules():
    """加载所有Cython模块"""
    return _cython_loader.load_all_modules()

# 自动加载所有模块
if __name__ != "__main__":
    load_all_cython_modules()
'''

        loader_path = os.path.join(BUILD_DIRS["nuitka"], "cython_loader.py")
        with open(loader_path, 'w', encoding='utf-8') as f:
            f.write(loader_content)

        self.success("Cython模块加载器已创建")
        return True

    def compile_with_nuitka(self):
        """使用Nuitka编译最终程序（外层保护）"""
        self.step("Nuitka编译最终程序（外层保护）")

        nuitka_dir = BUILD_DIRS["nuitka"]
        original_dir = os.getcwd()

        try:
            os.chdir(nuitka_dir)

            # 验证必需文件
            main_script = PROJECT_CONFIG["main_script"]
            if not os.path.exists(main_script):
                self.error(f"主脚本不存在: {main_script}")
                os.chdir(original_dir)
                return False

            # 验证Cython模块
            cython_files = [f for f in os.listdir('.') if f.endswith(('.pyd', '.so'))]
            if len(cython_files) != len(CORE_MODULES):
                self.warning(f"Cython模块数量不匹配: 期望{len(CORE_MODULES)}, 实际{len(cython_files)}")

            for cf in cython_files:
                file_size = os.path.getsize(cf)
                self.log(f"包含Cython模块: {cf} ({file_size:,} 字节)")

            # 构建Nuitka命令（分层保护版）
            nuitka_cmd = [
                sys.executable, "-m", "nuitka",
                "--standalone",
                "--onefile",
                "--onefile-tempdir-spec={{TEMP}}\\\\syiming\\\\layered_{{PID}}_{{TIME}}",
                "--windows-console-mode=disable",
                "--enable-plugin=pyqt6",
                "--assume-yes-for-downloads",
                "--show-progress",
                "--show-memory",
                "--remove-output",
                f"--output-filename={PROJECT_CONFIG['output_name']}",
                "--include-module=cython_loader",  # 包含Cython加载器
            ]

            # 添加图标
            icon_path = PROJECT_CONFIG["icon_path"]
            if os.path.exists(icon_path):
                nuitka_cmd.append(f"--windows-icon-from-ico={icon_path}")
                self.success(f"添加图标: {icon_path}")

            # 添加所有Cython编译模块
            for cf in cython_files:
                nuitka_cmd.append(f"--include-data-file={cf}={cf}")
                self.log(f"包含Cython模块: {cf}")

            # 添加Cython映射文件
            if os.path.exists("cython_mapping.json"):
                nuitka_cmd.append("--include-data-file=cython_mapping.json=cython_mapping.json")
                self.log("包含Cython映射文件")

            # 添加资源目录（特别是ADBTools）
            for dir_name in REQUIRED_DIRS:
                if os.path.exists(dir_name):
                    if dir_name == "ADBTools":
                        # 为ADBTools使用文件级包含
                        for file in os.listdir(dir_name):
                            file_path = os.path.join(dir_name, file)
                            if os.path.isfile(file_path):
                                nuitka_cmd.append(f"--include-data-file={file_path}={dir_name}/{file}")
                        self.success(f"逐文件包含ADBTools")
                    else:
                        nuitka_cmd.append(f"--include-data-dir={dir_name}={dir_name}")
                        self.log(f"包含目录: {dir_name}")

            # 添加主脚本
            nuitka_cmd.append(main_script)

            self.log("开始Nuitka编译（外层保护）...")
            self.log(f"编译命令长度: {len(' '.join(nuitka_cmd))} 字符")

            # 执行编译
            result = subprocess.run(nuitka_cmd, timeout=3600)  # 1小时超时

            os.chdir(original_dir)

            if result.returncode == 0:
                output_file = os.path.join(nuitka_dir, PROJECT_CONFIG['output_name'])
                if os.path.exists(output_file):
                    file_size = os.path.getsize(output_file)
                    self.success(f"Nuitka编译成功: {output_file} ({file_size:,} 字节)")
                    return True
                else:
                    self.error("Nuitka编译完成但未找到输出文件")
                    return False
            else:
                self.error(f"Nuitka编译失败，返回码: {result.returncode}")
                return False

        except Exception as e:
            if 'original_dir' in locals():
                os.chdir(original_dir)
            self.error(f"Nuitka编译异常: {e}")
            return False

    def prepare_vmprotect(self):
        """准备VMProtect加壳配置"""
        self.step("准备VMProtect加壳配置")

        # 创建VMProtect项目文件
        vmprotect_config = f'''<?xml version="1.0" encoding="UTF-8"?>
<Document Version="1.0">
    <Options>
        <InputFileName>{PROJECT_CONFIG['output_name']}</InputFileName>
        <OutputFileName>{PROJECT_CONFIG['output_name'].replace('.exe', '_VMProtect.exe')}</OutputFileName>
        <Watermark>益民工具箱分层保护版</Watermark>
        <CheckKernelDebugger>true</CheckKernelDebugger>
        <CheckDebugger>true</CheckDebugger>
        <CheckVirtualMachine>true</CheckVirtualMachine>
        <StripDebugInfo>true</StripDebugInfo>
        <StripRelocations>true</StripRelocations>
        <PackOutputFile>true</PackOutputFile>
        <ImportProtection>true</ImportProtection>
        <ResourceProtection>true</ResourceProtection>
        <MemoryProtection>true</MemoryProtection>
    </Options>
    <Procedures>
        <!-- 这里可以添加特定函数的保护配置 -->
    </Procedures>
</Document>'''

        vmprotect_file = os.path.join(BUILD_DIRS["output"], "vmprotect_config.vmp")
        with open(vmprotect_file, 'w', encoding='utf-8') as f:
            f.write(vmprotect_config)

        # 创建自签名证书脚本
        cert_script = '''@echo off
echo 创建自签名证书...

:: 生成私钥
openssl genrsa -out private.key 2048

:: 生成证书签名请求
openssl req -new -key private.key -out certificate.csr -subj "/C=CN/ST=Beijing/L=Beijing/O=YiMin/OU=Tools/CN=YiMinTools"

:: 生成自签名证书
openssl x509 -req -days 365 -in certificate.csr -signkey private.key -out certificate.crt

:: 转换为PFX格式
openssl pkcs12 -export -out certificate.pfx -inkey private.key -in certificate.crt -password pass:yimin123

echo 自签名证书创建完成: certificate.pfx
echo 密码: yimin123
pause
'''

        cert_script_file = os.path.join(BUILD_DIRS["output"], "create_certificate.bat")
        with open(cert_script_file, 'w', encoding='utf-8') as f:
            f.write(cert_script)

        self.success("VMProtect配置文件已创建")
        self.success("自签名证书脚本已创建")

        # 创建使用说明
        instructions = f'''# 分层保护使用说明

## 当前状态
✅ 内层保护: Cython编译核心算法 ({len(CORE_MODULES)} 个模块)
✅ 外层保护: Nuitka编译完成

## 下一步操作

### 1. VMProtect加壳（推荐）
1. 安装VMProtect Ultimate
2. 打开 vmprotect_config.vmp 项目文件
3. 加载 {PROJECT_CONFIG['output_name']}
4. 点击"保护"开始加壳
5. 输出文件: {PROJECT_CONFIG['output_name'].replace('.exe', '_VMProtect.exe')}

### 2. 自签名证书（可选）
1. 安装OpenSSL
2. 运行 create_certificate.bat
3. 使用signtool对exe文件签名:
   signtool sign /f certificate.pfx /p yimin123 /t http://timestamp.digicert.com {PROJECT_CONFIG['output_name']}

### 3. 验证保护效果
- 使用PE工具检查是否被加壳
- 使用反编译工具验证代码混淆效果
- 测试反调试功能

## 保护层级
- 🔒 外层: Nuitka编译 + VMProtect加壳 + 自签名证书
- 🛡️ 内层: Cython编译核心算法 (C扩展)

## 核心算法模块 (已Cython编译)
{chr(10).join([f"- {module}" for module in CORE_MODULES])}
'''

        instructions_file = os.path.join(BUILD_DIRS["output"], "分层保护说明.txt")
        with open(instructions_file, 'w', encoding='utf-8') as f:
            f.write(instructions)

        self.success("分层保护说明已创建")
        return True

    def finalize_build(self):
        """完成构建并生成报告"""
        self.step("完成构建")

        # 移动最终文件到输出目录
        nuitka_dir = BUILD_DIRS["nuitka"]
        output_dir = BUILD_DIRS["output"]

        output_file = os.path.join(nuitka_dir, PROJECT_CONFIG['output_name'])
        if os.path.exists(output_file):
            final_output = os.path.join(output_dir, PROJECT_CONFIG['output_name'])
            shutil.copy2(output_file, final_output)

            file_size = os.path.getsize(final_output)
            self.success(f"最终输出: {final_output} ({file_size:,} 字节)")

            # 生成构建报告
            self.generate_build_report(final_output, file_size)

            return True
        else:
            self.error("未找到构建输出文件")
            return False

    def generate_build_report(self, output_file, file_size):
        """生成详细构建报告"""
        build_time = time.time() - self.start_time
        minutes = int(build_time // 60)
        seconds = int(build_time % 60)

        report = {
            "project": PROJECT_CONFIG,
            "build_info": {
                "build_time": f"{minutes}分{seconds}秒",
                "build_timestamp": datetime.now().isoformat(),
                "output_file": output_file,
                "file_size": file_size,
                "file_size_mb": round(file_size / 1024 / 1024, 2)
            },
            "protection_layers": {
                "outer_layer": "Nuitka编译 + VMProtect加壳 + 自签名证书",
                "inner_layer": "Cython编译核心算法"
            },
            "cython_modules": {
                "total_modules": len(CORE_MODULES),
                "compiled_modules": len(self.cython_modules),
                "success_rate": f"{len(self.cython_modules)/len(CORE_MODULES)*100:.1f}%",
                "modules": self.cython_modules
            },
            "included_resources": {
                "directories": REQUIRED_DIRS,
                "core_modules": CORE_MODULES,
                "non_core_modules": NON_CORE_MODULES
            },
            "next_steps": [
                "使用VMProtect对exe文件进行加壳保护",
                "创建自签名证书并对exe文件签名",
                "测试所有功能确保正常运行",
                "验证反调试和反逆向效果"
            ]
        }

        report_file = os.path.join(BUILD_DIRS["output"], "layered_protection_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        self.success(f"构建报告已保存: {report_file}")

    def build(self):
        """执行完整的分层保护构建"""
        self.step("开始分层保护构建", "启动")

        try:
            # 显示构建信息
            self.log("=" * 60)
            self.log("🔒 分层保护打包工具 v3.0.0")
            self.log("=" * 60)
            self.log("保护方案:")
            self.log("  外层: Nuitka编译 + VMProtect加壳 + 自签名证书")
            self.log("  内层: Cython编译核心算法（必须全部使用）")
            self.log("=" * 60)

            # 步骤1: 清理和准备
            self.cleanup_and_prepare()

            # 步骤2: 检查核心模块
            if not self.check_core_modules():
                self.error("核心模块检查失败")
                return False

            # 步骤3: 复制源文件
            if not self.copy_source_files():
                self.error("源文件复制失败")
                return False

            # 步骤4: 内层保护 - 强制Cython编译
            self.log("\n🛡️ 内层保护: 强制Cython编译核心算法")
            self.log("-" * 50)
            if not self.compile_cython_core():
                self.error("Cython编译失败 - 这是必需的！")
                return False

            # 步骤5: 创建模块加载器
            if not self.create_module_loader():
                self.error("模块加载器创建失败")
                return False

            # 步骤6: 整合Cython模块
            if not self.integrate_cython_modules():
                self.error("Cython模块整合失败")
                return False

            # 步骤7: 外层保护 - Nuitka编译
            self.log("\n⚡ 外层保护: Nuitka编译")
            self.log("-" * 50)
            if not self.compile_with_nuitka():
                self.error("Nuitka编译失败")
                return False

            # 步骤8: 准备VMProtect配置
            self.log("\n🔐 准备VMProtect加壳配置")
            self.log("-" * 50)
            if not self.prepare_vmprotect():
                self.warning("VMProtect配置准备失败")

            # 步骤9: 完成构建
            self.log("\n🎯 完成构建")
            self.log("-" * 50)
            if not self.finalize_build():
                self.error("构建完成失败")
                return False

            # 构建成功
            build_time = time.time() - self.start_time
            minutes = int(build_time // 60)
            seconds = int(build_time % 60)

            self.log("\n" + "=" * 60)
            self.success("🎉 分层保护构建成功完成！")
            self.log(f"⏱️ 总用时: {minutes}分{seconds}秒")
            self.log("🔒 保护状态:")
            self.log(f"   ✅ 内层: Cython编译 {len(self.cython_modules)}/{len(CORE_MODULES)} 个核心模块")
            self.log("   ✅ 外层: Nuitka编译完成")
            self.log("   🔄 待完成: VMProtect加壳 + 自签名证书")
            self.log(f"📁 输出目录: {BUILD_DIRS['output']}")
            self.log("📋 下一步: 查看'分层保护说明.txt'完成最终保护")
            self.log("=" * 60)

            return True

        except KeyboardInterrupt:
            self.warning("构建被用户中断")
            return False
        except Exception as e:
            self.error(f"构建过程异常: {e}")
            return False

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(
        description="分层保护打包工具 v3.0.0",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
分层保护方案：
  外层：Nuitka编译 + VMProtect加壳（自签名证书）
  内层：Cython编译核心算法（必须全部使用）

示例用法：
  python layered_protection_build.py              # 完整构建
  python layered_protection_build.py --clean      # 清理构建目录
  python layered_protection_build.py --cython     # 仅测试Cython编译
        """
    )

    parser.add_argument('--clean', action='store_true', help='仅清理构建目录')
    parser.add_argument('--cython', action='store_true', help='仅测试Cython编译')
    parser.add_argument('--verbose', action='store_true', help='详细输出模式')

    args = parser.parse_args()

    builder = LayeredProtectionBuilder()

    try:
        if args.clean:
            builder.step("清理构建目录")
            builder.cleanup_and_prepare()
            builder.success("清理完成")
            return 0

        if args.cython:
            builder.step("测试Cython编译")
            builder.cleanup_and_prepare()
            if builder.check_core_modules():
                builder.copy_source_files()
                success = builder.compile_cython_core()
                if success:
                    builder.success(f"Cython编译测试成功: {len(builder.cython_modules)}/{len(CORE_MODULES)} 个模块")
                else:
                    builder.error("Cython编译测试失败")
                return 0 if success else 1
            else:
                builder.error("核心模块检查失败")
                return 1

        # 执行完整构建
        success = builder.build()
        return 0 if success else 1

    except KeyboardInterrupt:
        builder.warning("\n构建被用户中断")
        return 1
    except Exception as e:
        builder.error(f"程序异常: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
