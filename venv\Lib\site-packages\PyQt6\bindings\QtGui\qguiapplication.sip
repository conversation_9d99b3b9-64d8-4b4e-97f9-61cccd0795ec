// qguiapplication.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QGuiApplication : public QCoreApplication
{
%TypeHeaderCode
#include <qguiapplication.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QAbstractTextDocumentLayout, &sipType_QAbstractTextDocumentLayout, -1, 1},
        {sipName_QAction, &sipType_QAction, -1, 2},
        {sipName_QActionGroup, &sipType_QActionGroup, -1, 3},
        {sipName_QClipboard, &sipType_QClipboard, -1, 4},
        {sipName_QValidator, &sipType_QValidator, 26, 5},
        {sipName_QDrag, &sipType_QDrag, -1, 6},
        {sipName_QFileSystemModel, &sipType_QFileSystemModel, -1, 7},
        {sipName_QGuiApplication, &sipType_QGuiApplication, -1, 8},
        {sipName_QInputDevice, &sipType_QInputDevice, 29, 9},
        {sipName_QInputMethod, &sipType_QInputMethod, -1, 10},
        {sipName_QMovie, &sipType_QMovie, -1, 11},
        {sipName_QOffscreenSurface, &sipType_QOffscreenSurface, -1, 12},
    #if defined(SIP_FEATURE_PyQt_OpenGL)
        {sipName_QOpenGLContext, &sipType_QOpenGLContext, -1, 13},
    #else
        {0, 0, -1, 13},
    #endif
    #if defined(SIP_FEATURE_PyQt_OpenGL)
        {sipName_QOpenGLContextGroup, &sipType_QOpenGLContextGroup, -1, 14},
    #else
        {0, 0, -1, 14},
    #endif
        {sipName_QWindow, &sipType_QWindow, 30, 15},
        {sipName_QPdfWriter, &sipType_QPdfWriter, -1, 16},
        {sipName_QScreen, &sipType_QScreen, -1, 17},
    #if defined(SIP_FEATURE_PyQt_SessionManager)
        {sipName_QSessionManager, &sipType_QSessionManager, -1, 18},
    #else
        {0, 0, -1, 18},
    #endif
        {sipName_QShortcut, &sipType_QShortcut, -1, 19},
        {sipName_QStandardItemModel, &sipType_QStandardItemModel, -1, 20},
        {sipName_QStyleHints, &sipType_QStyleHints, -1, 21},
        {sipName_QSyntaxHighlighter, &sipType_QSyntaxHighlighter, -1, 22},
        {sipName_QTextObject, &sipType_QTextObject, 32, 23},
        {sipName_QTextDocument, &sipType_QTextDocument, -1, 24},
        {sipName_QUndoGroup, &sipType_QUndoGroup, -1, 25},
        {sipName_QUndoStack, &sipType_QUndoStack, -1, -1},
        {sipName_QDoubleValidator, &sipType_QDoubleValidator, -1, 27},
        {sipName_QIntValidator, &sipType_QIntValidator, -1, 28},
        {sipName_QRegularExpressionValidator, &sipType_QRegularExpressionValidator, -1, -1},
        {sipName_QPointingDevice, &sipType_QPointingDevice, -1, -1},
        {sipName_QPaintDeviceWindow, &sipType_QPaintDeviceWindow, 31, -1},
        {sipName_QRasterWindow, &sipType_QRasterWindow, -1, -1},
        {sipName_QTextBlockGroup, &sipType_QTextBlockGroup, 34, 33},
        {sipName_QTextFrame, &sipType_QTextFrame, 35, -1},
        {sipName_QTextList, &sipType_QTextList, -1, -1},
        {sipName_QTextTable, &sipType_QTextTable, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    QGuiApplication(SIP_PYLIST argv /TypeHint="List[str]"/) /PostHook=__pyQtQAppHook__/ [(int &argc, char **argv, int = QCoreApplication::ApplicationFlags)];
%MethodCode
        // The Python interface is a list of argument strings that is modified.
        
        int argc;
        char **argv;
        
        // Convert the list.
        if ((argv = pyqt6_qtgui_from_argv_list(a0, argc)) == NULL)
            sipIsErr = 1;
        else
        {
            // Create it now the arguments are right.
            static int nargc;
            nargc = argc;
        
            Py_BEGIN_ALLOW_THREADS
            sipCpp = new sipQGuiApplication(nargc, argv, QT_VERSION);
            Py_END_ALLOW_THREADS
        
            // Now modify the original list.
            pyqt6_qtgui_update_argv_list(a0, argc, argv);
        }
%End

    virtual ~QGuiApplication() /ReleaseGIL/;
%MethodCode
        pyqt6_qtgui_cleanup_qobjects();
%End

    static QWindowList allWindows();
    static QWindowList topLevelWindows();
    static QWindow *topLevelAt(const QPoint &pos);
    static QString platformName();
    static QWindow *focusWindow();
    static QObject *focusObject();
    static QScreen *primaryScreen();
    static QList<QScreen *> screens();
    static QCursor *overrideCursor();
    static void setOverrideCursor(const QCursor &);
    static void changeOverrideCursor(const QCursor &);
    static void restoreOverrideCursor();
    static QFont font();
    static void setFont(const QFont &);
    static QClipboard *clipboard();
    static QPalette palette();
    static void setPalette(const QPalette &pal);
    static Qt::KeyboardModifiers keyboardModifiers();
    static Qt::KeyboardModifiers queryKeyboardModifiers();
    static Qt::MouseButtons mouseButtons();
    static void setLayoutDirection(Qt::LayoutDirection direction);
    static Qt::LayoutDirection layoutDirection();
    static bool isRightToLeft();
    static bool isLeftToRight();
    static void setDesktopSettingsAware(bool on);
    static bool desktopSettingsAware();
    static void setQuitOnLastWindowClosed(bool quit);
    static bool quitOnLastWindowClosed();
    static int exec() /PostHook=__pyQtPostEventLoopHook__,PreHook=__pyQtPreEventLoopHook__,ReleaseGIL/;
    virtual bool notify(QObject *, QEvent *);

signals:
    void fontDatabaseChanged();
    void screenAdded(QScreen *screen);
    void lastWindowClosed();
    void focusObjectChanged(QObject *focusObject);
%If (PyQt_SessionManager)
    void commitDataRequest(QSessionManager &sessionManager);
%End
%If (PyQt_SessionManager)
    void saveStateRequest(QSessionManager &sessionManager);
%End
    void focusWindowChanged(QWindow *focusWindow);
    void applicationStateChanged(Qt::ApplicationState state);
    void applicationDisplayNameChanged();

public:
    static void setApplicationDisplayName(const QString &name);
    static QString applicationDisplayName();
    static QWindow *modalWindow();
    static QStyleHints *styleHints();
    static QInputMethod *inputMethod();
    qreal devicePixelRatio() const;
%If (PyQt_SessionManager)
    bool isSessionRestored() const;
%End
%If (PyQt_SessionManager)
    QString sessionId() const;
%End
%If (PyQt_SessionManager)
    QString sessionKey() const;
%End
%If (PyQt_SessionManager)
    bool isSavingSession() const;
%End
    static Qt::ApplicationState applicationState();
    static void sync();
    static void setWindowIcon(const QIcon &icon);
    static QIcon windowIcon();

signals:
    void screenRemoved(QScreen *screen);
    void layoutDirectionChanged(Qt::LayoutDirection direction);
    void primaryScreenChanged(QScreen *screen);

public:
    static void setDesktopFileName(const QString &name);
    static QString desktopFileName();
    static QScreen *screenAt(const QPoint &point);
    static void setHighDpiScaleFactorRoundingPolicy(Qt::HighDpiScaleFactorRoundingPolicy policy);
    static Qt::HighDpiScaleFactorRoundingPolicy highDpiScaleFactorRoundingPolicy();
%If (Qt_6_5_0 -)
    void setBadgeNumber(qint64 number);
%End

protected:
    virtual bool event(QEvent *);
};

%ModuleHeaderCode
// Imports from QtCore.
typedef void (*pyqt6_qtgui_cleanup_qobjects_t)();
extern pyqt6_qtgui_cleanup_qobjects_t pyqt6_qtgui_cleanup_qobjects;

typedef char **(*pyqt6_qtgui_from_argv_list_t)(PyObject *, int &);
extern pyqt6_qtgui_from_argv_list_t pyqt6_qtgui_from_argv_list;

typedef void (*pyqt6_qtgui_update_argv_list_t)(PyObject *, int, char **);
extern pyqt6_qtgui_update_argv_list_t pyqt6_qtgui_update_argv_list;
%End

%ModuleCode
// Imports from QtCore.
pyqt6_qtgui_cleanup_qobjects_t pyqt6_qtgui_cleanup_qobjects;
pyqt6_qtgui_from_argv_list_t pyqt6_qtgui_from_argv_list;
pyqt6_qtgui_update_argv_list_t pyqt6_qtgui_update_argv_list;

// Forward declarations not in any header files but are part of the API.
void qt_set_sequence_auto_mnemonic(bool enable);
%End

%InitialisationCode
// Export our own helpers.
sipExportSymbol("qtgui_wrap_ancestors", (void *)qtgui_wrap_ancestors);
%End

%PostInitialisationCode
// Imports from QtCore.
pyqt6_qtgui_cleanup_qobjects = (pyqt6_qtgui_cleanup_qobjects_t)sipImportSymbol("pyqt6_cleanup_qobjects");
Q_ASSERT(pyqt6_qtgui_cleanup_qobjects);

pyqt6_qtgui_from_argv_list = (pyqt6_qtgui_from_argv_list_t)sipImportSymbol("pyqt6_from_argv_list");
Q_ASSERT(pyqt6_qtgui_from_argv_list);

pyqt6_qtgui_update_argv_list = (pyqt6_qtgui_update_argv_list_t)sipImportSymbol("pyqt6_update_argv_list");
Q_ASSERT(pyqt6_qtgui_update_argv_list);
%End
