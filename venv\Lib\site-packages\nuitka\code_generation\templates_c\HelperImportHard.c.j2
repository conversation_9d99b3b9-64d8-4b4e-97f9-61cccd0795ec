{#     Copyright 2025, <PERSON>, mailto:<EMAIL> find license text at end of file #}

{{ target.getTypeDecl() }} {{module_code_name}}(void) {
    static PyObject *module_{{module_code_name.lower()}} = NULL;

    if (module_{{module_code_name.lower()}} == NULL) {
        module_{{module_code_name.lower()}} = PyImport_ImportModule("{{module_name}}");

        if (unlikely(module_{{module_code_name.lower()}} == NULL)) {
{% if is_stdlib %}
#ifndef __NUITKA_NO_ASSERT__
            PyErr_PrintEx(0);
#endif
            NUITKA_ERROR_EXIT("Unexpected failure of hard import of '{{module_name}}'");
{% else %}
            return NULL;
{% endif %}
        }
    }

    return module_{{module_code_name.lower()}};
}

{#     Part of "Nuitka", an optimizing Python compiler that is compatible and   #}
{#     integrates with CPython, but also works on its own.                      #}
{#                                                                              #}
{#     Licensed under the Apache License, Version 2.0 (the "License");          #}
{#     you may not use this file except in compliance with the License.         #}
{#     You may obtain a copy of the License at                                  #}
{#                                                                              #}
{#        http://www.apache.org/licenses/LICENSE-2.0                            #}
{#                                                                              #}
{#     Unless required by applicable law or agreed to in writing, software      #}
{#     distributed under the License is distributed on an "AS IS" BASIS,        #}
{#     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. #}
{#     See the License for the specific language governing permissions and      #}
{#     limitations under the License.                                           #}
