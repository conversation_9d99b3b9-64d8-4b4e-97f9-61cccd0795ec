// qsgtexture.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSGTexture : public QObject
{
%TypeHeaderCode
#include <qsgtexture.h>
%End

public:
    QSGTexture();
    virtual ~QSGTexture();

    enum WrapMode
    {
        Repeat,
        ClampToEdge,
        MirroredRepeat,
    };

    enum Filtering
    {
        None,
        Nearest,
        Linear,
    };

    virtual QSize textureSize() const = 0;
    virtual bool hasAlphaChannel() const = 0;
    virtual bool hasMipmaps() const = 0;
    virtual QRectF normalizedTextureSubRect() const;
    virtual bool isAtlasTexture() const;
    void setMipmapFiltering(QSGTexture::Filtering filter);
    QSGTexture::Filtering mipmapFiltering() const;
    void setFiltering(QSGTexture::Filtering filter);
    QSGTexture::Filtering filtering() const;
    void setHorizontalWrapMode(QSGTexture::WrapMode hwrap);
    QSGTexture::WrapMode horizontalWrapMode() const;
    void setVerticalWrapMode(QSGTexture::WrapMode vwrap);
    QSGTexture::WrapMode verticalWrapMode() const;
    QRectF convertToNormalizedSourceRect(const QRectF &rect) const;

    enum AnisotropyLevel
    {
        AnisotropyNone,
        Anisotropy2x,
        Anisotropy4x,
        Anisotropy8x,
        Anisotropy16x,
    };

    void setAnisotropyLevel(QSGTexture::AnisotropyLevel level);
    QSGTexture::AnisotropyLevel anisotropyLevel() const;
    virtual qint64 comparisonKey() const = 0;
};

class QSGDynamicTexture : public QSGTexture
{
%TypeHeaderCode
#include <qsgtexture.h>
%End

public:
    QSGDynamicTexture();
    virtual bool updateTexture() = 0;
%If (Qt_6_4_0 -)
    virtual ~QSGDynamicTexture();
%End
};
