VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'080404b0',
        [StringStruct(u'CompanyName', u'益民工具箱'),
        StringStruct(u'FileDescription', u'益民欧加真固件刷写工具'),
        StringStruct(u'FileVersion', u'1.0.0'),
        StringStruct(u'LegalCopyright', u'Copyright (C) 2023-2024'),
        StringStruct(u'ProductName', u'益民欧加真固件刷写工具'),
        StringStruct(u'ProductVersion', u'1.0.0')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
) 