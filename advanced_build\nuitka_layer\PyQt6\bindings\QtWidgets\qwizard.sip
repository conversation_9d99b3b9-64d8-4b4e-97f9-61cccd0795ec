// qwizard.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LIC<PERSON><PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QWizard : public QDialog
{
%TypeHeaderCode
#include <qwizard.h>
%End

public:
    enum WizardButton
    {
        BackButton,
        NextButton,
        CommitButton,
        FinishButton,
        CancelButton,
        HelpButton,
        CustomButton1,
        CustomButton2,
        CustomButton3,
        Stretch,
    };

    enum WizardPixmap
    {
        WatermarkPixmap,
        LogoPixmap,
        BannerPixmap,
        BackgroundPixmap,
    };

    enum WizardStyle
    {
        ClassicStyle,
        ModernStyle,
        MacStyle,
        AeroStyle,
    };

    enum WizardOption /BaseType=Flag/
    {
        IndependentPages,
        IgnoreSubTitles,
        ExtendedWatermarkPixmap,
        NoDefaultButton,
        NoBackButtonOnStartPage,
        NoBackButtonOnLastPage,
        DisabledBackButtonOnLastPage,
        HaveNextButtonOnLastPage,
        HaveFinishButtonOnEarlyPages,
        NoCancelButton,
        CancelButtonOnLeft,
        HaveHelpButton,
        HelpButtonOnRight,
        HaveCustomButton1,
        HaveCustomButton2,
        HaveCustomButton3,
        NoCancelButtonOnLastPage,
    };

    typedef QFlags<QWizard::WizardOption> WizardOptions;
    QWizard(QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    virtual ~QWizard();
    int addPage(QWizardPage *page /Transfer/);
    void setPage(int id, QWizardPage *page /Transfer/);
    QWizardPage *page(int id) const;
    bool hasVisitedPage(int id) const;
    QList<int> visitedIds() const;
    void setStartId(int id);
    int startId() const;
    QWizardPage *currentPage() const;
    int currentId() const;
    virtual bool validateCurrentPage();
    virtual int nextId() const;
    void setField(const QString &name, const QVariant &value);
    QVariant field(const QString &name) const;
    void setWizardStyle(QWizard::WizardStyle style);
    QWizard::WizardStyle wizardStyle() const;
    void setOption(QWizard::WizardOption option, bool on = true);
    bool testOption(QWizard::WizardOption option) const;
    void setOptions(QWizard::WizardOptions options);
    QWizard::WizardOptions options() const;
    void setButtonText(QWizard::WizardButton which, const QString &text);
    QString buttonText(QWizard::WizardButton which) const;
    void setButtonLayout(const QList<QWizard::WizardButton> &layout);
    void setButton(QWizard::WizardButton which, QAbstractButton *button /Transfer/);
    QAbstractButton *button(QWizard::WizardButton which) const /Transfer/;
    void setTitleFormat(Qt::TextFormat format);
    Qt::TextFormat titleFormat() const;
    void setSubTitleFormat(Qt::TextFormat format);
    Qt::TextFormat subTitleFormat() const;
    void setPixmap(QWizard::WizardPixmap which, const QPixmap &pixmap);
    QPixmap pixmap(QWizard::WizardPixmap which) const;
    void setDefaultProperty(const char *className, const char *property, SIP_PYOBJECT changedSignal /TypeHint="PYQT_SIGNAL"/);
%MethodCode
        QByteArray signal_signature;
        
        if ((sipError = pyqt6_qtwidgets_get_signal_signature(a2, 0, signal_signature)) == sipErrorNone)
        {
            sipCpp->setDefaultProperty(a0, a1, signal_signature.constData());
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(2, a2);
        }
%End

    virtual void setVisible(bool visible);
    virtual QSize sizeHint() const;

signals:
    void currentIdChanged(int id);
    void helpRequested();
    void customButtonClicked(int which);

public slots:
    void back();
    void next();
    void restart();

protected:
    virtual bool event(QEvent *event);
    virtual void resizeEvent(QResizeEvent *event);
    virtual void paintEvent(QPaintEvent *event);
    virtual void done(int result);
    virtual void initializePage(int id);
    virtual void cleanupPage(int id);

public:
    void removePage(int id);
    QList<int> pageIds() const;
    void setSideWidget(QWidget *widget /Transfer/);
    QWidget *sideWidget() const;

signals:
    void pageAdded(int id);
    void pageRemoved(int id);

public slots:
%If (Qt_6_4_0 -)
    void setCurrentId(int id);
%End
};

class QWizardPage : public QWidget
{
%TypeHeaderCode
#include <qwizard.h>
%End

public:
    explicit QWizardPage(QWidget *parent /TransferThis/ = 0);
    virtual ~QWizardPage();
    void setTitle(const QString &title);
    QString title() const;
    void setSubTitle(const QString &subTitle);
    QString subTitle() const;
    void setPixmap(QWizard::WizardPixmap which, const QPixmap &pixmap);
    QPixmap pixmap(QWizard::WizardPixmap which) const;
    void setFinalPage(bool finalPage);
    bool isFinalPage() const;
    void setCommitPage(bool commitPage);
    bool isCommitPage() const;
    void setButtonText(QWizard::WizardButton which, const QString &text);
    QString buttonText(QWizard::WizardButton which) const;
    virtual void initializePage();
    virtual void cleanupPage();
    virtual bool validatePage();
    virtual bool isComplete() const;
    virtual int nextId() const;

signals:
    void completeChanged();

protected:
    void setField(const QString &name, const QVariant &value);
    QVariant field(const QString &name) const;
    void registerField(const QString &name, QWidget *widget, const char *property = 0, SIP_PYOBJECT changedSignal /TypeHint="PYQT_SIGNAL"/ = 0) [void (const QString &name, QWidget *widget, const char *property = 0, const char *changedSignal = 0)];
%MethodCode
        typedef sipErrorState (*pyqt6_get_signal_signature_t)(PyObject *, QObject *, QByteArray &);
        
        static pyqt6_get_signal_signature_t pyqt6_get_signal_signature = 0;
        
        if (!pyqt6_get_signal_signature)
        {
            pyqt6_get_signal_signature = (pyqt6_get_signal_signature_t)sipImportSymbol("pyqt6_get_signal_signature");
            Q_ASSERT(pyqt6_get_signal_signature);
        }
        
        QByteArray signal_signature;
        const char *signal = 0;
        
        if (a3 && a3 != Py_None)
        {
            if ((sipError = pyqt6_get_signal_signature(a3, a1, signal_signature)) == sipErrorNone)
            {
                signal = signal_signature.constData();
            }
            else if (sipError == sipErrorContinue)
            {
                sipError = sipBadCallableArg(3, a3);
            }
        }
        
        if (sipError == sipErrorNone)
        {
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            sipCpp->registerField(*a0, a1, a2, signal);
        #else
            sipCpp->sipProtect_registerField(*a0, a1, a2, signal);
        #endif
            Py_END_ALLOW_THREADS
        }
%End

    QWizard *wizard() const;
};
