from pybench import Test

class CompareIntegers(Test):

    version = 2.0
    operations = 30 * 5
    rounds = 120000

    def test(self):

        for i in range(self.rounds):

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

            2 < 3
            2 > 3
            2 == 3
            2 > 3
            2 < 3

    def calibrate(self):

        for i in range(self.rounds):
            pass


class CompareFloats(Test):

    version = 2.0
    operations = 30 * 5
    rounds = 80000

    def test(self):

        for i in range(self.rounds):

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

            2.1 < 3.31
            2.1 > 3.31
            2.1 == 3.31
            2.1 > 3.31
            2.1 < 3.31

    def calibrate(self):

        for i in range(self.rounds):
            pass


class CompareFloatsIntegers(Test):

    version = 2.0
    operations = 30 * 5
    rounds = 60000

    def test(self):

        for i in range(self.rounds):

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

            2.1 < 4
            2.1 > 4
            2.1 == 4
            2.1 > 4
            2.1 < 4

    def calibrate(self):

        for i in range(self.rounds):
            pass


class CompareLongs(Test):

    version = 2.0
    operations = 30 * 5
    rounds = 70000

    def test(self):

        for i in range(self.rounds):

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

            1234567890 < 3456789012345
            1234567890 > 3456789012345
            1234567890 == 3456789012345
            1234567890 > 3456789012345
            1234567890 < 3456789012345

    def calibrate(self):

        for i in range(self.rounds):
            pass
