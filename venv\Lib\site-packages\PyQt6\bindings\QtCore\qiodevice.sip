// qiodevice.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>ENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QIODevice : public QObject, public QIODeviceBase
{
%TypeHeaderCode
#include <qiodevice.h>
%End

public:
    QIODevice();
    explicit QIODevice(QObject *parent /TransferThis/);
    virtual ~QIODevice();
    QIODeviceBase::OpenMode openMode() const;
    void setTextModeEnabled(bool enabled);
    bool isTextModeEnabled() const;
    bool isOpen() const;
    bool isReadable() const;
    bool isWritable() const;
    virtual bool isSequential() const;
    int readChannelCount() const;
    int writeChannelCount() const;
    int currentReadChannel() const;
    void setCurrentReadChannel(int channel);
    int currentWriteChannel() const;
    void setCurrentWriteChannel(int channel);
    virtual bool open(QIODeviceBase::OpenMode mode) /ReleaseGIL/;
    virtual void close() /ReleaseGIL/;
    virtual qint64 pos() const;
    virtual qint64 size() const;
    virtual bool seek(qint64 pos) /ReleaseGIL/;
    virtual bool atEnd() const;
    virtual bool reset();
    virtual qint64 bytesAvailable() const;
    virtual qint64 bytesToWrite() const;
    SIP_PYOBJECT read(qint64 maxlen) /ReleaseGIL,TypeHint="bytes"/;
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
            len = sipCpp->read(s, a0);
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = PyBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

    SIP_PYOBJECT readLine(qint64 maxlen) /ReleaseGIL,TypeHint="bytes"/;
%MethodCode
        // Return a bytes object or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
            len = sipCpp->readLine(s, a0);
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = PyBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

    QByteArray readLine() /ReleaseGIL/;
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipRes = new QByteArray(sipCpp->readLine());
        Py_END_ALLOW_THREADS
%End

    QByteArray readAll() /ReleaseGIL/;
    virtual bool canReadLine() const;
    void startTransaction();
    void commitTransaction();
    void rollbackTransaction();
    bool isTransactionStarted() const;
    qint64 write(SIP_PYBUFFER) /ReleaseGIL/;
%MethodCode
        sipBufferInfoDef bi;
        
        if (sipGetBufferInfo(a0, &bi) > 0)
        {
            Py_BEGIN_ALLOW_THREADS
            sipRes = sipCpp->write(reinterpret_cast<char *>(bi.bi_buf), bi.bi_len);
            Py_END_ALLOW_THREADS
            
            sipReleaseBufferInfo(&bi);
        }
        else
        {
            sipIsErr = 1;
        }
%End

    SIP_PYOBJECT peek(qint64 maxlen) /ReleaseGIL,TypeHint="bytes"/;
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
            len = sipCpp->peek(s, a0);
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = PyBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

    qint64 skip(qint64 maxSize) /ReleaseGIL/;
    virtual bool waitForReadyRead(int msecs) /ReleaseGIL/;
    virtual bool waitForBytesWritten(int msecs) /ReleaseGIL/;
    void ungetChar(char c /Encoding="None"/);
    bool putChar(char c /Encoding="None"/) /ReleaseGIL/;
    bool getChar(char *c /Encoding="None",Out/) /ReleaseGIL/;
    QString errorString() const;

signals:
    void aboutToClose();
    void bytesWritten(qint64 bytes);
    void channelBytesWritten(int channel, qint64 bytes);
    void channelReadyRead(int channel);
    void readChannelFinished();
    void readyRead();

protected:
    virtual SIP_PYOBJECT readData(qint64 maxlen) = 0 /ReleaseGIL,TypeHint="bytes"/ [qint64 (char *data, qint64 maxlen)];
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            len = sipCpp->readData(s, a0);
        #else
            len = sipCpp->sipProtect_readData(s, a0);
        #endif
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = PyBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

%VirtualCatcherCode
        PyObject *result = sipCallMethod(&sipIsErr, sipMethod, "n", a1);
        
        if (result)
        {
            PyObject *buf;
        
            if (sipParseResult(&sipIsErr, sipMethod, result, "$", &buf) == 0)
            {
                if (buf == Py_None)
                {
                    sipRes = -1L;
                }
                else 
                {
                    sipBufferInfoDef bi;
                
                    if (sipGetBufferInfo(buf, &bi) < 0)
                    {
                        sipBadCatcherResult(sipMethod);
                        sipIsErr = 1;
                    }
                    else
                    {
                        sipRes = (a1 < bi.bi_len) ? a1 : bi.bi_len;
                        memcpy(a0, bi.bi_buf, sipRes);
                    
                        sipReleaseBufferInfo(&bi);
                    }
                }
                
                Py_DECREF(buf);
            }
        
            Py_DECREF(result);
        }
%End

    virtual SIP_PYOBJECT readLineData(qint64 maxlen) /ReleaseGIL,TypeHint="bytes"/ [qint64 (char *data, qint64 maxlen)];
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            len = sipSelfWasArg ? sipCpp->QIODevice::readLineData(s, a0) : sipCpp->readLineData(s, a0);
        #else
            len = sipCpp->sipProtectVirt_readLineData(sipSelfWasArg, s, a0);
        #endif
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = PyBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

%VirtualCatcherCode
        PyObject *result = sipCallMethod(&sipIsErr, sipMethod, "n", a1);
        
        if (result)
        {
            PyObject *buf;
        
            if (sipParseResult(&sipIsErr, sipMethod, result, "$", &buf) == 0)
            {
                if (buf == Py_None)
                {
                    sipRes = -1L;
                }
                else 
                {
                    sipBufferInfoDef bi;
                
                    if (sipGetBufferInfo(buf, &bi) < 0)
                    {
                        sipBadCatcherResult(sipMethod);
                        sipIsErr = 1;
                    }
                    else
                    {
                        sipRes = (a1 < bi.bi_len) ? a1 : bi.bi_len;
                        memcpy(a0, bi.bi_buf, sipRes);
                    
                        sipReleaseBufferInfo(&bi);
                    }
                }
                
                Py_DECREF(buf);
            }
        
            Py_DECREF(result);
        }
%End

    virtual qint64 writeData(SIP_PYBUFFER) = 0 /ReleaseGIL/ [qint64 (const char *data, qint64 len)];
%MethodCode
        sipBufferInfoDef bi;
        
        if (sipGetBufferInfo(a0, &bi) > 0)
        {
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            sipRes = sipCpp->writeData(reinterpret_cast<char *>(bi.bi_buf), bi.bi_len);
        #else
            sipRes = sipCpp->sipProtect_writeData(reinterpret_cast<char *>(bi.bi_buf),
                    bi.bi_len);
        #endif
            Py_END_ALLOW_THREADS
            
            sipReleaseBufferInfo(&bi);
        }
        else
        {
            sipIsErr = 1;
        }
%End

%VirtualCatcherCode
        PyObject *result = sipCallMethod(&sipIsErr, sipMethod, "n", a1);
        
        if (result)
        {
            PyObject *buf;
        
            if (sipParseResult(&sipIsErr, sipMethod, result, "$", &buf) == 0)
            {
                if (buf == Py_None)
                {
                    sipRes = -1L;
                }
                else 
                {
                    sipBufferInfoDef bi;
                
                    if (sipGetBufferInfo(buf, &bi) < 0)
                    {
                        sipBadCatcherResult(sipMethod);
                        sipIsErr = 1;
                    }
                    else
                    {
                        if (bi.bi_readonly)
                        {
                            sipBadCatcherResult(sipMethod);
                            sipIsErr = 1;
                        }
                        else
                        {
                            sipRes = (a1 < bi.bi_len) ? a1 : bi.bi_len;
                            memcpy(bi.bi_buf, a0, sipRes);
                        }
                    
                        sipReleaseBufferInfo(&bi);
                    }
                }
                
                Py_DECREF(buf);
            }
        
            Py_DECREF(result);
        }
%End

    virtual qint64 skipData(qint64 maxSize) /ReleaseGIL/;
    void setOpenMode(QIODeviceBase::OpenMode openMode);
    void setErrorString(const QString &errorString);
};
