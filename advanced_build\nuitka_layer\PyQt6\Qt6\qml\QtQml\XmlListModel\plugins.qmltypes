import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qqmlxmllistmodel_p.h"
        name: "QQmlXmlListModel"
        accessSemantics: "reference"
        defaultProperty: "roles"
        prototype: "QAbstractListModel"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQml.XmlListModel/XmlListModel 6.0",
            "QtQml.XmlListModel/XmlListModel 6.4"
        ]
        exportMetaObjectRevisions: [1536, 1540]
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "progress"
            type: "double"
            read: "progress"
            notify: "progressChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 2
        }
        Property {
            name: "query"
            type: "QString"
            read: "query"
            write: "setQuery"
            notify: "queryChanged"
            index: 3
        }
        Property {
            name: "roles"
            type: "QQmlXmlListModelRole"
            isList: true
            read: "roleObjects"
            index: 4
            isReadonly: true
        }
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 5
            isReadonly: true
        }
        Signal {
            name: "statusChanged"
            Parameter { type: "QQmlXmlListModel::Status" }
        }
        Signal {
            name: "progressChanged"
            Parameter { name: "progress"; type: "double" }
        }
        Signal { name: "countChanged" }
        Signal { name: "sourceChanged" }
        Signal { name: "queryChanged" }
        Method { name: "reload" }
        Method { name: "requestFinished" }
        Method {
            name: "requestProgress"
            Parameter { type: "qlonglong" }
            Parameter { type: "qlonglong" }
        }
        Method { name: "dataCleared" }
        Method {
            name: "queryCompleted"
            Parameter { type: "QQmlXmlListModelQueryResult" }
        }
        Method {
            name: "queryError"
            Parameter { name: "object"; type: "void"; isPointer: true }
            Parameter { name: "error"; type: "QString" }
        }
        Method { name: "errorString"; type: "QString" }
    }
    Component {
        file: "private/qqmlxmllistmodel_p.h"
        name: "QQmlXmlListModelRole"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQml.XmlListModel/XmlListModelRole 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 0
        }
        Property {
            name: "elementName"
            type: "QString"
            read: "elementName"
            write: "setElementName"
            notify: "elementNameChanged"
            index: 1
        }
        Property {
            name: "attributeName"
            type: "QString"
            read: "attributeName"
            write: "setAttributeName"
            notify: "attributeNameChanged"
            index: 2
        }
        Signal { name: "nameChanged" }
        Signal { name: "elementNameChanged" }
        Signal { name: "attributeNameChanged" }
    }
}
