// qprintengine.sip generated by MetaSIP
//
// This file is part of the QtPrintSupport Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_Printer)

class QPrintEngine
{
%TypeHeaderCode
#include <qprintengine.h>
%End

public:
    virtual ~QPrintEngine();

    enum PrintEnginePropertyKey
    {
        PPK_CollateCopies,
        PPK_ColorMode,
        PPK_Creator,
        PPK_DocumentName,
        PPK_FullPage,
        PPK_NumberOfCopies,
        PPK_Orientation,
        PPK_OutputFileName,
        PPK_PageOrder,
        PPK_PageRect,
        PPK_PageSize,
        PPK_PaperRect,
        PPK_PaperSource,
        PPK_PrinterName,
        PPK_PrinterProgram,
        PPK_Resolution,
        PPK_SelectionOption,
        PPK_SupportedResolutions,
        PPK_WindowsPageSize,
        PPK_FontEmbedding,
        PPK_Duplex,
        PPK_PaperSources,
        PPK_CustomPaperSize,
        PPK_PageMargins,
        PPK_PaperSize,
        PPK_CopyCount,
        PPK_SupportsMultipleCopies,
        PPK_PaperName,
        PPK_QPageSize,
        PPK_QPageMargins,
        PPK_QPageLayout,
        PPK_CustomBase,
    };

    virtual void setProperty(QPrintEngine::PrintEnginePropertyKey key, const QVariant &value) = 0;
    virtual QVariant property(QPrintEngine::PrintEnginePropertyKey key) const = 0;
    virtual bool newPage() = 0;
    virtual bool abort() = 0;
    virtual int metric(QPaintDevice::PaintDeviceMetric) const = 0;
    virtual QPrinter::PrinterState printerState() const = 0;
};

%End
