{"project": {"name": "益民欧加真固件刷写工具", "version": "2.0.0", "author": "益民工具箱", "description": "Android固件刷写工具 - 三合一加密版", "main_script": "main.py", "icon_path": "ico/icon.ico", "output_name": "益民欧加真固件刷写工具_加密版.exe"}, "build_time": "3分0秒", "output_file": "advanced_build/final_output\\益民欧加真固件刷写工具_加密版.exe", "file_size": 28853760, "file_size_mb": 27.52, "protection_layers": ["外层: Nuitka编译 + VMProtect加壳", "中层: PyArmor混淆非核心代码", "内层: Cython编译核心算法"], "included_directories": ["ADBTools", "ico", "tup", "PyQt6"], "core_modules": ["coloros.py", "coloros15.py", "utils.py", "fastboodt.py", "zhidinyishuaxie.py", "payload_extractor.py", "custom_messagebox.py"], "non_core_modules": ["flash_tool.py", "main.py", "config.py"], "timestamp": "2025-07-16T22:24:30.779684"}