// qevent.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QInputEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

%ConvertToSubClassCode
    switch (sipCpp->type())
    {
    case QEvent::ActionAdded:
    case QEvent::ActionChanged:
    case QEvent::ActionRemoved:
        sipType = sipType_QActionEvent;
        break;
    
    case QEvent::Close:
        sipType = sipType_QCloseEvent;
        break;
    
    case QEvent::ContextMenu:
        sipType = sipType_QContextMenuEvent;
        break;
    
    case QEvent::DragEnter:
        sipType = sipType_QDragEnterEvent;
        break;
    
    case QEvent::DragLeave:
        sipType = sipType_QDragLeaveEvent;
        break;
    
    case QEvent::DragMove:
        sipType = sipType_QDragMoveEvent;
        break;
    
    case QEvent::Drop:
        sipType = sipType_QDropEvent;
        break;
    
    case QEvent::Enter:
        sipType = sipType_QEnterEvent;
        break;
    
    case QEvent::FileOpen:
        sipType = sipType_QFileOpenEvent;
        break;
    
    case QEvent::FocusIn:
    case QEvent::FocusOut:
        sipType = sipType_QFocusEvent;
        break;
    
    case QEvent::Hide:
        sipType = sipType_QHideEvent;
        break;
    
    case QEvent::HoverEnter:
    case QEvent::HoverLeave:
    case QEvent::HoverMove:
        sipType = sipType_QHoverEvent;
        break;
    
    case QEvent::IconDrag:
        sipType = sipType_QIconDragEvent;
        break;
    
    case QEvent::InputMethod:
        sipType = sipType_QInputMethodEvent;
        break;
    
    case QEvent::KeyPress:
    case QEvent::KeyRelease:
    case QEvent::ShortcutOverride:
        sipType = sipType_QKeyEvent;
        break;
    
    case QEvent::MouseButtonDblClick:
    case QEvent::MouseButtonPress:
    case QEvent::MouseButtonRelease:
    case QEvent::MouseMove:
        sipType = sipType_QMouseEvent;
        break;
    
    case QEvent::Move:
        sipType = sipType_QMoveEvent;
        break;
    
    case QEvent::Paint:
        sipType = sipType_QPaintEvent;
        break;
    
    case QEvent::Resize:
        sipType = sipType_QResizeEvent;
        break;
    
    case QEvent::Shortcut:
        sipType = sipType_QShortcutEvent;
        break;
    
    case QEvent::Show:
        sipType = sipType_QShowEvent;
        break;
    
    case QEvent::StatusTip:
        sipType = sipType_QStatusTipEvent;
        break;
    
    case QEvent::TabletMove:
    case QEvent::TabletPress:
    case QEvent::TabletRelease:
    case QEvent::TabletEnterProximity:
    case QEvent::TabletLeaveProximity:
        sipType = sipType_QTabletEvent;
        break;
    
    case QEvent::ToolTip:
    case QEvent::WhatsThis:
        sipType = sipType_QHelpEvent;
        break;
    
    case QEvent::WhatsThisClicked:
        sipType = sipType_QWhatsThisClickedEvent;
        break;
    
    case QEvent::Wheel:
        sipType = sipType_QWheelEvent;
        break;
    
    case QEvent::WindowStateChange:
        sipType = sipType_QWindowStateChangeEvent;
        break;
    
    case QEvent::TouchBegin:
    case QEvent::TouchUpdate:
    case QEvent::TouchEnd:
    case QEvent::TouchCancel:
        sipType = sipType_QTouchEvent;
        break;
    
    case QEvent::InputMethodQuery:
        sipType = sipType_QInputMethodQueryEvent;
        break;
    
    case QEvent::Expose:
        sipType = sipType_QExposeEvent;
        break;
    
    case QEvent::ScrollPrepare:
        sipType = sipType_QScrollPrepareEvent;
        break;
    
    case QEvent::Scroll:
        sipType = sipType_QScrollEvent;
        break;
    
    case QEvent::NativeGesture:
        sipType = sipType_QNativeGestureEvent;
        break;
    
    case QEvent::PlatformSurface:
        sipType = sipType_QPlatformSurfaceEvent;
        break;
    
    #if QT_VERSION >= 0x060700
    case QEvent::ChildWindowAdded:
    case QEvent::ChildWindowRemoved:
        sipType = sipType_QChildWindowEvent;
        break;
    #endif
    
    default:
        sipType = 0;
    }
%End

public:
    virtual ~QInputEvent();
    Qt::KeyboardModifiers modifiers() const;
    quint64 timestamp() const;
    const QInputDevice *device() const;
    QInputDevice::DeviceType deviceType() const;
    virtual QInputEvent *clone() const /Factory/;

private:
    QInputEvent(const QInputEvent &);
};

class QKeyEvent : public QInputEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QKeyEvent(QEvent::Type type, int key, Qt::KeyboardModifiers modifiers, quint32 nativeScanCode, quint32 nativeVirtualKey, quint32 nativeModifiers, const QString &text = QString(), bool autorep = false, quint16 count = 1, const QInputDevice *device = QInputDevice::primaryKeyboard());
    QKeyEvent(QEvent::Type type, int key, Qt::KeyboardModifiers modifiers, const QString &text = QString(), bool autorep = false, quint16 count = 1);
    virtual ~QKeyEvent();
    int key() const;
    Qt::KeyboardModifiers modifiers() const;
    QString text() const;
    bool isAutoRepeat() const;
    int count() const /__len__/;
    bool matches(QKeySequence::StandardKey key) const;
    quint32 nativeModifiers() const;
    quint32 nativeScanCode() const;
    quint32 nativeVirtualKey() const;
    QKeyCombination keyCombination() const;
    virtual QKeyEvent *clone() const /Factory/;
};

bool operator==(QKeyEvent *e, QKeySequence::StandardKey key);
bool operator==(QKeySequence::StandardKey key, QKeyEvent *e);

class QFocusEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QFocusEvent(QEvent::Type type, Qt::FocusReason reason = Qt::OtherFocusReason);
    virtual ~QFocusEvent();
    bool gotFocus() const;
    bool lostFocus() const;
    Qt::FocusReason reason() const;
    virtual QFocusEvent *clone() const /Factory/;
};

class QPaintEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    explicit QPaintEvent(const QRegion &paintRegion);
    explicit QPaintEvent(const QRect &paintRect);
    virtual ~QPaintEvent();
    const QRect &rect() const;
    const QRegion &region() const;
    virtual QPaintEvent *clone() const /Factory/;
};

class QMoveEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QMoveEvent(const QPoint &pos, const QPoint &oldPos);
    virtual ~QMoveEvent();
    const QPoint &pos() const;
    const QPoint &oldPos() const;
    virtual QMoveEvent *clone() const /Factory/;
};

class QResizeEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QResizeEvent(const QSize &size, const QSize &oldSize);
    virtual ~QResizeEvent();
    const QSize &size() const;
    const QSize &oldSize() const;
    virtual QResizeEvent *clone() const /Factory/;
};

class QCloseEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QCloseEvent();
    virtual ~QCloseEvent();
%If (Qt_6_4_0 -)
    virtual QCloseEvent *clone() const /Factory/;
%End
};

class QIconDragEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QIconDragEvent();
    virtual ~QIconDragEvent();
%If (Qt_6_4_0 -)
    virtual QIconDragEvent *clone() const /Factory/;
%End
};

class QShowEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QShowEvent();
    virtual ~QShowEvent();
%If (Qt_6_4_0 -)
    virtual QShowEvent *clone() const /Factory/;
%End
};

class QHideEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QHideEvent();
    virtual ~QHideEvent();
%If (Qt_6_4_0 -)
    virtual QHideEvent *clone() const /Factory/;
%End
};

class QContextMenuEvent : public QInputEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    enum Reason
    {
        Mouse,
        Keyboard,
        Other,
    };

    QContextMenuEvent(QContextMenuEvent::Reason reason, const QPoint &pos, const QPoint &globalPos, Qt::KeyboardModifiers modifiers = Qt::NoModifier);
    QContextMenuEvent(QContextMenuEvent::Reason reason, const QPoint &pos);
    virtual ~QContextMenuEvent();
    int x() const;
    int y() const;
    int globalX() const;
    int globalY() const;
    const QPoint &pos() const;
    const QPoint &globalPos() const;
    QContextMenuEvent::Reason reason() const;
    virtual QContextMenuEvent *clone() const /Factory/;
};

class QInputMethodEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    enum AttributeType
    {
        TextFormat,
        Cursor,
        Language,
        Ruby,
        Selection,
    };

    class Attribute
    {
%TypeHeaderCode
#include <qevent.h>
%End

    public:
        Attribute(QInputMethodEvent::AttributeType t, int s, int l, QVariant val);
        Attribute(QInputMethodEvent::AttributeType typ, int s, int l);
        QInputMethodEvent::AttributeType type;
        int start;
        int length;
        QVariant value;
    };

    QInputMethodEvent();
    QInputMethodEvent(const QString &preeditText, const QList<QInputMethodEvent::Attribute> &attributes);
    virtual ~QInputMethodEvent();
    void setCommitString(const QString &commitString, int from = 0, int length = 0);
    const QList<QInputMethodEvent::Attribute> &attributes() const;
    const QString &preeditString() const;
    const QString &commitString() const;
    int replacementStart() const;
    int replacementLength() const;
    virtual QInputMethodEvent *clone() const /Factory/;
};

bool operator==(const QInputMethodEvent::Attribute &lhs, const QInputMethodEvent::Attribute &rhs);
bool operator!=(const QInputMethodEvent::Attribute &lhs, const QInputMethodEvent::Attribute &rhs);

class QInputMethodQueryEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    explicit QInputMethodQueryEvent(Qt::InputMethodQueries queries);
    virtual ~QInputMethodQueryEvent();
    Qt::InputMethodQueries queries() const;
    void setValue(Qt::InputMethodQuery query, const QVariant &value);
    QVariant value(Qt::InputMethodQuery query) const;
    virtual QInputMethodQueryEvent *clone() const /Factory/;
};

class QDropEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QDropEvent(const QPointF &pos, Qt::DropActions actions, const QMimeData *data, Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers, QEvent::Type type = QEvent::Drop);
    virtual ~QDropEvent();
    Qt::DropActions possibleActions() const;
    Qt::DropAction proposedAction() const;
    void acceptProposedAction();
    Qt::DropAction dropAction() const;
    void setDropAction(Qt::DropAction action);
    QObject *source() const;
    const QMimeData *mimeData() const;
    QPointF position() const;
    Qt::MouseButtons buttons() const;
    Qt::KeyboardModifiers modifiers() const;
    virtual QDropEvent *clone() const /Factory/;
};

class QDragMoveEvent : public QDropEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QDragMoveEvent(const QPoint &pos, Qt::DropActions actions, const QMimeData *data, Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers, QEvent::Type type = QEvent::DragMove);
    virtual ~QDragMoveEvent();
    QRect answerRect() const;
    void accept();
    void ignore();
    void accept(const QRect &r);
    void ignore(const QRect &r);
    virtual QDragMoveEvent *clone() const /Factory/;
};

class QDragEnterEvent : public QDragMoveEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QDragEnterEvent(const QPoint &pos, Qt::DropActions actions, const QMimeData *data, Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers);
    virtual ~QDragEnterEvent();
%If (Qt_6_4_0 -)
    virtual QDragEnterEvent *clone() const /Factory/;
%End
};

class QDragLeaveEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QDragLeaveEvent();
    virtual ~QDragLeaveEvent();
%If (Qt_6_4_0 -)
    virtual QDragLeaveEvent *clone() const /Factory/;
%End
};

class QHelpEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QHelpEvent(QEvent::Type type, const QPoint &pos, const QPoint &globalPos);
    virtual ~QHelpEvent();
    int x() const;
    int y() const;
    int globalX() const;
    int globalY() const;
    const QPoint &pos() const;
    const QPoint &globalPos() const;
    virtual QHelpEvent *clone() const /Factory/;
};

class QStatusTipEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    explicit QStatusTipEvent(const QString &tip);
    virtual ~QStatusTipEvent();
    QString tip() const;
    virtual QStatusTipEvent *clone() const /Factory/;
};

class QWhatsThisClickedEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    explicit QWhatsThisClickedEvent(const QString &href);
    virtual ~QWhatsThisClickedEvent();
    QString href() const;
    virtual QWhatsThisClickedEvent *clone() const /Factory/;
};

class QActionEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QActionEvent(int type, QAction *action, QAction *before = 0);
    virtual ~QActionEvent();
    QAction *action() const;
    QAction *before() const;
    virtual QActionEvent *clone() const /Factory/;
};

class QFileOpenEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    virtual ~QFileOpenEvent();
    QString file() const;
    QUrl url() const;
    bool openFile(QFile &file, QIODeviceBase::OpenMode flags) const /ReleaseGIL/;
    virtual QFileOpenEvent *clone() const /Factory/;

private:
    QFileOpenEvent(const QFileOpenEvent &);
};

class QShortcutEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QShortcutEvent(const QKeySequence &key, int id, bool ambiguous = false);
%If (Qt_6_5_0 -)
    QShortcutEvent(const QKeySequence &key, const QShortcut *shortcut = 0, bool ambiguous = false);
%End
    virtual ~QShortcutEvent();
    bool isAmbiguous() const;
    const QKeySequence &key() const;
    int shortcutId() const;
    virtual QShortcutEvent *clone() const /Factory/;
};

class QWindowStateChangeEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    virtual ~QWindowStateChangeEvent();
    Qt::WindowStates oldState() const;
    virtual QWindowStateChangeEvent *clone() const /Factory/;

private:
    QWindowStateChangeEvent(const QWindowStateChangeEvent &);
};

class QExposeEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    explicit QExposeEvent(const QRegion &rgn);
    virtual ~QExposeEvent();
    virtual QExposeEvent *clone() const /Factory/;
};

class QScrollPrepareEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    explicit QScrollPrepareEvent(const QPointF &startPos);
    virtual ~QScrollPrepareEvent();
    QPointF startPos() const;
    QSizeF viewportSize() const;
    QRectF contentPosRange() const;
    QPointF contentPos() const;
    void setViewportSize(const QSizeF &size);
    void setContentPosRange(const QRectF &rect);
    void setContentPos(const QPointF &pos);
    virtual QScrollPrepareEvent *clone() const /Factory/;
};

class QScrollEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    enum ScrollState
    {
        ScrollStarted,
        ScrollUpdated,
        ScrollFinished,
    };

    QScrollEvent(const QPointF &contentPos, const QPointF &overshoot, QScrollEvent::ScrollState scrollState);
    virtual ~QScrollEvent();
    QPointF contentPos() const;
    QPointF overshootDistance() const;
    QScrollEvent::ScrollState scrollState() const;
    virtual QScrollEvent *clone() const /Factory/;
};

class QPlatformSurfaceEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    enum SurfaceEventType
    {
        SurfaceCreated,
        SurfaceAboutToBeDestroyed,
    };

    explicit QPlatformSurfaceEvent(QPlatformSurfaceEvent::SurfaceEventType surfaceEventType);
    virtual ~QPlatformSurfaceEvent();
    QPlatformSurfaceEvent::SurfaceEventType surfaceEventType() const;
    virtual QPlatformSurfaceEvent *clone() const /Factory/;
};

class QPointerEvent : public QInputEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    virtual ~QPointerEvent();
    const QPointingDevice *pointingDevice() const;
    QPointingDevice::PointerType pointerType() const;
    qsizetype pointCount() const;
    QEventPoint &point(qsizetype i);
    const QList<QEventPoint> &points() const;
    QEventPoint *pointById(int id);
    virtual bool isBeginEvent() const;
    virtual bool isUpdateEvent() const;
    virtual bool isEndEvent() const;
    bool allPointsAccepted() const;
    virtual void setAccepted(bool accepted);
    virtual QPointerEvent *clone() const /Factory/;

private:
    QPointerEvent(const QPointerEvent &);
};

class QSinglePointEvent : public QPointerEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
%If (Qt_6_4_0 -)
    virtual ~QSinglePointEvent();
%End
    Qt::MouseButton button() const;
    Qt::MouseButtons buttons() const;
    QPointF position() const;
    QPointF scenePosition() const;
    QPointF globalPosition() const;
    virtual bool isBeginEvent() const;
    virtual bool isUpdateEvent() const;
    virtual bool isEndEvent() const;
    QObject *exclusivePointGrabber() const;
    void setExclusivePointGrabber(QObject *exclusiveGrabber);
    virtual QSinglePointEvent *clone() const /Factory/;

private:
    QSinglePointEvent(const QSinglePointEvent &);
};

class QEnterEvent : public QSinglePointEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QEnterEvent(const QPointF &localPos, const QPointF &scenePos, const QPointF &globalPos, const QPointingDevice *device = QPointingDevice::primaryPointingDevice());
    virtual ~QEnterEvent();
    virtual QEnterEvent *clone() const /Factory/;
};

class QMouseEvent : public QSinglePointEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QMouseEvent(QEvent::Type type, const QPointF &localPos, Qt::MouseButton button, Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers, const QPointingDevice *device = QPointingDevice::primaryPointingDevice());
    QMouseEvent(QEvent::Type type, const QPointF &localPos, const QPointF &globalPos, Qt::MouseButton button, Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers, const QPointingDevice *device = QPointingDevice::primaryPointingDevice());
    QMouseEvent(QEvent::Type type, const QPointF &localPos, const QPointF &scenePos, const QPointF &globalPos, Qt::MouseButton button, Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers, const QPointingDevice *device = QPointingDevice::primaryPointingDevice());
    virtual ~QMouseEvent();
    QPoint pos() const;
    Qt::MouseEventFlags flags() const;
    virtual QMouseEvent *clone() const /Factory/;
};

class QHoverEvent : public QSinglePointEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
%If (Qt_6_3_0 -)
    QHoverEvent(QEvent::Type type, const QPointF &pos, const QPointF &globalPos, const QPointF &oldPos, Qt::KeyboardModifiers modifiers = Qt::NoModifier, const QPointingDevice *device = QPointingDevice::primaryPointingDevice());
%End
    QHoverEvent(QEvent::Type type, const QPointF &pos, const QPointF &oldPos, Qt::KeyboardModifiers modifiers = Qt::NoModifier, const QPointingDevice *device = QPointingDevice::primaryPointingDevice());
    virtual ~QHoverEvent();
    virtual bool isUpdateEvent() const;
    QPoint oldPos() const;
    QPointF oldPosF() const;
    virtual QHoverEvent *clone() const /Factory/;
};

class QWheelEvent : public QSinglePointEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QWheelEvent(const QPointF &pos, const QPointF &globalPos, QPoint pixelDelta, QPoint angleDelta, Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers, Qt::ScrollPhase phase, bool inverted, Qt::MouseEventSource source = Qt::MouseEventNotSynthesized, const QPointingDevice *device = QPointingDevice::primaryPointingDevice());
    virtual ~QWheelEvent();
    QPoint pixelDelta() const;
    QPoint angleDelta() const;
    Qt::ScrollPhase phase() const;
    bool inverted() const;
    virtual bool isBeginEvent() const;
    virtual bool isUpdateEvent() const;
    virtual bool isEndEvent() const;
    virtual QWheelEvent *clone() const /Factory/;
};

class QTabletEvent : public QSinglePointEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QTabletEvent(QEvent::Type t, const QPointingDevice *device, const QPointF &pos, const QPointF &globalPos, qreal pressure, float xTilt, float yTilt, float tangentialPressure, qreal rotation, float z, Qt::KeyboardModifiers keyState, Qt::MouseButton button, Qt::MouseButtons buttons);
    virtual ~QTabletEvent();
    qreal pressure() const;
    qreal rotation() const;
    qreal z() const;
    qreal tangentialPressure() const;
    qreal xTilt() const;
    qreal yTilt() const;
    virtual QTabletEvent *clone() const /Factory/;
};

class QNativeGestureEvent : public QSinglePointEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
%If (Qt_6_2_0 -)
    QNativeGestureEvent(Qt::NativeGestureType type, const QPointingDevice *dev, int fingerCount, const QPointF &localPos, const QPointF &scenePos, const QPointF &globalPos, qreal value, const QPointF &delta, quint64 sequenceId = UINT64_MAX);
%End
    QNativeGestureEvent(Qt::NativeGestureType type, const QPointingDevice *dev, const QPointF &localPos, const QPointF &scenePos, const QPointF &globalPos, qreal value, quint64 sequenceId, quint64 intArgument);
    virtual ~QNativeGestureEvent();
    Qt::NativeGestureType gestureType() const;
    qreal value() const;
    virtual QNativeGestureEvent *clone() const /Factory/;
%If (Qt_6_2_0 -)
    int fingerCount() const;
%End
%If (Qt_6_2_0 -)
    QPointF delta() const;
%End
};

class QTouchEvent : public QPointerEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QTouchEvent(QEvent::Type eventType, const QPointingDevice *device = 0, Qt::KeyboardModifiers modifiers = Qt::NoModifier, const QList<QEventPoint> &touchPoints = {});
    virtual ~QTouchEvent();
    QObject *target() const;
    QEventPoint::States touchPointStates() const;
    virtual bool isBeginEvent() const;
    virtual bool isUpdateEvent() const;
    virtual bool isEndEvent() const;
    virtual QTouchEvent *clone() const /Factory/;
};

%If (Qt_6_7_0 -)

class QChildWindowEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QChildWindowEvent(QEvent::Type type, QWindow *childWindow);
    virtual ~QChildWindowEvent();
    QWindow *child() const;
    virtual QChildWindowEvent *clone() const /Factory/;
};

%End
