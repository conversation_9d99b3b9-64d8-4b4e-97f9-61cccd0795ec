// qglyphrun.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_RawFont)

class QGlyphRun
{
%TypeHeaderCode
#include <qglyphrun.h>
%End

public:
    QGlyphRun();
    QGlyphRun(const QGlyphRun &other);
    ~QGlyphRun();
    QRawFont rawFont() const;
    void setRawFont(const QRawFont &rawFont);
    QList<unsigned int> glyphIndexes() const;
    void setGlyphIndexes(const QList<unsigned int> &glyphIndexes);
    QList<QPointF> positions() const;
    void setPositions(const QList<QPointF> &positions);
    void clear();
    bool operator==(const QGlyphRun &other) const;
    bool operator!=(const QGlyphRun &other) const;
    void setOverline(bool overline);
    bool overline() const;
    void setUnderline(bool underline);
    bool underline() const;
    void setStrikeOut(bool strikeOut);
    bool strikeOut() const;

    enum GlyphRunFlag /BaseType=Flag/
    {
        Overline,
        Underline,
        StrikeOut,
        RightToLeft,
        SplitLigature,
    };

    typedef QFlags<QGlyphRun::GlyphRunFlag> GlyphRunFlags;
    void setRightToLeft(bool on);
    bool isRightToLeft() const;
    void setFlag(QGlyphRun::GlyphRunFlag flag, bool enabled = true);
    void setFlags(QGlyphRun::GlyphRunFlags flags);
    QGlyphRun::GlyphRunFlags flags() const;
    void setBoundingRect(const QRectF &boundingRect);
    QRectF boundingRect() const;
    bool isEmpty() const;
    void swap(QGlyphRun &other /Constrained/);
%If (Qt_6_5_0 -)
    QList<qsizetype> stringIndexes() const;
%End
%If (Qt_6_5_0 -)
    void setStringIndexes(const QList<qsizetype> &stringIndexes);
%End
%If (Qt_6_5_0 -)
    void setSourceString(const QString &sourceString);
%End
%If (Qt_6_5_0 -)
    QString sourceString() const;
%End
};

%End
