@echo off
chcp 65001 >nul
title 构建环境设置

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      🔧 构建环境设置工具                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python 3.8+并添加到系统PATH
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 正在检查pip...
python -m pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip不可用
    pause
    exit /b 1
)

echo ✅ pip检查通过

echo.
echo 正在升级pip到最新版本...
python -m pip install --upgrade pip

echo.
echo 正在安装构建依赖...
python -m pip install -r requirements_build.txt

echo.
echo 正在检查Visual Studio Build Tools...
where cl >nul 2>&1
if errorlevel 1 (
    echo ⚠️  警告: 未检测到Visual Studio Build Tools
    echo 这可能会导致Cython编译失败
    echo 请安装Visual Studio Build Tools或Visual Studio
    echo.
    echo 下载地址: https://visualstudio.microsoft.com/visual-cpp-build-tools/
    echo.
    set /p continue=是否继续? (y/n): 
    if /i not "%continue%"=="y" exit /b 1
) else (
    echo ✅ Visual Studio Build Tools检查通过
)

echo.
echo 正在检查Git...
git --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  警告: 未检测到Git
    echo 某些依赖可能需要Git来下载
) else (
    echo ✅ Git检查通过
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      🎉 环境设置完成                           ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║  现在可以运行以下命令开始构建:                                    ║
echo ║                                                              ║
echo ║  build.bat          - 交互式构建菜单                           ║
echo ║  python build.py    - 完整构建                                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

pause
