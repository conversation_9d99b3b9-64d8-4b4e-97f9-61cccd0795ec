import os
import sys
import shutil
import random
import string
import tempfile
import io
import subprocess
import importlib.metadata as metadata
from python_minifier import minify

# 增加递归限制
sys.setrecursionlimit(sys.getrecursionlimit() * 5)

# 定义需要混淆的所有模块
MODULES_TO_OBFUSCATE = [
    "coloros.py",
    "coloros15.py",
    "flash_tool.py",
    "main.py",
    "utils.py",
    "payload_extractor.py",
    "zhidinyishuaxie.py",
    "config.py",
    "font_extractor.py",
    "genduodakhd.py",
    "fastboodt.py",
]

# 定义项目依赖
REQUIRED_PACKAGES = [
    'PyQt6>=6.4.0',
    'python-minifier>=2.5.0',
    'nuitka>=2.0.0',
    'pywin32>=305;platform_system=="Windows"',
]

# 资源文件夹配置
RESOURCE_DIRS = {
    'ADBTools': 'ADBTools',
    'ico': 'ico',
    'tup': 'tup'
}

def generate_random_string(length=8):
    """生成随机变量名"""
    return random.choice(string.ascii_letters) + ''.join(
        random.choices(string.ascii_letters + string.digits, k=length-1)
    )

def get_protected_names():
    """获取需要保护的类名和方法名"""
    return [
        'ADBTools', 'execute_command', 'get_adb_path', 'check_adb_exists',
        'check_device_status', 'FlashToolUI',
        'QApplication', 'QMainWindow', 'QWidget', 'QVBoxLayout', 'QHBoxLayout',
        'QPushButton', 'QLabel', 'QTextEdit', 'QMessageBox', 'QFileDialog',
        'QComboBox'
    ]

def obfuscate_code(source_code):
    """优化后的混淆函数"""
    try:
        protected_names = get_protected_names()
        return minify(
            source_code,
            rename_globals=False,
            rename_locals=True,
            remove_annotations=True,
            remove_pass=True,
            remove_literal_statements=True,
            combine_imports=False,
            hoist_literals=True,
            preserve_shebang=True,
            preserve_globals=protected_names
        )
    except Exception as e:
        print(f"代码混淆出错: {e}")
        return source_code

def prepare_resources():
    """准备资源文件"""
    print("\n=== 准备资源文件 ===")
    
    # 创建临时构建目录
    temp_dir = "temp_build"
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
    os.makedirs(temp_dir)
    
    # 确保主模块存在
    if not os.path.exists("main.py"):
        print("错误: 主文件 main.py 不存在")
        return False
    
    # 复制资源文件夹
    for src_dir, dest_dir in RESOURCE_DIRS.items():
        if os.path.exists(src_dir):
            if src_dir == 'ADBTools':
                # 完整复制ADBTools文件夹，不做任何处理
                shutil.copytree(src_dir, os.path.join(temp_dir, dest_dir), dirs_exist_ok=True)
                print(f"已完整复制 {src_dir} 文件夹及其所有内容")
            else:
                # 其他资源文件夹正常复制
                shutil.copytree(src_dir, os.path.join(temp_dir, dest_dir))
            print(f"已复制 {src_dir} 文件夹")
        else:
            print(f"警告: 未找到 {src_dir} 文件夹")
    
    # 混淆源代码
    for module in MODULES_TO_OBFUSCATE:
        src_path = module
        dest_path = os.path.join(temp_dir, module)
        
        if os.path.exists(src_path):
            print(f"处理文件: {module}")
            try:
                with open(src_path, 'r', encoding='utf-8') as f:
                    code = f.read()
                
                obfuscated = obfuscate_code(code)
                os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                
                with open(dest_path, 'w', encoding='utf-8') as f:
                    f.write(obfuscated)
            except Exception as e:
                print(f"处理文件 {module} 出错: {e}")
                return False
        else:
            print(f"警告: 未找到文件 {module}")
    
    return True

def check_dependencies():
    """检查依赖"""
    print("\n=== 检查依赖 ===")
    try:
        installed = {dist.metadata['Name'].lower(): dist.version 
                    for dist in metadata.distributions()}
        
        for package in REQUIRED_PACKAGES:
            parts = package.split('>=')
            name = parts[0].strip().lower()
            version = parts[1].split(';')[0].strip() if len(parts) > 1 else None
            
            if name not in installed:
                print(f"安装依赖: {package}")
                if not install_package(package):
                    return False
            elif version and metadata.version(name) < version:
                print(f"更新依赖: {package}")
                if not install_package(package, upgrade=True):
                    return False
            else:
                print(f"依赖已满足: {name}")
        
        print("所有依赖检查完成")
        return True
    except Exception as e:
        print(f"依赖检查出错: {e}")
        return False

def install_package(package, upgrade=False):
    """安装单个包"""
    cmd = [sys.executable, "-m", "pip", "install"]
    if upgrade:
        cmd.append("--upgrade")
    cmd.append(package)
    try:
        subprocess.check_call(cmd)
        return True
    except subprocess.CalledProcessError as e:
        print(f"包安装失败: {package} - {e}")
        return False

def build():
    """主构建函数"""
    try:
        print("\n=== 开始打包过程 ===")
        
        if not check_dependencies():
            print("依赖检查失败，打包终止！")
            return False
            
        if not prepare_resources():
            print("资源准备失败，打包终止！")
            return False

        # Nuitka 配置
        nuitka_args = [
            sys.executable,
            "-m", "nuitka",
            "--standalone",  # 创建独立可执行文件
            "--windows-console-mode=disable",  # 禁用控制台窗口
            "--windows-icon-from-ico=ico/icon.ico",  # 设置图标
            
            # 只包含必要的 PyQt6 模块
            "--include-package=PyQt6.QtCore",
            "--include-package=PyQt6.QtGui",
            "--include-package=PyQt6.QtWidgets",
            
            # 包含完整的ADBTools文件夹
            "--include-data-dir=temp_build/ADBTools=ADBTools",
            "--include-data-files=temp_build/ADBTools/**/*.*=ADBTools/",
            
            # 包含资源文件
            "--include-data-files=temp_build/ico/icon.ico=ico/",
            "--include-data-files=temp_build/tup/1.png=tup/",
            "--include-data-files=temp_build/tup/2.png=tup/",
            
            "--enable-plugin=pyqt6",  # 启用 PyQt6 插件
            "--remove-output",  # 清理输出目录
            "--output-dir=dist",  # 输出目录
            "--output-filename=益民固件刷写工具",  # 输出文件名
            "--jobs=6",  # 使用6个线程
            "--show-progress",  # 显示进度
            "--show-memory",  # 显示内存使用
            "--assume-yes-for-downloads",  # 自动下载依赖
            "--onefile",  # 生成单个文件
            "--onefile-tempdir-spec=\syiming\onefile_%PID%_%TIME%",  # 指定解包目录
            "--windows-uac-admin",  # 请求管理员权限
            "--windows-company-name=益民科技",  # 公司名称
            "--windows-product-name=益民固件刷写器",  # 产品名称
            "--windows-file-version=5.0.0.0",  # 文件版本
            "--windows-product-version=5.0.0.3",  # 产品版本
            
            # 优化参数
            "--noinclude-qt-translations",  # 排除Qt翻译文件
            "--remove-output",              # 清理中间文件
            
            "temp_build/main.py"  # 主文件
        ]
        
        # 运行 Nuitka
        print("\n=== 开始 Nuitka 打包 ===")
        subprocess.check_call(nuitka_args)
        
        print("\n打包完成！")
        return True
        
    except Exception as e:
        print(f"打包过程出错: {e}")
        return False

if __name__ == "__main__":
    build()