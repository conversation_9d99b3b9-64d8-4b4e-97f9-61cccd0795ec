# ------------------------------------------------------------------
# Copyright (c) 2023 PyInstaller Development Team.
#
# This file is distributed under the terms of the GNU General Public
# License (version 2.0 or later).
#
# The full license is available in LICENSE, distributed with
# this software.
#
# SPDX-License-Identifier: GPL-2.0-or-later
# ------------------------------------------------------------------

from PyInstaller.utils.hooks import collect_data_files

# Collect version.info (which is read during package import at run-time). Avoid collecting data from `lightning.app`,
# which likely does not work with PyInstaller without additional tricks (if we need to collect that data, it should
# be done in separate `lightning.app` hook).
datas = collect_data_files(
    'lightning',
    includes=['version.info'],
)
