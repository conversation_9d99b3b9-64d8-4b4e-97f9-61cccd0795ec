from PyQt6.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton, QHBoxLayout
from PyQt6.QtCore import Qt

class CustomMessageBox(QDialog):
    def __init__(self, title, message, parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setModal(True)
        self.setFixedSize(350, 150)
        self.setStyleSheet("""
            QDialog {
                background-color: #2E4F7C;
                border-radius: 12px;
                border: 2px solid #1E3F6C;
            }
            QLabel {
                color: white;
                font-size: 15px;
                font-weight: bold;
            }
            QPushButton {
                background-color: #ff3333;
                color: white;
                border: none;
                padding: 8px 24px;
                border-radius: 12px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #cc2929;
            }
            QPushButton:pressed {
                background-color: #b32424;
            }
        """)
        layout = QVBoxLayout(self)
        label = QLabel(message)
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label)
        btn_layout = QHBoxLayout()
        btn_layout.addStretch()
        ok_btn = QPushButton("OK")
        ok_btn.clicked.connect(self.accept)
        btn_layout.addWidget(ok_btn)
        btn_layout.addStretch()
        layout.addLayout(btn_layout) 