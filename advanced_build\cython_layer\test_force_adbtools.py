# -*- coding: utf-8 -*-
"""
ADBTools测试程序 - 强制版本
"""

import os
import sys
import subprocess

def test_adbtools_in_exe():
    """测试打包后的ADBTools"""
    print("🔍 测试强制ADBTools版本...")
    
    # 方法1: 检查_MEIPASS目录
    if hasattr(sys, '_MEIPASS'):
        print(f"📁 _MEIPASS目录: {sys._MEIPASS}")
        adbtools_path = os.path.join(sys._MEIPASS, "ADBTools")
        if os.path.exists(adbtools_path):
            print("✅ 在_MEIPASS中找到ADBTools目录")
            files = os.listdir(adbtools_path)
            print(f"包含 {len(files)} 个文件:")
            for file in files:
                file_path = os.path.join(adbtools_path, file)
                if os.path.exists(file_path):
                    size = os.path.getsize(file_path)
                    print(f"  ✅ {file} ({size:,} 字节)")
                else:
                    print(f"  ❌ {file} (不存在)")
        else:
            print("❌ 在_MEIPASS中未找到ADBTools目录")
    
    # 方法2: 检查当前目录
    local_adbtools = "ADBTools"
    if os.path.exists(local_adbtools):
        print("✅ 在当前目录找到ADBTools")
    else:
        print("❌ 在当前目录未找到ADBTools")
    
    # 方法3: 尝试导入资源管理器
    try:
        import adbtools_manager
        adb_path = adbtools_manager.get_adb_exe()
        if adb_path and os.path.exists(adb_path):
            print(f"✅ 通过资源管理器找到adb.exe: {adb_path}")
            # 测试运行
            try:
                result = subprocess.run([adb_path, "version"], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print("✅ adb.exe 可以正常运行")
                    print(f"版本信息: {result.stdout.strip()}")
                else:
                    print("⚠️ adb.exe 运行失败")
            except Exception as e:
                print(f"⚠️ adb.exe 测试异常: {e}")
        else:
            print("❌ 资源管理器无法找到adb.exe")
    except ImportError:
        print("❌ 无法导入adbtools_manager模块")

if __name__ == "__main__":
    test_adbtools_in_exe()
    input("按回车键退出...")
