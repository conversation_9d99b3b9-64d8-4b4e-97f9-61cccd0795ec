# custom_loader.py
import sys
import marshal
import zlib
import hashlib
import time

class SecureImporter:
    """安全模块加载器"""
    def __init__(self):
        self._security_check()
        
    def _security_check(self):
        """隐蔽的反调试检查"""
        start = time.perf_counter()
        sum(hashlib.sha256(str(x).encode()).hexdigest() for x in range(100))
        if time.perf_counter() - start < 0.001:
            sys.exit(0)
    
    def load_module(self, name, data):
        """安全加载模块"""
        self._security_check()
        
        try:
            # 解密数据（示例使用简单异或）
            key = b'MySecretKey123!'
            decrypted = bytes([b ^ key[i%len(key)] for i,b in enumerate(data)])
            
            # 解压并加载
            code = marshal.loads(zlib.decompress(decrypted))
            module = type(sys)(name)
            exec(code, module.__dict__)
            return module
        except:
            raise ImportError(f"安全验证失败: {name}")

# 替换默认finder
sys.meta_path.insert(0, SecureImporter())