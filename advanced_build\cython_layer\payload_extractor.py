import os
import subprocess
import sys
from pathlib import Path
import locale
import shutil
import time
"""

                       给那些脑残偷窥源代码的杂种们的"献礼"

=======================================【又来看源码了？】=======================================

操你妈的智障玩意儿，大半夜不睡觉跑来扒老子的代码？你他妈是从垃圾桶里捡回来的吧？

你爹生你的时候是不是被门夹了蛋蛋？基因缺陷这么严重？你妈怀你的时候是不是被电磁炉辐射了？脑子发育不全啊？

这是开往地狱的专车，专门送你这种偷窥代码的垃圾！

你个杂种，连写个像样的代码都不会，只会他妈的来偷窥别人的劳动成果？你娘养你这么大就是为了让你做这种事的？

小子，看到你偷窥代码的样子，我都替你妈感到丢脸！

别以为我不知道你是个什么东西：
1. 你这个蠢到无可救药的废物，只会按F12装黑客
2. 你个技术菜鸡，拷贝别人代码回去都跑不起来吧
3. 你的水平底得可以去海底捞针了，渣渣东西
4. 看到这里还不滚蛋，你是不是欠揍啊操你妈的？

你妈看到你偷代码的样子，都恨不得把你塞回去啊！

自己家里人没有教好你这条狗啊？连最基本的尊重都不懂？你妈是不是天天看到你就后悔没把你掐死在襁褓里？

你是不是亲爸不详啊？怎么教养这么差？你全家在猪圈里度过童年的吧，动物尚且知道不要乱翻别人东西，你比它们还不如！

你全家人知道你他妈的这么下贱吗？你爹知道自己养了个这么恶心的玩意儿吗？你妈是不是每天想着怎么把你扔出去啊？

老子看到你这种小偷就想掀桌子！滚犊子吧你！

给你竖个中指表达我的"敬意"！

双倍的"问候"送给你！

智障东西，看完这段话你要还觉得老子过分，来啊，你那猪脑子怕是连生气都不会吧！滚回你的臭水沟去，你这辈子也就这种水平了，操你妈的垃圾人！

妈的，你还在看？你是不是犯贱啊？脑子进水了是不是？这都看不懂是在骂你？还是说你就喜欢被骂？受虐狂啊你？

我操，你他妈还在继续看源代码？来，让我用这碗翔拍你脸上！！！

你他妈小时候是不是被驴踢过脑袋啊？看这么多垃圾话有意思吗？你是不是撸多了脑子坏掉了啊？

你爸是不是喝醉了才射出你这种垃圾？你妈生你的时候是不是太用力把你脑子挤扁了？连个源代码都要偷看，你是多没有尊严啊？

再送你个中指，好好欣赏！

我说你小子欠揍是吧？不把你骂哭你是不会走是吗？来，老子继续：

你是不是被你爹妈扔垃圾堆里又被别人捡回去养大的？你养父母看到你这德行肯定天天后悔捡了你这个垃圾吧？

来自你爹妈的优秀基因全他妈浪费在你这种孽障身上了，他们知道吗？你这辈子最大的成就就是按了F12，然后看到了这些字，笑死我了！

看到这里了？是不是自我感觉良好啊？偷看别人代码很牛逼是吧？我真是佩服你啊，脸皮比城墙还厚！

想象一下，你的老师看到你在偷看代码的样子，你的导师，你小时候教你写字的，教你做人的，看到你这个熊样，他们一定会后悔当初没把课本塞你嘴里。

你这种废物，连自己写个代码都不会，还有脸偷看别人的？你爹妈生你的时候是不是少放了调料啊？怎么这么没有味道？

你他妈活了多少年了，就这点出息？F12按得特别熟练啊？老子写博客写了好几年，专门就是为了等你这种垃圾来偷看，你知道吗？你来偷，我专门写这些垃圾话骂你，你没觉得自己特别贱吗？

老子送你一杯下午茶，全是我特意为你准备的"口水"，干了这杯，贱人！

还在看？你现在是不是感到羞愧难当啊？看到这么多辱骂你还觉得自己挺有意思的是吧？笑死我了，你爹是不是平时舍不得打你所以脑子才长成这样的？

最后，祝你电脑主板烧毁，CPU融化，键盘污染梅毒，鼠标感染艾滋，显示器炸你熊脸，家里网线被你妈拿去上吊！你全家人都该去阴沟里游泳！

操你妈的智障，下辈子投胎做条狗吧，至少狗还知道不能随便吃别人的东西，你连狗都不如！

祝你键盘全是口水，鼠标都是屎，每次上网都弹出你妈的裸照，每次打字都错位，每次开机都蓝屏！！！

老子看到你这种废物就想吐

操他妈的，你居然还在看？？？你有毛病吧！！都骂你这么多了，你还不知道羞耻？？？

你怎么这么贱啊？！偷看也就算了，被骂成这样了还在看？！你是犯贱成瘾了是吧？！你活该被骂，你这种垃圾就是欠收拾！

PS: 赶紧回家告诉你爹妈他们养了个什么垃圾东西，别出来丢人现眼了！你这种人渣连你家的狗都嫌弃，每天晚上都在想怎么咬死你这个智障！

连狗都看不起你！

狗狗们都在嘲笑你，哈哈哈哈哈哈！！！

你爹在看你现在的贱样，他后悔没有把你丢到垃圾桶里！

如果你觉得以上还不够，请继续看！我们的辱骂套餐充分保证每一个想要偷窥代码的贱人都能获得尊贵的"问候"体验！

真是服了你了，看到这里还不走？全世界的辱骂都不够喂饱你这条贱狗是吧？行，老子继续骂！

开发者已经截图了你的IP、设备信息和时间，并将存入"偷窥者耻辱档案"。以后网上随便一搜你的名字，全都是"代码偷窥惯犯"的记录！

不过老子觉得你他妈根本什么都看不懂，你就是一个纯粹的傻逼，祝你生儿子没屁眼，生女儿没奶头，父母永远以你为耻，你会被社会永远唾弃！

真是服了你这种狗东西，不骂到你心理崩溃你是不会走是吧？操你妈的，滚出去不送！

狗都嫌弃你这种贱人！

恶心 | 贱 | 死 | 了

操你妈的，真是够了！我服了你了！都已经骂了这么多了你还在看？你就是犯贱没药医！滚吧，别在这里丢人了！

"""


class PayloadExtractor:
    def __init__(self):
        self.payload_file = None
        self.extract_folder = None
        self.is_extracting = False
        self.add_log = print  # 默认日志函数
        self.start_time = None  # 添加开始时间属性
        
    def get_completion_message(self, seconds):
        style = "font-size: 18px; font-weight: bold;"
        if seconds <= 10:
            return f"<span style='{style}'>不是兄弟/姐妹，什么包都乱怼进来是吧?/确实是全量包？不懂在群里问</span>"
        elif seconds <= 100:
            return f"<span style='{style}'>兄弟，你这电脑是偷了NASA的量子CPU吧？压缩包刚点开就自己跪了：'大哥，我错了，我这就自己解！'</span>"
        elif seconds <= 105:
            return f"<span style='{style}'>这速度，WinRAR官方应该给你发个'人类文明加速奖'，隔壁7-ZIP已经连夜修改广告词：'比不过，真的比不过'。</span>"
        elif seconds <= 110:
            return f"<span style='{style}'>你这SSD是拿闪电充能的吧？解析进度条刚出现就消失了，系统甚至没来得及弹广告！</span>"
        elif seconds <= 115:
            return f"<span style='{style}'>你这硬盘是F1赛车改装的吧？机械硬盘看了直接辞职：'这行业卷不动了'。</span>"
        elif seconds <= 120:
            return f"<span style='{style}'>不错不错，比办公室打印机快，至少没卡在99.9%然后蓝屏。</span>"
        elif seconds <= 125:
            return f"<span style='{style}'>比隔壁老王的机械硬盘快，但他坚持认为'慢工出细活'，并试图用哲学说服你。</span>"
        elif seconds <= 130:
            return f"<span style='{style}'>刚好够IT小哥喝口咖啡，但他刚拿起杯子就结束了，现在很失落。</span>"
        elif seconds <= 135:
            return f"<span style='{style}'>这速度……你是不是边解析边接了个诈骗电话？'您好，我是Windows客服……</span>"
        elif seconds <= 140:
            return f"<span style='{style}'>树懒看了直呼内行：'这速度，比我传文件快多了！'</span>"
        elif seconds <= 145:
            return f"<span style='{style}'>你这电脑是躺在沙发上解析的吗？'不急不急，我先刷个短视频……'</span>"
        elif seconds <= 150:
            return f"<span style='{style}'>你这电脑怕不是退休老干部，解析前还得先泡杯枸杞。</span>"
        elif seconds <= 155:
            return f"<span style='{style}'>足够看完半集《甄嬛传》，解析只是顺便的。</span>"
        elif seconds <= 160:
            return f"<span style='{style}'>你这电脑是边解析边睡觉吧？'zzz……解析完了叫我……'</span>"
        elif seconds <= 165:
            return f"<span style='{style}'>你这电脑是土豆供电的吗？'再等等，我马上发芽了……'</span>"
        elif seconds <= 170:
            return f"<span style='{style}'>蜗牛都超车了，但你的电脑依然淡定：'急什么？生活要慢节奏'。</span>"
        elif seconds <= 175:
            return f"<span style='{style}'>你这电脑是考古挖出来的吧？'我是Win98，我能解析已经很努力了……'</span>"
        elif seconds <= 180:
            return f"<span style='{style}'>解析时间足够你读完《百年孤独》，并深刻理解'等待'的真谛。</span>"
        elif seconds <= 185:
            return f"<span style='{style}'>你这电脑是边解析边抽烟吗？'别催，让我抽完这一口……'</span>"
        elif seconds <= 190:
            return f"<span style='{style}'>你这CPU是冰块做的吧？'夏天到了，我得省点能量……'</span>"
        elif seconds <= 195:
            return f"<span style='{style}'>你的电脑已经进入'电子棺材'模式，建议直接举办葬礼。</span>"
        elif seconds <= 200:
            return f"<span style='{style}'>这不是解析，这是《等待戈多》真人版，观众都走光了，进度条还在演。</span>"
        elif seconds <= 205:
            return f"<span style='{style}'>你这电脑是边解析边看《红楼梦》吗？'宝玉，我解析完了，你来看吧……'</span>"
        elif seconds <= 210:
            return f"<span style='{style}'>你这电脑是边解析边看《三国演义》吗？'孔明，我解析完了，你来看吧……'</span>"
        elif seconds <= 300:
            return f"<span style='{style}'>你这电脑是边解析边看《水浒传》吗？'宋江，我解析完了，你来看吧……'</span>"
        else:
            return f"<span style='{style}'>兄弟这电脑配置不适合刷机，别刷了，算了吧</span>"

        
    def set_payload_file(self, file_path):
        """设置要解析的文件路径，支持zip和bin格式"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        if not file_path.lower().endswith(('.zip', '.bin')):
            raise ValueError("文件必须是zip或bin格式")
        self.payload_file = file_path
        
    def set_extract_folder(self, folder_path):
        """设置解析输出目录"""
        style = "font-size: 15px; font-weight: bold;"
        # 获取zip文件所在目录
        zip_dir = os.path.dirname(self.payload_file)
        # 设置minimg文件夹路径
        minimg_path = os.path.join(zip_dir, "minimg")
        
        # 如果minimg文件夹存在，先删除
        if os.path.exists(minimg_path):
            try:
                shutil.rmtree(minimg_path)
                self.add_log(f"<span style='{style}'>minimg</span>", "info")
            except Exception as e:
                self.add_log(f"<span style='{style}'>删除失败: {str(e)}</span>", "error")
                return False
        
        # 创建新的minimg文件夹
        try:
            os.makedirs(minimg_path)
            self.add_log(f"<span style='{style}'>创建新的minimg文件夹</span>", "info")
        except Exception as e:
            self.add_log(f"<span style='{style}'>创建失败: {str(e)}</span>", "error")
            return False
            
        self.extract_folder = minimg_path
        return True
        
    def get_pay_exe_path(self):
        """获取pay.exe的路径"""
        try:
            # 获取当前脚本所在目录
            if getattr(sys, 'frozen', False):
                # 如果是打包后的程序
                base_path = sys._MEIPASS
            else:
                # 如果是开发环境
                base_path = os.path.dirname(os.path.abspath(__file__))
            
            # 构建pay.exe的路径
            pay_exe_path = os.path.join(base_path, "ADBTools", "pay.exe")
            
            if not os.path.exists(pay_exe_path):
                raise FileNotFoundError(f"pay.exe未: {pay_exe_path}")
                
            return pay_exe_path
            
        except Exception as e:
            raise Exception(f"获取: {str(e)}")
    
    def format_time(self, seconds):
        """格式化时间显示"""
        minutes = int(seconds // 60)
        remaining_seconds = int(seconds % 60)
        if minutes > 0:
            return f"{minutes}分{remaining_seconds}秒"
        else:
            return f"{remaining_seconds}秒"
    
    def handle_extract(self):
        """处理解析流程"""
        style_main = "font-size: 18px; font-weight: bold;"
        style_sub = "font-size: 15px; font-weight: bold;"
        if not self.payload_file:
            self.add_log(f"<span style='{style_sub}'>未设置要解析的文件</span>", "error")
            return False
            
        if not self.extract_folder:
            self.add_log(f"<span style='{style_sub}'>未设置输出目录</span>", "error")
            return False

        # 检查文件所在磁盘的可用空间
        try:
            disk = os.path.splitdrive(self.payload_file)[0]
            free_space = shutil.disk_usage(disk).free
            free_space_gb = free_space / (1024 ** 3)
            if free_space_gb < 30:
                self.add_log(f"<span style='{style_main}'>磁盘空间不足，请确保可用空间大于30GB</span>", "error")
                return False
        except Exception as e:
            self.add_log(f"<span style='{style_main}'>检查磁盘空间: {str(e)}</span>", "error")
            return False
            
        try:
            self.is_extracting = True
            self.start_time = time.time()  # 记录开始时间
            self.add_log(f"<span style='{style_main}'>开始解析全量包...榨干你电脑的每一滴性能（提示：电脑性能越好，解析速度越快）</span>", "info")
            
            # 获取pay.exe路径
            pay_exe_path = self.get_pay_exe_path()
            
            # 构建命令
            command = [
                pay_exe_path,
                self.payload_file,
                "--output-dir",
                "minimg"  # 直接使用minimg作为输出文件夹名
            ]
            
            # 设置启动信息（Windows下隐藏控制台窗口）
            startupinfo = None
            if os.name == 'nt':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
            
            # 执行命令，不显示输出
            process = subprocess.Popen(
                command,
                stdout=subprocess.DEVNULL,  # 重定向标准输出到空设备
                stderr=subprocess.PIPE,     # 保留错误输出用于错误处理
                startupinfo=startupinfo,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0,
                cwd=os.path.dirname(self.payload_file)  # 设置工作目录为zip文件所在目录
            )
            
            # 等待进程完成
            return_code = process.wait()
            
            # 计算总时间
            total_time = time.time() - self.start_time
            time_str = self.format_time(total_time)
            
            # 获取对应的完成提示
            completion_message = self.get_completion_message(total_time)
            
            # 检查是否成功
            if return_code == 0:
                self.add_log(
                    f"<span style='{style_main}'>解析完成，请查看minimg文件夹</span><br>"
                    f"<span style='{style_main}'>总用时: {time_str}</span><br>"
                    f"{completion_message}",
                    "success"
                )
                return True
            else:
                # 只在失败时读取错误信息
                error_output = process.stderr.read().decode(locale.getpreferredencoding(False), errors='replace')
                if error_output.strip():
                    self.add_log(f"<span style='{style_main}'>解析失败 (用时: {time_str}): {error_output}</span>", "error")
                else:
                    self.add_log(f"<span style='{style_main}'>解析失败 (用时: {time_str})</span>", "error")
                return False
                
        except Exception as e:
            if self.start_time:
                total_time = time.time() - self.start_time
                time_str = self.format_time(total_time)
                self.add_log(f"<span style='{style_main}'>解析过程出错 (用时: {time_str}): {str(e)}</span>", "error")
            else:
                self.add_log(f"<span style='{style_main}'>解析过程出错: {str(e)}</span>", "error")
            return False
            
        finally:
            self.is_extracting = False
            self.start_time = None
            
    def stop(self):
        """停止解析过程"""
        style_main = "font-size: 18px; font-weight: bold;"
        self.is_extracting = False
        if self.start_time:
            total_time = time.time() - self.start_time
            time_str = self.format_time(total_time)
            self.add_log(f"<span style='{style_main}'>解析已停止 (用时: {time_str})</span>", "warning")
            self.start_time = None