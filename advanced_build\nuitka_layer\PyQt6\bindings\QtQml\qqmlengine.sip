// qqmlengine.sip generated by MetaSIP
//
// This file is part of the QtQml Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQmlEngine : public QJSEngine
{
%TypeHeaderCode
#include <qqmlengine.h>
%End

public:
    explicit QQmlEngine(QObject *parent /TransferThis/ = 0);
    virtual ~QQmlEngine();
    QQmlContext *rootContext() const;
    void clearComponentCache();
    void trimComponentCache();
    QStringList importPathList() const;
    void setImportPathList(const QStringList &paths);
    void addImportPath(const QString &dir);
    QStringList pluginPathList() const;
    void setPluginPathList(const QStringList &paths);
    void addPluginPath(const QString &dir);
    bool importPlugin(const QString &filePath, const QString &uri, QList<QQmlError> *errors /GetWrapper/);
%MethodCode
        int orig_size = (a2 ? a2->size() : 0);
        
        sipRes = sipCpp->importPlugin(*a0, *a1, a2);
        
        if (a2)
        {
            for (int i = a2->size(); i > orig_size; --i)
            {
                QQmlError *new_error = new QQmlError(a2->at(i - orig_size - 1));
                PyObject *new_error_obj = sipConvertFromNewType(new_error, sipType_QQmlError, 0);
                
                if (!new_error_obj)
                {
                    delete new_error;
                    sipError = sipErrorFail;
                    break;
                }
                
                if (PyList_Insert(a2Wrapper, 0, new_error_obj) < 0)
                {
                    Py_DECREF(new_error_obj);
                    sipError = sipErrorFail;
                    break;
                }
                
                Py_DECREF(new_error_obj);
            }
        }
%End

    void setNetworkAccessManagerFactory(QQmlNetworkAccessManagerFactory * /KeepReference/);
    QQmlNetworkAccessManagerFactory *networkAccessManagerFactory() const;
    QNetworkAccessManager *networkAccessManager() const;
    void addImageProvider(const QString &id, QQmlImageProviderBase * /Transfer/);
    QQmlImageProviderBase *imageProvider(const QString &id) const;
    void removeImageProvider(const QString &id);
    void setIncubationController(QQmlIncubationController * /KeepReference/);
    QQmlIncubationController *incubationController() const;
    void setOfflineStoragePath(const QString &dir);
    QString offlineStoragePath() const;
    QUrl baseUrl() const;
    void setBaseUrl(const QUrl &);
    bool outputWarningsToStandardError() const;
    void setOutputWarningsToStandardError(bool);
    static QQmlContext *contextForObject(const QObject *);
    static void setContextForObject(QObject *, QQmlContext *);

public slots:
    void retranslate();

protected:
    virtual bool event(QEvent *);

signals:
    void quit();
    void warnings(const QList<QQmlError> &warnings);
    void exit(int retCode);

public:
    QString offlineStorageDatabaseFilePath(const QString &databaseName) const;
    SIP_PYOBJECT singletonInstance(int qmlTypeId) /TypeHint="QObject"/;
%MethodCode
        QJSValue instance = sipCpp->singletonInstance<QJSValue>(a0);
        
        if (instance.isQObject())
        {
            sipRes = sipConvertFromType(instance.toQObject(), sipType_QObject, NULL);
                
            if (!sipRes)
                sipError = sipErrorFail;
        }
        else
        {
            sipRes = Py_None;
            Py_INCREF(sipRes);
        }
%End

%If (Qt_6_5_0 -)
    SIP_PYOBJECT singletonInstance(QAnyStringView moduleName, QAnyStringView typeName) /TypeHint="QObject"/;
%MethodCode
        QJSValue instance = sipCpp->singletonInstance<QJSValue>(*a0, *a1);
        
        if (instance.isQObject())
        {
            sipRes = sipConvertFromType(instance.toQObject(), sipType_QObject, NULL);
                
            if (!sipRes)
                sipError = sipErrorFail;
        }
        else
        {
            sipRes = Py_None;
            Py_INCREF(sipRes);
        }
%End

%End
    void addUrlInterceptor(QQmlAbstractUrlInterceptor *urlInterceptor);
    void removeUrlInterceptor(QQmlAbstractUrlInterceptor *urlInterceptor);
    QUrl interceptUrl(const QUrl &url, QQmlAbstractUrlInterceptor::DataType type) const;
%If (Qt_6_2_0 -)
    QList<QQmlAbstractUrlInterceptor *> urlInterceptors() const;
%End
%If (Qt_6_3_0 -)
    void clearSingletons();
%End

signals:
%If (Qt_6_5_0 -)
    void offlineStoragePathChanged();
%End

public:
%If (Qt_6_6_0 -)
    void markCurrentFunctionAsTranslationBinding();
%End
};

class QQmlImageProviderBase : public QObject
{
%TypeHeaderCode
#include <qqmlengine.h>
%End

public:
    enum ImageType
    {
        Image,
        Pixmap,
        Texture,
        ImageResponse,
    };

    enum Flag /BaseType=Flag/
    {
        ForceAsynchronousImageLoading,
    };

    typedef QFlags<QQmlImageProviderBase::Flag> Flags;
    virtual ~QQmlImageProviderBase();
    virtual QQmlImageProviderBase::ImageType imageType() const = 0;
    virtual QQmlImageProviderBase::Flags flags() const = 0;

private:
    QQmlImageProviderBase();
};
