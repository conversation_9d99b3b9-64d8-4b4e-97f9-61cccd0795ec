// qgraphicslayout.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QGraphicsLayout : public QGraphicsLayoutItem
{
%TypeHeaderCode
#include <qgraphicslayout.h>
%End

public:
    QGraphicsLayout(QGraphicsLayoutItem *parent /TransferThis/ = 0);
    virtual ~QGraphicsLayout();
    void setContentsMargins(qreal left, qreal top, qreal right, qreal bottom);
    virtual void getContentsMargins(qreal *left, qreal *top, qreal *right, qreal *bottom) const;
    void activate();
    bool isActivated() const;
    virtual void invalidate();
    virtual void widgetEvent(QEvent *e);
    virtual int count() const = 0 /__len__/;
    virtual QGraphicsLayoutItem *itemAt(int i) const = 0;
    virtual void removeAt(int index) = 0;
    virtual void updateGeometry();

protected:
    void addChildLayoutItem(QGraphicsLayoutItem *layoutItem /Transfer/);
};
