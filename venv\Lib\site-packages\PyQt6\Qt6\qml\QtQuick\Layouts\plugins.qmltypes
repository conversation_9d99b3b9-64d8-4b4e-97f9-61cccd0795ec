import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquicklinearlayout_p.h"
        name: "QQuickColumnLayout"
        accessSemantics: "reference"
        prototype: "QQuickLinearLayout"
        exports: [
            "QtQuick.Layouts/ColumnLayout 1.0",
            "QtQuick.Layouts/ColumnLayout 1.1",
            "QtQuick.Layouts/ColumnLayout 2.0",
            "QtQuick.Layouts/ColumnLayout 2.1",
            "QtQuick.Layouts/ColumnLayout 2.4",
            "QtQuick.Layouts/ColumnLayout 2.7",
            "QtQuick.Layouts/ColumnLayout 2.11",
            "QtQuick.Layouts/ColumnLayout 6.0",
            "QtQuick.Layouts/ColumnLayout 6.3",
            "QtQuick.Layouts/ColumnLayout 6.6",
            "QtQuick.Layouts/ColumnLayout 6.7"
        ]
        exportMetaObjectRevisions: [
            256,
            257,
            512,
            513,
            516,
            519,
            523,
            1536,
            1539,
            1542,
            1543
        ]
    }
    Component {
        file: "private/qquicklinearlayout_p.h"
        name: "QQuickGridLayout"
        accessSemantics: "reference"
        prototype: "QQuickGridLayoutBase"
        exports: [
            "QtQuick.Layouts/GridLayout 1.0",
            "QtQuick.Layouts/GridLayout 1.1",
            "QtQuick.Layouts/GridLayout 2.0",
            "QtQuick.Layouts/GridLayout 2.1",
            "QtQuick.Layouts/GridLayout 2.4",
            "QtQuick.Layouts/GridLayout 2.7",
            "QtQuick.Layouts/GridLayout 2.11",
            "QtQuick.Layouts/GridLayout 6.0",
            "QtQuick.Layouts/GridLayout 6.3",
            "QtQuick.Layouts/GridLayout 6.6",
            "QtQuick.Layouts/GridLayout 6.7"
        ]
        exportMetaObjectRevisions: [
            256,
            257,
            512,
            513,
            516,
            519,
            523,
            1536,
            1539,
            1542,
            1543
        ]
        Enum {
            name: "Flow"
            values: ["LeftToRight", "TopToBottom"]
        }
        Property {
            name: "columnSpacing"
            type: "double"
            read: "columnSpacing"
            write: "setColumnSpacing"
            notify: "columnSpacingChanged"
            index: 0
        }
        Property {
            name: "rowSpacing"
            type: "double"
            read: "rowSpacing"
            write: "setRowSpacing"
            notify: "rowSpacingChanged"
            index: 1
        }
        Property {
            name: "columns"
            type: "int"
            read: "columns"
            write: "setColumns"
            notify: "columnsChanged"
            index: 2
        }
        Property {
            name: "rows"
            type: "int"
            read: "rows"
            write: "setRows"
            notify: "rowsChanged"
            index: 3
        }
        Property {
            name: "flow"
            type: "Flow"
            read: "flow"
            write: "setFlow"
            notify: "flowChanged"
            index: 4
        }
        Property {
            name: "uniformCellWidths"
            revision: 1542
            type: "bool"
            read: "uniformCellWidths"
            write: "setUniformCellWidths"
            notify: "uniformCellWidthsChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "uniformCellHeights"
            revision: 1542
            type: "bool"
            read: "uniformCellHeights"
            write: "setUniformCellHeights"
            notify: "uniformCellHeightsChanged"
            index: 6
            isFinal: true
        }
        Signal { name: "columnSpacingChanged" }
        Signal { name: "rowSpacingChanged" }
        Signal { name: "columnsChanged" }
        Signal { name: "rowsChanged" }
        Signal { name: "flowChanged" }
        Signal { name: "uniformCellWidthsChanged"; revision: 1542 }
        Signal { name: "uniformCellHeightsChanged"; revision: 1542 }
    }
    Component {
        file: "private/qquicklinearlayout_p.h"
        name: "QQuickGridLayoutBase"
        accessSemantics: "reference"
        prototype: "QQuickLayout"
        Property {
            name: "layoutDirection"
            revision: 257
            type: "Qt::LayoutDirection"
            read: "layoutDirection"
            write: "setLayoutDirection"
            notify: "layoutDirectionChanged"
            index: 0
        }
        Signal { name: "layoutDirectionChanged"; revision: 257 }
    }
    Component {
        file: "private/qquicklayout_p.h"
        name: "QQuickLayout"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Layouts/Layout 1.0",
            "QtQuick.Layouts/Layout 2.0",
            "QtQuick.Layouts/Layout 2.1",
            "QtQuick.Layouts/Layout 2.4",
            "QtQuick.Layouts/Layout 2.7",
            "QtQuick.Layouts/Layout 2.11",
            "QtQuick.Layouts/Layout 6.0",
            "QtQuick.Layouts/Layout 6.3",
            "QtQuick.Layouts/Layout 6.7"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [
            256,
            512,
            513,
            516,
            519,
            523,
            1536,
            1539,
            1543
        ]
        attachedType: "QQuickLayoutAttached"
        Method { name: "invalidateSenderItem" }
        Method { name: "_q_dumpLayoutTree" }
    }
    Component {
        file: "private/qquicklayout_p.h"
        name: "QQuickLayoutAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "minimumWidth"
            type: "double"
            read: "minimumWidth"
            write: "setMinimumWidth"
            notify: "minimumWidthChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "minimumHeight"
            type: "double"
            read: "minimumHeight"
            write: "setMinimumHeight"
            notify: "minimumHeightChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "preferredWidth"
            type: "double"
            read: "preferredWidth"
            write: "setPreferredWidth"
            notify: "preferredWidthChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "preferredHeight"
            type: "double"
            read: "preferredHeight"
            write: "setPreferredHeight"
            notify: "preferredHeightChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "maximumWidth"
            type: "double"
            read: "maximumWidth"
            write: "setMaximumWidth"
            notify: "maximumWidthChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "maximumHeight"
            type: "double"
            read: "maximumHeight"
            write: "setMaximumHeight"
            notify: "maximumHeightChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "fillHeight"
            type: "bool"
            read: "fillHeight"
            write: "setFillHeight"
            notify: "fillHeightChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "fillWidth"
            type: "bool"
            read: "fillWidth"
            write: "setFillWidth"
            notify: "fillWidthChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "row"
            type: "int"
            read: "row"
            write: "setRow"
            notify: "rowChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "column"
            type: "int"
            read: "column"
            write: "setColumn"
            notify: "columnChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "rowSpan"
            type: "int"
            read: "rowSpan"
            write: "setRowSpan"
            notify: "rowSpanChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "columnSpan"
            type: "int"
            read: "columnSpan"
            write: "setColumnSpan"
            notify: "columnSpanChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "alignment"
            type: "Qt::Alignment"
            read: "alignment"
            write: "setAlignment"
            notify: "alignmentChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "horizontalStretchFactor"
            type: "int"
            read: "horizontalStretchFactor"
            write: "setHorizontalStretchFactor"
            notify: "horizontalStretchFactorChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "verticalStretchFactor"
            type: "int"
            read: "verticalStretchFactor"
            write: "setVerticalStretchFactor"
            notify: "verticalStretchFactorChanged"
            index: 14
            isFinal: true
        }
        Property {
            name: "margins"
            type: "double"
            read: "margins"
            write: "setMargins"
            notify: "marginsChanged"
            index: 15
            isFinal: true
        }
        Property {
            name: "leftMargin"
            type: "double"
            read: "leftMargin"
            write: "setLeftMargin"
            reset: "resetLeftMargin"
            notify: "leftMarginChanged"
            index: 16
            isFinal: true
        }
        Property {
            name: "topMargin"
            type: "double"
            read: "topMargin"
            write: "setTopMargin"
            reset: "resetTopMargin"
            notify: "topMarginChanged"
            index: 17
            isFinal: true
        }
        Property {
            name: "rightMargin"
            type: "double"
            read: "rightMargin"
            write: "setRightMargin"
            reset: "resetRightMargin"
            notify: "rightMarginChanged"
            index: 18
            isFinal: true
        }
        Property {
            name: "bottomMargin"
            type: "double"
            read: "bottomMargin"
            write: "setBottomMargin"
            reset: "resetBottomMargin"
            notify: "bottomMarginChanged"
            index: 19
            isFinal: true
        }
        Signal { name: "minimumWidthChanged" }
        Signal { name: "minimumHeightChanged" }
        Signal { name: "preferredWidthChanged" }
        Signal { name: "preferredHeightChanged" }
        Signal { name: "maximumWidthChanged" }
        Signal { name: "maximumHeightChanged" }
        Signal { name: "fillWidthChanged" }
        Signal { name: "fillHeightChanged" }
        Signal { name: "leftMarginChanged" }
        Signal { name: "topMarginChanged" }
        Signal { name: "rightMarginChanged" }
        Signal { name: "bottomMarginChanged" }
        Signal { name: "marginsChanged" }
        Signal { name: "rowChanged" }
        Signal { name: "columnChanged" }
        Signal { name: "rowSpanChanged" }
        Signal { name: "columnSpanChanged" }
        Signal { name: "alignmentChanged" }
        Signal { name: "horizontalStretchFactorChanged" }
        Signal { name: "verticalStretchFactorChanged" }
    }
    Component {
        file: "private/qquicklayoutitemproxy_p.h"
        name: "QQuickLayoutItemProxy"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Layouts/LayoutItemProxy 6.6",
            "QtQuick.Layouts/LayoutItemProxy 6.7"
        ]
        exportMetaObjectRevisions: [1542, 1543]
        Property {
            name: "target"
            type: "QQuickItem"
            isPointer: true
            read: "target"
            write: "setTarget"
            notify: "targetChanged"
            index: 0
        }
        Signal { name: "targetChanged" }
        Method { name: "updatePos" }
        Method { name: "targetMinimumWidthChanged" }
        Method { name: "proxyMinimumWidthChanged" }
        Method { name: "targetMinimumHeightChanged" }
        Method { name: "proxyMinimumHeightChanged" }
        Method { name: "targetPreferredWidthChanged" }
        Method { name: "proxyPreferredWidthChanged" }
        Method { name: "targetPreferredHeightChanged" }
        Method { name: "proxyPreferredHeightChanged" }
        Method { name: "targetMaximumWidthChanged" }
        Method { name: "proxyMaximumWidthChanged" }
        Method { name: "targetMaximumHeightChanged" }
        Method { name: "proxyMaximumHeightChanged" }
        Method { name: "targetFillWidthChanged" }
        Method { name: "proxyFillWidthChanged" }
        Method { name: "targetFillHeightChanged" }
        Method { name: "proxyFillHeightChanged" }
        Method { name: "targetAlignmentChanged" }
        Method { name: "proxyAlignmentChanged" }
        Method { name: "targetHorizontalStretchFactorChanged" }
        Method { name: "proxyHorizontalStretchFactorChanged" }
        Method { name: "targetVerticalStretchFactorChanged" }
        Method { name: "proxyVerticalStretchFactorChanged" }
        Method { name: "targetMarginsChanged" }
        Method { name: "proxyMarginsChanged" }
        Method { name: "targetLeftMarginChanged" }
        Method { name: "proxyLeftMarginChanged" }
        Method { name: "targetTopMarginChanged" }
        Method { name: "proxyTopMarginChanged" }
        Method { name: "targetRightMarginChanged" }
        Method { name: "proxyRightMarginChanged" }
        Method { name: "targetBottomMarginChanged" }
        Method { name: "proxyBottomMarginChanged" }
        Method { name: "effectiveTarget"; type: "QQuickItem"; isPointer: true }
    }
    Component {
        file: "private/qquicklayoutitemproxy_p.h"
        name: "QQuickLayoutItemProxyAttachedData"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "proxyHasControl"
            type: "bool"
            read: "proxyHasControl"
            notify: "controllingProxyChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "controllingProxy"
            type: "QQuickLayoutItemProxy"
            isPointer: true
            read: "getControllingProxy"
            notify: "controllingProxyChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "proxies"
            type: "QList<QQuickLayoutItemProxy*>"
            read: "getProxies"
            notify: "proxiesChanged"
            index: 2
            isReadonly: true
        }
        Signal { name: "controlTaken" }
        Signal { name: "controlReleased" }
        Signal { name: "controllingProxyChanged" }
        Signal { name: "proxiesChanged" }
    }
    Component {
        file: "private/qquicklinearlayout_p.h"
        name: "QQuickLinearLayout"
        accessSemantics: "reference"
        prototype: "QQuickGridLayoutBase"
        Property {
            name: "spacing"
            type: "double"
            read: "spacing"
            write: "setSpacing"
            notify: "spacingChanged"
            index: 0
        }
        Property {
            name: "uniformCellSizes"
            revision: 1542
            type: "bool"
            read: "uniformCellSizes"
            write: "setUniformCellSizes"
            notify: "uniformCellSizesChanged"
            index: 1
            isFinal: true
        }
        Signal { name: "spacingChanged" }
        Signal { name: "uniformCellSizesChanged"; revision: 1542 }
    }
    Component {
        file: "private/qquicklinearlayout_p.h"
        name: "QQuickRowLayout"
        accessSemantics: "reference"
        prototype: "QQuickLinearLayout"
        exports: [
            "QtQuick.Layouts/RowLayout 1.0",
            "QtQuick.Layouts/RowLayout 1.1",
            "QtQuick.Layouts/RowLayout 2.0",
            "QtQuick.Layouts/RowLayout 2.1",
            "QtQuick.Layouts/RowLayout 2.4",
            "QtQuick.Layouts/RowLayout 2.7",
            "QtQuick.Layouts/RowLayout 2.11",
            "QtQuick.Layouts/RowLayout 6.0",
            "QtQuick.Layouts/RowLayout 6.3",
            "QtQuick.Layouts/RowLayout 6.6",
            "QtQuick.Layouts/RowLayout 6.7"
        ]
        exportMetaObjectRevisions: [
            256,
            257,
            512,
            513,
            516,
            519,
            523,
            1536,
            1539,
            1542,
            1543
        ]
    }
    Component {
        file: "private/qquickstacklayout_p.h"
        name: "QQuickStackLayout"
        accessSemantics: "reference"
        prototype: "QQuickLayout"
        exports: [
            "QtQuick.Layouts/StackLayout 1.3",
            "QtQuick.Layouts/StackLayout 2.0",
            "QtQuick.Layouts/StackLayout 2.1",
            "QtQuick.Layouts/StackLayout 2.4",
            "QtQuick.Layouts/StackLayout 2.7",
            "QtQuick.Layouts/StackLayout 2.11",
            "QtQuick.Layouts/StackLayout 6.0",
            "QtQuick.Layouts/StackLayout 6.3",
            "QtQuick.Layouts/StackLayout 6.7"
        ]
        exportMetaObjectRevisions: [
            259,
            512,
            513,
            516,
            519,
            523,
            1536,
            1539,
            1543
        ]
        attachedType: "QQuickStackLayoutAttached"
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "currentIndex"
            type: "int"
            read: "currentIndex"
            write: "setCurrentIndex"
            notify: "currentIndexChanged"
            index: 1
        }
        Signal { name: "currentIndexChanged" }
        Signal { name: "countChanged" }
        Method {
            name: "itemAt"
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        file: "private/qquickstacklayout_p.h"
        name: "QQuickStackLayoutAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "index"
            type: "int"
            read: "index"
            notify: "indexChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "isCurrentItem"
            type: "bool"
            read: "isCurrentItem"
            notify: "isCurrentItemChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "layout"
            type: "QQuickStackLayout"
            isPointer: true
            read: "layout"
            notify: "layoutChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Signal { name: "indexChanged" }
        Signal { name: "isCurrentItemChanged" }
        Signal { name: "layoutChanged" }
    }
}
