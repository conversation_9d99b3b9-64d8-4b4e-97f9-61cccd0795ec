// qbytearraymatcher.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QByteArrayMatcher
{
%TypeHeaderCode
#include <qbytearraymatcher.h>
%End

public:
%If (Qt_6_3_0 -)
    QByteArrayMatcher(const char *pattern /Encoding="None"/, qsizetype length = -1);
%End
%If (- Qt_6_3_0)
    QByteArrayMatcher(const char *pattern /Encoding="None"/, qsizetype length);
%End
%If (Qt_6_3_0 -)
    explicit QByteArrayMatcher(QByteArrayView pattern);
%End
%If (- Qt_6_3_0)
%If (Qt_6_4_0 -)
    explicit QByteArrayMatcher(const QByteArray &pattern);
%End
%End
    QByteArrayMatcher();
    QByteArrayMatcher(const QByteArrayMatcher &other);
    ~QByteArrayMatcher();
    void setPattern(const QByteArray &pattern);
%If (Qt_6_3_0 -)
    qsizetype indexIn(QByteArrayView data, qsizetype from = 0) const;
%End
%If (- Qt_6_3_0)
    qsizetype indexIn(const QByteArray &ba, qsizetype from = 0) const;
%End
    qsizetype indexIn(const char *str /Encoding="None"/, qsizetype len, qsizetype from = 0) const;
    QByteArray pattern() const;
};
