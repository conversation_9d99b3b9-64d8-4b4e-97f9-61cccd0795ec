../../Scripts/__pycache__/pywin32_postinstall.cpython-312.pyc,,
../../Scripts/__pycache__/pywin32_testall.cpython-312.pyc,,
../../Scripts/pywin32_postinstall.py,sha256=u95n7QQUxpCjrZistYE-3gN451zXzopuJna8cXRQ4Jw,28115
../../Scripts/pywin32_testall.py,sha256=-6yvZmd2lPQc4e8i6PgLsr_totF6mScvoq0Jqr0V2fM,3844
__pycache__/pythoncom.cpython-312.pyc,,
adodbapi/__init__.py,sha256=0L_Um9dr60VTy0ISENrUrCCRFBkOqrR8_mqUrMz3KfE,2617
adodbapi/__pycache__/__init__.cpython-312.pyc,,
adodbapi/__pycache__/ado_consts.cpython-312.pyc,,
adodbapi/__pycache__/adodbapi.cpython-312.pyc,,
adodbapi/__pycache__/apibase.cpython-312.pyc,,
adodbapi/__pycache__/is64bit.cpython-312.pyc,,
adodbapi/__pycache__/process_connect_string.cpython-312.pyc,,
adodbapi/__pycache__/schema_table.cpython-312.pyc,,
adodbapi/__pycache__/setup.cpython-312.pyc,,
adodbapi/ado_consts.py,sha256=-ZjSTIoVrj5MInKt6iK8FKqm0HYZcDN3Iq31vWmaFRo,9653
adodbapi/adodbapi.py,sha256=QY3YL77rctfZ_E8nM-8Gt6IURIe02ugjJdzGHZhZKIg,48975
adodbapi/apibase.py,sha256=lbsXjt7M_uymvQXmKnXhPLk-q1m_OGskW4F5xm9dj1Y,27153
adodbapi/examples/__pycache__/db_print.cpython-312.pyc,,
adodbapi/examples/__pycache__/db_table_names.cpython-312.pyc,,
adodbapi/examples/__pycache__/xls_read.cpython-312.pyc,,
adodbapi/examples/__pycache__/xls_write.cpython-312.pyc,,
adodbapi/examples/db_print.py,sha256=LK8YiSZUfYu2oq-ORNUp7KwwBbTXRFSz8mr4VKTgBBQ,2289
adodbapi/examples/db_table_names.py,sha256=tEnGimTs2GKejZff8-zbxT0b31SA9BfA8E0LWSS96ZA,527
adodbapi/examples/xls_read.py,sha256=Wm9Q4zA9HuvfUXfYwGb0050wS9EeKhiSBaVF8ABdUT4,1131
adodbapi/examples/xls_write.py,sha256=id_xuH2YWvw2c22trCJAjPs5gSymeq4FWovSzjpLkrg,1463
adodbapi/is64bit.py,sha256=KLTBXzQHSsDsfStQvCL-XxgTgwlRxJy7R6n-dFmqvFo,1025
adodbapi/license.txt,sha256=XqLyPH8AwwBrrPJnGD6BbY1LbclepXpN2w_4HebYcZ4,26925
adodbapi/process_connect_string.py,sha256=ycFwG_eUhrtun7vmxhRUTka__DzjptR9Ha0kuScbO64,5422
adodbapi/readme.txt,sha256=mWgTrSv8mU4AEdZ1RrSAyQF3XC5KobOSrWNTIwFlPB4,4767
adodbapi/schema_table.py,sha256=w9WvhgjevQKz-tCjgKaIoHGvoEw-VF5jM9meurY7LI0,438
adodbapi/setup.py,sha256=AweR-jbfpJLOBsW_BvNO0u5_Y02BdqWrsWxVyHQUPmo,2146
adodbapi/test/__pycache__/adodbapitest.cpython-312.pyc,,
adodbapi/test/__pycache__/adodbapitestconfig.cpython-312.pyc,,
adodbapi/test/__pycache__/dbapi20.cpython-312.pyc,,
adodbapi/test/__pycache__/is64bit.cpython-312.pyc,,
adodbapi/test/__pycache__/setuptestframework.cpython-312.pyc,,
adodbapi/test/__pycache__/test_adodbapi_dbapi20.cpython-312.pyc,,
adodbapi/test/__pycache__/tryconnection.cpython-312.pyc,,
adodbapi/test/adodbapitest.py,sha256=S6vA9U1x7y5UVr4889CYr_N5MTNynDKwo-d8ql3clv0,58631
adodbapi/test/adodbapitestconfig.py,sha256=O5dFvjUvUR3TbVgWH6WJ_0klKn9wVR3eyXRaHxKJQ54,6516
adodbapi/test/dbapi20.py,sha256=haD5w3ux_4bsmGm9BSv7RS-CDdYHGUhcSIH91ONglbA,33630
adodbapi/test/is64bit.py,sha256=4cV_bAkGOPCifRbl4qyX6Q5Ygpdrq0yehtWWLEXZjT8,1013
adodbapi/test/setuptestframework.py,sha256=oxXEWly-gNSMJFtUdxRRSwgFUZcZFHaHITAocWUHQkA,3015
adodbapi/test/test_adodbapi_dbapi20.py,sha256=JU48fG1aWpYJw3Kr9U0Z5sg9fRZoEHSIg7LQGBdebRc,5948
adodbapi/test/tryconnection.py,sha256=CLGWl3PPthmMeAKCYxt_vUo19bqRo4kuOwaY1RaSdK4,1027
isapi/PyISAPI_loader.dll,sha256=NO3je-TSrw8cizc-xskPuent99DL7ix17M1k9See8mE,69120
isapi/README.txt,sha256=QTg7zJXtuBONNwd65Uv9y9Kjt3v3RahSmpwWEUbiIwM,330
isapi/__init__.py,sha256=Xh2QEWZEv4NmOh-dLvfJ25WtMMRzhPP7A02lQ_phF1A,1267
isapi/__pycache__/__init__.cpython-312.pyc,,
isapi/__pycache__/install.cpython-312.pyc,,
isapi/__pycache__/isapicon.cpython-312.pyc,,
isapi/__pycache__/simple.cpython-312.pyc,,
isapi/__pycache__/threaded_extension.cpython-312.pyc,,
isapi/doc/isapi.html,sha256=AYDPK5YBfQSSjFUbbJn2zK0cJYHfi_fwaUe9p4MiZjM,4239
isapi/install.py,sha256=WB5x2ymRE_tcaE2m-cTD_6empnD8MScjQcuukllHlB8,28252
isapi/isapicon.py,sha256=ioDHC7EVZ7agwAdOZBqfAZU1iLYYv6IZieqMLIbXWzE,4236
isapi/samples/README.txt,sha256=PTPDqgwjJd754woO3V1QuUGrBstA8svXj_yASFbGr6k,1023
isapi/samples/__pycache__/advanced.cpython-312.pyc,,
isapi/samples/__pycache__/redirector.cpython-312.pyc,,
isapi/samples/__pycache__/redirector_asynch.cpython-312.pyc,,
isapi/samples/__pycache__/redirector_with_filter.cpython-312.pyc,,
isapi/samples/__pycache__/test.cpython-312.pyc,,
isapi/samples/advanced.py,sha256=V8kQ81JUumgNE_XXHo5x8s6wPdv_RxPpdqF30ogtotw,8124
isapi/samples/redirector.py,sha256=ipw65m6RcjHkiKMS7GOxIE63VnQXcnAHBto5IsiNDik,4586
isapi/samples/redirector_asynch.py,sha256=ZIfc1bBcMvvYfNPwWV_GkKNQ-qq68l0w89l0OYcWIr4,2811
isapi/samples/redirector_with_filter.py,sha256=sDh2CdJvFQyRl4nESKvE_BD5RmS2LueTDKzo68yYXBU,6571
isapi/samples/test.py,sha256=NSLKDlBkyVHcAidp7KTAsR1aDwNLZDKsii1GdnTpsXk,6513
isapi/simple.py,sha256=xdHaFf_WbSk-AcZyINCFJJgZnuuD6e4ouXbTCoufRPw,2564
isapi/test/README.txt,sha256=dnOLDi56Q9BHGqdFRs_RdOTGs30GeL9YZl-UzyIUTeI,115
isapi/test/__pycache__/extension_simple.cpython-312.pyc,,
isapi/test/extension_simple.py,sha256=DPRrNspfpfwJiNU9xXtFVn5Q09ufQxQ44zMIZObqxF0,4339
isapi/threaded_extension.py,sha256=rb52CK3yvuBuHpTXUfap6Fx0A7_3pWXRfeGa__8pn4s,7524
pythoncom.py,sha256=jZNSoCIsjUCLtqaBgK0SExe4U-zmn1C1pydIsdnU8NQ,143
pythonwin/Pythonwin.exe,sha256=NJ7WX4cGZU7X1h8HxmkFe2pycnYH6THZ942XTYw-wws,58368
pythonwin/dde.pyd,sha256=OWBFR09D2ekCLZQZ6Mv0BFgJUoYAofrjj22LqdjCXZo,97280
pythonwin/license.txt,sha256=EdhGLhNOjv6PS9Ujcikqcv8LZwdT0vSfcWde7Q2_0IU,1540
pythonwin/mfc140u.dll,sha256=2Mod6oYghfAgRoAjDSm_9NFo__Z1q0cA7q9jcE2ZXLM,5653536
pythonwin/pywin/Demos/__pycache__/cmdserver.cpython-312.pyc,,
pythonwin/pywin/Demos/__pycache__/createwin.cpython-312.pyc,,
pythonwin/pywin/Demos/__pycache__/demoutils.cpython-312.pyc,,
pythonwin/pywin/Demos/__pycache__/dibdemo.cpython-312.pyc,,
pythonwin/pywin/Demos/__pycache__/dlgtest.cpython-312.pyc,,
pythonwin/pywin/Demos/__pycache__/dyndlg.cpython-312.pyc,,
pythonwin/pywin/Demos/__pycache__/fontdemo.cpython-312.pyc,,
pythonwin/pywin/Demos/__pycache__/guidemo.cpython-312.pyc,,
pythonwin/pywin/Demos/__pycache__/hiertest.cpython-312.pyc,,
pythonwin/pywin/Demos/__pycache__/menutest.cpython-312.pyc,,
pythonwin/pywin/Demos/__pycache__/objdoc.cpython-312.pyc,,
pythonwin/pywin/Demos/__pycache__/openGLDemo.cpython-312.pyc,,
pythonwin/pywin/Demos/__pycache__/progressbar.cpython-312.pyc,,
pythonwin/pywin/Demos/__pycache__/sliderdemo.cpython-312.pyc,,
pythonwin/pywin/Demos/__pycache__/splittst.cpython-312.pyc,,
pythonwin/pywin/Demos/__pycache__/threadedgui.cpython-312.pyc,,
pythonwin/pywin/Demos/__pycache__/toolbar.cpython-312.pyc,,
pythonwin/pywin/Demos/app/__pycache__/basictimerapp.cpython-312.pyc,,
pythonwin/pywin/Demos/app/__pycache__/customprint.cpython-312.pyc,,
pythonwin/pywin/Demos/app/__pycache__/demoutils.cpython-312.pyc,,
pythonwin/pywin/Demos/app/__pycache__/dlgappdemo.cpython-312.pyc,,
pythonwin/pywin/Demos/app/__pycache__/dojobapp.cpython-312.pyc,,
pythonwin/pywin/Demos/app/__pycache__/helloapp.cpython-312.pyc,,
pythonwin/pywin/Demos/app/basictimerapp.py,sha256=OehZlZxoeFTN9Qhw10wH9o5yLQn399rRA_O4dyGCMRI,7917
pythonwin/pywin/Demos/app/customprint.py,sha256=eMTFwPWzSr5zp4o3RpIBJ5tdkLfRTccXockfcxBVZ3Q,6094
pythonwin/pywin/Demos/app/demoutils.py,sha256=WZjWFvLE_XlRIWp7yGpy5L05DennJxNCu0kA0p7xt_I,1514
pythonwin/pywin/Demos/app/dlgappdemo.py,sha256=BsB-H2RV5hiszuoPUEgiMddpaUKheoVPE6wpbJoFxts,1429
pythonwin/pywin/Demos/app/dojobapp.py,sha256=6tFP3TBat-UOYvmoh7zVZoaxqA8VLFA8WKI3MF6kj20,1555
pythonwin/pywin/Demos/app/helloapp.py,sha256=ap4sUJRzGfxp_2J3N6Y_xI2IT4STzSskFosRih7Bzkc,1664
pythonwin/pywin/Demos/cmdserver.py,sha256=8ZSf7_zibyJiAJzncHhHjK0YVh_8n-m_nmkKUgFMHpA,3192
pythonwin/pywin/Demos/createwin.py,sha256=Re72IXdT7MISybyKI-vWWnU-WgbyxU4nsdV5uL6EOGQ,2621
pythonwin/pywin/Demos/demoutils.py,sha256=WZjWFvLE_XlRIWp7yGpy5L05DennJxNCu0kA0p7xt_I,1514
pythonwin/pywin/Demos/dibdemo.py,sha256=88AlSi1PuZxItiAr_-9aNVKwqqR4NKYhrmweaiZJ8Tk,2364
pythonwin/pywin/Demos/dlgtest.py,sha256=T0z2oqAn4BKmrZWIGV85JPzO2gkE8YzbnlWOiiRlbys,4709
pythonwin/pywin/Demos/dyndlg.py,sha256=DHgQgVq7JiY7XTSmfzxO_fWJFSUXGKNfypajmH47BzQ,2925
pythonwin/pywin/Demos/fontdemo.py,sha256=mT7JdmojwGERVtGd0ELppihVrkYmjMVRLRzQKVwXg9Y,2851
pythonwin/pywin/Demos/guidemo.py,sha256=ECv9UHWBV6iCj-_5Q__1dGkL93tJBB8v9N-hAakM8EA,2669
pythonwin/pywin/Demos/hiertest.py,sha256=czJN4WJTP5xpMVLJrzHzXJbEZPTw0oCF4PoIMP2yZ1c,3904
pythonwin/pywin/Demos/menutest.py,sha256=6r5SLYuSXBIs5cqmLKPpT71GuAwMqXDoZ7efE5CFqQ8,503
pythonwin/pywin/Demos/objdoc.py,sha256=wAqP-bmFfuCHcK-5Rv81UKOZCaOGy2kcLmitanyyTIY,1784
pythonwin/pywin/Demos/ocx/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pythonwin/pywin/Demos/ocx/__pycache__/__init__.cpython-312.pyc,,
pythonwin/pywin/Demos/ocx/__pycache__/demoutils.cpython-312.pyc,,
pythonwin/pywin/Demos/ocx/__pycache__/flash.cpython-312.pyc,,
pythonwin/pywin/Demos/ocx/__pycache__/msoffice.cpython-312.pyc,,
pythonwin/pywin/Demos/ocx/__pycache__/ocxserialtest.cpython-312.pyc,,
pythonwin/pywin/Demos/ocx/__pycache__/ocxtest.cpython-312.pyc,,
pythonwin/pywin/Demos/ocx/__pycache__/webbrowser.cpython-312.pyc,,
pythonwin/pywin/Demos/ocx/demoutils.py,sha256=zWynEmbGV47X6YEPRLNheLUSgFKKsXzytKxdrgQNFms,1521
pythonwin/pywin/Demos/ocx/flash.py,sha256=fJXyKTlPkWtMhMzIkMq-YO6rPKoasNM0wyUio9o6E2Q,3087
pythonwin/pywin/Demos/ocx/msoffice.py,sha256=OKvNCBDH0ReuqvYCp_2ITq0TZlWd2op-QA-h8-0LaIA,5488
pythonwin/pywin/Demos/ocx/ocxserialtest.py,sha256=yfEGZ9VuFHfoj7EP8aOS5hSn__c0Yydjl0m-Ab4llsI,3744
pythonwin/pywin/Demos/ocx/ocxtest.py,sha256=GBBx183Z0ZWABku47EDWZAbKLp6yrOyRF4TJk_XD_vM,6964
pythonwin/pywin/Demos/ocx/webbrowser.py,sha256=72xHrZYC9MLqru-uyrUYQhf6cY4fNw5HaapUWPXmMoo,2362
pythonwin/pywin/Demos/openGLDemo.py,sha256=EsjV5uGo50wMSlEUZDHxf2_HfIJTbpo3E4MO3HGBAIE,10129
pythonwin/pywin/Demos/progressbar.py,sha256=37EBolFhACa7GilW2zNuRsx9-NIFk8wkzjm9zFlDo0w,2560
pythonwin/pywin/Demos/sliderdemo.py,sha256=m7zIf7hptDqsAQiAFR2yR7m7wJNMnJ7uqPQwQKEhFjg,2267
pythonwin/pywin/Demos/splittst.py,sha256=_5QLEoowtIRyHDypfGhZadUS1YhY3_vW1zzRvd6WRps,2924
pythonwin/pywin/Demos/threadedgui.py,sha256=ttdFyBW8uv10bSTBKLebsdUVkaLLRGl0YWjHMUY1c9I,6433
pythonwin/pywin/Demos/toolbar.py,sha256=6kfea1vyOGXJ-w4Ry2qMaOCHjxS3LPKQBAGC4kQX1Hc,3273
pythonwin/pywin/IDLE.cfg,sha256=f8UR0TurGmhAiLY-uXN16GtpOHWuksbaMAROBmMzZcg,769
pythonwin/pywin/__init__.py,sha256=5k58_x5AUlngsR9sFUH5PFwSROsiVAKCo-osZNk4zCI,147
pythonwin/pywin/__pycache__/__init__.cpython-312.pyc,,
pythonwin/pywin/debugger/__init__.py,sha256=4OcMpzLb0MJM9827f6XF2ko4aV3Ap0Zdei04sTKLCFI,3173
pythonwin/pywin/debugger/__pycache__/__init__.cpython-312.pyc,,
pythonwin/pywin/debugger/__pycache__/configui.cpython-312.pyc,,
pythonwin/pywin/debugger/__pycache__/dbgcon.cpython-312.pyc,,
pythonwin/pywin/debugger/__pycache__/dbgpyapp.cpython-312.pyc,,
pythonwin/pywin/debugger/__pycache__/debugger.cpython-312.pyc,,
pythonwin/pywin/debugger/__pycache__/fail.cpython-312.pyc,,
pythonwin/pywin/debugger/configui.py,sha256=5exN9Ztk0oAU6Hdla9g_1BmtrfikJDZPjUZ7ziuClmU,1217
pythonwin/pywin/debugger/dbgcon.py,sha256=Y8jYB7BopVNNw0XNjGQsHaZ2c4bZBK7w6HsZQL3iPPg,877
pythonwin/pywin/debugger/dbgpyapp.py,sha256=1s_Xxh05oJ8_priOSWgtBot-iuMDGemBxp6wyL8Qh00,1601
pythonwin/pywin/debugger/debugger.py,sha256=DEf2Dh0T5WVxYTd0YDPrAb_uWpaX3A3-exHhBrpte2E,38796
pythonwin/pywin/debugger/fail.py,sha256=qTAaxMn5QUwFPZzrr0HyLpb3wjsgrzu_oGmH99bwLrU,982
pythonwin/pywin/default.cfg,sha256=rTY4l0qdsGCOPSkuKAPAzegK4oV-tAkCUfUg1o-BIR4,6995
pythonwin/pywin/dialogs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pythonwin/pywin/dialogs/__pycache__/__init__.cpython-312.pyc,,
pythonwin/pywin/dialogs/__pycache__/ideoptions.cpython-312.pyc,,
pythonwin/pywin/dialogs/__pycache__/list.cpython-312.pyc,,
pythonwin/pywin/dialogs/__pycache__/login.cpython-312.pyc,,
pythonwin/pywin/dialogs/__pycache__/status.cpython-312.pyc,,
pythonwin/pywin/dialogs/ideoptions.py,sha256=jx_lnIf3fw6Ui7PGqstBz88DF1-IN1GIdN2pmk87-ao,5209
pythonwin/pywin/dialogs/list.py,sha256=vvnm1F1sOUVkyxPD-KM3fupjWMV0YaMBqMsO6Xm4td8,4689
pythonwin/pywin/dialogs/login.py,sha256=XF6f-OKZbTYODK4JRIo-OTTTx1jVvVolBa_5P3hBcTo,4899
pythonwin/pywin/dialogs/status.py,sha256=tRXJQPPSfbw5HrC-YmjooNEDUMHEV8v3EHrbQdTtcM0,6862
pythonwin/pywin/docking/DockingBar.py,sha256=8c8int8IKjH7LhMWYFjou27hf6d5PviKeQjhBciRKZY,24017
pythonwin/pywin/docking/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pythonwin/pywin/docking/__pycache__/DockingBar.cpython-312.pyc,,
pythonwin/pywin/docking/__pycache__/__init__.cpython-312.pyc,,
pythonwin/pywin/framework/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pythonwin/pywin/framework/__pycache__/__init__.cpython-312.pyc,,
pythonwin/pywin/framework/__pycache__/app.cpython-312.pyc,,
pythonwin/pywin/framework/__pycache__/bitmap.cpython-312.pyc,,
pythonwin/pywin/framework/__pycache__/cmdline.cpython-312.pyc,,
pythonwin/pywin/framework/__pycache__/dbgcommands.cpython-312.pyc,,
pythonwin/pywin/framework/__pycache__/dlgappcore.cpython-312.pyc,,
pythonwin/pywin/framework/__pycache__/help.cpython-312.pyc,,
pythonwin/pywin/framework/__pycache__/interact.cpython-312.pyc,,
pythonwin/pywin/framework/__pycache__/intpyapp.cpython-312.pyc,,
pythonwin/pywin/framework/__pycache__/intpydde.cpython-312.pyc,,
pythonwin/pywin/framework/__pycache__/mdi_pychecker.cpython-312.pyc,,
pythonwin/pywin/framework/__pycache__/scriptutils.cpython-312.pyc,,
pythonwin/pywin/framework/__pycache__/sgrepmdi.cpython-312.pyc,,
pythonwin/pywin/framework/__pycache__/startup.cpython-312.pyc,,
pythonwin/pywin/framework/__pycache__/stdin.cpython-312.pyc,,
pythonwin/pywin/framework/__pycache__/toolmenu.cpython-312.pyc,,
pythonwin/pywin/framework/__pycache__/window.cpython-312.pyc,,
pythonwin/pywin/framework/__pycache__/winout.cpython-312.pyc,,
pythonwin/pywin/framework/app.py,sha256=XA47u8V5GoP1pikyxmFb9swHXu_uVM2MBM4VyJnI-nI,15302
pythonwin/pywin/framework/bitmap.py,sha256=HHKwcTNx4HwaEGtZx4Og-a11tumRww1Ca0_t5aMVPUk,5679
pythonwin/pywin/framework/cmdline.py,sha256=384-bMUQqbPZ9KzlbOzwBxP68pIboDssB7rgXVZBhJM,1508
pythonwin/pywin/framework/dbgcommands.py,sha256=43gUCnsx5xdbVDMnihAQCDM1FTlYWNngIw3ooqSlI58,7020
pythonwin/pywin/framework/dlgappcore.py,sha256=HHGD9b5OHCqEPXSxMS6Fn9JRQ11qftOU6P1gQbBPGrE,2222
pythonwin/pywin/framework/editor/ModuleBrowser.py,sha256=N4fn6NPa0ukCoGHxOb62ieuVw6rcBbcAdU2TU2RGnsM,7291
pythonwin/pywin/framework/editor/__init__.py,sha256=oQ5qsl28M7NOnUf2QU7ISqtpFyMzExpyfAtZSN1_J7o,3028
pythonwin/pywin/framework/editor/__pycache__/ModuleBrowser.cpython-312.pyc,,
pythonwin/pywin/framework/editor/__pycache__/__init__.cpython-312.pyc,,
pythonwin/pywin/framework/editor/__pycache__/configui.cpython-312.pyc,,
pythonwin/pywin/framework/editor/__pycache__/document.cpython-312.pyc,,
pythonwin/pywin/framework/editor/__pycache__/editor.cpython-312.pyc,,
pythonwin/pywin/framework/editor/__pycache__/frame.cpython-312.pyc,,
pythonwin/pywin/framework/editor/__pycache__/template.cpython-312.pyc,,
pythonwin/pywin/framework/editor/__pycache__/vss.cpython-312.pyc,,
pythonwin/pywin/framework/editor/color/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pythonwin/pywin/framework/editor/color/__pycache__/__init__.cpython-312.pyc,,
pythonwin/pywin/framework/editor/color/__pycache__/coloreditor.cpython-312.pyc,,
pythonwin/pywin/framework/editor/color/coloreditor.py,sha256=WaRNRhMvgt9BU6fI2GRE34jpFmuKBpBr_J0skI7B7OU,26029
pythonwin/pywin/framework/editor/configui.py,sha256=xjktaWwgOkF_re6GzVGY0pw4Dv92x5-QKkcsAvP8jug,11765
pythonwin/pywin/framework/editor/document.py,sha256=NBeLrimD5z4muxL5T0e989lm6Gsh5O_zlqTZeKBmTiY,15172
pythonwin/pywin/framework/editor/editor.py,sha256=0QHwaLqUTPVYRwUTv84QmeYVpsfSadg8lvyvEJ-CWyU,18746
pythonwin/pywin/framework/editor/frame.py,sha256=pL_-8ekJEZ51oC3rFPzhVjfH4iFv8zde6T8AU5G8K7Q,3162
pythonwin/pywin/framework/editor/template.py,sha256=79jYuoo8ZePg5-KOqB-SpQzKXwhJVNldBkRxOduGh9E,2127
pythonwin/pywin/framework/editor/vss.py,sha256=1niW3JUZ6KtjlLJtahkdv-xMdNqmpgfbGjkkWgbbhGA,3490
pythonwin/pywin/framework/help.py,sha256=Lqxmo0KpxkToBuQkPXDONYFUABaEJKGUy6NB6SWQ3oE,5791
pythonwin/pywin/framework/interact.py,sha256=NRSjNToKW5WF9QAUDF00AJhqJ5M9tCYKlPBf6sCwOhQ,37223
pythonwin/pywin/framework/intpyapp.py,sha256=B367VRue11WvIsrGSN4uhEru5kTfBJLWZ72a0dmLx-I,20673
pythonwin/pywin/framework/intpydde.py,sha256=hMweIkKePsFPopJNDCOFGbI91l07JJkGXZBVnyrCWpU,1622
pythonwin/pywin/framework/mdi_pychecker.py,sha256=nL1vIYS4Rt8OfkftfcGJr5IL0OPLZnckjsvKlBpmGr8,29794
pythonwin/pywin/framework/scriptutils.py,sha256=6eD1kYqASDpt5_X_A5ULmcQa3mwO_0KLgvpEvptXdnY,23742
pythonwin/pywin/framework/sgrepmdi.py,sha256=fZ2zQlnAg6Z71pAXbF9hAqoEbbfvsNpWpYCiuUkg3nU,25680
pythonwin/pywin/framework/startup.py,sha256=EFxx61aPTY6yEzerAEehyEtgO2_7vgq_UqzL1epu86M,2582
pythonwin/pywin/framework/stdin.py,sha256=FRbM2XgK1fsCin-i6ADVohdD_iUTu4zAMCmahXF0tFg,6623
pythonwin/pywin/framework/toolmenu.py,sha256=KpNPpfODcJ91CbPDYIjVCO6vFxT9Yxym278D3cbf4Z4,9527
pythonwin/pywin/framework/window.py,sha256=zuXZIWd7sYN17NDCNGLzKXQKMuKcRAo3mMDNofYVf1c,549
pythonwin/pywin/framework/winout.py,sha256=BuVtryahETE6ldV7TvW0VEa7jRcYUF5CMPxhisfQjn0,20821
pythonwin/pywin/idle/AutoExpand.py,sha256=ycWMvPVvWrbwZ3s6B59oKiXL7-KvTlyy4R7k1hn9taU,2806
pythonwin/pywin/idle/AutoIndent.py,sha256=4ldVT_SnPZp929CcR8sAsUP5iBHq-LlCmX0Xo_NNyZ0,20683
pythonwin/pywin/idle/CallTips.py,sha256=RPnmVz_s1p2nuQ4Yh-meFSpDSQ8X6YYQ7ZY6ac4QKpI,6541
pythonwin/pywin/idle/FormatParagraph.py,sha256=IJ6frapL54gHF83T9wMtC2bXv-jV3RuLeg-uyoIEtp0,5850
pythonwin/pywin/idle/IdleHistory.py,sha256=FoGAkZcsENQxT0XEl7RwAOAH27Nn9ICwOtRPF5JoJbQ,3138
pythonwin/pywin/idle/PyParse.py,sha256=4pouJ8bqiN2sIvoBYfu2Twzxi6RTVCERaQUJHDe8u1k,18708
pythonwin/pywin/idle/__init__.py,sha256=HXPbAC-BAMLGZ5rkKlkAxULmQ_i5wSOJcNqsaW9Pqdk,56
pythonwin/pywin/idle/__pycache__/AutoExpand.cpython-312.pyc,,
pythonwin/pywin/idle/__pycache__/AutoIndent.cpython-312.pyc,,
pythonwin/pywin/idle/__pycache__/CallTips.cpython-312.pyc,,
pythonwin/pywin/idle/__pycache__/FormatParagraph.cpython-312.pyc,,
pythonwin/pywin/idle/__pycache__/IdleHistory.cpython-312.pyc,,
pythonwin/pywin/idle/__pycache__/PyParse.cpython-312.pyc,,
pythonwin/pywin/idle/__pycache__/__init__.cpython-312.pyc,,
pythonwin/pywin/mfc/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pythonwin/pywin/mfc/__pycache__/__init__.cpython-312.pyc,,
pythonwin/pywin/mfc/__pycache__/activex.cpython-312.pyc,,
pythonwin/pywin/mfc/__pycache__/afxres.cpython-312.pyc,,
pythonwin/pywin/mfc/__pycache__/dialog.cpython-312.pyc,,
pythonwin/pywin/mfc/__pycache__/docview.cpython-312.pyc,,
pythonwin/pywin/mfc/__pycache__/object.cpython-312.pyc,,
pythonwin/pywin/mfc/__pycache__/thread.cpython-312.pyc,,
pythonwin/pywin/mfc/__pycache__/window.cpython-312.pyc,,
pythonwin/pywin/mfc/activex.py,sha256=Rmv-mmZGslMYLPVKqkw484cod8b0JtPlbiZFPGp5jXI,2692
pythonwin/pywin/mfc/afxres.py,sha256=IW5gB60xCjAmEChmhup-9FGqZwQu900rld_ljO0KVxg,15595
pythonwin/pywin/mfc/dialog.py,sha256=ZCqPBaGefUiHng1I1pyVkcl5112eilkixIwdQfhbj2I,9287
pythonwin/pywin/mfc/docview.py,sha256=evdfNHfC3D1PeC7zOlg_p-nIZpA14a6PJLWwghN6dGw,4240
pythonwin/pywin/mfc/object.py,sha256=smXeJHzUcz3Taidw21e8I45KEFCXRDZfuwouf-Xo-HY,2244
pythonwin/pywin/mfc/thread.py,sha256=D8sRSs6oaNpzxAfCslLe3iTjbukmVg2k03wBWiL2JG4,613
pythonwin/pywin/mfc/window.py,sha256=EI_EjjZT946FUXtSeitR_TNtU2fXtf-w1SXShwbp62o,1549
pythonwin/pywin/scintilla/IDLEenvironment.py,sha256=4sVjwJmvNpazm32obUE0S58VEr1OBT-yamH1IbRqtOc,20126
pythonwin/pywin/scintilla/__init__.py,sha256=2ZvGzdW7YjDsTGTfTFhxkzSwQk8SFNG_8WudIf6zfWU,17
pythonwin/pywin/scintilla/__pycache__/IDLEenvironment.cpython-312.pyc,,
pythonwin/pywin/scintilla/__pycache__/__init__.cpython-312.pyc,,
pythonwin/pywin/scintilla/__pycache__/bindings.cpython-312.pyc,,
pythonwin/pywin/scintilla/__pycache__/config.cpython-312.pyc,,
pythonwin/pywin/scintilla/__pycache__/configui.cpython-312.pyc,,
pythonwin/pywin/scintilla/__pycache__/control.cpython-312.pyc,,
pythonwin/pywin/scintilla/__pycache__/document.cpython-312.pyc,,
pythonwin/pywin/scintilla/__pycache__/find.cpython-312.pyc,,
pythonwin/pywin/scintilla/__pycache__/formatter.cpython-312.pyc,,
pythonwin/pywin/scintilla/__pycache__/keycodes.cpython-312.pyc,,
pythonwin/pywin/scintilla/__pycache__/scintillacon.cpython-312.pyc,,
pythonwin/pywin/scintilla/__pycache__/view.cpython-312.pyc,,
pythonwin/pywin/scintilla/bindings.py,sha256=d4V6O0C505ANCPsCu5z1PtU9SItGDXFLtJ7pGNI3ACU,6157
pythonwin/pywin/scintilla/config.py,sha256=0vd5ZqfOK77nA9hRLJI_X2mGpGfH6YSJ1hctJK9quTQ,12776
pythonwin/pywin/scintilla/configui.py,sha256=2UolnV9RYbM8OAh4aBiOP6MOpiOQENk85rYtpq40aRM,11483
pythonwin/pywin/scintilla/control.py,sha256=41a_6Y1gpTMFqf2trpcsEg--rKQvV-ognBJOaeggiYk,20920
pythonwin/pywin/scintilla/document.py,sha256=bgf_uGe8Q4wIQ-4Hre_B3OxoM_Pl_jJjYQDiGAYKC3s,11732
pythonwin/pywin/scintilla/find.py,sha256=kkQTzqucGL2iKGK9sTF8M_i_64zSr0LWLl_b8ebd4kE,17302
pythonwin/pywin/scintilla/formatter.py,sha256=jlPH7qLnfKDoCcQjqU-u8EjARQyM2y-yvpB4uT8o3Zk,27218
pythonwin/pywin/scintilla/keycodes.py,sha256=6u38fNwjboFUY_iB8Bx3IMuLegu6haxgPnA9Wk416DI,5473
pythonwin/pywin/scintilla/scintillacon.py,sha256=m6cOL4LHbwg_XbSyDdvj5-AtrWP4kxbEV40Hitf5FXA,46897
pythonwin/pywin/scintilla/view.py,sha256=X6ps1w93TUlD8PX-UhHRiNy0kXgKthZx72tK7bwA-NM,31844
pythonwin/pywin/tools/TraceCollector.py,sha256=ohcs6wr_8ectgE6wbM_z22oJyl6TGtD2CEVtmOtUw6I,2520
pythonwin/pywin/tools/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pythonwin/pywin/tools/__pycache__/TraceCollector.cpython-312.pyc,,
pythonwin/pywin/tools/__pycache__/__init__.cpython-312.pyc,,
pythonwin/pywin/tools/__pycache__/browseProjects.cpython-312.pyc,,
pythonwin/pywin/tools/__pycache__/browser.cpython-312.pyc,,
pythonwin/pywin/tools/__pycache__/hierlist.cpython-312.pyc,,
pythonwin/pywin/tools/__pycache__/regedit.cpython-312.pyc,,
pythonwin/pywin/tools/__pycache__/regpy.cpython-312.pyc,,
pythonwin/pywin/tools/browseProjects.py,sha256=3jNB3nLqSFN2OLFlMqs_WzPZ0iK2oq7bm1MT1eROTng,9819
pythonwin/pywin/tools/browser.py,sha256=LYthe0YwG2OLL5JW9UORN6nokxV0Q7efx2P--4UgvL8,13657
pythonwin/pywin/tools/hierlist.py,sha256=l6ewm66Exav-cwxlYkhDRLnocqFiMLM4YE_guHPM0cI,12260
pythonwin/pywin/tools/regedit.py,sha256=_0A-DhFIYcmYejTMgUvOMCp-XjMLVWdUY1SGFVOPx7Y,13533
pythonwin/pywin/tools/regpy.py,sha256=W8JYCAI4Z0KpFA7feQHMp1BicALJQrrj0cJSAwarv9E,2307
pythonwin/scintilla.dll,sha256=u6R261Rde8yI1IQGsgXDjN3KG7h9ERldx7c26E79_WI,647680
pythonwin/start_pythonwin.pyw,sha256=e9s-cTpkOcErB5RST2ZsHoUBpGnBX0WtI00BeY7KEqo,589
pythonwin/win32ui.pyd,sha256=qonekMDR98tAGMUn1MGmc1asZwUlydUJsAOsxB4sxKw,1044992
pythonwin/win32uiole.pyd,sha256=mww6X4fALAncr5PE02b1GMc_WcxhncRQAdDXXDSP_Xk,74240
pywin32-308.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pywin32-308.dist-info/METADATA,sha256=9dwVf3n5HQysA_mLJm2HUNXz_ysSPtzbGRGh58CtkwM,8323
pywin32-308.dist-info/RECORD,,
pywin32-308.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pywin32-308.dist-info/WHEEL,sha256=3vidnDuZ-QSnHIxLhNbI1gIM-KgyEcMHuZuv1mWPd_Q,101
pywin32-308.dist-info/top_level.txt,sha256=9fOhB-8eoAwdlKPKLOMPmKEmYsfmTwmlnnDDEst23_g,1335
pywin32.pth,sha256=2QJYSioKUhbOEscS0TeP4HVB0yw4PQzFq81oQSFE_k0,185
pywin32.version.txt,sha256=lrAOJp659c04ATWs4W-JSeIv3FxwbLYNMnj9MkJSOag,5
pywin32_system32/pythoncom312.dll,sha256=PZFIzvfQSn3AsuPJV_qr9fl1tj7vPoyAdJOZvvFEF2w,678400
pywin32_system32/pywintypes312.dll,sha256=LR1guZbR1cVsJDE9l-D82kGovWvwKZ9upOtKHiXUkLc,136192
win32/Demos/BackupRead_BackupWrite.py,sha256=zA1vJAVLU2W8g0WqHgnvmHs4oOu7W2H262G7QZSFlxE,3829
win32/Demos/BackupSeek_streamheaders.py,sha256=c2DN6YZkFbxGvuhBSNuVoPhe8iO5Ri5tMpRDJqXhbXA,4001
win32/Demos/CopyFileEx.py,sha256=GL4nKio9kLlYX6fW3lGJvtupNCfn45yb-Ngg5iStDSs,1300
win32/Demos/CreateFileTransacted_MiniVersion.py,sha256=j13roHU33YDNSXbz9SPfCwibMipo39rWjbVGo1Jx1lQ,3717
win32/Demos/EvtFormatMessage.py,sha256=j_sY0AUa1eT21rzs-RVj6G1YtvCXm6IwCybmqUmbcDo,3403
win32/Demos/EvtSubscribe_pull.py,sha256=Q0EGeb1jj0L6o7iOf2ntPXY5k6t4fsPIiY8K7NdEhLM,820
win32/Demos/EvtSubscribe_push.py,sha256=hdPfM3AXGxdqGWRUKqZbdQ4cv32BiKIuYSEHQp6tK5o,717
win32/Demos/FileSecurityTest.py,sha256=YeLSY6D9E0LMkPaKh-gcIv5SmMVdT4KvXw1Q6JyRnSA,4523
win32/Demos/GetSaveFileName.py,sha256=aMIxJEXKK8EfeGtfmZSvhn2PGPnBhVDgn_MqtUlfUZI,1227
win32/Demos/NetValidatePasswordPolicy.py,sha256=ebcZ6wM_pxD5NDjPKbZSKmNYBB-B51FJl1J34g5Ca-U,3635
win32/Demos/OpenEncryptedFileRaw.py,sha256=CRo-xRwjC7-czg1ca-peQmgQGQwftmI7ach6raJPdEU,2024
win32/Demos/RegCreateKeyTransacted.py,sha256=XLrSJTl1dW7sLwszEcpRd4Psl6jMQikC8bpVvprAtvk,1972
win32/Demos/RegRestoreKey.py,sha256=Laz20DabelrPu2B-elo2HKHVilJteaHRiG87ty51VqE,2134
win32/Demos/SystemParametersInfo.py,sha256=R8N6m_JLBN4Y77m5g-3pgrSEx1Pf4_VfagZOLZTGZZc,8162
win32/Demos/__pycache__/BackupRead_BackupWrite.cpython-312.pyc,,
win32/Demos/__pycache__/BackupSeek_streamheaders.cpython-312.pyc,,
win32/Demos/__pycache__/CopyFileEx.cpython-312.pyc,,
win32/Demos/__pycache__/CreateFileTransacted_MiniVersion.cpython-312.pyc,,
win32/Demos/__pycache__/EvtFormatMessage.cpython-312.pyc,,
win32/Demos/__pycache__/EvtSubscribe_pull.cpython-312.pyc,,
win32/Demos/__pycache__/EvtSubscribe_push.cpython-312.pyc,,
win32/Demos/__pycache__/FileSecurityTest.cpython-312.pyc,,
win32/Demos/__pycache__/GetSaveFileName.cpython-312.pyc,,
win32/Demos/__pycache__/NetValidatePasswordPolicy.cpython-312.pyc,,
win32/Demos/__pycache__/OpenEncryptedFileRaw.cpython-312.pyc,,
win32/Demos/__pycache__/RegCreateKeyTransacted.cpython-312.pyc,,
win32/Demos/__pycache__/RegRestoreKey.cpython-312.pyc,,
win32/Demos/__pycache__/SystemParametersInfo.cpython-312.pyc,,
win32/Demos/__pycache__/desktopmanager.cpython-312.pyc,,
win32/Demos/__pycache__/eventLogDemo.cpython-312.pyc,,
win32/Demos/__pycache__/getfilever.cpython-312.pyc,,
win32/Demos/__pycache__/mmapfile_demo.cpython-312.pyc,,
win32/Demos/__pycache__/print_desktop.cpython-312.pyc,,
win32/Demos/__pycache__/rastest.cpython-312.pyc,,
win32/Demos/__pycache__/timer_demo.cpython-312.pyc,,
win32/Demos/__pycache__/win32clipboardDemo.cpython-312.pyc,,
win32/Demos/__pycache__/win32clipboard_bitmapdemo.cpython-312.pyc,,
win32/Demos/__pycache__/win32comport_demo.cpython-312.pyc,,
win32/Demos/__pycache__/win32console_demo.cpython-312.pyc,,
win32/Demos/__pycache__/win32cred_demo.cpython-312.pyc,,
win32/Demos/__pycache__/win32fileDemo.cpython-312.pyc,,
win32/Demos/__pycache__/win32gui_demo.cpython-312.pyc,,
win32/Demos/__pycache__/win32gui_devicenotify.cpython-312.pyc,,
win32/Demos/__pycache__/win32gui_dialog.cpython-312.pyc,,
win32/Demos/__pycache__/win32gui_menu.cpython-312.pyc,,
win32/Demos/__pycache__/win32gui_taskbar.cpython-312.pyc,,
win32/Demos/__pycache__/win32netdemo.cpython-312.pyc,,
win32/Demos/__pycache__/win32rcparser_demo.cpython-312.pyc,,
win32/Demos/__pycache__/win32servicedemo.cpython-312.pyc,,
win32/Demos/__pycache__/win32ts_logoff_disconnected.cpython-312.pyc,,
win32/Demos/__pycache__/winprocess.cpython-312.pyc,,
win32/Demos/c_extension/__pycache__/setup.cpython-312.pyc,,
win32/Demos/c_extension/setup.py,sha256=b7MnUcX_AW7vO4VP-R9VDC4k1cltYuRsDbN2rvmhiPM,789
win32/Demos/dde/__pycache__/ddeclient.cpython-312.pyc,,
win32/Demos/dde/__pycache__/ddeserver.cpython-312.pyc,,
win32/Demos/dde/ddeclient.py,sha256=6FJFClhU5408XFhjGJKWCXvjqbJ96Bwq1AfIzxK141s,446
win32/Demos/dde/ddeserver.py,sha256=tjxIe58z19l2wVAmhH1jn8kYADeKSxEuYpLlr0J5_r8,1135
win32/Demos/desktopmanager.py,sha256=TBUOimaZ_FASbKT_nFdxmP0wpkIU0agvqnKT6vNnuEc,8363
win32/Demos/eventLogDemo.py,sha256=xZ5ltihKBywrdO6DpsjvObSp-mlk_Ic3h133TNF6DvU,4522
win32/Demos/getfilever.py,sha256=7yrzCXBUQ-Ez1dxMPy1WXCEh3Iqkhx2WoYvJ-mipABI,1154
win32/Demos/images/frowny.bmp,sha256=RgGveVt053KlmV4qVGwdCtrPyRA0JT57KQvf9PNOIvU,3126
win32/Demos/images/smiley.bmp,sha256=wi53jYCy523fFYj_FYgzG1dxQdErw-ow2__dfoX9gsA,3126
win32/Demos/mmapfile_demo.py,sha256=K54G08lGwiOqlRLqUbsGJy3zBbubQE_hwDt4p69Ze0U,2870
win32/Demos/pipes/__pycache__/cat.cpython-312.pyc,,
win32/Demos/pipes/__pycache__/runproc.cpython-312.pyc,,
win32/Demos/pipes/cat.py,sha256=g0SJ53yfYY7wJgQ1srdnr1zCQuwcG1VQr6-Cb80MHBw,352
win32/Demos/pipes/runproc.py,sha256=deDMUm31BwSXDr1WA9lwlCFJljInJhuxNsDQRxrozl0,4143
win32/Demos/print_desktop.py,sha256=FYDuNxsKKMZSrzE3K8Bd-xB3kipuRaSX9DiR33OHugs,2972
win32/Demos/rastest.py,sha256=pZAtvG8c2o_h3p5EQ8pyhRRQ2c6LuhK00s94Pv6-43I,5266
win32/Demos/security/GetTokenInformation.py,sha256=ISR_82BQqBdV5cgdez-ekrlyEdu-h_lpPxk2YNkNEcQ,3848
win32/Demos/security/__pycache__/GetTokenInformation.cpython-312.pyc,,
win32/Demos/security/__pycache__/account_rights.cpython-312.pyc,,
win32/Demos/security/__pycache__/explicit_entries.cpython-312.pyc,,
win32/Demos/security/__pycache__/get_policy_info.cpython-312.pyc,,
win32/Demos/security/__pycache__/list_rights.cpython-312.pyc,,
win32/Demos/security/__pycache__/localized_names.cpython-312.pyc,,
win32/Demos/security/__pycache__/lsaregevent.cpython-312.pyc,,
win32/Demos/security/__pycache__/lsastore.cpython-312.pyc,,
win32/Demos/security/__pycache__/query_information.cpython-312.pyc,,
win32/Demos/security/__pycache__/regsave_sa.cpython-312.pyc,,
win32/Demos/security/__pycache__/regsecurity.cpython-312.pyc,,
win32/Demos/security/__pycache__/sa_inherit.cpython-312.pyc,,
win32/Demos/security/__pycache__/security_enums.cpython-312.pyc,,
win32/Demos/security/__pycache__/set_file_audit.cpython-312.pyc,,
win32/Demos/security/__pycache__/set_file_owner.cpython-312.pyc,,
win32/Demos/security/__pycache__/set_policy_info.cpython-312.pyc,,
win32/Demos/security/__pycache__/setkernelobjectsecurity.cpython-312.pyc,,
win32/Demos/security/__pycache__/setnamedsecurityinfo.cpython-312.pyc,,
win32/Demos/security/__pycache__/setsecurityinfo.cpython-312.pyc,,
win32/Demos/security/__pycache__/setuserobjectsecurity.cpython-312.pyc,,
win32/Demos/security/account_rights.py,sha256=k1XksqZJyhk03NtS-Ubo9iHzeibQ44ieE128LLSrR4Q,1652
win32/Demos/security/explicit_entries.py,sha256=HKS10XQ2adHlQQpcUZyH8KkmJodjQ5qYdGEhPL1gI9c,5153
win32/Demos/security/get_policy_info.py,sha256=_SIluUzGJiXDe65-2eE9tHxbmwPv8qdqWDuFjYzxNy8,1284
win32/Demos/security/list_rights.py,sha256=vDsLFVL2nAzFlu40NfpscICIeXVOVTjApKeusAChsMg,1179
win32/Demos/security/localized_names.py,sha256=rdwo8MYxlAvy3I6FzfTdRLM0_sAzDvkkEaI7mo5ygI0,2069
win32/Demos/security/lsaregevent.py,sha256=qbaXi4LuE3TfghhaA5wNycD99POftDL7Hl-1fo-IRYw,546
win32/Demos/security/lsastore.py,sha256=sB5REt96fufZ5k7ScSagSa_ha_nXa17N6uGg8QYjEDY,484
win32/Demos/security/query_information.py,sha256=lhpzbgd2ZpGm6w1HWzAPAtsKfzRFVDePSdWi8XDyXl8,816
win32/Demos/security/regsave_sa.py,sha256=UV91b_TL5ucBLk98CxrFmAm0kGRyCwbCStFJBHDJUIE,1751
win32/Demos/security/regsecurity.py,sha256=wojf1ElZ5Kx8xtC_M3DUi7y70_eGk7b2A1bXrotvpyM,1157
win32/Demos/security/sa_inherit.py,sha256=xkT9HFqNvLwdBTHMmPdEtjjf19tsrfl8lOqJhyscvmg,289
win32/Demos/security/security_enums.py,sha256=Hc-FaHytYLO-HmUkw5ZAkSlBShcv_GUosD0wWyLl_hg,9776
win32/Demos/security/set_file_audit.py,sha256=egffb3wx1MVGERqm6avMjtXAtrP6JyOUvSO6bTStrek,3479
win32/Demos/security/set_file_owner.py,sha256=XGMyAAmt_mpRNbHr8XJIcRBPQqpFdyeryNPggjWNbf4,2332
win32/Demos/security/set_policy_info.py,sha256=XZzn2IfhNiMlLpZ6a_u3FOuiRNTdkZlFh3bQn9-BHmE,993
win32/Demos/security/setkernelobjectsecurity.py,sha256=EdBznj4Fk6UNZyU-Y-dgrY0GckBo-WmLi3MN3wKMYxY,5048
win32/Demos/security/setnamedsecurityinfo.py,sha256=q_BOqNEvjhBak_3-JR6zV7ck03ZDNEQKaZymrEeZLPE,4519
win32/Demos/security/setsecurityinfo.py,sha256=wt0Ez6RmcyAi_QA6b4V1qsWXeap38KI7r5Zib6_B1LI,4669
win32/Demos/security/setuserobjectsecurity.py,sha256=sKhQyiMQvqqcMDlzJqjDJcN3vDBre8_GK57IwAsF8Xs,3485
win32/Demos/security/sspi/__pycache__/fetch_url.cpython-312.pyc,,
win32/Demos/security/sspi/__pycache__/simple_auth.cpython-312.pyc,,
win32/Demos/security/sspi/__pycache__/socket_server.cpython-312.pyc,,
win32/Demos/security/sspi/__pycache__/validate_password.cpython-312.pyc,,
win32/Demos/security/sspi/fetch_url.py,sha256=svnxIZgQoLW2lYWKQtDU5FUzpUZWUwMlnZRXDaqX6PU,5573
win32/Demos/security/sspi/simple_auth.py,sha256=gAAp7WpNCuCiVSEh_k38QUpFd6jnyhnu9n0qnWxJjbw,2925
win32/Demos/security/sspi/socket_server.py,sha256=lEXolGZtxBP23tT5rZai1dkRaNJF0Fn_VGIvh9brY2k,6559
win32/Demos/security/sspi/validate_password.py,sha256=kcbQZ2t1KpRVusHOqtxQio-ebhNyue-1axgl-9_aocg,1164
win32/Demos/service/__pycache__/nativePipeTestService.cpython-312.pyc,,
win32/Demos/service/__pycache__/pipeTestService.cpython-312.pyc,,
win32/Demos/service/__pycache__/pipeTestServiceClient.cpython-312.pyc,,
win32/Demos/service/__pycache__/serviceEvents.cpython-312.pyc,,
win32/Demos/service/nativePipeTestService.py,sha256=WxzjVSk1OSppnSzRnWrk2aajjo0hMKHKMWtE9UcBM5w,2198
win32/Demos/service/pipeTestService.py,sha256=ifkqEhg7Q0vL1WyPVN-ffuv80TOiPbzkptVBbH2l1FE,7007
win32/Demos/service/pipeTestServiceClient.py,sha256=6R0pFR0_XVvCQNF_WnNXo4-XV7N5-upv_lbQ5WljbSY,4609
win32/Demos/service/serviceEvents.py,sha256=uWJTvSk6q4tv-oEBUjrXKSvXjjduazrN0jKj4Kqjvks,4157
win32/Demos/timer_demo.py,sha256=fgZF0lDgDDdG2s6J7KNbKrUT7JcO9xQK3w-KPQjm9O0,2257
win32/Demos/win32clipboardDemo.py,sha256=YH1nIdKqikH2bqp4DH4xtsSZ1JoSwSEoUltU-c9BCwQ,4742
win32/Demos/win32clipboard_bitmapdemo.py,sha256=VzNleCkptwBvQk4nJUSa57orM6vopMhc0tXGMmbDb6E,4022
win32/Demos/win32comport_demo.py,sha256=qWscxG01Irf345ZmjUJa9N2Xq_vnpKJhOrJRKzZ7l6Y,5675
win32/Demos/win32console_demo.py,sha256=8dwTgJeljYP6Y3eorFWExpUs0vGmv-ULYqTmsjhRnjI,5244
win32/Demos/win32cred_demo.py,sha256=vybtZIqmImPgyyzS96gFvgxpglRN-l_1bOZ0_Y3gaSo,2834
win32/Demos/win32fileDemo.py,sha256=-a5j_owrcFqRfZl3xVF0boMjkTj4p8HAp3ZrJCbnoFU,1407
win32/Demos/win32gui_demo.py,sha256=gXDLYY5m49yHYKaP11EVotKhAzDy7hupbtnG1gIo7KQ,5205
win32/Demos/win32gui_devicenotify.py,sha256=A5-Gj444auozLdcTtqejlRaETWOcbbpmIgCII4xrA5s,3936
win32/Demos/win32gui_dialog.py,sha256=HG8vtj-IUIl2THx7BkjG9MQsCcBDkCdDX3s8N6OflOQ,15763
win32/Demos/win32gui_menu.py,sha256=dGhhDjecmfLYzFyOeHgvhBw8EMOvLhJ7Mp7nppJjcU8,16136
win32/Demos/win32gui_taskbar.py,sha256=sqecu0b2rxNpsHHXxlLfGoFTMCyDxxXlsy_Jqdl4WyE,5113
win32/Demos/win32netdemo.py,sha256=jUD_ghMX1ImSkU_MwJnDdJDfsbG9dqbiDA8iQTJb9JM,9248
win32/Demos/win32rcparser_demo.py,sha256=rBt-k4tcPc8dqTEl-P0T8oPf3dD-U9Ywkujy6hUOriQ,2889
win32/Demos/win32servicedemo.py,sha256=NF4QE0g-GtX-hWQ7RW59xgBEtN7p-wPD0rqIkL36H0w,602
win32/Demos/win32ts_logoff_disconnected.py,sha256=MSY5O4GXxON4gwfaj6bdT9KtUap-dVeF3fM8i-5Q8Oo,1008
win32/Demos/win32wnet/__pycache__/testwnet.cpython-312.pyc,,
win32/Demos/win32wnet/__pycache__/winnetwk.cpython-312.pyc,,
win32/Demos/win32wnet/testwnet.py,sha256=Q3oM6o6EpxBAf8d9S8MnxX4dZWNj1rgKzrDU3JwNQMQ,4407
win32/Demos/win32wnet/winnetwk.py,sha256=B_7izZlXTH3ksPlgEnw9U7h0pT2ab4MEUDNvORM5aTg,3293
win32/Demos/winprocess.py,sha256=rwoiNxJWL5LPFuVzr36tFfgLZhn8sAiUA7k-LBKakBw,7588
win32/__pycache__/winxpgui.cpython-312.pyc,,
win32/_win32sysloader.pyd,sha256=NmoooZVfLACq58-bcfIJMQR5WiO0r-nU-zDES1jndbU,14848
win32/_winxptheme.pyd,sha256=ntX4JG5fj1Y1wm1Rlnvgd6dCdqyFnkW5c8A7muzZ3I0,25600
win32/include/PyWinTypes.h,sha256=rsvlt8XmTMF4xtaA6thMloOQiT2DlGvy4oHra7VPoUY,31187
win32/lib/__pycache__/_win32verstamp_pywin32ctypes.cpython-312.pyc,,
win32/lib/__pycache__/afxres.cpython-312.pyc,,
win32/lib/__pycache__/commctrl.cpython-312.pyc,,
win32/lib/__pycache__/mmsystem.cpython-312.pyc,,
win32/lib/__pycache__/netbios.cpython-312.pyc,,
win32/lib/__pycache__/ntsecuritycon.cpython-312.pyc,,
win32/lib/__pycache__/pywin32_bootstrap.cpython-312.pyc,,
win32/lib/__pycache__/pywin32_testutil.cpython-312.pyc,,
win32/lib/__pycache__/pywintypes.cpython-312.pyc,,
win32/lib/__pycache__/rasutil.cpython-312.pyc,,
win32/lib/__pycache__/regcheck.cpython-312.pyc,,
win32/lib/__pycache__/regutil.cpython-312.pyc,,
win32/lib/__pycache__/sspi.cpython-312.pyc,,
win32/lib/__pycache__/sspicon.cpython-312.pyc,,
win32/lib/__pycache__/win2kras.cpython-312.pyc,,
win32/lib/__pycache__/win32con.cpython-312.pyc,,
win32/lib/__pycache__/win32cryptcon.cpython-312.pyc,,
win32/lib/__pycache__/win32evtlogutil.cpython-312.pyc,,
win32/lib/__pycache__/win32gui_struct.cpython-312.pyc,,
win32/lib/__pycache__/win32inetcon.cpython-312.pyc,,
win32/lib/__pycache__/win32netcon.cpython-312.pyc,,
win32/lib/__pycache__/win32pdhquery.cpython-312.pyc,,
win32/lib/__pycache__/win32pdhutil.cpython-312.pyc,,
win32/lib/__pycache__/win32rcparser.cpython-312.pyc,,
win32/lib/__pycache__/win32serviceutil.cpython-312.pyc,,
win32/lib/__pycache__/win32timezone.cpython-312.pyc,,
win32/lib/__pycache__/win32traceutil.cpython-312.pyc,,
win32/lib/__pycache__/win32verstamp.cpython-312.pyc,,
win32/lib/__pycache__/winerror.cpython-312.pyc,,
win32/lib/__pycache__/winioctlcon.cpython-312.pyc,,
win32/lib/__pycache__/winnt.cpython-312.pyc,,
win32/lib/__pycache__/winperf.cpython-312.pyc,,
win32/lib/__pycache__/winxptheme.cpython-312.pyc,,
win32/lib/_win32verstamp_pywin32ctypes.py,sha256=Xqcq7vwKpotveqy3FQE_5uPBbfsw1n0813sOWpqJfUA,4668
win32/lib/afxres.py,sha256=gtezukpzwGELMruMta8uSo4r8p7NOr0Wrotclj4ilVQ,206
win32/lib/commctrl.py,sha256=SPUHV8rnM30RZU5W6mJbAU4UP1k-rSA50I9mXggKCMY,47709
win32/lib/mmsystem.py,sha256=SFVv8C2CCNxErz7NhFEooi0qR9aQ8hohpuiC4yEgPIQ,31230
win32/lib/netbios.py,sha256=fBz2EdzALoPAsFNw1C0dmxqEVikaYYtxyUXDPTlAlmQ,6900
win32/lib/ntsecuritycon.py,sha256=OizInbaYu2Jb9LAGw-T8oSafEbGm393Jb0A9wkJmR-s,23075
win32/lib/pywin32_bootstrap.py,sha256=W3ogo7cWFeHQj92bkRJcphUpVFe-VKd3E3BYdHcqwok,1312
win32/lib/pywin32_testutil.py,sha256=796kZGrqwwY6VijK7VSp8SIStheXV7nea28mPb-t3e0,10876
win32/lib/pywintypes.py,sha256=Q4cteSHftAoKrTpL35nQHjaQRMJhYpPKMqhBJEfD-tw,6039
win32/lib/rasutil.py,sha256=XwAs1m2kOSd2im9ga7j7M7SmPr7cgmic3oFNXs2xwZA,1764
win32/lib/regcheck.py,sha256=KM7xNRc_Cf-IeFOMwxMLmg2DId35NBRFJ2Uc4BZ0bVo,4602
win32/lib/regutil.py,sha256=qxBwrB8xaSmUC2Ld69zzNGu1jUHWh82mC77RXkgAsX0,12699
win32/lib/sspi.py,sha256=Rvk_TryRYCUePN-EH1-fpzGtf9su43IevP2MY6lXBtA,15995
win32/lib/sspicon.py,sha256=8ZcVf1MYj3SY9ggHWA4S8Pqo_3vayyoOxgpJUIlJ2PE,16174
win32/lib/win2kras.py,sha256=uv0-T72YHqvOVbamwASGHBBV1qkD4FY68Bypq7Nx56k,341
win32/lib/win32con.py,sha256=PwG9_5Uja1kSWNi9pgXMch5ekh01DMBIP8UXDfeOZy0,122135
win32/lib/win32cryptcon.py,sha256=CxZ6sILPGvI-rVDOZkod5OkoZfgiOCKj6ydI-q04ZHw,74419
win32/lib/win32evtlogutil.py,sha256=aO1xyhsEiV2jw5BQsQXG5oc4sPiH5eWTg0peBXrbeEc,7829
win32/lib/win32gui_struct.py,sha256=n5ga8yNYM7x8ZRiMIqM59HhEtourCspCLzIFbgWFMlI,29889
win32/lib/win32inetcon.py,sha256=XQjRII6WtquAeGZzuLUjio5LE57s0RGLmf0_xaldXMk,44303
win32/lib/win32netcon.py,sha256=6gCFewIQDOmRzmIPpkOMZWkTb2QuhE_HQt-m9rTh4Nk,19159
win32/lib/win32pdhquery.py,sha256=NBivcecedU-BlCzy_oHYyb_BU6kqk_OyWe6VKmiHqD4,23976
win32/lib/win32pdhutil.py,sha256=rdw-zkakictYTBNw8NMLoIm0R9pf2hRzgK2KNDmFlAQ,7807
win32/lib/win32rcparser.py,sha256=kaVkxbI7UgAbr-m7OpABVyrlPANzy6Ygka390wLjk0w,22331
win32/lib/win32serviceutil.py,sha256=mX9iLiTH9K_QeRSrtrh25_ryJcf-D-3mQSLIDjWAzj8,39104
win32/lib/win32timezone.py,sha256=VANZa7cZaDVCpROGil2vE96ri3vwQUKjE_B1XFgpr4c,35460
win32/lib/win32traceutil.py,sha256=e02cApqmwa3HCqTlOaf8qb53mtfcOOIydr3YF6i9aK0,1661
win32/lib/win32verstamp.py,sha256=obuHGSZOPesHUf9c3zpA-DVhb3PVBUujcAG3TCdyKgg,7216
win32/lib/winerror.py,sha256=LLRIfu6Y7a8NyXl0sgM-yu-28XbryDZjCV7oay4fj4I,104517
win32/lib/winioctlcon.py,sha256=ZsPO8DIVDR7jH9--M-BYWAeDl4ojHf_6Nrb_MGD7nN0,36454
win32/lib/winnt.py,sha256=qwkCWPv4TpFWBI0d4oH3i6KXaA_iQu50QEIGNoekyf0,38459
win32/lib/winperf.py,sha256=XkGwhw00wYoSDehZ3Dv4fXNBM7LeysFuGijiHY0Jbyc,6147
win32/lib/winxptheme.py,sha256=p6C1z9ZpAQNBtUtag_J_qYueNTqPNJI5gOqCCvou9_o,263
win32/libs/pywintypes.lib,sha256=YgffA9X2vivolop91ej7FKICc8VGpytLwn4mK-0vpoQ,107794
win32/license.txt,sha256=EdhGLhNOjv6PS9Ujcikqcv8LZwdT0vSfcWde7Q2_0IU,1540
win32/mmapfile.pyd,sha256=lLajZMxXauIcvds1A-kALtVGhtjYSRLslu7uacv0vSg,22016
win32/odbc.pyd,sha256=BRHd3Zx5JBS9ZH9ajrU-uHEXtEMJN8oHulLOU4aCK1Q,41984
win32/perfmon.pyd,sha256=1cz3IM5Mg7RJxRe1PvM_5jucu3JtsiOgmpJaiwZUZak,30208
win32/perfmondata.dll,sha256=0yudYa9BROXn69PomRVRak_qUhEes8q1I72tiXneEY4,19456
win32/pythonservice.exe,sha256=YHfrc2pgeLjmFeLNYr91llq0-Gc3uZ7MiGLKhL5hi0w,20992
win32/scripts/ControlService.py,sha256=MZbB-jMYN8iIaXiM-DTju5G-EUfvmf5rCtLh0IpPtxM,18634
win32/scripts/VersionStamp/BrandProject.py,sha256=YlLAtJn7UUzJS0lacSPDG1dxCmC1AnoU1VJe9tP0f48,2873
win32/scripts/VersionStamp/__pycache__/BrandProject.cpython-312.pyc,,
win32/scripts/VersionStamp/__pycache__/bulkstamp.cpython-312.pyc,,
win32/scripts/VersionStamp/__pycache__/vssutil.cpython-312.pyc,,
win32/scripts/VersionStamp/bulkstamp.py,sha256=G5Y2Laj-1XHBH8k5KHGUJ6TCvbxQRxaPFsf8fytNvPA,4823
win32/scripts/VersionStamp/vssutil.py,sha256=uQAgnBRalWdM3i--le4vZ230KTTD1qAsfIYx4tcOIfo,5818
win32/scripts/__pycache__/ControlService.cpython-312.pyc,,
win32/scripts/__pycache__/backupEventLog.cpython-312.pyc,,
win32/scripts/__pycache__/killProcName.cpython-312.pyc,,
win32/scripts/__pycache__/rasutil.cpython-312.pyc,,
win32/scripts/__pycache__/regsetup.cpython-312.pyc,,
win32/scripts/__pycache__/setup_d.cpython-312.pyc,,
win32/scripts/backupEventLog.py,sha256=lOsKv3kchT4Khxf0rfC1EeylVi1ZmKhKReaQabkYJqQ,1288
win32/scripts/killProcName.py,sha256=5vhW4UTNZKoI19ErCHFIdWeLO3cOgaDu6ri6nDCTJks,2050
win32/scripts/rasutil.py,sha256=53y0U5f1DHHLoAjTe7UiZYIPaHdarrwLSUo91vLKNh4,2821
win32/scripts/regsetup.py,sha256=d5_6DVCQK326vz8v4wuU5Gw7Si4sVHN26njoHzveU_E,21058
win32/scripts/setup_d.py,sha256=fUhN0Q6jwL3IllbyW9qfFKpLWuac4aQMN3wB54NeUaI,3644
win32/servicemanager.pyd,sha256=rqJYIcVclH7MpH1SYzDFPL5LiyhtYle4-hchtC21eK4,41472
win32/test/__pycache__/handles.cpython-312.pyc,,
win32/test/__pycache__/test_clipboard.cpython-312.pyc,,
win32/test/__pycache__/test_exceptions.cpython-312.pyc,,
win32/test/__pycache__/test_odbc.cpython-312.pyc,,
win32/test/__pycache__/test_pywintypes.cpython-312.pyc,,
win32/test/__pycache__/test_security.cpython-312.pyc,,
win32/test/__pycache__/test_sspi.cpython-312.pyc,,
win32/test/__pycache__/test_win32api.cpython-312.pyc,,
win32/test/__pycache__/test_win32clipboard.cpython-312.pyc,,
win32/test/__pycache__/test_win32cred.cpython-312.pyc,,
win32/test/__pycache__/test_win32crypt.cpython-312.pyc,,
win32/test/__pycache__/test_win32event.cpython-312.pyc,,
win32/test/__pycache__/test_win32file.cpython-312.pyc,,
win32/test/__pycache__/test_win32gui.cpython-312.pyc,,
win32/test/__pycache__/test_win32guistruct.cpython-312.pyc,,
win32/test/__pycache__/test_win32inet.cpython-312.pyc,,
win32/test/__pycache__/test_win32net.cpython-312.pyc,,
win32/test/__pycache__/test_win32pipe.cpython-312.pyc,,
win32/test/__pycache__/test_win32print.cpython-312.pyc,,
win32/test/__pycache__/test_win32profile.cpython-312.pyc,,
win32/test/__pycache__/test_win32rcparser.cpython-312.pyc,,
win32/test/__pycache__/test_win32timezone.cpython-312.pyc,,
win32/test/__pycache__/test_win32trace.cpython-312.pyc,,
win32/test/__pycache__/test_win32wnet.cpython-312.pyc,,
win32/test/__pycache__/testall.cpython-312.pyc,,
win32/test/handles.py,sha256=n-De0yrnK4av_W5hDxNm20lak7VIoNbfI_CsaIMJyXA,5547
win32/test/test_clipboard.py,sha256=IniLjiHbncAHZwCMuRgwjFjWB3IzneuxnoFPhbS2zoY,4165
win32/test/test_exceptions.py,sha256=QryKfcrL-zknviQ7WfoQ5Q-uIpfyXRE0iskQiOLihlE,8227
win32/test/test_odbc.py,sha256=WiRUkPSfKmXqjVq6zanvo6EKLX2aMcXhxtS1K-QXYUA,8613
win32/test/test_pywintypes.py,sha256=ET3VdXK1jBkcsrsCMN0WeV_bLplrQcr_osgRLYXbkxc,4139
win32/test/test_security.py,sha256=fdbwFugHT_b3RCix2moGiugZ-tqN649xnhXFhhY5dQI,6222
win32/test/test_sspi.py,sha256=Tcc12sluZQ3dyEBmJbhRkNHGJd2ZLwNsRnar1OZ0_wI,8378
win32/test/test_win32api.py,sha256=ud4JsmiBbpM6lrR48rCk4o9C5VbmdiOJIHEi_EPE4l8,10007
win32/test/test_win32clipboard.py,sha256=GXudtPY_awuzadm_zDr8Q1eVCowQFMguDno8hpi_zVU,2015
win32/test/test_win32cred.py,sha256=5Xa9ZgBWDZeq3q2yz_IhSAJMHARc-xGQXp9BUfICXZU,3241
win32/test/test_win32crypt.py,sha256=bf4GxIua2kqXK1vhup-n36wbWpUN4DVBx9HJUnN9kk8,4521
win32/test/test_win32event.py,sha256=lmTcGu7PHccwC18jONrS7Mdk7DRKWFmMixeYGWXq-io,4487
win32/test/test_win32file.py,sha256=LW_5cXCnHjrQl8ibeoEj6k6ig8GOvTRQNAZBzC2-JHI,42171
win32/test/test_win32gui.py,sha256=SPASPQXUzN6uS94_7e8WiL3z4oxavO-W2bIGtbAKoQ0,7968
win32/test/test_win32guistruct.py,sha256=EpawQWV6NSddbAVs-qzgleKqljJeJNZAGxj0lHFWYP4,9694
win32/test/test_win32inet.py,sha256=p8kzMTJwG9hoiLo4VmCjoVFV_HxWPLTvrqNl1KO-GPs,3119
win32/test/test_win32net.py,sha256=xK33Cp5y8WEe6RyFmv5TJD43k5JNBYj33Diw_QQieaw,678
win32/test/test_win32pipe.py,sha256=WgeVnAKsgEo7ohssyzC4BA8TZ-Fy7cdoofVX27GV8cw,4945
win32/test/test_win32print.py,sha256=xvhCxi57i4EfOVX3Gn_9mLjmIlq_O7-YgM0jooa7rEw,705
win32/test/test_win32profile.py,sha256=vam5Hui9G48Khut0K8aMf6sFYl77uu9Qa7dV4-7hWLI,433
win32/test/test_win32rcparser.py,sha256=4dm5iFoNBl9Jv7zO1FGESQg1UZJXDU6ctiNZR12ywTU,2533
win32/test/test_win32timezone.py,sha256=3pMEXpZlzziMxW_TlT7WfI1j-_3oGfSwY9TB9Xff-KQ,327
win32/test/test_win32trace.py,sha256=Hd9IvCyvwPThEM87kLJeRKToa7PoGxQ_CTi7hW25WWE,11665
win32/test/test_win32wnet.py,sha256=SJmMhJoBQcXbtSLomChz01czO_AKlC62a_JL3BkAi6k,5827
win32/test/testall.py,sha256=BXPsu0hV8knaGZCrCcs9czZxA3MuaUZ9ID4wgVrd3aE,7257
win32/test/win32rcparser/python.bmp,sha256=GWIxMoFdranqXHxIgyJ_dovJBNfCxc_OQlnSGxTfHLE,778
win32/test/win32rcparser/python.ico,sha256=LnQC7RaDp1G7UiKgN55dilDjRn41wNDTWyo8ymRTctw,766
win32/test/win32rcparser/test.h,sha256=Zf6hCkotMQ3DIwEbCPuhpZ3jZtms3a2Jxi6SZWzdlNs,1215
win32/test/win32rcparser/test.rc,sha256=Rjg9hfZpikeVvz43nAFdOhcl8Uke78gPneHwsnyjrdw,6474
win32/timer.pyd,sha256=1Ib8MYyJ61xoJ2OU7k8HoqojbC_nK_rue1GF6KsMK9c,16896
win32/win32api.pyd,sha256=33JPar1moFSUFauqP99JBoDm4M4HWE6WS4v9AeGHtIc,133632
win32/win32clipboard.pyd,sha256=3G1lk2q2J6dN12GCe95R4s8H0rs-ZjzVnUKgp3smnqo,28160
win32/win32console.pyd,sha256=86VzzniGAJfQmd_RBut5l3HpOgP28UeRZMDdMJYh0as,60416
win32/win32cred.pyd,sha256=Yjy2U20ARVeRGq37_jz9E94887YaC_RJ8RatP1347Mk,38912
win32/win32crypt.pyd,sha256=MIj7pVqSACIwgFVMVfoAVDU_38q07UrFFxblQTlxuJg,125440
win32/win32event.pyd,sha256=coIXVtkMhNOIL90hw8G0N615D9D8ObmOXkSkGheFOF8,28672
win32/win32evtlog.pyd,sha256=Lnc6et1ykK0hyf__KG6lB4kXCu6kumCMc_8kdA1FxBs,73728
win32/win32file.pyd,sha256=WK3ynK-T4M9PeFjRf_9ua1xw9gFbnOrlS2e973pLSaE,144896
win32/win32gui.pyd,sha256=A_UTVav9g9tMYdOL5voDxZ02C2_7122Gn3pPfxwLck4,221696
win32/win32help.pyd,sha256=pTNR5pKx_g0BfOtbDEOnqY6t05KmXDS2SiKUaqisA-I,54784
win32/win32inet.pyd,sha256=oyIRJAzx33R2yCn7AqUfzNyEorcBFTer-cKb6palxOA,53248
win32/win32job.pyd,sha256=PN7JAjLpqk-Der9Ne740l927IFEBNGTys19qhkAbFXI,28672
win32/win32lz.pyd,sha256=fxJlQZkBTDuFFb6oor3I8NKLq6-nDnA1ylXytK9LXMU,16896
win32/win32net.pyd,sha256=GVcaxVaqt8vs5Wqm2t5syXZDkWGd4HeXhaSGb2Cvp9Q,92672
win32/win32pdh.pyd,sha256=0CJ8nKls7x8cq0sO3A0DUFc0BJwdMVAtcz-QP40HSK0,35328
win32/win32pipe.pyd,sha256=zXAiDg3JUfqTogOivijqpRC2mFHMwECP1lnzfv93eys,28672
win32/win32print.pyd,sha256=G4HW4Z7by2Slz_a_uXBIdzcBDqxpTCZB95GhCjDUHRc,76288
win32/win32process.pyd,sha256=XpBy5Djju0HRt_jVBNUEEDu-Kp1NZsfPK2lH9PMDYwU,53760
win32/win32profile.pyd,sha256=3_H5ZIB7VzubUmprJaEbFQ9bHVUA3DxRMlyKTQd_jFU,27136
win32/win32ras.pyd,sha256=T4MPPs7pAFqK7cmz0HmglSi9e0ww1daUxONlfgfm-0Y,34304
win32/win32security.pyd,sha256=NlBgFlo4PSg6dG8fMEYqU0Lpk8PvW-69YQZzxnNrTCM,137728
win32/win32service.pyd,sha256=PjOJWcRu_xJKdgOy2u1sqn8qdnlo59VPoxutGvp_ecQ,59392
win32/win32trace.pyd,sha256=AU1817b_cwoVv0KyvF0Pruv1TfsGhTZAMMOs2t0eVm0,24064
win32/win32transaction.pyd,sha256=S9Xrc34aiL2-oXfFbK0Fw-mz55bmvQ5OUZQgUDQc23c,19456
win32/win32ts.pyd,sha256=sytwHDBhrvTCQ0UlZN-ziGDUQJh_Ar7cv_aTWwqtU0I,33280
win32/win32wnet.pyd,sha256=UoyV80-TTuBDMVbRAQ_EGh7uDfqHe5Ef0CD3VpnufdQ,37888
win32/winxpgui.py,sha256=8mkwAAil2AjQFF8q5B9x25F5PpXAW5whZGkV0354PXM,563
win32com/HTML/GeneratedSupport.html,sha256=Mbsg9s1LQkrCVrP9w6vAl6yYK7OWXFQvxghpgt1y_tg,6128
win32com/HTML/PythonCOM.html,sha256=U9-XjbaUIYMXnJjndacN23ueLZFVw2z8k2DGKpUr9wo,9033
win32com/HTML/QuickStartClientCom.html,sha256=TPE6j4rnjOQ5FMASBz7RFg8lgtA44KxxbdPA1dORvok,7395
win32com/HTML/QuickStartServerCom.html,sha256=wZousHb7M3SGuJuZiRsoMjwUc_D3mDFMMReY1Et4bSM,13143
win32com/HTML/docindex.html,sha256=j2wKLgTf_fl6cSQZwflV5tsCXJhyDslWfIfR24Risd8,1317
win32com/HTML/image/BTN_HomePage.gif,sha256=nTV7ZQiN6x1fFcWKt4jHj3WsIzjv04XjJrCbqRpSIBk,211
win32com/HTML/image/BTN_ManualTop.gif,sha256=LwVu_ClkEDG1xhVBiCAy-OLi9-ZJ6BIINjAyi2R7jJ4,215
win32com/HTML/image/BTN_NextPage.gif,sha256=hGnR6jZJERMUsnduVHP4AlntrkgehcFpDyfhI4xvj4k,218
win32com/HTML/image/BTN_PrevPage.gif,sha256=wFJ1YHrsOEzBr3jDEOqBGKQmqWGBkADtnCPEMJHpm-U,216
win32com/HTML/image/blank.gif,sha256=iJO_Up8XRXUyA8YYNoftgJlVONefdsXEFNfIuQxWFMs,864
win32com/HTML/image/pycom_blowing.gif,sha256=s8ZSBzs8dfWsgTgbb0S43urQZcY1xjdxoIBuSHeLr6o,20926
win32com/HTML/image/pythoncom.gif,sha256=pBINoAg9LpAFllAeRM5vHHgNcSUtWlAty7bYkjMnBho,5767
win32com/HTML/image/www_icon.gif,sha256=TJw2huqtQFld280AhhQ39etm1ITsh4cg896hMi2Pr4c,275
win32com/HTML/index.html,sha256=mHQwG0gRAoef9GI_8B_187mgYaBr_2sOfZM1p0OETJs,1660
win32com/HTML/misc.html,sha256=hbEYo8Jrzl3yB9Zx10NGWM8NztpIgJ1ikPaMuaHbFgY,1182
win32com/HTML/package.html,sha256=DCW376oB7Kp0z-ur-pHK7nAFJW0BY9z-LDzkehATWwM,3290
win32com/HTML/variant.html,sha256=Ye7qjI0TaAv5pKYm3LGYA8DpTzTjer-ajj66u8seAMc,6016
win32com/License.txt,sha256=w736EVKpudQE3ckLgX4baUWAOHmsU6FNLd4w1nf4g70,1569
win32com/__init__.py,sha256=TMcmZn-I6mjdWlDfmBRM_A7urJwuf2Oe9eSuvmxexdQ,5020
win32com/__pycache__/__init__.cpython-312.pyc,,
win32com/__pycache__/olectl.cpython-312.pyc,,
win32com/__pycache__/storagecon.cpython-312.pyc,,
win32com/__pycache__/universal.cpython-312.pyc,,
win32com/__pycache__/util.cpython-312.pyc,,
win32com/client/CLSIDToClass.py,sha256=5mVYlMU-OZSs-okDyl0KpLCETgApygjaENql5WSOSxc,1816
win32com/client/__init__.py,sha256=Cd6qgsb_TIuPap-yU37-kGpPTN_D9AqnJegW0bCfDZM,26014
win32com/client/__pycache__/CLSIDToClass.cpython-312.pyc,,
win32com/client/__pycache__/__init__.cpython-312.pyc,,
win32com/client/__pycache__/build.cpython-312.pyc,,
win32com/client/__pycache__/combrowse.cpython-312.pyc,,
win32com/client/__pycache__/connect.cpython-312.pyc,,
win32com/client/__pycache__/dynamic.cpython-312.pyc,,
win32com/client/__pycache__/gencache.cpython-312.pyc,,
win32com/client/__pycache__/genpy.cpython-312.pyc,,
win32com/client/__pycache__/makepy.cpython-312.pyc,,
win32com/client/__pycache__/selecttlb.cpython-312.pyc,,
win32com/client/__pycache__/tlbrowse.cpython-312.pyc,,
win32com/client/__pycache__/util.cpython-312.pyc,,
win32com/client/build.py,sha256=48d1dYhBwyVzSOVvmu5PNTwpVEpZQzZwOiaGaCs7YRQ,29247
win32com/client/combrowse.py,sha256=LguRj58fdUimGYz1UmfgDwDOa4rBjKEnDPtBvQZmUPY,20820
win32com/client/connect.py,sha256=aZ_uVHbzRkoIKHPZApXvHkO3q2u01JjkBStrOWiThEk,1585
win32com/client/dynamic.py,sha256=rVN4gXqzebZ6c7-YCHiW_9gts-1GGLim8QhyZ4Zubzs,28283
win32com/client/gencache.py,sha256=MMxlKdjhVc4jvZNRvArdrg2jBR8LhEPEYejSzMp7MGM,29094
win32com/client/genpy.py,sha256=F9IyrhIoby9OYlLjWaDsQ464HRxMzchbfk0rQ3Yk_NA,56812
win32com/client/makepy.py,sha256=NN3kqRvnC9jzLnsTx379QVuR1tmFbVqAcyna89MAAjk,14864
win32com/client/selecttlb.py,sha256=1ndXeLdWo-TzU92FBTxybn5-mqVPHp9uMvigywSGm1s,6415
win32com/client/tlbrowse.py,sha256=sUxYod_WK0Zj_LomNFG0zpy_gGrI71DitDQ76ZEmHDk,9787
win32com/client/util.py,sha256=ujlUcPJQ_r0ifYIHkwGN25skHNBseC1uCvtiErfnlVM,3455
win32com/demos/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
win32com/demos/__pycache__/__init__.cpython-312.pyc,,
win32com/demos/__pycache__/connect.cpython-312.pyc,,
win32com/demos/__pycache__/dump_clipboard.cpython-312.pyc,,
win32com/demos/__pycache__/eventsApartmentThreaded.cpython-312.pyc,,
win32com/demos/__pycache__/eventsFreeThreaded.cpython-312.pyc,,
win32com/demos/__pycache__/excelAddin.cpython-312.pyc,,
win32com/demos/__pycache__/excelRTDServer.cpython-312.pyc,,
win32com/demos/__pycache__/iebutton.cpython-312.pyc,,
win32com/demos/__pycache__/ietoolbar.cpython-312.pyc,,
win32com/demos/__pycache__/outlookAddin.cpython-312.pyc,,
win32com/demos/__pycache__/trybag.cpython-312.pyc,,
win32com/demos/connect.py,sha256=ng7oma8V79Sq7_ASzmzpdh_wZFAbCmKRnclUvpxarmY,3854
win32com/demos/dump_clipboard.py,sha256=lZMtAgIUF65rmmEQehERtHgERNFGjBtAQjG-jpVpypM,3022
win32com/demos/eventsApartmentThreaded.py,sha256=VVeI06SigUgthPSS8Ph76H-uYuo3edi2qDK9F1aSlic,3764
win32com/demos/eventsFreeThreaded.py,sha256=7A11xf81Dtxn_tIRm84V4m_C1Xjfo_1JWgvNUV4wci8,3556
win32com/demos/excelAddin.py,sha256=N13gnUXQla2NYWkyEa_r9Dor0k5jQGBpbwtZWX6zK-4,6226
win32com/demos/excelRTDServer.py,sha256=oLuV2hXSXpJPBuCw17pTdGhjNIXTmPNl7DXPX5W4-k4,16608
win32com/demos/iebutton.py,sha256=5RD5ZHxNjDqy-hrp-iPfuIte3cAXyVoe_55p8h1gA6s,7293
win32com/demos/ietoolbar.py,sha256=VAISXs9hRKzlOn2Av--7EY_V0EwVwAAnvtPt5SgpOSo,11154
win32com/demos/outlookAddin.py,sha256=suofV-TLok2NbrBvNgHM1KMXwMOBDM-Qn61YEePtUPw,4781
win32com/demos/trybag.py,sha256=FRpficdhSSiCmDOfDsOM4PcqaBjm8BaMwXMYsaJe2R0,2185
win32com/include/PythonCOM.h,sha256=FqtZa5ZS01VJ1bLkD9FpQO1rqedVN42t5eK-aKoI3Dc,30323
win32com/include/PythonCOMRegister.h,sha256=cAcRgI1ib1UBRF2Sx9NfrnpTSHTycaY645x95LnQb0w,3092
win32com/include/PythonCOMServer.h,sha256=1YoPbEp7tEup4VPTLzHQlSeQTanEQfifRjj-HANogr0,8936
win32com/libs/axscript.lib,sha256=yvUcU7La82-c6sZvexhSpBBMPgLr-1pKQ177i6It6NE,72478
win32com/libs/pythoncom.lib,sha256=rLSyvA9RD-84mbFTYy3pDxVDRmuS1-yoeKmy8njyQUU,159886
win32com/makegw/__init__.py,sha256=IY9odXMAKP5RAezwUFBcVQnbG3aLS-r1orW03HupBCo,31
win32com/makegw/__pycache__/__init__.cpython-312.pyc,,
win32com/makegw/__pycache__/makegw.cpython-312.pyc,,
win32com/makegw/__pycache__/makegwenum.cpython-312.pyc,,
win32com/makegw/__pycache__/makegwparse.cpython-312.pyc,,
win32com/makegw/makegw.py,sha256=JveJdrBxWYzJh48jKV7HFlZRFLOT_tvDLhq4x3ZQoOI,22483
win32com/makegw/makegwenum.py,sha256=ctlb_glH3KK_s_pBzKDXP0JN1GpzJZcK_44MoHGig58,10851
win32com/makegw/makegwparse.py,sha256=PynCXDE7T9bvZQrQuVljFlWUuhYy_pb4zruinmmIHxY,36330
win32com/olectl.py,sha256=QoMK2yd6zwOJcmZ24k1AqpWVC_TJp9KzyBiyTDqs39k,2694
win32com/readme.html,sha256=q1RWJZdaATSGAUnhWCkM-rrj-I8BWL26TOjbquNKhxo,3777
win32com/server/__init__.py,sha256=dZAohMwEwJcjYQXeY-LGNk4MrlpumWbUJl9uJvE2qFE,51
win32com/server/__pycache__/__init__.cpython-312.pyc,,
win32com/server/__pycache__/connect.cpython-312.pyc,,
win32com/server/__pycache__/dispatcher.cpython-312.pyc,,
win32com/server/__pycache__/exception.cpython-312.pyc,,
win32com/server/__pycache__/factory.cpython-312.pyc,,
win32com/server/__pycache__/localserver.cpython-312.pyc,,
win32com/server/__pycache__/policy.cpython-312.pyc,,
win32com/server/__pycache__/register.cpython-312.pyc,,
win32com/server/__pycache__/util.cpython-312.pyc,,
win32com/server/connect.py,sha256=QikyUyI4lbKR_-bKfkq8DxQ5we_dNHPp0FYwLa-ylNI,2869
win32com/server/dispatcher.py,sha256=f4oaXeSpXrw_vSh462DwP2xjX5TzFT1cevU0-15yTZM,8524
win32com/server/exception.py,sha256=2SokZhzH5TrvOxW8phVZ20fNLMO3jv6q4efOzj3DhR4,3460
win32com/server/factory.py,sha256=qjfv8N20saP-QT9jJnnnmiYA9F2VdLCDldoIUrRHnt8,876
win32com/server/localserver.py,sha256=-asUtf48XySYQIJvD-4581OAJqk1xHato0mo6pC57c8,1247
win32com/server/policy.py,sha256=jqkdEJNH7mf0Q-C4OJ4B2iywurn2XjCN4uvu2KxJOMg,33631
win32com/server/register.py,sha256=2qiXPmeFnT2vIsoO85fEbNWCbaWzVOJdjGe3SVOTniA,25880
win32com/server/util.py,sha256=IT8YZ4M-sXeYu25WaHYIx7cnaflZzyPldbxMHPCTGpQ,6930
win32com/servers/PythonTools.py,sha256=RN-zg1PWCoyZHf5zh4ZyBmvSpK2LLNZmqMMKnWB2Cro,1224
win32com/servers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
win32com/servers/__pycache__/PythonTools.cpython-312.pyc,,
win32com/servers/__pycache__/__init__.cpython-312.pyc,,
win32com/servers/__pycache__/dictionary.cpython-312.pyc,,
win32com/servers/__pycache__/interp.cpython-312.pyc,,
win32com/servers/__pycache__/perfmon.cpython-312.pyc,,
win32com/servers/__pycache__/test_pycomtest.cpython-312.pyc,,
win32com/servers/dictionary.py,sha256=qlNMMoewwcGEptYYfzFlVO76IKR7og4a3ysZtKzvTbk,4596
win32com/servers/interp.py,sha256=rJvdo-6Uoo-j3FQleFp8-G6beF1Wmz8FhSOMUk9YQ9o,1808
win32com/servers/perfmon.py,sha256=johAqFw3MQjEptuoVc7l9g8sLOQk624VeAV1CZ2TnC0,1233
win32com/servers/test_pycomtest.py,sha256=oPCK0lR1qMnE2xSEVC-JkdKhLGmrQ5-cLf727BIuFCk,5287
win32com/storagecon.py,sha256=dnhSY8FCoS-zActUpteVibq0FbYlP4cRXTW-St-AIhE,3174
win32com/test/GenTestScripts.py,sha256=g6PHOFw62QbzZN0JTh5BhqHgFnl8_C-EaPM6y1LZQMQ,2702
win32com/test/Testpys.sct,sha256=P4cvNiMNIGI9cUW88w6Zbo7Yn7uNrZFlw8u-eQgVZwg,1098
win32com/test/__init__.py,sha256=arweq3JdN1698W-NboY6-gsIi2EtHNL2X7pHGbIYh6s,44
win32com/test/__pycache__/GenTestScripts.cpython-312.pyc,,
win32com/test/__pycache__/__init__.cpython-312.pyc,,
win32com/test/__pycache__/daodump.cpython-312.pyc,,
win32com/test/__pycache__/errorSemantics.cpython-312.pyc,,
win32com/test/__pycache__/pippo_server.cpython-312.pyc,,
win32com/test/__pycache__/policySemantics.cpython-312.pyc,,
win32com/test/__pycache__/testADOEvents.cpython-312.pyc,,
win32com/test/__pycache__/testAXScript.cpython-312.pyc,,
win32com/test/__pycache__/testAccess.cpython-312.pyc,,
win32com/test/__pycache__/testArrays.cpython-312.pyc,,
win32com/test/__pycache__/testClipboard.cpython-312.pyc,,
win32com/test/__pycache__/testCollections.cpython-312.pyc,,
win32com/test/__pycache__/testConversionErrors.cpython-312.pyc,,
win32com/test/__pycache__/testDCOM.cpython-312.pyc,,
win32com/test/__pycache__/testDates.cpython-312.pyc,,
win32com/test/__pycache__/testDictionary.cpython-312.pyc,,
win32com/test/__pycache__/testDynamic.cpython-312.pyc,,
win32com/test/__pycache__/testExchange.cpython-312.pyc,,
win32com/test/__pycache__/testExplorer.cpython-312.pyc,,
win32com/test/__pycache__/testGIT.cpython-312.pyc,,
win32com/test/__pycache__/testGatewayAddresses.cpython-312.pyc,,
win32com/test/__pycache__/testIterators.cpython-312.pyc,,
win32com/test/__pycache__/testMSOffice.cpython-312.pyc,,
win32com/test/__pycache__/testMSOfficeEvents.cpython-312.pyc,,
win32com/test/__pycache__/testMarshal.cpython-312.pyc,,
win32com/test/__pycache__/testNetscape.cpython-312.pyc,,
win32com/test/__pycache__/testPersist.cpython-312.pyc,,
win32com/test/__pycache__/testPippo.cpython-312.pyc,,
win32com/test/__pycache__/testPyComTest.cpython-312.pyc,,
win32com/test/__pycache__/testROT.cpython-312.pyc,,
win32com/test/__pycache__/testServers.cpython-312.pyc,,
win32com/test/__pycache__/testShell.cpython-312.pyc,,
win32com/test/__pycache__/testStorage.cpython-312.pyc,,
win32com/test/__pycache__/testStreams.cpython-312.pyc,,
win32com/test/__pycache__/testWMI.cpython-312.pyc,,
win32com/test/__pycache__/testall.cpython-312.pyc,,
win32com/test/__pycache__/testmakepy.cpython-312.pyc,,
win32com/test/__pycache__/testvb.cpython-312.pyc,,
win32com/test/__pycache__/testvbscript_regexp.cpython-312.pyc,,
win32com/test/__pycache__/testxslt.cpython-312.pyc,,
win32com/test/__pycache__/util.cpython-312.pyc,,
win32com/test/daodump.py,sha256=M1C4xSiNBm2UgGyVTltkCpdiwBOuD_iW_QPMtZiD9iM,2333
win32com/test/errorSemantics.py,sha256=mMiPvp8Its34qxwv_TCk8deXUVatkchORLvATiHCovY,8767
win32com/test/pippo.idl,sha256=RlCNcIUDLeWIrArOKpUAZTM_tF5Q_5brEN_fPrVNoq0,1916
win32com/test/pippo_server.py,sha256=s7sIy8jP5Z_-2kF5D5YpwIVjqy8bMTO9xsiKeK-f4bY,2823
win32com/test/policySemantics.py,sha256=X0QlROFXvuILHVfHFKdIhkNH_rrR_XFY_0G3Pycpph8,3226
win32com/test/readme.txt,sha256=AKnmwOK-3R3liZJnfspQc8ez7O7oTVVP-gGPboSb5IY,735
win32com/test/testADOEvents.py,sha256=z4kGQeFJ0zr6Ma43W8cBHmvoWsS5_o5Ox7PbtCoZ6WI,2888
win32com/test/testAXScript.py,sha256=qv8IboipRYGnfFNppCDhl8xhSJqZIGDUDpWzrmoaxbQ,1430
win32com/test/testAccess.py,sha256=hnamBPcaQAk1rHhoad4G2IrHFaYJBknbAkg9M_rQeEs,5874
win32com/test/testArrays.py,sha256=NHd38lgvPuSmZMXhxwb7ygcKAvcRWJbaoJZBn0BOsZo,2165
win32com/test/testClipboard.py,sha256=jSV-VmY6cK_WqMlZe-9K3Vg2U2K0qwgcxtLfn4ys-0o,5952
win32com/test/testCollections.py,sha256=UvD4_9DjNgY1Vj4EvDUQCvxvBQYSA97ZdPPuXICMrrc,4400
win32com/test/testConversionErrors.py,sha256=qAr-Xuuqgk7mulH5pTyKFQCrc0BNTgjzZCuTnBMy_P8,713
win32com/test/testDCOM.py,sha256=oeg22IlgE17uKzE7osVkbJlw0P5i61r5bZHJhbmdTzU,1766
win32com/test/testDates.py,sha256=arwZaOWLc1IidoCbrPFh2YiNfbxWacewQ6jXip_JktI,1908
win32com/test/testDictionary.py,sha256=CJpBlJS6nBpMrrGbK36UAd4B10fRyi1c2c7mnGRgajY,2890
win32com/test/testDictionary.vbs,sha256=Ig71tZaLLTYUaGX7O04J_p7hp7VXgcJlFMEoaSNRIMY,584
win32com/test/testDynamic.py,sha256=jO2MRVLnufS2kB4gNaW9H066XmlsVl1rTyGuQdFEjks,2812
win32com/test/testExchange.py,sha256=CRAvP6Z06a2EROm_IZASacR7q9CiBV4i6tQNAKJiuOM,3459
win32com/test/testExplorer.py,sha256=lfQSnc8dLwO8VeMS3Sq-Xft95sM-gDDYyb8O1S3JDxY,4599
win32com/test/testGIT.py,sha256=eqtoiz2ZSgfTxJvg4SJaUVuaCsB0XxzG_0uDWrqKt6s,4766
win32com/test/testGatewayAddresses.py,sha256=sorxV0ybCKME2MDlL1nfYGdVGy9FipXGhku5vDFU3ks,5349
win32com/test/testInterp.vbs,sha256=eRb3rB_-JT8R2luozqSMcZrkGstXAjUkaqCGoYUdHRw,268
win32com/test/testIterators.py,sha256=1hmsbwLHZQw3fXPJs-PgWtcjJMwp9krKilYIOZxou60,4775
win32com/test/testMSOffice.py,sha256=0rTlCLNOcLXqOqEWbk80Vt76fdwvUq1UXSI4RmQsRiE,6054
win32com/test/testMSOfficeEvents.py,sha256=p55bJW_TzP5SEOK-OIr1xCBGOMQpgNfFBf-nCQuAZ1w,4176
win32com/test/testMarshal.py,sha256=66_NdL4JQuMzste7dtNc6r8QJu69qZUESl_P3s4bX2g,6213
win32com/test/testNetscape.py,sha256=haqQ0CObVGk2SB4vN7JQG5BmdVoYqzxcCIggPM5gF_E,633
win32com/test/testPersist.py,sha256=nyxywylF9xon_iLT5rfSKd-AaNGiuh8vAJU2DGrbJxM,6523
win32com/test/testPippo.py,sha256=1scI-xUjKnyCrkenYZmjOivoXNwjuOmexqKB6Tw_zLw,2702
win32com/test/testPyComTest.py,sha256=6lV9QLYL7dTey5LAv9MLCHvgD0dz1acSsHkyP36YDHo,27713
win32com/test/testPyScriptlet.js,sha256=MrtjGik_AqNjSvjmj6mvyNRrQX6kpeKQKQcJQPsGtHc,1120
win32com/test/testROT.py,sha256=JiKNvFDPD4KVXybROKa4hVIaXX6HnAt1csVEyKoQshc,793
win32com/test/testServers.py,sha256=TaGY-vrHmKH5X5n4qJwtvurR_-HL-Fkahgv8zNridU0,1443
win32com/test/testShell.py,sha256=LVzOVnqM-ORarmXuqhCftWSPVkaeYT9-ZNPsDTUy7cA,9694
win32com/test/testStorage.py,sha256=rMf3A6SMNEZnfuVX8WKMx_AWRpMp9sdx1cwu0taDbFk,3707
win32com/test/testStreams.py,sha256=1gwvjh1PO83d3tozS_lqaY0BMANwVdWPO7kftpSMPXE,4336
win32com/test/testWMI.py,sha256=Akc3CRkgTg8OwokTTl28e2R5qmt749lHAJE0a5NgP-A,486
win32com/test/testall.py,sha256=e3bqFKKXd43yj1RXQWA5TNb7BIOgGrUxClsdkdgSR3w,10210
win32com/test/testmakepy.py,sha256=niMSkZ82z4y-LiQFMn393uSRqVZiY2uNvxvhML4SKr0,1953
win32com/test/testvb.py,sha256=GtfEqUSeDJIfVevtb2teRnwhLLqh5J8h62DJJkxvF2A,20697
win32com/test/testvbscript_regexp.py,sha256=CZRY3oq_tENUTVbnrcId-iEr1lwm86i26aRyoLbn-UM,1138
win32com/test/testxslt.js,sha256=pTnGKAzC3JDz1bx_hsG8rsz_I06zBWcSZBvMbMcgeYA,581
win32com/test/testxslt.py,sha256=Y_kKgIWpri9GsvkDofvGHdwjqvLi41n71tAiwepX2PA,948
win32com/test/testxslt.xsl,sha256=r0p8ENxr_X9RWvzTf18ftsT7Mw4f6wRyisSKroOlkPY,2111
win32com/test/util.py,sha256=fR70F-XTf_s1OzPUtx7IG_6arRxVKws7Dsq_udnmXa0,8413
win32com/universal.py,sha256=fQCzZDEamF9WKq3ub7UrLZMj_BbLdreqFyV8rULY_qA,8828
win32com/util.py,sha256=mIeStZYx8N1zanIvLB7NYlrj0E_cyzsjYix-hxjVnjE,1066
win32comext/adsi/__init__.py,sha256=bb9xfunC6BYstd9oBMDXH6-PMPRYAs7pzrIz-hOxxac,3709
win32comext/adsi/__pycache__/__init__.cpython-312.pyc,,
win32comext/adsi/__pycache__/adsicon.cpython-312.pyc,,
win32comext/adsi/adsi.pyd,sha256=X09r6Ub66eKG9Ma5vKTe0YwJqhkogaVLOYGarPWffQs,99328
win32comext/adsi/adsicon.py,sha256=lS8OiWLVMp-JLwMXq7ASjZZs6WW3Uht-dxlV4m8AQF0,12607
win32comext/adsi/demos/__pycache__/objectPicker.cpython-312.pyc,,
win32comext/adsi/demos/__pycache__/scp.cpython-312.pyc,,
win32comext/adsi/demos/__pycache__/search.cpython-312.pyc,,
win32comext/adsi/demos/__pycache__/test.cpython-312.pyc,,
win32comext/adsi/demos/objectPicker.py,sha256=TCvLVqrwQrMV_97-3uvdOHntrjdFH8RNYRnBVepjfXE,2009
win32comext/adsi/demos/scp.py,sha256=nAFMYzP73ZImhWEpd3dAG_ua3Vx3fT3z2DDHW4WOId0,19694
win32comext/adsi/demos/search.py,sha256=bB57Cdqn3ZXiRW3OgEc9llsmIBRdfFN1Bf2uG-H-bio,4312
win32comext/adsi/demos/test.py,sha256=B-5bjQ-H4VqVzjW2monTDBRBSCziG-wV7rhTlV0eEEU,8649
win32comext/authorization/__init__.py,sha256=BIYRSnhdPHSplAvYKL9NBLuQWZ66e-QnJpiVWA_AD38,198
win32comext/authorization/__pycache__/__init__.cpython-312.pyc,,
win32comext/authorization/authorization.pyd,sha256=bCdFFt5BuGsG3uucPXHpfmxPG5qOrl1mE9MEnTYCUZ8,29696
win32comext/authorization/demos/EditSecurity.py,sha256=2sYuN65BmIAhOVGWnhTLbg6zsA5QZyn5KjNwZ3fkge8,9190
win32comext/authorization/demos/EditServiceSecurity.py,sha256=0lLlvgngBSIdcK_olsFxzY4FuIc-mcQZM2dm0XhtrBM,8804
win32comext/authorization/demos/__pycache__/EditSecurity.cpython-312.pyc,,
win32comext/authorization/demos/__pycache__/EditServiceSecurity.cpython-312.pyc,,
win32comext/axcontrol/__init__.py,sha256=3TiL83QLqd52gHqSi3VShEAYlH06hVXuqizvzn1iPRM,139
win32comext/axcontrol/__pycache__/__init__.cpython-312.pyc,,
win32comext/axcontrol/axcontrol.pyd,sha256=sJWAqaRhya3wZRr8byXo5s4dTpBhvpBjpyy676F2FOk,144896
win32comext/axdebug/__init__.py,sha256=3TiL83QLqd52gHqSi3VShEAYlH06hVXuqizvzn1iPRM,139
win32comext/axdebug/__pycache__/__init__.cpython-312.pyc,,
win32comext/axdebug/__pycache__/adb.cpython-312.pyc,,
win32comext/axdebug/__pycache__/codecontainer.cpython-312.pyc,,
win32comext/axdebug/__pycache__/contexts.cpython-312.pyc,,
win32comext/axdebug/__pycache__/debugger.cpython-312.pyc,,
win32comext/axdebug/__pycache__/documents.cpython-312.pyc,,
win32comext/axdebug/__pycache__/dump.cpython-312.pyc,,
win32comext/axdebug/__pycache__/expressions.cpython-312.pyc,,
win32comext/axdebug/__pycache__/gateways.cpython-312.pyc,,
win32comext/axdebug/__pycache__/stackframe.cpython-312.pyc,,
win32comext/axdebug/__pycache__/util.cpython-312.pyc,,
win32comext/axdebug/adb.py,sha256=4q4EnvR_zUVN3vQMlYRLI7dKI7OSA9OSEF-aM79k38E,18142
win32comext/axdebug/codecontainer.py,sha256=5KgVtLXn2YtoV9K-mh4z0v4pvnyh1gwz0Rso6NY7rX0,9279
win32comext/axdebug/contexts.py,sha256=bfmY2scExJofNLF74fPr4zg0IYCL-aiTwyNUsoeEWlk,2130
win32comext/axdebug/debugger.py,sha256=QY_L0r6p_wRGs3OVkbdRhIvM2oJKbUrva4QEaWscxXU,7257
win32comext/axdebug/documents.py,sha256=V7yTzDYa3Y-6OKx7YSPtpA87Lzg4hZgoFJ7OUNjPf68,4300
win32comext/axdebug/dump.py,sha256=fAOwn0ln9VsPK7J4PznyLBT0gYgEu3T9dZG7Qo2i0fw,1837
win32comext/axdebug/expressions.py,sha256=B4JUREjxJ02cDlzvQe-Bt4PIIE8Rqxu6_A3KOuCoixU,6756
win32comext/axdebug/gateways.py,sha256=7FbJGAkBrTdRYUufZFZAFpd4mftY-OupbROK3OT1-UI,17951
win32comext/axdebug/stackframe.py,sha256=dq-bNoJKG0QDUSxflvCg0ULF7ypP2iC__iLzDnJMNE8,6032
win32comext/axdebug/util.py,sha256=i664aaIV5jVE8h0KL-uA29RhCBMFUDQOkdijLZcJK54,3289
win32comext/axscript/Demos/client/asp/CreateObject.asp,sha256=c7M3t9GXd8jFdaP3WV82KuQXgSUgdyekHYHh1jKcXNs,511
win32comext/axscript/Demos/client/asp/caps.asp,sha256=_LezoDQ21IWlVclECk-BvDCjwCKS4y8dH-lAvyFHQoQ,1356
win32comext/axscript/Demos/client/asp/interrupt/test.asp,sha256=MyC-unmCnOZJAtVi5OX6TcwkVGImPTljz8XdVlZWWBA,77
win32comext/axscript/Demos/client/asp/interrupt/test.html,sha256=GrEJ8qR-GtnpgwsrcKaYBRmN40kJ0uzfJpr7KGYCX8g,166
win32comext/axscript/Demos/client/asp/interrupt/test1.asp,sha256=kQdcNnIEpeOwH0g4lTLmumpEIgpcNxljrluhtoxHyIM,94
win32comext/axscript/Demos/client/asp/interrupt/test1.html,sha256=0ddcwP4vNiQk2MoLBjDD53-at-wePcfPRYn4bhafokE,166
win32comext/axscript/Demos/client/asp/tut1.asp,sha256=2c8Iz9W1qWb2pe1ProdeSJgS4vH2f9uwwg3cX8a2PMM,156
win32comext/axscript/Demos/client/ie/CHARTPY.HTM,sha256=JWmTYq16SH0h0vfD7h5oP4Md9hSxOTfMB1JEMqHIkl4,6912
win32comext/axscript/Demos/client/ie/FOO.HTM,sha256=4U5yZiBwkv0lVe4cfngtI2j2NDayk2SKB805oZrWKLU,990
win32comext/axscript/Demos/client/ie/MarqueeText1.htm,sha256=H1TNjr-TzwD61v9HqXaucKWUNVa4TVMWA3rUKp2ZPMg,726
win32comext/axscript/Demos/client/ie/calc.htm,sha256=kSg1xFHXTD6MNNUnAn5kaxVvjge4L60RP5kGYnUyDMo,4145
win32comext/axscript/Demos/client/ie/dbgtest.htm,sha256=vohzifdshgbIHGwWepOLmetfUhryVu6M5rgx6mQU73Q,207
win32comext/axscript/Demos/client/ie/demo.htm,sha256=MWLAko78ZiSfPlAV30ljuzUc-km1IvDyuJ86bla19sE,472
win32comext/axscript/Demos/client/ie/demo_check.htm,sha256=1qZEfQcxBUHNd7kkGDRP60UlzIROTDzGRmw6Kb6KY9U,1537
win32comext/axscript/Demos/client/ie/demo_intro.htm,sha256=GOqroZIb1MWqQ3pT89hMUnwTDIPBu9Z3AIJkhaQNUJc,1608
win32comext/axscript/Demos/client/ie/demo_menu.htm,sha256=aOKKRUlimg5gmFoogBrHJgP3zfQFeUBGVKKC7rZeeGc,507
win32comext/axscript/Demos/client/ie/docwrite.htm,sha256=FdePGctmTQuBQyQD06_wqwGYYpOneiRHu9l7F5ocMvY,492
win32comext/axscript/Demos/client/ie/foo2.htm,sha256=CK7VxClIKnlEcBxtdMlT8xKxKzb_eb9hNiKxk6DgkzM,3610
win32comext/axscript/Demos/client/ie/form.htm,sha256=EXyxmvMNGYLel3UMxWPQrF3D3twi9gqiWEepC1zi4NI,506
win32comext/axscript/Demos/client/ie/marqueeDemo.htm,sha256=meR1CHnWGEOR9DWRApQyIIVUQr0mb-H5BaopgbjxJ3Y,1221
win32comext/axscript/Demos/client/ie/mousetrack.htm,sha256=z2Bs6DBDs8zwrX_85bUPccxIIF4js_5XYUNq45fP7DM,2310
win32comext/axscript/Demos/client/ie/pycom_blowing.gif,sha256=s8ZSBzs8dfWsgTgbb0S43urQZcY1xjdxoIBuSHeLr6o,20926
win32comext/axscript/Demos/client/wsh/blank.pys,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
win32comext/axscript/Demos/client/wsh/excel.pys,sha256=mjfwcHv57O0REt1kiMMaoy2MO1M32rDGygWLF8nMSpQ,1079
win32comext/axscript/Demos/client/wsh/registry.pys,sha256=qjvZxdZ1yBjMo24ERINpFFcE-FsYtoVH0kj1BxqERKU,1667
win32comext/axscript/Demos/client/wsh/test.pys,sha256=Cp_cIXZiljzEjMAZQupISkuciQLxFLPg2RNBIEJYi4Y,384
win32comext/axscript/__init__.py,sha256=3TiL83QLqd52gHqSi3VShEAYlH06hVXuqizvzn1iPRM,139
win32comext/axscript/__pycache__/__init__.cpython-312.pyc,,
win32comext/axscript/__pycache__/asputil.cpython-312.pyc,,
win32comext/axscript/asputil.py,sha256=7XQB05vZlRwk436s_yBLVZILFaf4GOeLAn4dFFeEZ5E,262
win32comext/axscript/axscript.pyd,sha256=mdZ-G6Guf9caj57DK8ONddO__9HD25lmdBKeMt3NY9M,94208
win32comext/axscript/client/__init__.py,sha256=uS688bC0Cpa2R0O_ZEkDfcaaeFIlUOnv7w7G34M-DFI,28
win32comext/axscript/client/__pycache__/__init__.cpython-312.pyc,,
win32comext/axscript/client/__pycache__/debug.cpython-312.pyc,,
win32comext/axscript/client/__pycache__/error.cpython-312.pyc,,
win32comext/axscript/client/__pycache__/framework.cpython-312.pyc,,
win32comext/axscript/client/__pycache__/pydumper.cpython-312.pyc,,
win32comext/axscript/client/__pycache__/pyscript.cpython-312.pyc,,
win32comext/axscript/client/__pycache__/pyscript_rexec.cpython-312.pyc,,
win32comext/axscript/client/__pycache__/scriptdispatch.cpython-312.pyc,,
win32comext/axscript/client/debug.py,sha256=_5rLT3ZCbM9gDSJdlrZJ1SakDqyM55YcudCVtUR14jk,8640
win32comext/axscript/client/error.py,sha256=fPSdT_nI32ygWKvQdWn7rCZhwbZ390E0sVc3I_Hg9Uo,9312
win32comext/axscript/client/framework.py,sha256=P3u-VRzhq3RNfsl1OOavGdTQ_EJUP15cWqUnZRQ5OgM,47526
win32comext/axscript/client/pydumper.py,sha256=v1xo9tRaiY1gH3wzZNJ6Oj7HY6vlF2SFAhu75one_Ww,2300
win32comext/axscript/client/pyscript.py,sha256=6BcJYzFKWa1rCpFBzzXXxsFn-NCVMeJBCtT2gsqO-Yc,15795
win32comext/axscript/client/pyscript_rexec.py,sha256=D2ICO_ztYWdi9OLdQ4iXCn2TiB6cAefyBdgDe7Bs8ac,2203
win32comext/axscript/client/scriptdispatch.py,sha256=fcntK7AV24JPFlDTNN377CVldLktdzQbecam5GQwwcc,3899
win32comext/axscript/server/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
win32comext/axscript/server/__pycache__/__init__.cpython-312.pyc,,
win32comext/axscript/server/__pycache__/axsite.cpython-312.pyc,,
win32comext/axscript/server/axsite.py,sha256=a5KgAg7Q7R-OF78sSMEaoCun9jx4g7O7bIAesTd3lXY,4317
win32comext/axscript/test/__pycache__/leakTest.cpython-312.pyc,,
win32comext/axscript/test/__pycache__/testHost.cpython-312.pyc,,
win32comext/axscript/test/__pycache__/testHost4Dbg.cpython-312.pyc,,
win32comext/axscript/test/debugTest.pys,sha256=-xLy-41ZyVMRP3Lp2uMQqKuOQYLhBK8z6XWeXrpxUgs,217
win32comext/axscript/test/debugTest.vbs,sha256=Cc0iOIEsRM8iw-hrxXqzJxram3hzZGtStIJSEB_0fFo,89
win32comext/axscript/test/leakTest.py,sha256=VqkMMbYwQyOq8DhjWG4Mz3gT9GQsHZzpxb9NHfXdqjM,4872
win32comext/axscript/test/testHost.py,sha256=Bem3PWcz_VU7nf1OpAwNrgit2JAQ1DqX3dTztpNdnlI,7923
win32comext/axscript/test/testHost4Dbg.py,sha256=jX0L_z04XeJXJgr5AgELwRJdt24rbdaH2ntRq3U0xAc,2811
win32comext/bits/__init__.py,sha256=BIYRSnhdPHSplAvYKL9NBLuQWZ66e-QnJpiVWA_AD38,198
win32comext/bits/__pycache__/__init__.cpython-312.pyc,,
win32comext/bits/bits.pyd,sha256=3Vq9CssLLtzRhJSl81Zuvhg11O_5jW-9p3cbXKK9d6Q,63488
win32comext/bits/test/__pycache__/show_all_jobs.cpython-312.pyc,,
win32comext/bits/test/__pycache__/test_bits.cpython-312.pyc,,
win32comext/bits/test/show_all_jobs.py,sha256=hATQ6hLrRVueEVWWFsRhgq2RqibWCvuoaAKuNj4Sj28,1567
win32comext/bits/test/test_bits.py,sha256=jPjmArlhVJQGIJSLMKsfV4HU5Zqxid6tYteOA50H3hU,3996
win32comext/directsound/__init__.py,sha256=3TiL83QLqd52gHqSi3VShEAYlH06hVXuqizvzn1iPRM,139
win32comext/directsound/__pycache__/__init__.cpython-312.pyc,,
win32comext/directsound/directsound.pyd,sha256=_TPdao_lyA-EgEBLpqhGsMDSH2MAygeU7YXlz9nwUuM,77312
win32comext/directsound/test/__init__.py,sha256=9sn5qeYopF3bGoO97Wh548otV_NI_ZFFJumrGOyfpxM,66
win32comext/directsound/test/__pycache__/__init__.cpython-312.pyc,,
win32comext/directsound/test/__pycache__/ds_record.cpython-312.pyc,,
win32comext/directsound/test/__pycache__/ds_test.cpython-312.pyc,,
win32comext/directsound/test/ds_record.py,sha256=QnUNr3KfhpdHoxjhXzXutqimLkh9RST1iNlAfHuWREs,1463
win32comext/directsound/test/ds_test.py,sha256=deTEW_dc-lpzHlPSvJ49zEgdmHGukmdK0kpcbezojWw,13357
win32comext/ifilter/__init__.py,sha256=z1QtDSL_L8LgjMS57ub2XbTbTkYZTzbDjip25XcUGwc,41
win32comext/ifilter/__pycache__/__init__.cpython-312.pyc,,
win32comext/ifilter/__pycache__/ifiltercon.cpython-312.pyc,,
win32comext/ifilter/demo/__pycache__/filterDemo.cpython-312.pyc,,
win32comext/ifilter/demo/filterDemo.py,sha256=nfozgtNmRK5XP_TxhoJBy4KSOiKTS80BBxTnThZ4l_U,11774
win32comext/ifilter/ifilter.pyd,sha256=BSb7ElNHth8yIjyQVPn7uw79ZWD-dnnIWpQ7_nVwFc8,30720
win32comext/ifilter/ifiltercon.py,sha256=rMkE-I3MB9UlO__mLdfJuWdB9hmXWTI-vEQAYZY9JdQ,3269
win32comext/internet/__init__.py,sha256=3TiL83QLqd52gHqSi3VShEAYlH06hVXuqizvzn1iPRM,139
win32comext/internet/__pycache__/__init__.cpython-312.pyc,,
win32comext/internet/__pycache__/inetcon.cpython-312.pyc,,
win32comext/internet/inetcon.py,sha256=_W1SWateDPSBC-SpIb0dv-HyxRbuulys6HUvp3hvkic,11879
win32comext/internet/internet.pyd,sha256=8fXtzbcPgsg_bEYYIUqV8W_OG6a3_XJaR1vUa-n-vxA,94208
win32comext/mapi/__init__.py,sha256=9Ej7exvXJ6Fvoeo79x87FxUtlCo2bddHaiz1ZgYjfLE,490
win32comext/mapi/__pycache__/__init__.cpython-312.pyc,,
win32comext/mapi/__pycache__/emsabtags.cpython-312.pyc,,
win32comext/mapi/__pycache__/mapitags.cpython-312.pyc,,
win32comext/mapi/__pycache__/mapiutil.cpython-312.pyc,,
win32comext/mapi/demos/__pycache__/mapisend.cpython-312.pyc,,
win32comext/mapi/demos/mapisend.py,sha256=PDB9lJPiaLT0zx3bDUQhR6sLQeFAmX-e4wdTOjKu5JU,3656
win32comext/mapi/emsabtags.py,sha256=Yz65QhyGGNUhzI1rjEWImggTDRpYd7u40vcHgr9avdg,50194
win32comext/mapi/exchange.pyd,sha256=MnJHm2Xr_z6gXae467BsOEsjl2c-1DY6QlhGoZeTR_U,89088
win32comext/mapi/mapi.pyd,sha256=hcoSkDS3OZLm6FguLUVJQ4ORQSpRG985HD8_eTw6xiM,198144
win32comext/mapi/mapitags.py,sha256=R5ahaJP5vL_FLNG3uv1j9nCwKV_XccSdaPEib_9-6yo,52461
win32comext/mapi/mapiutil.py,sha256=d9Ens-iMfPA9atlvN2iJDQp1LKGox8OKC1nhIFJn-Jo,7191
win32comext/propsys/__init__.py,sha256=N4aoy_WnbMMu8gW69ZKrqHwchL0P5IJ7BzK01zsQPdY,28
win32comext/propsys/__pycache__/__init__.cpython-312.pyc,,
win32comext/propsys/__pycache__/pscon.cpython-312.pyc,,
win32comext/propsys/propsys.pyd,sha256=gCPaE5wJD3LV7l2bAjjtkCs2HwNpvyf0uyihIZMGZHs,139776
win32comext/propsys/pscon.py,sha256=kd9pPHutVlebm_sY1UnrH4B0lqLXC9G5NPM-8MieJMc,49460
win32comext/propsys/test/__pycache__/testpropsys.cpython-312.pyc,,
win32comext/propsys/test/testpropsys.py,sha256=Jq2-EZUrh0kaIU_Ire45M7ruN40wwloYdUr8siO-0vM,211
win32comext/shell/__init__.py,sha256=3TiL83QLqd52gHqSi3VShEAYlH06hVXuqizvzn1iPRM,139
win32comext/shell/__pycache__/__init__.cpython-312.pyc,,
win32comext/shell/__pycache__/shellcon.cpython-312.pyc,,
win32comext/shell/demos/IActiveDesktop.py,sha256=Acjy6d3ps3gUyBF6i5CCZhwTthTkh44_ZGD65RHlreQ,2205
win32comext/shell/demos/IFileOperationProgressSink.py,sha256=cvNYEVdgUirgAhDVEFL37HNORfjc47T6mhcR2gB9sZM,5315
win32comext/shell/demos/IShellLinkDataList.py,sha256=V1eyf38zH9nJko9G0Gy7ovFCcBuADmp8oOI36Z7NZEo,1984
win32comext/shell/demos/ITransferAdviseSink.py,sha256=uhSt7W0tNd-YvsmNu_bZiz7dozpbpLB4iqb7QqNStfk,2643
win32comext/shell/demos/IUniformResourceLocator.py,sha256=6MDOp7wXfe_loRT7Ua8s_D3uc9Dq_Idku4kyLO1H2SM,1708
win32comext/shell/demos/__pycache__/IActiveDesktop.cpython-312.pyc,,
win32comext/shell/demos/__pycache__/IFileOperationProgressSink.cpython-312.pyc,,
win32comext/shell/demos/__pycache__/IShellLinkDataList.cpython-312.pyc,,
win32comext/shell/demos/__pycache__/ITransferAdviseSink.cpython-312.pyc,,
win32comext/shell/demos/__pycache__/IUniformResourceLocator.cpython-312.pyc,,
win32comext/shell/demos/__pycache__/browse_for_folder.cpython-312.pyc,,
win32comext/shell/demos/__pycache__/create_link.cpython-312.pyc,,
win32comext/shell/demos/__pycache__/dump_link.cpython-312.pyc,,
win32comext/shell/demos/__pycache__/explorer_browser.cpython-312.pyc,,
win32comext/shell/demos/__pycache__/shellexecuteex.cpython-312.pyc,,
win32comext/shell/demos/__pycache__/viewstate.cpython-312.pyc,,
win32comext/shell/demos/__pycache__/walk_shell_folders.cpython-312.pyc,,
win32comext/shell/demos/browse_for_folder.py,sha256=PslxhEe1d_sXPj6GizQkH7z0YJNbKs7IxwLbgUWg3KU,1547
win32comext/shell/demos/create_link.py,sha256=9ssi5ha1l-tq0bQMXn60HULWP_hwgBU_649uaBPfluQ,2409
win32comext/shell/demos/dump_link.py,sha256=RtYwKDA1d4vctf9gYQqYVnRQVkIxO8J1BVwAStjFhb4,1746
win32comext/shell/demos/explorer_browser.py,sha256=xOajW3y7WdGVqqz3x0CrpTSofC399VrhUQqYqjpk3xs,5099
win32comext/shell/demos/servers/__pycache__/column_provider.cpython-312.pyc,,
win32comext/shell/demos/servers/__pycache__/context_menu.cpython-312.pyc,,
win32comext/shell/demos/servers/__pycache__/copy_hook.cpython-312.pyc,,
win32comext/shell/demos/servers/__pycache__/empty_volume_cache.cpython-312.pyc,,
win32comext/shell/demos/servers/__pycache__/folder_view.cpython-312.pyc,,
win32comext/shell/demos/servers/__pycache__/icon_handler.cpython-312.pyc,,
win32comext/shell/demos/servers/__pycache__/shell_view.cpython-312.pyc,,
win32comext/shell/demos/servers/column_provider.py,sha256=atz7cyfmFwklq8CWiLaKQT1yrCJk55blaTx8L8l8udE,3864
win32comext/shell/demos/servers/context_menu.py,sha256=ZI_sgpYLtr1vm3nz0XyCJExmgepUaIaJojUc23uhmAc,4539
win32comext/shell/demos/servers/copy_hook.py,sha256=trRaIj6phBYy4ETCbtRleMhr9M2u50szNpwxv6WMxPk,2765
win32comext/shell/demos/servers/empty_volume_cache.py,sha256=u0XO_GP2oAE3JiBDlzzJHblMFSVygLLIbtGErfr3Gxg,7819
win32comext/shell/demos/servers/folder_view.py,sha256=qI7zEzl3LiQJh5i_-xfz1dl42TfNoYKHHpU8pIQ-DFM,30130
win32comext/shell/demos/servers/icon_handler.py,sha256=2-YeG1d5pMlWOujKADKcTFEZqM2ZdGrxDCWjUItqVvo,2627
win32comext/shell/demos/servers/shell_view.py,sha256=plN2zDu9yuCsAI7j21SLwZMnObyhzk3JBMOVa2hvlqc,38191
win32comext/shell/demos/shellexecuteex.py,sha256=xxXKf9kwSmVOGWix0z0J3X4yMyL-P0PfChEOznL3lv8,489
win32comext/shell/demos/viewstate.py,sha256=Ob1c8ZAMDi_VQAXHiF7wFViIsH7b0QBFjpbmpojMgpY,2386
win32comext/shell/demos/walk_shell_folders.py,sha256=BBAmPYxizQYCTyXx7tjVa9tnWyrHnbDowc1e96y-HNM,693
win32comext/shell/shell.pyd,sha256=MJmwG5kEYcsODWJ1BO2JHsuE5UrN3FaiP-7x9Jw-nJE,538112
win32comext/shell/shellcon.py,sha256=I-XceAQw0OuRk1kmo5tFoN8zNCxmDYPrqHiEwnLi3EY,51330
win32comext/shell/test/__pycache__/testSHFileOperation.cpython-312.pyc,,
win32comext/shell/test/__pycache__/testShellFolder.cpython-312.pyc,,
win32comext/shell/test/__pycache__/testShellItem.cpython-312.pyc,,
win32comext/shell/test/testSHFileOperation.py,sha256=R2Eyz7EHY-dYe5ABOo8ldaqUheRQEmcfdSImZjjZ2kk,2167
win32comext/shell/test/testShellFolder.py,sha256=Fy_v0son7-j-A6Y23qVtUjFR3Aoc9ajrtWoMaKcZ72U,603
win32comext/shell/test/testShellItem.py,sha256=gAEEunh8AJE3aNHzPjl-cV9gfN4T28Nut4wHh6ZpXFk,2960
win32comext/taskscheduler/__init__.py,sha256=BIYRSnhdPHSplAvYKL9NBLuQWZ66e-QnJpiVWA_AD38,198
win32comext/taskscheduler/__pycache__/__init__.cpython-312.pyc,,
win32comext/taskscheduler/taskscheduler.pyd,sha256=X2P3-STwNzkX91gdaePblWdIiTB3dsHynuM2Q1o_dmk,53760
win32comext/taskscheduler/test/__pycache__/test_addtask.cpython-312.pyc,,
win32comext/taskscheduler/test/__pycache__/test_addtask_1.cpython-312.pyc,,
win32comext/taskscheduler/test/__pycache__/test_addtask_2.cpython-312.pyc,,
win32comext/taskscheduler/test/__pycache__/test_localsystem.cpython-312.pyc,,
win32comext/taskscheduler/test/test_addtask.py,sha256=Hms1Kl-wNf27Mf5lz1zjZhsi0UUofwHNnn9qhZvLyyw,2278
win32comext/taskscheduler/test/test_addtask_1.py,sha256=OrnDtPx_8SyG0u0_7ag6emXa1hlGFYva7Hofiwey6vo,2160
win32comext/taskscheduler/test/test_addtask_2.py,sha256=t-_ryc2-ggCOm_hi3KZpqDUdtz1mtcVykbuxWt27ayY,1676
win32comext/taskscheduler/test/test_localsystem.py,sha256=08ojAS48W6RLsUbRD45j0SJhg_Y2NFHZT6qjT4Vrig0,75
