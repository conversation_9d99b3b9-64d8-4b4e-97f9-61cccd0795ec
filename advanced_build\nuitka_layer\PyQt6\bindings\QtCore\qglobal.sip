// qglobal.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qglobal.h>
%End

const int QT_VERSION;
const char *QT_VERSION_STR;
%If (- Qt_6_5_0)
const char *qVersion();
%End
%If (- Qt_6_5_0)
typedef signed char qint8 /PyInt/;
%End
%If (- Qt_6_5_0)
typedef unsigned char quint8 /PyInt/;
%End
%If (- Qt_6_5_0)
typedef short qint16;
%End
%If (- Qt_6_5_0)
typedef unsigned short quint16;
%End
%If (- Qt_6_5_0)
typedef int qint32;
%End
%If (- Qt_6_5_0)
typedef unsigned int quint32;
%End
%If (- Qt_6_5_0)
typedef long long qint64;
%End
%If (- Qt_6_5_0)
typedef unsigned long long quint64;
%End
%If (- Qt_6_5_0)
typedef qint64 qlonglong;
%End
%If (- Qt_6_5_0)
typedef quint64 qulonglong;
%End
%If (- Qt_6_5_0)
%If (PyQt_qreal_double)
typedef double qreal;
%End
%End
%If (- Qt_6_5_0)
%If (!PyQt_qreal_double)
typedef float qreal;
%End
%End
%If (- Qt_6_5_0)
typedef unsigned char uchar;
%End
%If (- Qt_6_5_0)
typedef unsigned short ushort;
%End
%If (- Qt_6_5_0)
typedef unsigned int uint;
%End
%If (- Qt_6_5_0)
typedef unsigned long ulong;
%End
%If (- Qt_6_5_0)
typedef long long qsizetype;
%End
%If (- Qt_6_5_0)
double qAbs(const double &t);
%End
%If (- Qt_6_5_0)
int qRound(qreal d);
%End
%If (- Qt_6_5_0)
qint64 qRound64(qreal d);
%End
%If (- Qt_6_5_0)
bool qFuzzyCompare(double p1, double p2);
%End
typedef void *QFunctionPointer;
// Minimal support for the Qt resource system to help porting from PyQt5.
%ModuleCode
QT_BEGIN_NAMESPACE
extern bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
extern bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
QT_END_NAMESPACE
%End

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
// Mapped type for qintptr.
// Map qintptr onto sip.voidptr.  This means either an address (on Windows) or
// an integer file descriptor (on everything else) can be used.
%MappedType qintptr /TypeHint="PyQt6.sip.voidptr"/
{
%TypeHeaderCode
#include <QtGlobal>
%End

%ConvertToTypeCode
    qintptr ptr = (qintptr)sipConvertToVoidPtr(sipPy);

    if (!sipIsErr)
        return !PyErr_Occurred();

    // Mapped types deal with pointers, so create one on the heap.
    qintptr *heap = new qintptr;
    *heap = ptr;

    *sipCppPtr = heap;

    // Make sure the pointer doesn't leak.
    return SIP_TEMPORARY;
%End

%ConvertFromTypeCode
    return sipConvertFromVoidPtr((void *)*sipCpp);
%End
};
// Mapped type for quintptr.
// Map quintptr onto sip.voidptr.  This means either an address (on Windows) or
// an integer file descriptor (on everything else) can be used.
%MappedType quintptr /TypeHint="PyQt6.sip.voidptr"/
{
%TypeHeaderCode
#include <QtGlobal>
%End

%ConvertToTypeCode
    quintptr ptr = (quintptr)sipConvertToVoidPtr(sipPy);

    if (!sipIsErr)
        return !PyErr_Occurred();

    // Mapped types deal with pointers, so create one on the heap.
    quintptr *heap = new quintptr;
    *heap = ptr;

    *sipCppPtr = heap;

    // Make sure the pointer doesn't leak.
    return SIP_TEMPORARY;
%End

%ConvertFromTypeCode
    return sipConvertFromVoidPtr((void *)*sipCpp);
%End
};
// Implementations of pyqt[Set]PickleProtocol().
void pyqtSetPickleProtocol(SIP_PYOBJECT /TypeHint="Optional[int]"/);
%MethodCode
    Py_XDECREF(qpycore_pickle_protocol);
    qpycore_pickle_protocol = a0;
    Py_INCREF(qpycore_pickle_protocol);
%End

SIP_PYOBJECT pyqtPickleProtocol() /TypeHint="Optional[int]"/;
%MethodCode
    sipRes = qpycore_pickle_protocol;
    if (!sipRes)
        sipRes = Py_None;
    
    Py_INCREF(sipRes);
%End
%If (- Qt_6_5_0)
bool qEnvironmentVariableIsEmpty(const char *varName);
%End
%If (- Qt_6_5_0)
bool qEnvironmentVariableIsSet(const char *varName);
%End
%If (- Qt_6_5_0)
QString qEnvironmentVariable(const char *varName);
%End
%If (- Qt_6_5_0)
QString qEnvironmentVariable(const char *varName, const QString &defaultValue);
%End
%If (- Qt_6_5_0)
int qEnvironmentVariableIntValue(const char *varName, bool *ok = 0);
%End
