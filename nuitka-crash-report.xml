<?xml version='1.0' encoding='utf8'?>
<nuitka-compilation-report nuitka_version="2.6.8" nuitka_commercial_version="not installed" mode="onefile" completion="exception">
  <exception exception_type="ValueError" exception_value="read length must be non-negative or -1">
Traceback (most recent call last):
  File "D:\py\shuajishangyong\shuaji\venv\Lib\site-packages\nuitka\MainControl.py", line 1159, in main
    _main()
  File "D:\py\shuajishangyong\shuaji\venv\Lib\site-packages\nuitka\MainControl.py", line 1033, in _main
    executePostProcessing()
  File "D:\py\shuajishangyong\shuaji\venv\Lib\site-packages\nuitka\PostProcessing.py", line 369, in executePostProcessing
    executePostProcessingResources(manifest=manifest, onefile=False)
  File "D:\py\shuajishangyong\shuaji\venv\Lib\site-packages\nuitka\PostProcessing.py", line 332, in executePostProcessingResources
    _addWindowsIconFromIcons(onefile=onefile)
  File "D:\py\shuajishangyong\shuaji\venv\Lib\site-packages\nuitka\PostProcessing.py", line 168, in _addWindowsIconFromIcons
    images.append(icon_file.read(icon.image_size))
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ValueError: read length must be non-negative or -1
</exception>
  <module name="PyQt6" kind="CompiledPythonPackage" usage="package" reason="Containing package of 'PyQt6.QtWidgets'." source_path="${sys.real_prefix}\shuajishangyong\shuaji\PyQt6\__init__.py" distribution="PyQt6,PyQt6-Qt6,PyQt6_sip">
    <plugin-influence name="options-nanny" influence="condition-used" condition="use_pyqt6" tags_used="use_pyqt6" result="true" />
    <plugin-influence name="options-nanny" influence="condition-used" condition="macos and use_pyqt6" tags_used="macos" result="false" />
    <optimization-time pass="1" time="0.08" micro_passes="5" max_branch_merge="31" merged_total="220" />
    <optimization-time pass="2" time="0.01" micro_passes="1" max_branch_merge="21" merged_total="41" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="pkgutil" finding="absolute" line="20" />
      <module_usage name="os" finding="absolute" line="24" />
      <module_usage name="sys" finding="absolute" line="24" />
    </module_usages>
  </module>
  <module name="PyQt6-preLoad" kind="CompiledPythonModule" usage="plugins" reason="Adding binary folder to runtime 'PATH' environment variable for proper Qt loading." source_path="${sys.real_prefix}\shuajishangyong\shuaji\PyQt6\PyQt6-preLoad.py">
    <optimization-time pass="1" time="0.01" micro_passes="3" max_branch_merge="2" merged_total="6" />
    <optimization-time pass="2" time="0.01" micro_passes="1" max_branch_merge="2" merged_total="2" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
    </module_usages>
  </module>
  <module name="PyQt6.QtCore" kind="PythonExtensionModule" usage="root_module" reason="Root module" source_path="${sys.real_prefix}\shuajishangyong\shuaji\PyQt6\QtCore.pyd" distribution="PyQt6,PyQt6-Qt6,PyQt6_sip">
    <optimization-time pass="1" time="0.04" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="PyQt6.QtCore-postLoad" kind="CompiledPythonModule" usage="plugins" reason="Setting Qt library path to distribution folder. We need to avoid loading target&#10;system Qt plugins, which may be from another Qt version." source_path="${sys.real_prefix}\shuajishangyong\shuaji\PyQt6\PyQt6\QtCore-postLoad.py" distribution="PyQt6,PyQt6-Qt6,PyQt6_sip">
    <optimization-time pass="1" time="0.02" micro_passes="3" />
    <optimization-time pass="2" time="0.00" micro_passes="1" />
    <module_usages>
      <module_usage name="__future__" finding="absolute" line="1" />
      <module_usage name="PyQt6.QtCore" finding="absolute" line="3" />
      <module_usage name="os" finding="absolute" line="4" />
      <module_usage name="ntpath" finding="absolute" line="19" />
    </module_usages>
  </module>
  <module name="PyQt6.QtGui" kind="PythonExtensionModule" usage="root_module" reason="Root module" source_path="${sys.real_prefix}\shuajishangyong\shuaji\PyQt6\QtGui.pyd" distribution="PyQt6,PyQt6-Qt6,PyQt6_sip">
    <optimization-time pass="1" time="0.03" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="PyQt6.QtWidgets" kind="PythonExtensionModule" usage="root_module" reason="Root module" source_path="${sys.real_prefix}\shuajishangyong\shuaji\PyQt6\QtWidgets.pyd" distribution="PyQt6,PyQt6-Qt6,PyQt6_sip">
    <optimization-time pass="1" time="0.24" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="PyQt6.sip" kind="PythonExtensionModule" usage="plugin:implicit-imports" reason="Extension module needed for standalone mode." source_path="${sys.real_prefix}\shuajishangyong\shuaji\PyQt6\sip.cp312-win_amd64.pyd" distribution="PyQt6,PyQt6-Qt6,PyQt6_sip">
    <optimization-time pass="1" time="0.00" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="__future__" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\__future__.py">
    <optimization-time pass="1" time="0.04" micro_passes="5" max_branch_merge="8" merged_total="81" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="__hello__" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\__hello__.py">
    <optimization-time pass="1" time="0.03" micro_passes="5" max_branch_merge="8" merged_total="130" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="__main__" kind="PythonMainModule" usage="root_module" reason="Root module" source_path="${sys.real_prefix}\shuajishangyong\shuaji\temp_build\main.py">
    <optimization-time pass="1" time="1.16" micro_passes="7" max_branch_merge="44" merged_total="2554" />
    <optimization-time pass="2" time="0.08" micro_passes="1" max_branch_merge="39" merged_total="336" />
    <distribution-usages>
      <distribution-usage name="psutil" />
      <distribution-usage name="pywin32" />
      <distribution-usage name="PyQt6" />
      <distribution-usage name="PyQt6-Qt6" />
      <distribution-usage name="PyQt6_sip" />
    </distribution-usages>
    <module_usages>
      <module_usage name="sys" finding="absolute" line="4" />
      <module_usage name="os" finding="absolute" line="5" />
      <module_usage name="subprocess" finding="absolute" line="6" />
      <module_usage name="atexit" finding="absolute" line="7" />
      <module_usage name="shutil" finding="absolute" line="8" />
      <module_usage name="time" finding="absolute" line="9" />
      <module_usage name="psutil" finding="absolute" line="10" />
      <module_usage name="tempfile" finding="absolute" line="11" />
      <module_usage name="ctypes" finding="absolute" line="12" />
      <module_usage name="win32security" finding="absolute" line="13" />
      <module_usage name="ntsecuritycon" finding="absolute" line="14" />
      <module_usage name="PyQt6.QtWidgets" finding="absolute" line="15" />
      <module_usage name="PyQt6.QtGui" finding="absolute" line="16" />
      <module_usage name="flash_tool" finding="absolute" line="17" />
      <module_usage name="glob" finding="absolute" line="18" />
      <module_usage name="ntpath" finding="absolute" line="40" />
      <module_usage name="ntpath" finding="absolute" line="50" />
      <module_usage name="ntpath" finding="absolute" line="75" />
      <module_usage name="ntpath" finding="absolute" line="77" />
      <module_usage name="_collections_abc" finding="absolute" line="1" />
      <module_usage name="_weakrefset" finding="absolute" line="1" />
      <module_usage name="abc" finding="absolute" line="1" />
      <module_usage name="ast" finding="absolute" line="1" />
      <module_usage name="codecs" finding="absolute" line="1" />
      <module_usage name="collections" finding="absolute" line="1" />
      <module_usage name="collections.abc" finding="absolute" line="1" />
      <module_usage name="contextlib" finding="absolute" line="1" />
      <module_usage name="copyreg" finding="absolute" line="1" />
      <module_usage name="dis" finding="absolute" line="1" />
      <module_usage name="encodings" finding="absolute" line="1" />
      <module_usage name="encodings.aliases" finding="absolute" line="1" />
      <module_usage name="encodings.ascii" finding="absolute" line="1" />
      <module_usage name="encodings.big5" finding="absolute" line="1" />
      <module_usage name="encodings.big5hkscs" finding="absolute" line="1" />
      <module_usage name="encodings.charmap" finding="absolute" line="1" />
      <module_usage name="encodings.cp037" finding="absolute" line="1" />
      <module_usage name="encodings.cp1006" finding="absolute" line="1" />
      <module_usage name="encodings.cp1026" finding="absolute" line="1" />
      <module_usage name="encodings.cp1125" finding="absolute" line="1" />
      <module_usage name="encodings.cp1140" finding="absolute" line="1" />
      <module_usage name="encodings.cp1250" finding="absolute" line="1" />
      <module_usage name="encodings.cp1251" finding="absolute" line="1" />
      <module_usage name="encodings.cp1252" finding="absolute" line="1" />
      <module_usage name="encodings.cp1253" finding="absolute" line="1" />
      <module_usage name="encodings.cp1254" finding="absolute" line="1" />
      <module_usage name="encodings.cp1255" finding="absolute" line="1" />
      <module_usage name="encodings.cp1256" finding="absolute" line="1" />
      <module_usage name="encodings.cp1257" finding="absolute" line="1" />
      <module_usage name="encodings.cp1258" finding="absolute" line="1" />
      <module_usage name="encodings.cp273" finding="absolute" line="1" />
      <module_usage name="encodings.cp424" finding="absolute" line="1" />
      <module_usage name="encodings.cp437" finding="absolute" line="1" />
      <module_usage name="encodings.cp500" finding="absolute" line="1" />
      <module_usage name="encodings.cp720" finding="absolute" line="1" />
      <module_usage name="encodings.cp737" finding="absolute" line="1" />
      <module_usage name="encodings.cp775" finding="absolute" line="1" />
      <module_usage name="encodings.cp850" finding="absolute" line="1" />
      <module_usage name="encodings.cp852" finding="absolute" line="1" />
      <module_usage name="encodings.cp855" finding="absolute" line="1" />
      <module_usage name="encodings.cp856" finding="absolute" line="1" />
      <module_usage name="encodings.cp857" finding="absolute" line="1" />
      <module_usage name="encodings.cp858" finding="absolute" line="1" />
      <module_usage name="encodings.cp860" finding="absolute" line="1" />
      <module_usage name="encodings.cp861" finding="absolute" line="1" />
      <module_usage name="encodings.cp862" finding="absolute" line="1" />
      <module_usage name="encodings.cp863" finding="absolute" line="1" />
      <module_usage name="encodings.cp864" finding="absolute" line="1" />
      <module_usage name="encodings.cp865" finding="absolute" line="1" />
      <module_usage name="encodings.cp866" finding="absolute" line="1" />
      <module_usage name="encodings.cp869" finding="absolute" line="1" />
      <module_usage name="encodings.cp874" finding="absolute" line="1" />
      <module_usage name="encodings.cp875" finding="absolute" line="1" />
      <module_usage name="encodings.cp932" finding="absolute" line="1" />
      <module_usage name="encodings.cp949" finding="absolute" line="1" />
      <module_usage name="encodings.cp950" finding="absolute" line="1" />
      <module_usage name="encodings.euc_jis_2004" finding="absolute" line="1" />
      <module_usage name="encodings.euc_jisx0213" finding="absolute" line="1" />
      <module_usage name="encodings.euc_jp" finding="absolute" line="1" />
      <module_usage name="encodings.euc_kr" finding="absolute" line="1" />
      <module_usage name="encodings.gb18030" finding="absolute" line="1" />
      <module_usage name="encodings.gb2312" finding="absolute" line="1" />
      <module_usage name="encodings.gbk" finding="absolute" line="1" />
      <module_usage name="encodings.hp_roman8" finding="absolute" line="1" />
      <module_usage name="encodings.hz" finding="absolute" line="1" />
      <module_usage name="encodings.iso2022_jp" finding="absolute" line="1" />
      <module_usage name="encodings.iso2022_jp_1" finding="absolute" line="1" />
      <module_usage name="encodings.iso2022_jp_2" finding="absolute" line="1" />
      <module_usage name="encodings.iso2022_jp_2004" finding="absolute" line="1" />
      <module_usage name="encodings.iso2022_jp_3" finding="absolute" line="1" />
      <module_usage name="encodings.iso2022_jp_ext" finding="absolute" line="1" />
      <module_usage name="encodings.iso2022_kr" finding="absolute" line="1" />
      <module_usage name="encodings.iso8859_1" finding="absolute" line="1" />
      <module_usage name="encodings.iso8859_10" finding="absolute" line="1" />
      <module_usage name="encodings.iso8859_11" finding="absolute" line="1" />
      <module_usage name="encodings.iso8859_13" finding="absolute" line="1" />
      <module_usage name="encodings.iso8859_14" finding="absolute" line="1" />
      <module_usage name="encodings.iso8859_15" finding="absolute" line="1" />
      <module_usage name="encodings.iso8859_16" finding="absolute" line="1" />
      <module_usage name="encodings.iso8859_2" finding="absolute" line="1" />
      <module_usage name="encodings.iso8859_3" finding="absolute" line="1" />
      <module_usage name="encodings.iso8859_4" finding="absolute" line="1" />
      <module_usage name="encodings.iso8859_5" finding="absolute" line="1" />
      <module_usage name="encodings.iso8859_6" finding="absolute" line="1" />
      <module_usage name="encodings.iso8859_7" finding="absolute" line="1" />
      <module_usage name="encodings.iso8859_8" finding="absolute" line="1" />
      <module_usage name="encodings.iso8859_9" finding="absolute" line="1" />
      <module_usage name="encodings.johab" finding="absolute" line="1" />
      <module_usage name="encodings.koi8_r" finding="absolute" line="1" />
      <module_usage name="encodings.koi8_t" finding="absolute" line="1" />
      <module_usage name="encodings.koi8_u" finding="absolute" line="1" />
      <module_usage name="encodings.kz1048" finding="absolute" line="1" />
      <module_usage name="encodings.latin_1" finding="absolute" line="1" />
      <module_usage name="encodings.mac_arabic" finding="absolute" line="1" />
      <module_usage name="encodings.mac_croatian" finding="absolute" line="1" />
      <module_usage name="encodings.mac_cyrillic" finding="absolute" line="1" />
      <module_usage name="encodings.mac_farsi" finding="absolute" line="1" />
      <module_usage name="encodings.mac_greek" finding="absolute" line="1" />
      <module_usage name="encodings.mac_iceland" finding="absolute" line="1" />
      <module_usage name="encodings.mac_latin2" finding="absolute" line="1" />
      <module_usage name="encodings.mac_roman" finding="absolute" line="1" />
      <module_usage name="encodings.mac_romanian" finding="absolute" line="1" />
      <module_usage name="encodings.mac_turkish" finding="absolute" line="1" />
      <module_usage name="encodings.mbcs" finding="absolute" line="1" />
      <module_usage name="encodings.oem" finding="absolute" line="1" />
      <module_usage name="encodings.palmos" finding="absolute" line="1" />
      <module_usage name="encodings.ptcp154" finding="absolute" line="1" />
      <module_usage name="encodings.quopri_codec" finding="absolute" line="1" />
      <module_usage name="encodings.raw_unicode_escape" finding="absolute" line="1" />
      <module_usage name="encodings.shift_jis" finding="absolute" line="1" />
      <module_usage name="encodings.shift_jis_2004" finding="absolute" line="1" />
      <module_usage name="encodings.shift_jisx0213" finding="absolute" line="1" />
      <module_usage name="encodings.tis_620" finding="absolute" line="1" />
      <module_usage name="encodings.undefined" finding="absolute" line="1" />
      <module_usage name="encodings.unicode_escape" finding="absolute" line="1" />
      <module_usage name="encodings.utf_16" finding="absolute" line="1" />
      <module_usage name="encodings.utf_16_be" finding="absolute" line="1" />
      <module_usage name="encodings.utf_16_le" finding="absolute" line="1" />
      <module_usage name="encodings.utf_32" finding="absolute" line="1" />
      <module_usage name="encodings.utf_32_be" finding="absolute" line="1" />
      <module_usage name="encodings.utf_32_le" finding="absolute" line="1" />
      <module_usage name="encodings.utf_7" finding="absolute" line="1" />
      <module_usage name="encodings.utf_8" finding="absolute" line="1" />
      <module_usage name="encodings.utf_8_sig" finding="absolute" line="1" />
      <module_usage name="encodings.uu_codec" finding="absolute" line="1" />
      <module_usage name="encodings.zlib_codec" finding="absolute" line="1" />
      <module_usage name="enum" finding="absolute" line="1" />
      <module_usage name="functools" finding="absolute" line="1" />
      <module_usage name="genericpath" finding="absolute" line="1" />
      <module_usage name="importlib" finding="absolute" line="1" />
      <module_usage name="importlib._bootstrap" finding="absolute" line="1" />
      <module_usage name="importlib._bootstrap_external" finding="absolute" line="1" />
      <module_usage name="importlib.machinery" finding="absolute" line="1" />
      <module_usage name="inspect" finding="absolute" line="1" />
      <module_usage name="io" finding="absolute" line="1" />
      <module_usage name="keyword" finding="absolute" line="1" />
      <module_usage name="linecache" finding="absolute" line="1" />
      <module_usage name="locale" finding="absolute" line="1" />
      <module_usage name="ntpath" finding="absolute" line="1" />
      <module_usage name="opcode" finding="absolute" line="1" />
      <module_usage name="operator" finding="absolute" line="1" />
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="quopri" finding="absolute" line="1" />
      <module_usage name="re" finding="absolute" line="1" />
      <module_usage name="re._casefix" finding="absolute" line="1" />
      <module_usage name="re._compiler" finding="absolute" line="1" />
      <module_usage name="re._constants" finding="absolute" line="1" />
      <module_usage name="re._parser" finding="absolute" line="1" />
      <module_usage name="reprlib" finding="absolute" line="1" />
      <module_usage name="stat" finding="absolute" line="1" />
      <module_usage name="token" finding="absolute" line="1" />
      <module_usage name="tokenize" finding="absolute" line="1" />
      <module_usage name="types" finding="absolute" line="1" />
      <module_usage name="warnings" finding="absolute" line="1" />
      <module_usage name="weakref" finding="absolute" line="1" />
      <module_usage name="zipimport" finding="absolute" line="1" />
      <module_usage name="__future__" finding="absolute" line="1" />
      <module_usage name="__hello__" finding="absolute" line="1" />
      <module_usage name="__phello__" finding="absolute" line="1" />
      <module_usage name="__phello__.ham" finding="absolute" line="1" />
      <module_usage name="__phello__.ham.eggs" finding="absolute" line="1" />
      <module_usage name="__phello__.spam" finding="absolute" line="1" />
      <module_usage name="_aix_support" finding="absolute" line="1" />
      <module_usage name="_compat_pickle" finding="absolute" line="1" />
      <module_usage name="_compression" finding="absolute" line="1" />
      <module_usage name="_markupbase" finding="absolute" line="1" />
      <module_usage name="_osx_support" finding="absolute" line="1" />
      <module_usage name="_py_abc" finding="absolute" line="1" />
      <module_usage name="_pydatetime" finding="absolute" line="1" />
      <module_usage name="_pyio" finding="absolute" line="1" />
      <module_usage name="_pylong" finding="absolute" line="1" />
      <module_usage name="_sitebuiltins" finding="absolute" line="1" />
      <module_usage name="_strptime" finding="absolute" line="1" />
      <module_usage name="_threading_local" finding="absolute" line="1" />
      <module_usage name="_wmi" finding="absolute" line="1" />
      <module_usage name="base64" finding="absolute" line="1" />
      <module_usage name="bisect" finding="absolute" line="1" />
      <module_usage name="calendar" finding="absolute" line="1" />
      <module_usage name="cgi" finding="absolute" line="1" />
      <module_usage name="cgitb" finding="absolute" line="1" />
      <module_usage name="chunk" finding="absolute" line="1" />
      <module_usage name="cmd" finding="absolute" line="1" />
      <module_usage name="code" finding="absolute" line="1" />
      <module_usage name="codeop" finding="absolute" line="1" />
      <module_usage name="colorsys" finding="absolute" line="1" />
      <module_usage name="configparser" finding="absolute" line="1" />
      <module_usage name="contextvars" finding="absolute" line="1" />
      <module_usage name="copy" finding="absolute" line="1" />
      <module_usage name="dataclasses" finding="absolute" line="1" />
      <module_usage name="datetime" finding="absolute" line="1" />
      <module_usage name="difflib" finding="absolute" line="1" />
      <module_usage name="encodings.base64_codec" finding="absolute" line="1" />
      <module_usage name="encodings.bz2_codec" finding="absolute" line="1" />
      <module_usage name="encodings.hex_codec" finding="absolute" line="1" />
      <module_usage name="encodings.idna" finding="absolute" line="1" />
      <module_usage name="encodings.punycode" finding="absolute" line="1" />
      <module_usage name="encodings.rot_13" finding="absolute" line="1" />
      <module_usage name="filecmp" finding="absolute" line="1" />
      <module_usage name="fileinput" finding="absolute" line="1" />
      <module_usage name="fnmatch" finding="absolute" line="1" />
      <module_usage name="ftplib" finding="absolute" line="1" />
      <module_usage name="getopt" finding="absolute" line="1" />
      <module_usage name="gettext" finding="absolute" line="1" />
      <module_usage name="glob" finding="absolute" line="1" />
      <module_usage name="graphlib" finding="absolute" line="1" />
      <module_usage name="gzip" finding="absolute" line="1" />
      <module_usage name="heapq" finding="absolute" line="1" />
      <module_usage name="html" finding="absolute" line="1" />
      <module_usage name="html.entities" finding="absolute" line="1" />
      <module_usage name="html.parser" finding="absolute" line="1" />
      <module_usage name="imaplib" finding="absolute" line="1" />
      <module_usage name="imghdr" finding="absolute" line="1" />
      <module_usage name="importlib._abc" finding="absolute" line="1" />
      <module_usage name="importlib.abc" finding="absolute" line="1" />
      <module_usage name="importlib.metadata" finding="absolute" line="1" />
      <module_usage name="importlib.metadata._adapters" finding="absolute" line="1" />
      <module_usage name="importlib.metadata._collections" finding="absolute" line="1" />
      <module_usage name="importlib.metadata._functools" finding="absolute" line="1" />
      <module_usage name="importlib.metadata._itertools" finding="absolute" line="1" />
      <module_usage name="importlib.metadata._meta" finding="absolute" line="1" />
      <module_usage name="importlib.metadata._text" finding="absolute" line="1" />
      <module_usage name="importlib.readers" finding="absolute" line="1" />
      <module_usage name="importlib.resources" finding="absolute" line="1" />
      <module_usage name="importlib.resources._adapters" finding="absolute" line="1" />
      <module_usage name="importlib.resources._common" finding="absolute" line="1" />
      <module_usage name="importlib.resources._itertools" finding="absolute" line="1" />
      <module_usage name="importlib.resources._legacy" finding="absolute" line="1" />
      <module_usage name="importlib.resources.abc" finding="absolute" line="1" />
      <module_usage name="importlib.resources.readers" finding="absolute" line="1" />
      <module_usage name="importlib.resources.simple" finding="absolute" line="1" />
      <module_usage name="importlib.simple" finding="absolute" line="1" />
      <module_usage name="importlib.util" finding="absolute" line="1" />
      <module_usage name="ipaddress" finding="absolute" line="1" />
      <module_usage name="json" finding="absolute" line="1" />
      <module_usage name="json.decoder" finding="absolute" line="1" />
      <module_usage name="json.encoder" finding="absolute" line="1" />
      <module_usage name="json.scanner" finding="absolute" line="1" />
      <module_usage name="mailcap" finding="absolute" line="1" />
      <module_usage name="mimetypes" finding="absolute" line="1" />
      <module_usage name="modulefinder" finding="absolute" line="1" />
      <module_usage name="netrc" finding="absolute" line="1" />
      <module_usage name="nturl2path" finding="absolute" line="1" />
      <module_usage name="numbers" finding="absolute" line="1" />
      <module_usage name="pathlib" finding="absolute" line="1" />
      <module_usage name="pickle" finding="absolute" line="1" />
      <module_usage name="pickletools" finding="absolute" line="1" />
      <module_usage name="pipes" finding="absolute" line="1" />
      <module_usage name="pkgutil" finding="absolute" line="1" />
      <module_usage name="platform" finding="absolute" line="1" />
      <module_usage name="poplib" finding="absolute" line="1" />
      <module_usage name="posixpath" finding="absolute" line="1" />
      <module_usage name="pprint" finding="absolute" line="1" />
      <module_usage name="pstats" finding="absolute" line="1" />
      <module_usage name="pyclbr" finding="absolute" line="1" />
      <module_usage name="rlcompleter" finding="absolute" line="1" />
      <module_usage name="sched" finding="absolute" line="1" />
      <module_usage name="shlex" finding="absolute" line="1" />
      <module_usage name="shutil" finding="absolute" line="1" />
      <module_usage name="signal" finding="absolute" line="1" />
      <module_usage name="sndhdr" finding="absolute" line="1" />
      <module_usage name="socketserver" finding="absolute" line="1" />
      <module_usage name="sre_compile" finding="absolute" line="1" />
      <module_usage name="sre_constants" finding="absolute" line="1" />
      <module_usage name="sre_parse" finding="absolute" line="1" />
      <module_usage name="string" finding="absolute" line="1" />
      <module_usage name="stringprep" finding="absolute" line="1" />
      <module_usage name="struct" finding="absolute" line="1" />
      <module_usage name="symtable" finding="absolute" line="1" />
      <module_usage name="sysconfig" finding="absolute" line="1" />
      <module_usage name="tarfile" finding="absolute" line="1" />
      <module_usage name="threading" finding="absolute" line="1" />
      <module_usage name="timeit" finding="absolute" line="1" />
      <module_usage name="tomllib" finding="absolute" line="1" />
      <module_usage name="tomllib._parser" finding="absolute" line="1" />
      <module_usage name="tomllib._re" finding="absolute" line="1" />
      <module_usage name="tomllib._types" finding="absolute" line="1" />
      <module_usage name="trace" finding="absolute" line="1" />
      <module_usage name="traceback" finding="absolute" line="1" />
      <module_usage name="tracemalloc" finding="absolute" line="1" />
      <module_usage name="typing" finding="absolute" line="1" />
      <module_usage name="unicodedata" finding="absolute" line="1" />
      <module_usage name="uu" finding="absolute" line="1" />
      <module_usage name="webbrowser" finding="absolute" line="1" />
      <module_usage name="xdrlib" finding="absolute" line="1" />
      <module_usage name="zipfile" finding="absolute" line="1" />
      <module_usage name="zipfile._path" finding="absolute" line="1" />
      <module_usage name="zipfile._path.glob" finding="absolute" line="1" />
    </module_usages>
  </module>
  <module name="__phello__" kind="UncompiledPythonPackage" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\__phello__\__init__.py">
    <optimization-time pass="1" time="0.01" micro_passes="5" max_branch_merge="2" merged_total="4" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
    </module_usages>
  </module>
  <module name="__phello__.ham" kind="UncompiledPythonPackage" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\__phello__\ham\__init__.py">
    <optimization-time pass="1" time="0.01" micro_passes="3" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
    </module_usages>
  </module>
  <module name="__phello__.ham.eggs" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\__phello__\ham\eggs.py">
    <optimization-time pass="1" time="0.01" micro_passes="3" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="__phello__.spam" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\__phello__\spam.py">
    <optimization-time pass="1" time="0.02" micro_passes="5" max_branch_merge="2" merged_total="4" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="_aix_support" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\_aix_support.py">
    <optimization-time pass="1" time="0.18" micro_passes="6" max_branch_merge="16" merged_total="817" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="sys" finding="absolute" line="3" />
      <module_usage name="sysconfig" finding="absolute" line="4" />
      <module_usage name="os" finding="absolute" line="13" />
      <module_usage name="contextlib" finding="absolute" line="14" />
      <module_usage name="subprocess" finding="absolute" line="53" />
    </module_usages>
  </module>
  <module name="_bz2" kind="PythonExtensionModule" usage="import" reason="Extension module needed for standalone mode." source_path="${sys.real_prefix}\DLLs\_bz2.pyd">
    <optimization-time pass="1" time="0.00" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="_collections_abc" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\_collections_abc.py">
    <optimization-time pass="1" time="2.74" micro_passes="7" max_branch_merge="30" merged_total="15123" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="abc" finding="absolute" line="35" />
      <module_usage name="sys" finding="absolute" line="36" />
      <module_usage name="warnings" finding="absolute" line="1077" />
      <module_usage name="warnings" finding="absolute" line="1086" />
    </module_usages>
  </module>
  <module name="_compat_pickle" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\_compat_pickle.py">
    <optimization-time pass="1" time="0.07" micro_passes="5" max_branch_merge="4" merged_total="269" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="_compression" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\_compression.py">
    <optimization-time pass="1" time="0.17" micro_passes="5" max_branch_merge="59" merged_total="1623" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="io" finding="absolute" line="3" />
      <module_usage name="sys" finding="absolute" line="4" />
    </module_usages>
  </module>
  <module name="_ctypes" kind="PythonExtensionModule" usage="import" reason="Extension module needed for standalone mode." source_path="${sys.real_prefix}\DLLs\_ctypes.pyd">
    <optimization-time pass="1" time="0.00" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="_decimal" kind="PythonExtensionModule" usage="import" reason="Extension module needed for standalone mode." source_path="${sys.real_prefix}\DLLs\_decimal.pyd">
    <optimization-time pass="1" time="0.00" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="_elementtree" kind="PythonExtensionModule" usage="import" reason="Extension module needed for standalone mode." source_path="${sys.real_prefix}\DLLs\_elementtree.pyd">
    <optimization-time pass="1" time="0.00" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="_hashlib" kind="PythonExtensionModule" usage="import" reason="Extension module needed for standalone mode." source_path="${sys.real_prefix}\DLLs\_hashlib.pyd">
    <optimization-time pass="1" time="0.00" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="_lzma" kind="PythonExtensionModule" usage="import" reason="Extension module needed for standalone mode." source_path="${sys.real_prefix}\DLLs\_lzma.pyd">
    <optimization-time pass="1" time="0.00" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="_markupbase" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\_markupbase.py">
    <optimization-time pass="1" time="0.45" micro_passes="5" max_branch_merge="122" merged_total="3464" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="_osx_support" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\_osx_support.py">
    <optimization-time pass="1" time="0.71" micro_passes="7" max_branch_merge="82" merged_total="5490" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="3" />
      <module_usage name="re" finding="absolute" line="4" />
      <module_usage name="sys" finding="absolute" line="5" />
      <module_usage name="ntpath" finding="absolute" line="39" />
      <module_usage name="ntpath" finding="absolute" line="46" />
      <module_usage name="contextlib" finding="absolute" line="61" />
      <module_usage name="tempfile" finding="absolute" line="63" />
    </module_usages>
  </module>
  <module name="_py_abc" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\_py_abc.py">
    <optimization-time pass="1" time="0.23" micro_passes="7" max_branch_merge="55" merged_total="1835" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_weakrefset" finding="absolute" line="1" />
    </module_usages>
  </module>
  <module name="_pydatetime" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\_pydatetime.py">
    <optimization-time pass="1" time="2.52" micro_passes="6" max_branch_merge="179" merged_total="21673" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="time" finding="absolute" line="11" />
      <module_usage name="math" finding="absolute" line="12" />
      <module_usage name="sys" finding="absolute" line="13" />
      <module_usage name="operator" finding="absolute" line="14" />
      <module_usage name="warnings" finding="absolute" line="1809" />
      <module_usage name="warnings" finding="absolute" line="1827" />
      <module_usage name="_strptime" finding="absolute" line="2091" />
    </module_usages>
  </module>
  <module name="_pydecimal" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\_pydecimal.py">
    <optimization-time pass="1" time="4.70" micro_passes="6" max_branch_merge="277" merged_total="46990" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="math" finding="absolute" line="59" />
      <module_usage name="numbers" finding="absolute" line="60" />
      <module_usage name="sys" finding="absolute" line="61" />
      <module_usage name="collections" finding="absolute" line="64" />
      <module_usage name="collections.namedtuple" finding="not-found" line="64" />
      <module_usage name="contextvars" finding="absolute" line="343" />
      <module_usage name="re" finding="absolute" line="6044" />
      <module_usage name="locale" finding="absolute" line="6098" />
      <module_usage name="itertools" finding="absolute" line="6222" />
    </module_usages>
  </module>
  <module name="_pyio" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\_pyio.py">
    <optimization-time pass="1" time="3.36" micro_passes="7" max_branch_merge="123" merged_total="26688" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="5" />
      <module_usage name="abc" finding="absolute" line="6" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="errno" finding="absolute" line="8" />
      <module_usage name="stat" finding="absolute" line="9" />
      <module_usage name="sys" finding="absolute" line="10" />
      <module_usage name="_thread" finding="absolute" line="12" />
      <module_usage name="msvcrt" finding="absolute" line="14" />
      <module_usage name="io" finding="absolute" line="18" />
      <module_usage name="io" finding="absolute" line="19" />
      <module_usage name="warnings" finding="absolute" line="64" />
      <module_usage name="warnings" finding="absolute" line="230" />
      <module_usage name="warnings" finding="absolute" line="295" />
      <module_usage name="_io" finding="absolute" line="666" />
      <module_usage name="warnings" finding="absolute" line="1610" />
      <module_usage name="locale" finding="absolute" line="2254" />
    </module_usages>
  </module>
  <module name="_pylong" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\_pylong.py">
    <optimization-time pass="1" time="0.31" micro_passes="5" max_branch_merge="26" merged_total="2498" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="15" />
      <module_usage name="decimal" finding="absolute" line="16" />
      <module_usage name="_decimal" finding="absolute" line="18" />
    </module_usages>
  </module>
  <module name="_sitebuiltins" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\_sitebuiltins.py">
    <optimization-time pass="1" time="0.29" micro_passes="8" max_branch_merge="16" merged_total="1824" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="sys" finding="absolute" line="11" />
      <module_usage name="os" finding="absolute" line="36" />
      <module_usage name="ntpath" finding="absolute" line="40" />
      <module_usage name="pydoc" finding="absolute" line="102" />
    </module_usages>
  </module>
  <module name="_socket" kind="PythonExtensionModule" usage="import" reason="Extension module needed for standalone mode." source_path="${sys.real_prefix}\DLLs\_socket.pyd">
    <optimization-time pass="1" time="0.00" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="_strptime" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\_strptime.py">
    <optimization-time pass="1" time="0.93" micro_passes="7" max_branch_merge="206" merged_total="7486" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="time" finding="absolute" line="13" />
      <module_usage name="locale" finding="absolute" line="14" />
      <module_usage name="calendar" finding="absolute" line="15" />
      <module_usage name="re" finding="absolute" line="16" />
      <module_usage name="re.compile" finding="not-found" line="16" />
      <module_usage name="re" finding="absolute" line="17" />
      <module_usage name="re.IGNORECASE" finding="not-found" line="17" />
      <module_usage name="re" finding="absolute" line="18" />
      <module_usage name="re.escape" finding="not-found" line="18" />
      <module_usage name="datetime" finding="absolute" line="19" />
      <module_usage name="_thread" finding="absolute" line="22" />
    </module_usages>
  </module>
  <module name="_threading_local" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\_threading_local.py">
    <optimization-time pass="1" time="0.13" micro_passes="5" max_branch_merge="20" merged_total="984" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="weakref" finding="absolute" line="131" />
      <module_usage name="contextlib" finding="absolute" line="132" />
      <module_usage name="threading" finding="absolute" line="242" />
    </module_usages>
  </module>
  <module name="_weakrefset" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\_weakrefset.py">
    <optimization-time pass="1" time="0.32" micro_passes="7" max_branch_merge="46" merged_total="2203" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_weakref" finding="absolute" line="5" />
      <module_usage name="types" finding="absolute" line="6" />
    </module_usages>
  </module>
  <module name="_wmi" kind="PythonExtensionModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\DLLs\_wmi.pyd">
    <optimization-time pass="1" time="0.00" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="abc" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\abc.py">
    <optimization-time pass="1" time="0.34" micro_passes="7" max_branch_merge="28" merged_total="2006" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_abc" finding="absolute" line="85" />
      <module_usage name="_py_abc" finding="absolute" line="89" />
    </module_usages>
  </module>
  <module name="argparse" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\argparse.py">
    <optimization-time pass="1" time="4.88" micro_passes="7" max_branch_merge="119" merged_total="33064" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="88" />
      <module_usage name="re" finding="absolute" line="89" />
      <module_usage name="sys" finding="absolute" line="90" />
      <module_usage name="warnings" finding="absolute" line="92" />
      <module_usage name="gettext" finding="absolute" line="94" />
      <module_usage name="copy" finding="absolute" line="148" />
      <module_usage name="shutil" finding="absolute" line="172" />
      <module_usage name="textwrap" finding="absolute" line="659" />
      <module_usage name="textwrap" finding="absolute" line="664" />
      <module_usage name="warnings" finding="absolute" line="2476" />
    </module_usages>
  </module>
  <module name="ast" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\ast.py">
    <optimization-time pass="1" time="3.14" micro_passes="5" max_branch_merge="191" merged_total="24711" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="sys" finding="absolute" line="27" />
      <module_usage name="re" finding="absolute" line="28" />
      <module_usage name="_ast" finding="absolute" line="29" />
      <module_usage name="contextlib" finding="absolute" line="30" />
      <module_usage name="enum" finding="absolute" line="31" />
      <module_usage name="inspect" finding="absolute" line="302" />
      <module_usage name="collections" finding="absolute" line="375" />
      <module_usage name="collections.deque" finding="not-found" line="375" />
      <module_usage name="warnings" finding="absolute" line="434" />
      <module_usage name="warnings" finding="absolute" line="516" />
      <module_usage name="warnings" finding="absolute" line="523" />
      <module_usage name="warnings" finding="absolute" line="531" />
      <module_usage name="warnings" finding="absolute" line="538" />
      <module_usage name="warnings" finding="absolute" line="554" />
      <module_usage name="warnings" finding="absolute" line="583" />
      <module_usage name="warnings" finding="absolute" line="610" />
      <module_usage name="warnings" finding="absolute" line="1802" />
    </module_usages>
  </module>
  <module name="base64" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\base64.py">
    <optimization-time pass="1" time="0.65" micro_passes="7" max_branch_merge="74" merged_total="5586" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="9" />
      <module_usage name="struct" finding="absolute" line="10" />
      <module_usage name="binascii" finding="absolute" line="11" />
    </module_usages>
  </module>
  <module name="bisect" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\bisect.py">
    <plugin-influence name="anti-bloat" influence="condition-used" condition="not has_builtin_module(&quot;_bisect&quot;)" tags_used="has_builtin_module" result="false" />
    <optimization-time pass="1" time="0.07" micro_passes="5" max_branch_merge="36" merged_total="601" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_bisect" finding="absolute" line="112" />
    </module_usages>
  </module>
  <module name="bz2" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\bz2.py">
    <optimization-time pass="1" time="0.18" micro_passes="5" max_branch_merge="66" merged_total="1779" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="io" finding="absolute" line="13" />
      <module_usage name="os" finding="absolute" line="14" />
      <module_usage name="_compression" finding="absolute" line="15" />
      <module_usage name="_bz2" finding="absolute" line="17" />
    </module_usages>
  </module>
  <module name="calendar" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\calendar.py">
    <optimization-time pass="1" time="1.89" micro_passes="9" max_branch_merge="51" merged_total="13696" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="sys" finding="absolute" line="8" />
      <module_usage name="datetime" finding="absolute" line="9" />
      <module_usage name="enum" finding="absolute" line="10" />
      <module_usage name="locale" finding="absolute" line="11" />
      <module_usage name="itertools" finding="absolute" line="12" />
      <module_usage name="warnings" finding="absolute" line="13" />
    </module_usages>
  </module>
  <module name="cgi" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\cgi.py">
    <optimization-time pass="1" time="0.88" micro_passes="6" max_branch_merge="158" merged_total="8017" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="io" finding="absolute" line="39" />
      <module_usage name="collections.abc" finding="absolute" line="40" />
      <module_usage name="sys" finding="absolute" line="41" />
      <module_usage name="os" finding="absolute" line="42" />
      <module_usage name="urllib.parse" finding="absolute" line="43" />
      <module_usage name="email.parser" finding="absolute" line="44" />
      <module_usage name="email.message" finding="absolute" line="45" />
      <module_usage name="html" finding="absolute" line="46" />
      <module_usage name="locale" finding="absolute" line="47" />
      <module_usage name="tempfile" finding="absolute" line="48" />
      <module_usage name="warnings" finding="absolute" line="49" />
      <module_usage name="traceback" finding="absolute" line="895" />
      <module_usage name="re" finding="absolute" line="1000" />
    </module_usages>
  </module>
  <module name="cgitb" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\cgitb.py">
    <optimization-time pass="1" time="0.91" micro_passes="7" max_branch_merge="75" merged_total="5054" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="inspect" finding="absolute" line="24" />
      <module_usage name="keyword" finding="absolute" line="25" />
      <module_usage name="linecache" finding="absolute" line="26" />
      <module_usage name="os" finding="absolute" line="27" />
      <module_usage name="pydoc" finding="absolute" line="28" />
      <module_usage name="sys" finding="absolute" line="29" />
      <module_usage name="tempfile" finding="absolute" line="30" />
      <module_usage name="time" finding="absolute" line="31" />
      <module_usage name="tokenize" finding="absolute" line="32" />
      <module_usage name="traceback" finding="absolute" line="33" />
      <module_usage name="warnings" finding="absolute" line="34" />
      <module_usage name="html" finding="absolute" line="35" />
      <module_usage name="html.escape" finding="not-found" line="35" />
    </module_usages>
  </module>
  <module name="chunk" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\chunk.py">
    <optimization-time pass="1" time="0.11" micro_passes="5" max_branch_merge="42" merged_total="998" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="warnings" finding="absolute" line="51" />
      <module_usage name="struct" finding="absolute" line="57" />
    </module_usages>
  </module>
  <module name="cmd" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\cmd.py">
    <optimization-time pass="1" time="0.61" micro_passes="7" max_branch_merge="53" merged_total="4407" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="string" finding="absolute" line="45" />
      <module_usage name="sys" finding="absolute" line="45" />
      <module_usage name="readline" finding="not-found" line="108" />
      <module_usage name="readline" finding="not-found" line="144" />
      <module_usage name="readline" finding="not-found" line="258" />
    </module_usages>
  </module>
  <module name="code" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\code.py">
    <optimization-time pass="1" time="0.29" micro_passes="7" max_branch_merge="31" merged_total="1929" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="sys" finding="absolute" line="8" />
      <module_usage name="traceback" finding="absolute" line="9" />
      <module_usage name="codeop" finding="absolute" line="10" />
      <module_usage name="readline" finding="not-found" line="307" />
    </module_usages>
  </module>
  <module name="codecs" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\codecs.py">
    <optimization-time pass="1" time="0.81" micro_passes="5" max_branch_merge="103" merged_total="5062" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="builtins" finding="absolute" line="10" />
      <module_usage name="sys" finding="absolute" line="11" />
      <module_usage name="_codecs" finding="absolute" line="16" />
    </module_usages>
  </module>
  <module name="codeop" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\codeop.py">
    <optimization-time pass="1" time="0.14" micro_passes="5" max_branch_merge="16" merged_total="848" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="__future__" finding="absolute" line="35" />
      <module_usage name="warnings" finding="absolute" line="36" />
    </module_usages>
  </module>
  <module name="collections" kind="UncompiledPythonPackage" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\collections\__init__.py">
    <optimization-time pass="1" time="2.85" micro_passes="7" max_branch_merge="124" merged_total="16522" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="_collections_abc" finding="absolute" line="29" />
      <module_usage name="sys" finding="absolute" line="30" />
      <module_usage name="itertools" finding="absolute" line="32" />
      <module_usage name="itertools" finding="absolute" line="33" />
      <module_usage name="itertools" finding="absolute" line="34" />
      <module_usage name="keyword" finding="absolute" line="35" />
      <module_usage name="operator" finding="absolute" line="36" />
      <module_usage name="operator" finding="absolute" line="37" />
      <module_usage name="reprlib" finding="absolute" line="38" />
      <module_usage name="_weakref" finding="absolute" line="39" />
      <module_usage name="_collections" finding="absolute" line="42" />
      <module_usage name="_collections" finding="absolute" line="49" />
      <module_usage name="_collections" finding="absolute" line="54" />
      <module_usage name="_collections" finding="absolute" line="340" />
      <module_usage name="_collections" finding="absolute" line="351" />
      <module_usage name="_collections" finding="absolute" line="540" />
      <module_usage name="heapq" finding="absolute" line="631" />
      <module_usage name="copy" finding="absolute" line="1192" />
    </module_usages>
  </module>
  <module name="collections.abc" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\collections\abc.py">
    <optimization-time pass="1" time="0.01" micro_passes="3" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_collections_abc" finding="absolute" line="1" />
      <module_usage name="_collections_abc" finding="absolute" line="2" />
      <module_usage name="_collections_abc" finding="absolute" line="3" />
    </module_usages>
  </module>
  <module name="coloros" kind="CompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\coloros.py">
    <optimization-time pass="1" time="0.47" micro_passes="7" max_branch_merge="56" merged_total="3829" />
    <optimization-time pass="2" time="0.06" micro_passes="1" max_branch_merge="51" merged_total="515" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="4" />
      <module_usage name="time" finding="absolute" line="5" />
      <module_usage name="utils" finding="absolute" line="6" />
      <module_usage name="subprocess" finding="absolute" line="7" />
      <module_usage name="sys" finding="absolute" line="8" />
      <module_usage name="ntpath" finding="absolute" line="125" />
      <module_usage name="ntpath" finding="absolute" line="128" />
      <module_usage name="ntpath" finding="absolute" line="271" />
      <module_usage name="ntpath" finding="absolute" line="309" />
    </module_usages>
  </module>
  <module name="coloros15" kind="CompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\coloros15.py">
    <optimization-time pass="1" time="1.01" micro_passes="10" max_branch_merge="95" merged_total="7893" />
    <optimization-time pass="2" time="0.10" micro_passes="1" max_branch_merge="77" merged_total="750" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="5" />
      <module_usage name="sys" finding="absolute" line="6" />
      <module_usage name="time" finding="absolute" line="7" />
      <module_usage name="threading" finding="absolute" line="8" />
      <module_usage name="subprocess" finding="absolute" line="9" />
      <module_usage name="utils" finding="absolute" line="10" />
      <module_usage name="ntpath" finding="absolute" line="126" />
      <module_usage name="ntpath" finding="absolute" line="129" />
      <module_usage name="ntpath" finding="absolute" line="232" />
      <module_usage name="ntpath" finding="absolute" line="269" />
      <module_usage name="ntpath" finding="absolute" line="310" />
      <module_usage name="ntpath" finding="absolute" line="347" />
      <module_usage name="ntpath" finding="absolute" line="388" />
      <module_usage name="ntpath" finding="absolute" line="405" />
    </module_usages>
  </module>
  <module name="colorsys" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\colorsys.py">
    <optimization-time pass="1" time="0.12" micro_passes="4" max_branch_merge="31" merged_total="757" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="configparser" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\configparser.py">
    <optimization-time pass="1" time="3.60" micro_passes="9" max_branch_merge="159" merged_total="21168" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="collections.abc" finding="absolute" line="142" />
      <module_usage name="collections" finding="absolute" line="143" />
      <module_usage name="collections.ChainMap" finding="not-found" line="143" />
      <module_usage name="functools" finding="absolute" line="144" />
      <module_usage name="io" finding="absolute" line="145" />
      <module_usage name="itertools" finding="absolute" line="146" />
      <module_usage name="os" finding="absolute" line="147" />
      <module_usage name="re" finding="absolute" line="148" />
      <module_usage name="sys" finding="absolute" line="149" />
      <module_usage name="warnings" finding="absolute" line="150" />
      <module_usage name="functools" finding="absolute" line="515" />
      <module_usage name="io" finding="absolute" line="709" />
      <module_usage name="functools" finding="absolute" line="1216" />
      <module_usage name="functools" finding="absolute" line="1308" />
      <module_usage name="functools" finding="absolute" line="1312" />
    </module_usages>
  </module>
  <module name="contextlib" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\contextlib.py">
    <optimization-time pass="1" time="1.05" micro_passes="6" max_branch_merge="24" merged_total="6946" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="abc" finding="absolute" line="2" />
      <module_usage name="os" finding="absolute" line="3" />
      <module_usage name="sys" finding="absolute" line="4" />
      <module_usage name="_collections_abc" finding="absolute" line="5" />
      <module_usage name="collections" finding="absolute" line="6" />
      <module_usage name="collections.deque" finding="not-found" line="6" />
      <module_usage name="functools" finding="absolute" line="7" />
      <module_usage name="types" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="contextvars" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\contextvars.py">
    <optimization-time pass="1" time="0.01" micro_passes="3" max_branch_merge="4" merged_total="12" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_contextvars" finding="absolute" line="1" />
    </module_usages>
  </module>
  <module name="copy" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\copy.py">
    <optimization-time pass="1" time="0.57" micro_passes="6" max_branch_merge="45" merged_total="2206" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="types" finding="absolute" line="51" />
      <module_usage name="weakref" finding="absolute" line="52" />
      <module_usage name="copyreg" finding="absolute" line="53" />
    </module_usages>
  </module>
  <module name="copyreg" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\copyreg.py">
    <optimization-time pass="1" time="0.41" micro_passes="6" max_branch_merge="38" merged_total="1437" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="functools" finding="absolute" line="34" />
      <module_usage name="operator" finding="absolute" line="34" />
    </module_usages>
  </module>
  <module name="csv" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\csv.py">
    <optimization-time pass="1" time="1.03" micro_passes="9" max_branch_merge="105" merged_total="7754" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="6" />
      <module_usage name="types" finding="absolute" line="7" />
      <module_usage name="_csv" finding="absolute" line="8" />
      <module_usage name="_csv" finding="absolute" line="14" />
      <module_usage name="io" finding="absolute" line="16" />
    </module_usages>
  </module>
  <module name="ctypes" kind="UncompiledPythonPackage" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\ctypes\__init__.py">
    <optimization-time pass="1" time="1.78" micro_passes="6" max_branch_merge="55" merged_total="8819" />
    <optimization-time pass="2" time="0.01" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="os" finding="absolute" line="3" />
      <module_usage name="sys" finding="absolute" line="3" />
      <module_usage name="types" finding="absolute" line="4" />
      <module_usage name="_ctypes" finding="absolute" line="8" />
      <module_usage name="_ctypes" finding="absolute" line="9" />
      <module_usage name="_ctypes" finding="absolute" line="10" />
      <module_usage name="_ctypes" finding="absolute" line="11" />
      <module_usage name="_ctypes" finding="absolute" line="12" />
      <module_usage name="_ctypes" finding="absolute" line="13" />
      <module_usage name="_ctypes" finding="absolute" line="14" />
      <module_usage name="struct" finding="absolute" line="16" />
      <module_usage name="_ctypes" finding="absolute" line="22" />
      <module_usage name="_ctypes" finding="absolute" line="34" />
      <module_usage name="_ctypes" finding="absolute" line="110" />
      <module_usage name="_ctypes" finding="absolute" line="111" />
      <module_usage name="_ctypes" finding="absolute" line="141" />
      <module_usage name="_ctypes" finding="absolute" line="142" />
      <module_usage name="_ctypes" finding="absolute" line="143" />
      <module_usage name="struct" finding="absolute" line="148" />
      <module_usage name="_ctypes" finding="absolute" line="255" />
      <module_usage name="nt" finding="absolute" line="367" />
      <module_usage name="_ctypes" finding="absolute" line="419" />
      <module_usage name="_ctypes" finding="absolute" line="480" />
      <module_usage name="_ctypes" finding="absolute" line="501" />
      <module_usage name="_ctypes" finding="absolute" line="528" />
      <module_usage name="comtypes.server.inprocserver" finding="not-found" line="543" />
      <module_usage name="comtypes.server.inprocserver" finding="not-found" line="551" />
      <module_usage name="ctypes._endian" finding="absolute" line="556" />
      <module_usage name="ctypes._endian" finding="absolute" line="557" />
    </module_usages>
  </module>
  <module name="ctypes._endian" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\ctypes\_endian.py">
    <optimization-time pass="1" time="0.13" micro_passes="5" max_branch_merge="24" merged_total="929" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="sys" finding="absolute" line="1" />
      <module_usage name="ctypes" finding="absolute" line="2" />
    </module_usages>
  </module>
  <module name="custom_messagebox" kind="CompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\custom_messagebox.py">
    <optimization-time pass="1" time="0.04" micro_passes="4" max_branch_merge="48" merged_total="341" />
    <optimization-time pass="2" time="0.01" micro_passes="1" max_branch_merge="48" merged_total="85" />
    <distribution-usages>
      <distribution-usage name="PyQt6" />
      <distribution-usage name="PyQt6-Qt6" />
      <distribution-usage name="PyQt6_sip" />
    </distribution-usages>
    <module_usages>
      <module_usage name="PyQt6.QtWidgets" finding="absolute" line="1" />
      <module_usage name="PyQt6.QtCore" finding="absolute" line="2" />
    </module_usages>
  </module>
  <module name="dataclasses" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\dataclasses.py">
    <optimization-time pass="1" time="1.85" micro_passes="6" max_branch_merge="162" merged_total="10746" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="1" />
      <module_usage name="sys" finding="absolute" line="2" />
      <module_usage name="copy" finding="absolute" line="3" />
      <module_usage name="types" finding="absolute" line="4" />
      <module_usage name="inspect" finding="absolute" line="5" />
      <module_usage name="keyword" finding="absolute" line="6" />
      <module_usage name="functools" finding="absolute" line="7" />
      <module_usage name="itertools" finding="absolute" line="8" />
      <module_usage name="abc" finding="absolute" line="9" />
      <module_usage name="_thread" finding="absolute" line="10" />
      <module_usage name="types" finding="absolute" line="11" />
    </module_usages>
  </module>
  <module name="datetime" kind="UncompiledPythonModule" usage="plugin:implicit-imports" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\datetime.py">
    <plugin-influence name="anti-bloat" influence="condition-used" condition="not has_builtin_module(&quot;_datetime&quot;)" tags_used="has_builtin_module" result="false" />
    <optimization-time pass="1" time="0.57" micro_passes="3" max_branch_merge="7" merged_total="42" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_datetime" finding="absolute" line="2" />
      <module_usage name="_datetime" finding="absolute" line="3" />
      <module_usage name="_pydatetime" finding="absolute" line="5" />
      <module_usage name="_pydatetime" finding="absolute" line="6" />
    </module_usages>
  </module>
  <module name="decimal" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\decimal.py">
    <optimization-time pass="1" time="1.20" micro_passes="3" max_branch_merge="9" merged_total="54" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_decimal" finding="absolute" line="102" />
      <module_usage name="_decimal" finding="absolute" line="103" />
      <module_usage name="_decimal" finding="absolute" line="104" />
      <module_usage name="_pydecimal" finding="absolute" line="106" />
      <module_usage name="_pydecimal" finding="absolute" line="107" />
      <module_usage name="_pydecimal" finding="absolute" line="108" />
    </module_usages>
  </module>
  <module name="difflib" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\difflib.py">
    <optimization-time pass="1" time="3.28" micro_passes="8" max_branch_merge="131" merged_total="20240" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="heapq" finding="absolute" line="33" />
      <module_usage name="collections" finding="absolute" line="34" />
      <module_usage name="collections.namedtuple" finding="not-found" line="34" />
      <module_usage name="types" finding="absolute" line="35" />
      <module_usage name="re" finding="absolute" line="1043" />
      <module_usage name="re" finding="absolute" line="1374" />
    </module_usages>
  </module>
  <module name="dis" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\dis.py">
    <optimization-time pass="1" time="2.20" micro_passes="9" max_branch_merge="137" merged_total="14021" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="sys" finding="absolute" line="3" />
      <module_usage name="types" finding="absolute" line="4" />
      <module_usage name="collections" finding="absolute" line="5" />
      <module_usage name="io" finding="absolute" line="6" />
      <module_usage name="opcode" finding="absolute" line="8" />
      <module_usage name="opcode" finding="absolute" line="9" />
      <module_usage name="io" finding="absolute" line="779" />
      <module_usage name="argparse" finding="absolute" line="794" />
    </module_usages>
  </module>
  <module name="email" kind="UncompiledPythonPackage" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\email\__init__.py">
    <optimization-time pass="1" time="0.07" micro_passes="4" max_branch_merge="6" merged_total="112" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="email.parser" finding="absolute" line="36" />
      <module_usage name="email.parser" finding="absolute" line="44" />
      <module_usage name="email.parser" finding="absolute" line="52" />
      <module_usage name="email.parser" finding="absolute" line="60" />
    </module_usages>
  </module>
  <module name="email._encoded_words" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\email\_encoded_words.py">
    <optimization-time pass="1" time="0.31" micro_passes="6" max_branch_merge="27" merged_total="1581" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="42" />
      <module_usage name="base64" finding="absolute" line="43" />
      <module_usage name="binascii" finding="absolute" line="44" />
      <module_usage name="functools" finding="absolute" line="45" />
      <module_usage name="string" finding="absolute" line="46" />
      <module_usage name="email" finding="absolute" line="47" />
      <module_usage name="email.errors" finding="relative" line="47" />
      <module_usage name="functools" finding="absolute" line="64" />
    </module_usages>
  </module>
  <module name="email._header_value_parser" kind="UncompiledPythonModule" usage="import fromlist" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\email\_header_value_parser.py">
    <optimization-time pass="1" time="8.77" micro_passes="7" max_branch_merge="228" merged_total="56656" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="70" />
      <module_usage name="sys" finding="absolute" line="71" />
      <module_usage name="urllib" finding="absolute" line="72" />
      <module_usage name="string" finding="absolute" line="73" />
      <module_usage name="operator" finding="absolute" line="74" />
      <module_usage name="email" finding="absolute" line="75" />
      <module_usage name="email._encoded_words" finding="relative" line="75" />
      <module_usage name="email" finding="absolute" line="76" />
      <module_usage name="email.errors" finding="relative" line="76" />
      <module_usage name="email" finding="absolute" line="77" />
      <module_usage name="email.utils" finding="relative" line="77" />
    </module_usages>
  </module>
  <module name="email._parseaddr" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\email\_parseaddr.py">
    <optimization-time pass="1" time="0.81" micro_passes="6" max_branch_merge="164" merged_total="7196" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="time" finding="absolute" line="16" />
      <module_usage name="calendar" finding="absolute" line="16" />
    </module_usages>
  </module>
  <module name="email._policybase" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\email\_policybase.py">
    <optimization-time pass="1" time="0.55" micro_passes="7" max_branch_merge="41" merged_total="2773" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="abc" finding="absolute" line="6" />
      <module_usage name="email" finding="absolute" line="7" />
      <module_usage name="email.header" finding="relative" line="7" />
      <module_usage name="email" finding="absolute" line="8" />
      <module_usage name="email.charset" finding="relative" line="8" />
      <module_usage name="email.utils" finding="absolute" line="9" />
    </module_usages>
  </module>
  <module name="email.base64mime" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\email\base64mime.py">
    <optimization-time pass="1" time="0.06" micro_passes="5" max_branch_merge="23" merged_total="399" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="base64" finding="absolute" line="37" />
      <module_usage name="binascii" finding="absolute" line="38" />
    </module_usages>
  </module>
  <module name="email.charset" kind="UncompiledPythonModule" usage="import fromlist" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\email\charset.py">
    <optimization-time pass="1" time="0.22" micro_passes="6" max_branch_merge="43" merged_total="1540" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="functools" finding="absolute" line="12" />
      <module_usage name="email.base64mime" finding="absolute" line="14" />
      <module_usage name="email.quoprimime" finding="absolute" line="15" />
      <module_usage name="email" finding="absolute" line="17" />
      <module_usage name="email.errors" finding="relative" line="17" />
      <module_usage name="email.encoders" finding="absolute" line="18" />
    </module_usages>
  </module>
  <module name="email.contentmanager" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\email\contentmanager.py">
    <optimization-time pass="1" time="0.36" micro_passes="6" max_branch_merge="70" merged_total="2659" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="binascii" finding="absolute" line="1" />
      <module_usage name="email.charset" finding="absolute" line="2" />
      <module_usage name="email.message" finding="absolute" line="3" />
      <module_usage name="email.errors" finding="absolute" line="4" />
      <module_usage name="email" finding="absolute" line="5" />
      <module_usage name="email.quoprimime" finding="relative" line="5" />
    </module_usages>
  </module>
  <module name="email.encoders" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\email\encoders.py">
    <optimization-time pass="1" time="0.03" micro_passes="4" max_branch_merge="9" merged_total="163" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="base64" finding="absolute" line="15" />
      <module_usage name="quopri" finding="absolute" line="16" />
    </module_usages>
  </module>
  <module name="email.errors" kind="UncompiledPythonModule" usage="import fromlist" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\email\errors.py">
    <optimization-time pass="1" time="1.29" micro_passes="6" max_branch_merge="16" merged_total="5121" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="email.feedparser" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\email\feedparser.py">
    <optimization-time pass="1" time="0.94" micro_passes="6" max_branch_merge="357" merged_total="5209" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="24" />
      <module_usage name="email" finding="absolute" line="26" />
      <module_usage name="email.errors" finding="relative" line="26" />
      <module_usage name="email._policybase" finding="absolute" line="27" />
      <module_usage name="collections" finding="absolute" line="28" />
      <module_usage name="collections.deque" finding="not-found" line="28" />
      <module_usage name="io" finding="absolute" line="29" />
      <module_usage name="email.message" finding="absolute" line="149" />
    </module_usages>
  </module>
  <module name="email.generator" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\email\generator.py">
    <optimization-time pass="1" time="0.35" micro_passes="5" max_branch_merge="85" merged_total="3563" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="9" />
      <module_usage name="sys" finding="absolute" line="10" />
      <module_usage name="time" finding="absolute" line="11" />
      <module_usage name="random" finding="absolute" line="12" />
      <module_usage name="copy" finding="absolute" line="14" />
      <module_usage name="io" finding="absolute" line="15" />
      <module_usage name="email.utils" finding="absolute" line="16" />
      <module_usage name="email.errors" finding="absolute" line="17" />
    </module_usages>
  </module>
  <module name="email.header" kind="UncompiledPythonModule" usage="import fromlist" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\email\header.py">
    <optimization-time pass="1" time="0.76" micro_passes="7" max_branch_merge="48" merged_total="6453" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="13" />
      <module_usage name="binascii" finding="absolute" line="14" />
      <module_usage name="email.quoprimime" finding="absolute" line="16" />
      <module_usage name="email.base64mime" finding="absolute" line="17" />
      <module_usage name="email.errors" finding="absolute" line="19" />
      <module_usage name="email" finding="absolute" line="20" />
      <module_usage name="email.charset" finding="relative" line="20" />
    </module_usages>
  </module>
  <module name="email.headerregistry" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\email\headerregistry.py">
    <optimization-time pass="1" time="2.03" micro_passes="7" max_branch_merge="29" merged_total="7002" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="types" finding="absolute" line="6" />
      <module_usage name="email" finding="absolute" line="8" />
      <module_usage name="email.utils" finding="relative" line="8" />
      <module_usage name="email" finding="absolute" line="9" />
      <module_usage name="email.errors" finding="relative" line="9" />
      <module_usage name="email" finding="absolute" line="10" />
      <module_usage name="email._header_value_parser" finding="relative" line="10" />
    </module_usages>
  </module>
  <module name="email.iterators" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\email\iterators.py">
    <optimization-time pass="1" time="0.08" micro_passes="5" max_branch_merge="23" merged_total="506" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="sys" finding="absolute" line="14" />
      <module_usage name="io" finding="absolute" line="15" />
    </module_usages>
  </module>
  <module name="email.message" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\email\message.py">
    <optimization-time pass="1" time="1.81" micro_passes="6" max_branch_merge="63" merged_total="10378" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="binascii" finding="absolute" line="9" />
      <module_usage name="re" finding="absolute" line="10" />
      <module_usage name="quopri" finding="absolute" line="11" />
      <module_usage name="io" finding="absolute" line="12" />
      <module_usage name="email" finding="absolute" line="15" />
      <module_usage name="email.utils" finding="relative" line="15" />
      <module_usage name="email" finding="absolute" line="16" />
      <module_usage name="email.errors" finding="relative" line="16" />
      <module_usage name="email._policybase" finding="absolute" line="17" />
      <module_usage name="email" finding="absolute" line="18" />
      <module_usage name="email.charset" finding="relative" line="18" />
      <module_usage name="email._encoded_words" finding="absolute" line="19" />
      <module_usage name="email.generator" finding="absolute" line="181" />
      <module_usage name="email.generator" finding="absolute" line="204" />
      <module_usage name="email.iterators" finding="absolute" line="974" />
      <module_usage name="email.policy" finding="absolute" line="981" />
    </module_usages>
  </module>
  <module name="email.parser" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\email\parser.py">
    <optimization-time pass="1" time="0.22" micro_passes="5" max_branch_merge="17" merged_total="721" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="io" finding="absolute" line="10" />
      <module_usage name="email.feedparser" finding="absolute" line="12" />
      <module_usage name="email._policybase" finding="absolute" line="13" />
    </module_usages>
  </module>
  <module name="email.policy" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\email\policy.py">
    <optimization-time pass="1" time="0.39" micro_passes="5" max_branch_merge="54" merged_total="1019" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="5" />
      <module_usage name="sys" finding="absolute" line="6" />
      <module_usage name="email._policybase" finding="absolute" line="7" />
      <module_usage name="email.utils" finding="absolute" line="8" />
      <module_usage name="email.headerregistry" finding="absolute" line="9" />
      <module_usage name="email.contentmanager" finding="absolute" line="10" />
      <module_usage name="email.message" finding="absolute" line="11" />
    </module_usages>
  </module>
  <module name="email.quoprimime" kind="UncompiledPythonModule" usage="import fromlist" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\email\quoprimime.py">
    <optimization-time pass="1" time="0.26" micro_passes="7" max_branch_merge="75" merged_total="2237" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="42" />
      <module_usage name="string" finding="absolute" line="44" />
    </module_usages>
  </module>
  <module name="email.utils" kind="UncompiledPythonModule" usage="import fromlist" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\email\utils.py">
    <plugin-influence name="anti-bloat" influence="condition-used" condition="not has_builtin_module(&quot;_socket&quot;)" tags_used="has_builtin_module" result="true" />
    <optimization-time pass="1" time="0.99" micro_passes="7" max_branch_merge="31" merged_total="4060" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="25" />
      <module_usage name="re" finding="absolute" line="26" />
      <module_usage name="time" finding="absolute" line="27" />
      <module_usage name="random" finding="absolute" line="28" />
      <module_usage name="datetime" finding="absolute" line="30" />
      <module_usage name="urllib.parse" finding="absolute" line="31" />
      <module_usage name="email._parseaddr" finding="absolute" line="33" />
      <module_usage name="email._parseaddr" finding="absolute" line="34" />
      <module_usage name="email._parseaddr" finding="absolute" line="35" />
      <module_usage name="email._parseaddr" finding="absolute" line="37" />
      <module_usage name="email.charset" finding="absolute" line="40" />
      <module_usage name="socket" finding="excluded" line="308" exclusion_reason="according to yaml 'no-auto-follow' configuration of 'email.utils' for 'socket'" />
      <module_usage name="warnings" finding="absolute" line="478" />
    </module_usages>
  </module>
  <module name="encodings" kind="UncompiledPythonPackage" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\__init__.py">
    <optimization-time pass="1" time="0.19" micro_passes="6" max_branch_merge="108" merged_total="1571" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="codecs" finding="absolute" line="31" />
      <module_usage name="sys" finding="absolute" line="32" />
      <module_usage name="encodings" finding="absolute" line="33" />
      <module_usage name="encodings.aliases" finding="relative" line="33" />
      <module_usage name="_winapi" finding="absolute" line="165" />
      <module_usage name="encodings.mbcs" finding="absolute" line="168" />
    </module_usages>
  </module>
  <module name="encodings.aliases" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\aliases.py">
    <optimization-time pass="1" time="0.01" micro_passes="3" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="encodings.ascii" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\ascii.py">
    <optimization-time pass="1" time="0.13" micro_passes="4" max_branch_merge="17" merged_total="886" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="9" />
    </module_usages>
  </module>
  <module name="encodings.base64_codec" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\encodings\base64_codec.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="765" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="8" />
      <module_usage name="base64" finding="absolute" line="9" />
    </module_usages>
  </module>
  <module name="encodings.big5" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\big5.py">
    <optimization-time pass="1" time="0.08" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_tw" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.big5hkscs" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\big5hkscs.py">
    <optimization-time pass="1" time="0.08" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_hk" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.bz2_codec" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\encodings\bz2_codec.py">
    <optimization-time pass="1" time="0.13" micro_passes="4" max_branch_merge="17" merged_total="894" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="10" />
      <module_usage name="bz2" finding="absolute" line="11" />
    </module_usages>
  </module>
  <module name="encodings.charmap" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\charmap.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="813" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="13" />
    </module_usages>
  </module>
  <module name="encodings.cp037" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp037.py">
    <optimization-time pass="1" time="0.10" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp1006" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp1006.py">
    <optimization-time pass="1" time="0.10" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp1026" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp1026.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp1125" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp1125.py">
    <optimization-time pass="1" time="0.14" micro_passes="5" max_branch_merge="17" merged_total="861" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp1140" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp1140.py">
    <optimization-time pass="1" time="0.12" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp1250" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp1250.py">
    <optimization-time pass="1" time="0.12" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp1251" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp1251.py">
    <optimization-time pass="1" time="0.12" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp1252" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp1252.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp1253" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp1253.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp1254" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp1254.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp1255" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp1255.py">
    <optimization-time pass="1" time="0.12" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp1256" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp1256.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp1257" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp1257.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp1258" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp1258.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp273" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp273.py">
    <optimization-time pass="1" time="0.10" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp424" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp424.py">
    <optimization-time pass="1" time="0.10" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp437" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp437.py">
    <optimization-time pass="1" time="0.14" micro_passes="5" max_branch_merge="17" merged_total="861" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp500" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp500.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp720" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp720.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="7" />
    </module_usages>
  </module>
  <module name="encodings.cp737" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp737.py">
    <optimization-time pass="1" time="0.14" micro_passes="5" max_branch_merge="17" merged_total="861" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp775" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp775.py">
    <optimization-time pass="1" time="0.15" micro_passes="5" max_branch_merge="17" merged_total="861" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp850" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp850.py">
    <optimization-time pass="1" time="0.13" micro_passes="5" max_branch_merge="17" merged_total="861" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp852" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp852.py">
    <optimization-time pass="1" time="0.14" micro_passes="5" max_branch_merge="17" merged_total="861" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp855" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp855.py">
    <optimization-time pass="1" time="0.15" micro_passes="5" max_branch_merge="17" merged_total="861" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp856" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp856.py">
    <optimization-time pass="1" time="0.19" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp857" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp857.py">
    <optimization-time pass="1" time="0.17" micro_passes="5" max_branch_merge="17" merged_total="861" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp858" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp858.py">
    <optimization-time pass="1" time="0.16" micro_passes="5" max_branch_merge="17" merged_total="861" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp860" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp860.py">
    <optimization-time pass="1" time="0.15" micro_passes="5" max_branch_merge="17" merged_total="861" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp861" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp861.py">
    <optimization-time pass="1" time="0.16" micro_passes="5" max_branch_merge="17" merged_total="861" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp862" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp862.py">
    <optimization-time pass="1" time="0.16" micro_passes="5" max_branch_merge="17" merged_total="861" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp863" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp863.py">
    <optimization-time pass="1" time="0.16" micro_passes="5" max_branch_merge="17" merged_total="861" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp864" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp864.py">
    <optimization-time pass="1" time="0.14" micro_passes="5" max_branch_merge="17" merged_total="861" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp865" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp865.py">
    <optimization-time pass="1" time="0.17" micro_passes="5" max_branch_merge="17" merged_total="861" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp866" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp866.py">
    <optimization-time pass="1" time="0.15" micro_passes="5" max_branch_merge="17" merged_total="861" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp869" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp869.py">
    <optimization-time pass="1" time="0.14" micro_passes="5" max_branch_merge="17" merged_total="861" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp874" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp874.py">
    <optimization-time pass="1" time="0.12" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp875" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp875.py">
    <optimization-time pass="1" time="0.13" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.cp932" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp932.py">
    <optimization-time pass="1" time="0.10" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_jp" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.cp949" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp949.py">
    <optimization-time pass="1" time="0.10" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_kr" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.cp950" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\cp950.py">
    <optimization-time pass="1" time="0.09" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_tw" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.euc_jis_2004" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\euc_jis_2004.py">
    <optimization-time pass="1" time="0.09" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_jp" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.euc_jisx0213" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\euc_jisx0213.py">
    <optimization-time pass="1" time="0.09" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_jp" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.euc_jp" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\euc_jp.py">
    <optimization-time pass="1" time="0.09" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_jp" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.euc_kr" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\euc_kr.py">
    <optimization-time pass="1" time="0.08" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_kr" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.gb18030" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\gb18030.py">
    <optimization-time pass="1" time="1.19" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_cn" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.gb2312" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\gb2312.py">
    <optimization-time pass="1" time="0.09" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_cn" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.gbk" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\gbk.py">
    <optimization-time pass="1" time="0.09" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_cn" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.hex_codec" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\encodings\hex_codec.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="765" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="8" />
      <module_usage name="binascii" finding="absolute" line="9" />
    </module_usages>
  </module>
  <module name="encodings.hp_roman8" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\hp_roman8.py">
    <optimization-time pass="1" time="0.12" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="12" />
    </module_usages>
  </module>
  <module name="encodings.hz" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\hz.py">
    <optimization-time pass="1" time="0.08" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_cn" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.idna" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\encodings\idna.py">
    <optimization-time pass="1" time="0.48" micro_passes="6" max_branch_merge="45" merged_total="3694" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="stringprep" finding="absolute" line="3" />
      <module_usage name="re" finding="absolute" line="3" />
      <module_usage name="codecs" finding="absolute" line="3" />
      <module_usage name="unicodedata" finding="absolute" line="4" />
    </module_usages>
  </module>
  <module name="encodings.iso2022_jp" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso2022_jp.py">
    <optimization-time pass="1" time="0.08" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_iso2022" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.iso2022_jp_1" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso2022_jp_1.py">
    <optimization-time pass="1" time="0.09" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_iso2022" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.iso2022_jp_2" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso2022_jp_2.py">
    <optimization-time pass="1" time="0.08" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_iso2022" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.iso2022_jp_2004" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso2022_jp_2004.py">
    <optimization-time pass="1" time="0.09" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_iso2022" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.iso2022_jp_3" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso2022_jp_3.py">
    <optimization-time pass="1" time="0.08" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_iso2022" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.iso2022_jp_ext" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso2022_jp_ext.py">
    <optimization-time pass="1" time="0.08" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_iso2022" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.iso2022_kr" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso2022_kr.py">
    <optimization-time pass="1" time="0.08" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_iso2022" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.iso8859_1" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso8859_1.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.iso8859_10" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso8859_10.py">
    <optimization-time pass="1" time="0.12" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.iso8859_11" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso8859_11.py">
    <optimization-time pass="1" time="0.10" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.iso8859_13" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso8859_13.py">
    <optimization-time pass="1" time="0.10" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.iso8859_14" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso8859_14.py">
    <optimization-time pass="1" time="0.10" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.iso8859_15" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso8859_15.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.iso8859_16" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso8859_16.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.iso8859_2" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso8859_2.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.iso8859_3" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso8859_3.py">
    <optimization-time pass="1" time="0.18" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.iso8859_4" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso8859_4.py">
    <optimization-time pass="1" time="0.12" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.iso8859_5" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso8859_5.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.iso8859_6" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso8859_6.py">
    <optimization-time pass="1" time="0.12" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.iso8859_7" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso8859_7.py">
    <optimization-time pass="1" time="0.10" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.iso8859_8" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso8859_8.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.iso8859_9" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\iso8859_9.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.johab" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\johab.py">
    <optimization-time pass="1" time="0.08" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_kr" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.koi8_r" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\koi8_r.py">
    <optimization-time pass="1" time="0.10" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.koi8_t" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\koi8_t.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="6" />
    </module_usages>
  </module>
  <module name="encodings.koi8_u" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\koi8_u.py">
    <optimization-time pass="1" time="0.10" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.kz1048" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\kz1048.py">
    <optimization-time pass="1" time="0.10" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.latin_1" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\latin_1.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="886" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="9" />
    </module_usages>
  </module>
  <module name="encodings.mac_arabic" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\mac_arabic.py">
    <optimization-time pass="1" time="0.12" micro_passes="5" max_branch_merge="17" merged_total="861" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.mac_croatian" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\mac_croatian.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.mac_cyrillic" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\mac_cyrillic.py">
    <optimization-time pass="1" time="0.09" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.mac_farsi" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\mac_farsi.py">
    <optimization-time pass="1" time="0.09" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.mac_greek" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\mac_greek.py">
    <optimization-time pass="1" time="0.09" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.mac_iceland" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\mac_iceland.py">
    <optimization-time pass="1" time="0.09" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.mac_latin2" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\mac_latin2.py">
    <optimization-time pass="1" time="0.10" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="10" />
    </module_usages>
  </module>
  <module name="encodings.mac_roman" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\mac_roman.py">
    <optimization-time pass="1" time="0.10" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.mac_romanian" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\mac_romanian.py">
    <optimization-time pass="1" time="0.09" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.mac_turkish" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\mac_turkish.py">
    <optimization-time pass="1" time="0.09" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.mbcs" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\mbcs.py">
    <optimization-time pass="1" time="0.08" micro_passes="4" max_branch_merge="16" merged_total="588" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="12" />
      <module_usage name="codecs" finding="absolute" line="14" />
    </module_usages>
  </module>
  <module name="encodings.oem" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\oem.py">
    <optimization-time pass="1" time="0.08" micro_passes="4" max_branch_merge="16" merged_total="588" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="6" />
      <module_usage name="codecs" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.palmos" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\palmos.py">
    <optimization-time pass="1" time="0.10" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="7" />
    </module_usages>
  </module>
  <module name="encodings.ptcp154" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\ptcp154.py">
    <optimization-time pass="1" time="0.10" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="10" />
    </module_usages>
  </module>
  <module name="encodings.punycode" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\encodings\punycode.py">
    <optimization-time pass="1" time="0.61" micro_passes="7" max_branch_merge="50" merged_total="3745" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="6" />
    </module_usages>
  </module>
  <module name="encodings.quopri_codec" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\quopri_codec.py">
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="17" merged_total="825" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="6" />
      <module_usage name="quopri" finding="absolute" line="7" />
      <module_usage name="io" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.raw_unicode_escape" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\raw_unicode_escape.py">
    <optimization-time pass="1" time="0.09" micro_passes="4" max_branch_merge="17" merged_total="723" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="9" />
    </module_usages>
  </module>
  <module name="encodings.rot_13" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\encodings\rot_13.py">
    <optimization-time pass="1" time="0.18" micro_passes="5" max_branch_merge="17" merged_total="877" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="9" />
    </module_usages>
  </module>
  <module name="encodings.shift_jis" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\shift_jis.py">
    <optimization-time pass="1" time="0.08" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_jp" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.shift_jis_2004" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\shift_jis_2004.py">
    <optimization-time pass="1" time="0.07" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_jp" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.shift_jisx0213" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\shift_jisx0213.py">
    <optimization-time pass="1" time="0.07" micro_passes="3" max_branch_merge="19" merged_total="596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_codecs_jp" finding="absolute" line="7" />
      <module_usage name="codecs" finding="absolute" line="7" />
      <module_usage name="_multibytecodec" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="encodings.tis_620" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\tis_620.py">
    <optimization-time pass="1" time="0.10" micro_passes="4" max_branch_merge="17" merged_total="701" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.undefined" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\undefined.py">
    <optimization-time pass="1" time="0.10" micro_passes="4" max_branch_merge="17" merged_total="665" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="12" />
    </module_usages>
  </module>
  <module name="encodings.unicode_escape" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\unicode_escape.py">
    <optimization-time pass="1" time="0.10" micro_passes="4" max_branch_merge="17" merged_total="723" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="9" />
    </module_usages>
  </module>
  <module name="encodings.utf_16" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\utf_16.py">
    <optimization-time pass="1" time="0.19" micro_passes="4" max_branch_merge="24" merged_total="1222" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="9" />
      <module_usage name="sys" finding="absolute" line="9" />
    </module_usages>
  </module>
  <module name="encodings.utf_16_be" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\utf_16_be.py">
    <optimization-time pass="1" time="0.09" micro_passes="4" max_branch_merge="16" merged_total="600" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="9" />
    </module_usages>
  </module>
  <module name="encodings.utf_16_le" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\utf_16_le.py">
    <optimization-time pass="1" time="0.09" micro_passes="4" max_branch_merge="16" merged_total="600" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="9" />
    </module_usages>
  </module>
  <module name="encodings.utf_32" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\utf_32.py">
    <optimization-time pass="1" time="0.17" micro_passes="4" max_branch_merge="24" merged_total="1222" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="4" />
      <module_usage name="sys" finding="absolute" line="4" />
    </module_usages>
  </module>
  <module name="encodings.utf_32_be" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\utf_32_be.py">
    <optimization-time pass="1" time="0.09" micro_passes="4" max_branch_merge="16" merged_total="600" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="4" />
    </module_usages>
  </module>
  <module name="encodings.utf_32_le" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\utf_32_le.py">
    <optimization-time pass="1" time="0.09" micro_passes="4" max_branch_merge="16" merged_total="600" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="4" />
    </module_usages>
  </module>
  <module name="encodings.utf_7" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\utf_7.py">
    <optimization-time pass="1" time="0.09" micro_passes="4" max_branch_merge="16" merged_total="600" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="encodings.utf_8" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\utf_8.py">
    <optimization-time pass="1" time="0.10" micro_passes="4" max_branch_merge="16" merged_total="600" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="9" />
    </module_usages>
  </module>
  <module name="encodings.utf_8_sig" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\utf_8_sig.py">
    <optimization-time pass="1" time="0.18" micro_passes="4" max_branch_merge="24" merged_total="1163" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="10" />
    </module_usages>
  </module>
  <module name="encodings.uu_codec" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\uu_codec.py">
    <optimization-time pass="1" time="0.19" micro_passes="5" max_branch_merge="35" merged_total="1339" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="10" />
      <module_usage name="binascii" finding="absolute" line="11" />
      <module_usage name="io" finding="absolute" line="12" />
    </module_usages>
  </module>
  <module name="encodings.zlib_codec" kind="UncompiledPythonModule" usage="stdlib" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\encodings\zlib_codec.py">
    <optimization-time pass="1" time="0.15" micro_passes="4" max_branch_merge="17" merged_total="921" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="8" />
      <module_usage name="zlib" finding="absolute" line="9" />
    </module_usages>
  </module>
  <module name="enum" kind="UncompiledPythonModule" usage="plugin:implicit-imports" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\enum.py">
    <plugin-influence name="anti-bloat" influence="condition-used" condition="plugin(&quot;pyside6&quot;)" tags_used="plugin" result="false" />
    <optimization-time pass="1" time="3.40" micro_passes="7" max_branch_merge="191" merged_total="25630" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="sys" finding="absolute" line="1" />
      <module_usage name="builtins" finding="absolute" line="2" />
      <module_usage name="types" finding="absolute" line="3" />
      <module_usage name="operator" finding="absolute" line="4" />
      <module_usage name="functools" finding="absolute" line="5" />
      <module_usage name="warnings" finding="absolute" line="400" />
      <module_usage name="warnings" finding="absolute" line="1210" />
    </module_usages>
  </module>
  <module name="fastboodt" kind="CompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\fastboodt.py">
    <optimization-time pass="1" time="0.21" micro_passes="6" max_branch_merge="36" merged_total="1140" />
    <optimization-time pass="2" time="0.03" micro_passes="1" max_branch_merge="30" merged_total="169" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="utils" finding="absolute" line="2" />
      <module_usage name="subprocess" finding="absolute" line="3" />
      <module_usage name="sys" finding="absolute" line="4" />
      <module_usage name="time" finding="absolute" line="5" />
      <module_usage name="ntpath" finding="absolute" line="61" />
    </module_usages>
  </module>
  <module name="filecmp" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\filecmp.py">
    <optimization-time pass="1" time="0.27" micro_passes="5" max_branch_merge="55" merged_total="2239" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="13" />
      <module_usage name="stat" finding="absolute" line="14" />
      <module_usage name="itertools" finding="absolute" line="15" />
      <module_usage name="types" finding="absolute" line="16" />
      <module_usage name="ntpath" finding="absolute" line="145" />
      <module_usage name="ntpath" finding="absolute" line="146" />
      <module_usage name="ntpath" finding="absolute" line="157" />
      <module_usage name="ntpath" finding="absolute" line="158" />
      <module_usage name="ntpath" finding="absolute" line="199" />
      <module_usage name="ntpath" finding="absolute" line="200" />
      <module_usage name="ntpath" finding="absolute" line="275" />
      <module_usage name="ntpath" finding="absolute" line="276" />
      <module_usage name="sys" finding="absolute" line="303" />
      <module_usage name="getopt" finding="absolute" line="304" />
    </module_usages>
  </module>
  <module name="fileinput" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\fileinput.py">
    <optimization-time pass="1" time="0.33" micro_passes="5" max_branch_merge="116" merged_total="2699" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="io" finding="absolute" line="68" />
      <module_usage name="sys" finding="absolute" line="69" />
      <module_usage name="os" finding="absolute" line="69" />
      <module_usage name="types" finding="absolute" line="70" />
      <module_usage name="warnings" finding="absolute" line="215" />
      <module_usage name="ntpath" finding="absolute" line="404" />
      <module_usage name="gzip" finding="absolute" line="406" />
      <module_usage name="bz2" finding="absolute" line="409" />
      <module_usage name="getopt" finding="absolute" line="427" />
    </module_usages>
  </module>
  <module name="flash_tool" kind="CompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\flash_tool.py">
    <optimization-time pass="1" time="2.01" micro_passes="5" max_branch_merge="456" merged_total="12906" />
    <optimization-time pass="2" time="0.20" micro_passes="1" max_branch_merge="456" merged_total="2394" />
    <distribution-usages>
      <distribution-usage name="PyQt6" />
      <distribution-usage name="PyQt6-Qt6" />
      <distribution-usage name="PyQt6_sip" />
    </distribution-usages>
    <module_usages>
      <module_usage name="sys" finding="absolute" line="1" />
      <module_usage name="os" finding="absolute" line="2" />
      <module_usage name="subprocess" finding="absolute" line="3" />
      <module_usage name="time" finding="absolute" line="4" />
      <module_usage name="datetime" finding="absolute" line="5" />
      <module_usage name="PyQt6.QtWidgets" finding="absolute" line="6" />
      <module_usage name="PyQt6.QtCore" finding="absolute" line="9" />
      <module_usage name="PyQt6.QtGui" finding="absolute" line="10" />
      <module_usage name="utils" finding="absolute" line="11" />
      <module_usage name="coloros15" finding="absolute" line="12" />
      <module_usage name="coloros" finding="absolute" line="13" />
      <module_usage name="payload_extractor" finding="absolute" line="14" />
      <module_usage name="zhidinyishuaxie" finding="absolute" line="15" />
      <module_usage name="custom_messagebox" finding="absolute" line="16" />
      <module_usage name="threading" finding="absolute" line="17" />
      <module_usage name="traceback" finding="absolute" line="18" />
      <module_usage name="fastboodt" finding="absolute" line="19" />
      <module_usage name="ntpath" finding="absolute" line="253" />
      <module_usage name="ntpath" finding="absolute" line="258" />
      <module_usage name="ntpath" finding="absolute" line="497" />
      <module_usage name="ntpath" finding="absolute" line="1518" />
      <module_usage name="ntpath" finding="absolute" line="1521" />
      <module_usage name="ntpath" finding="absolute" line="1523" />
      <module_usage name="ntpath" finding="absolute" line="1524" />
      <module_usage name="webbrowser" finding="absolute" line="1794" />
      <module_usage name="genduodakhd" finding="absolute" line="1802" />
    </module_usages>
  </module>
  <module name="fnmatch" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\fnmatch.py">
    <optimization-time pass="1" time="0.27" micro_passes="6" max_branch_merge="207" merged_total="2118" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="12" />
      <module_usage name="posixpath" finding="absolute" line="13" />
      <module_usage name="re" finding="absolute" line="14" />
      <module_usage name="functools" finding="absolute" line="15" />
      <module_usage name="ntpath" finding="absolute" line="34" />
      <module_usage name="ntpath" finding="absolute" line="35" />
      <module_usage name="ntpath" finding="absolute" line="51" />
      <module_usage name="ntpath" finding="absolute" line="53" />
      <module_usage name="ntpath" finding="absolute" line="60" />
    </module_usages>
  </module>
  <module name="font_extractor" kind="CompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\font_extractor.py">
    <optimization-time pass="1" time="0.47" micro_passes="5" max_branch_merge="202" merged_total="4987" />
    <optimization-time pass="2" time="0.09" micro_passes="1" max_branch_merge="202" merged_total="941" />
    <distribution-usages>
      <distribution-usage name="PyQt6" />
      <distribution-usage name="PyQt6-Qt6" />
      <distribution-usage name="PyQt6_sip" />
    </distribution-usages>
    <module_usages>
      <module_usage name="PyQt6.QtWidgets" finding="absolute" line="1" />
      <module_usage name="PyQt6.QtCore" finding="absolute" line="4" />
      <module_usage name="os" finding="absolute" line="5" />
      <module_usage name="utils" finding="absolute" line="6" />
      <module_usage name="subprocess" finding="absolute" line="7" />
    </module_usages>
  </module>
  <module name="fractions" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\fractions.py">
    <optimization-time pass="1" time="0.87" micro_passes="6" max_branch_merge="159" merged_total="7958" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="decimal" finding="absolute" line="6" />
      <module_usage name="functools" finding="absolute" line="7" />
      <module_usage name="math" finding="absolute" line="8" />
      <module_usage name="numbers" finding="absolute" line="9" />
      <module_usage name="operator" finding="absolute" line="10" />
      <module_usage name="re" finding="absolute" line="11" />
      <module_usage name="sys" finding="absolute" line="12" />
      <module_usage name="decimal" finding="absolute" line="308" />
    </module_usages>
  </module>
  <module name="ftplib" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\ftplib.py">
    <optimization-time pass="1" time="1.41" micro_passes="7" max_branch_merge="61" merged_total="10247" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="sys" finding="absolute" line="39" />
      <module_usage name="socket" finding="absolute" line="40" />
      <module_usage name="socket" finding="absolute" line="41" />
      <module_usage name="ssl" finding="absolute" line="671" />
      <module_usage name="re" finding="absolute" line="801" />
      <module_usage name="re" finding="absolute" line="820" />
    </module_usages>
  </module>
  <module name="functools" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\functools.py">
    <optimization-time pass="1" time="2.51" micro_passes="9" max_branch_merge="69" merged_total="13831" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="abc" finding="absolute" line="17" />
      <module_usage name="collections" finding="absolute" line="18" />
      <module_usage name="collections.namedtuple" finding="not-found" line="18" />
      <module_usage name="reprlib" finding="absolute" line="20" />
      <module_usage name="_thread" finding="absolute" line="21" />
      <module_usage name="types" finding="absolute" line="22" />
      <module_usage name="_functools" finding="absolute" line="226" />
      <module_usage name="_functools" finding="absolute" line="266" />
      <module_usage name="_functools" finding="absolute" line="342" />
      <module_usage name="_functools" finding="absolute" line="640" />
      <module_usage name="types" finding="absolute" line="810" />
      <module_usage name="weakref" finding="absolute" line="810" />
      <module_usage name="typing" finding="absolute" line="840" />
      <module_usage name="typing" finding="absolute" line="846" />
      <module_usage name="typing" finding="absolute" line="876" />
      <module_usage name="typing" finding="absolute" line="891" />
    </module_usages>
  </module>
  <module name="genduodakhd" kind="CompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\genduodakhd.py">
    <optimization-time pass="1" time="0.89" micro_passes="5" max_branch_merge="117" merged_total="7202" />
    <optimization-time pass="2" time="0.12" micro_passes="1" max_branch_merge="117" merged_total="1388" />
    <distribution-usages>
      <distribution-usage name="PyQt6" />
      <distribution-usage name="PyQt6-Qt6" />
      <distribution-usage name="PyQt6_sip" />
    </distribution-usages>
    <module_usages>
      <module_usage name="PyQt6.QtWidgets" finding="absolute" line="1" />
      <module_usage name="PyQt6.QtCore" finding="absolute" line="2" />
      <module_usage name="PyQt6.QtGui" finding="absolute" line="3" />
      <module_usage name="subprocess" finding="absolute" line="4" />
      <module_usage name="os" finding="absolute" line="5" />
      <module_usage name="utils" finding="absolute" line="6" />
      <module_usage name="genduodakhd" finding="absolute" line="257" />
      <module_usage name="genduodakhd" finding="absolute" line="262" />
      <module_usage name="ntpath" finding="absolute" line="285" />
      <module_usage name="ntpath" finding="absolute" line="286" />
      <module_usage name="PyQt6.QtWidgets" finding="absolute" line="293" />
      <module_usage name="font_extractor" finding="absolute" line="297" />
      <module_usage name="PyQt6.QtWidgets" finding="absolute" line="326" />
      <module_usage name="PyQt6.QtGui" finding="absolute" line="401" />
      <module_usage name="PyQt6.QtGui" finding="absolute" line="745" />
      <module_usage name="PyQt6.QtGui" finding="absolute" line="820" />
    </module_usages>
  </module>
  <module name="genericpath" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\genericpath.py">
    <optimization-time pass="1" time="0.13" micro_passes="6" max_branch_merge="35" merged_total="1025" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="6" />
      <module_usage name="stat" finding="absolute" line="7" />
    </module_usages>
  </module>
  <module name="getopt" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\getopt.py">
    <optimization-time pass="1" time="0.35" micro_passes="6" max_branch_merge="49" merged_total="2439" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="36" />
      <module_usage name="gettext" finding="absolute" line="38" />
    </module_usages>
  </module>
  <module name="gettext" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\gettext.py">
    <optimization-time pass="1" time="0.90" micro_passes="7" max_branch_merge="73" merged_total="6592" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="operator" finding="absolute" line="49" />
      <module_usage name="os" finding="absolute" line="50" />
      <module_usage name="re" finding="absolute" line="51" />
      <module_usage name="sys" finding="absolute" line="52" />
      <module_usage name="ntpath" finding="absolute" line="62" />
      <module_usage name="warnings" finding="absolute" line="175" />
      <module_usage name="locale" finding="absolute" line="225" />
      <module_usage name="builtins" finding="absolute" line="316" />
      <module_usage name="struct" finding="absolute" line="344" />
      <module_usage name="ntpath" finding="absolute" line="506" />
      <module_usage name="errno" finding="absolute" line="527" />
      <module_usage name="copy" finding="absolute" line="544" />
    </module_usages>
  </module>
  <module name="glob" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\glob.py">
    <optimization-time pass="1" time="0.46" micro_passes="7" max_branch_merge="65" merged_total="2998" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="contextlib" finding="absolute" line="3" />
      <module_usage name="os" finding="absolute" line="4" />
      <module_usage name="re" finding="absolute" line="5" />
      <module_usage name="fnmatch" finding="absolute" line="6" />
      <module_usage name="itertools" finding="absolute" line="7" />
      <module_usage name="stat" finding="absolute" line="8" />
      <module_usage name="sys" finding="absolute" line="9" />
      <module_usage name="ntpath" finding="absolute" line="62" />
      <module_usage name="ntpath" finding="absolute" line="99" />
      <module_usage name="ntpath" finding="absolute" line="195" />
      <module_usage name="ntpath" finding="absolute" line="218" />
      <module_usage name="ntpath" finding="absolute" line="244" />
    </module_usages>
  </module>
  <module name="graphlib" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\graphlib.py">
    <optimization-time pass="1" time="0.41" micro_passes="7" max_branch_merge="47" merged_total="2251" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="types" finding="absolute" line="1" />
    </module_usages>
  </module>
  <module name="gzip" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\gzip.py">
    <optimization-time pass="1" time="0.63" micro_passes="6" max_branch_merge="86" merged_total="6389" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="struct" finding="absolute" line="8" />
      <module_usage name="sys" finding="absolute" line="8" />
      <module_usage name="time" finding="absolute" line="8" />
      <module_usage name="os" finding="absolute" line="8" />
      <module_usage name="zlib" finding="absolute" line="9" />
      <module_usage name="builtins" finding="absolute" line="10" />
      <module_usage name="io" finding="absolute" line="11" />
      <module_usage name="_compression" finding="absolute" line="12" />
      <module_usage name="warnings" finding="absolute" line="212" />
      <module_usage name="errno" finding="absolute" line="294" />
      <module_usage name="errno" finding="absolute" line="322" />
      <module_usage name="errno" finding="absolute" line="332" />
      <module_usage name="errno" finding="absolute" line="342" />
      <module_usage name="io" finding="absolute" line="626" />
    </module_usages>
  </module>
  <module name="hashlib" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\hashlib.py">
    <optimization-time pass="1" time="0.60" micro_passes="5" max_branch_merge="41" merged_total="1030" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_sha1" finding="absolute" line="89" />
      <module_usage name="_md5" finding="absolute" line="92" />
      <module_usage name="_sha2" finding="absolute" line="95" />
      <module_usage name="_sha2" finding="absolute" line="99" />
      <module_usage name="_blake2" finding="absolute" line="103" />
      <module_usage name="_sha3" finding="absolute" line="107" />
      <module_usage name="_sha3" finding="absolute" line="113" />
      <module_usage name="_hashlib" finding="absolute" line="170" />
      <module_usage name="_hashlib" finding="absolute" line="182" />
      <module_usage name="_hashlib" finding="absolute" line="190" />
      <module_usage name="logging" finding="absolute" line="247" />
    </module_usages>
  </module>
  <module name="heapq" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\heapq.py">
    <plugin-influence name="anti-bloat" influence="condition-used" condition="not has_builtin_module(&quot;_heapq&quot;)" tags_used="has_builtin_module" result="false" />
    <optimization-time pass="1" time="0.61" micro_passes="5" max_branch_merge="51" merged_total="3891" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_heapq" finding="absolute" line="583" />
      <module_usage name="_heapq" finding="absolute" line="587" />
      <module_usage name="_heapq" finding="absolute" line="591" />
      <module_usage name="_heapq" finding="absolute" line="595" />
    </module_usages>
  </module>
  <module name="html" kind="UncompiledPythonPackage" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\html\__init__.py">
    <optimization-time pass="1" time="0.07" micro_passes="5" max_branch_merge="43" merged_total="441" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="re" finding="absolute" line="5" />
      <module_usage name="html.entities" finding="absolute" line="6" />
    </module_usages>
  </module>
  <module name="html.entities" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\html\entities.py">
    <optimization-time pass="1" time="0.04" micro_passes="5" max_branch_merge="13" merged_total="97" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="html.parser" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\html\parser.py">
    <optimization-time pass="1" time="0.37" micro_passes="5" max_branch_merge="272" merged_total="3596" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="11" />
      <module_usage name="_markupbase" finding="absolute" line="12" />
      <module_usage name="html" finding="absolute" line="14" />
      <module_usage name="html.unescape" finding="not-found" line="14" />
    </module_usages>
  </module>
  <module name="imaplib" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\imaplib.py">
    <optimization-time pass="1" time="2.01" micro_passes="7" max_branch_merge="131" merged_total="13526" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="binascii" finding="absolute" line="25" />
      <module_usage name="errno" finding="absolute" line="25" />
      <module_usage name="random" finding="absolute" line="25" />
      <module_usage name="re" finding="absolute" line="25" />
      <module_usage name="socket" finding="absolute" line="25" />
      <module_usage name="subprocess" finding="absolute" line="25" />
      <module_usage name="sys" finding="absolute" line="25" />
      <module_usage name="time" finding="absolute" line="25" />
      <module_usage name="calendar" finding="absolute" line="25" />
      <module_usage name="datetime" finding="absolute" line="26" />
      <module_usage name="io" finding="absolute" line="27" />
      <module_usage name="ssl" finding="absolute" line="30" />
      <module_usage name="hmac" finding="absolute" line="628" />
    </module_usages>
  </module>
  <module name="imghdr" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\imghdr.py">
    <optimization-time pass="1" time="0.13" micro_passes="5" max_branch_merge="32" merged_total="767" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="3" />
      <module_usage name="warnings" finding="absolute" line="4" />
      <module_usage name="sys" finding="absolute" line="159" />
      <module_usage name="os" finding="absolute" line="160" />
      <module_usage name="glob" finding="absolute" line="166" />
      <module_usage name="ntpath" finding="absolute" line="167" />
    </module_usages>
  </module>
  <module name="importlib" kind="UncompiledPythonPackage" usage="import path parent" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\importlib\__init__.py">
    <optimization-time pass="1" time="0.08" micro_passes="5" max_branch_merge="19" merged_total="635" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="_imp" finding="absolute" line="12" />
      <module_usage name="sys" finding="absolute" line="13" />
      <module_usage name="_frozen_importlib" finding="absolute" line="16" />
      <module_usage name="_frozen_importlib_external" finding="absolute" line="34" />
      <module_usage name="warnings" finding="absolute" line="57" />
      <module_usage name="importlib._bootstrap" finding="absolute" line="62" />
    </module_usages>
  </module>
  <module name="importlib._abc" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\importlib\_abc.py">
    <optimization-time pass="1" time="0.03" micro_passes="5" max_branch_merge="12" merged_total="162" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="importlib" finding="absolute" line="2" />
      <module_usage name="importlib._bootstrap" finding="relative" line="2" />
      <module_usage name="abc" finding="absolute" line="3" />
    </module_usages>
  </module>
  <module name="importlib._bootstrap" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\importlib\_bootstrap.py">
    <optimization-time pass="1" time="1.40" micro_passes="7" max_branch_merge="108" merged_total="14657" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_frozen_importlib_external" finding="absolute" line="1549" />
    </module_usages>
  </module>
  <module name="importlib._bootstrap_external" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\importlib\_bootstrap_external.py">
    <optimization-time pass="1" time="1.76" micro_passes="6" max_branch_merge="79" merged_total="12656" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_imp" finding="absolute" line="26" />
      <module_usage name="_io" finding="absolute" line="27" />
      <module_usage name="sys" finding="absolute" line="28" />
      <module_usage name="_warnings" finding="absolute" line="29" />
      <module_usage name="marshal" finding="absolute" line="30" />
      <module_usage name="nt" finding="absolute" line="35" />
      <module_usage name="winreg" finding="absolute" line="36" />
      <module_usage name="tokenize" finding="absolute" line="790" />
      <module_usage name="_io" finding="absolute" line="791" />
      <module_usage name="importlib.readers" finding="absolute" line="1194" />
      <module_usage name="importlib.readers" finding="absolute" line="1424" />
      <module_usage name="importlib.metadata" finding="absolute" line="1453" />
      <module_usage name="importlib.metadata" finding="absolute" line="1554" />
    </module_usages>
  </module>
  <module name="importlib.abc" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\importlib\abc.py">
    <optimization-time pass="1" time="0.29" micro_passes="5" max_branch_merge="27" merged_total="1733" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="importlib" finding="absolute" line="2" />
      <module_usage name="importlib._bootstrap_external" finding="relative" line="2" />
      <module_usage name="importlib" finding="absolute" line="3" />
      <module_usage name="importlib.machinery" finding="relative" line="3" />
      <module_usage name="_frozen_importlib" finding="absolute" line="5" />
      <module_usage name="_frozen_importlib_external" finding="absolute" line="11" />
      <module_usage name="importlib._abc" finding="absolute" line="14" />
      <module_usage name="abc" finding="absolute" line="15" />
      <module_usage name="warnings" finding="absolute" line="16" />
      <module_usage name="importlib.resources.abc" finding="absolute" line="18" />
    </module_usages>
  </module>
  <module name="importlib.machinery" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\importlib\machinery.py">
    <optimization-time pass="1" time="0.67" micro_passes="3" max_branch_merge="5" merged_total="15" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="importlib._bootstrap" finding="absolute" line="3" />
      <module_usage name="importlib._bootstrap" finding="absolute" line="4" />
      <module_usage name="importlib._bootstrap" finding="absolute" line="5" />
      <module_usage name="importlib._bootstrap_external" finding="absolute" line="6" />
      <module_usage name="importlib._bootstrap_external" finding="absolute" line="9" />
      <module_usage name="importlib._bootstrap_external" finding="absolute" line="10" />
      <module_usage name="importlib._bootstrap_external" finding="absolute" line="11" />
      <module_usage name="importlib._bootstrap_external" finding="absolute" line="12" />
      <module_usage name="importlib._bootstrap_external" finding="absolute" line="13" />
      <module_usage name="importlib._bootstrap_external" finding="absolute" line="14" />
      <module_usage name="importlib._bootstrap_external" finding="absolute" line="15" />
    </module_usages>
  </module>
  <module name="importlib.metadata" kind="UncompiledPythonPackage" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\importlib\metadata\__init__.py">
    <optimization-time pass="1" time="3.06" micro_passes="7" max_branch_merge="85" merged_total="11485" />
    <optimization-time pass="2" time="0.01" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="re" finding="absolute" line="2" />
      <module_usage name="abc" finding="absolute" line="3" />
      <module_usage name="csv" finding="absolute" line="4" />
      <module_usage name="sys" finding="absolute" line="5" />
      <module_usage name="email" finding="absolute" line="6" />
      <module_usage name="pathlib" finding="absolute" line="7" />
      <module_usage name="zipfile" finding="absolute" line="8" />
      <module_usage name="operator" finding="absolute" line="9" />
      <module_usage name="textwrap" finding="absolute" line="10" />
      <module_usage name="warnings" finding="absolute" line="11" />
      <module_usage name="functools" finding="absolute" line="12" />
      <module_usage name="itertools" finding="absolute" line="13" />
      <module_usage name="posixpath" finding="absolute" line="14" />
      <module_usage name="contextlib" finding="absolute" line="15" />
      <module_usage name="collections" finding="absolute" line="16" />
      <module_usage name="inspect" finding="absolute" line="17" />
      <module_usage name="importlib.metadata" finding="absolute" line="19" />
      <module_usage name="importlib.metadata._adapters" finding="relative" line="19" />
      <module_usage name="importlib.metadata._meta" finding="relative" line="19" />
      <module_usage name="importlib.metadata._collections" finding="absolute" line="20" />
      <module_usage name="importlib.metadata._functools" finding="absolute" line="21" />
      <module_usage name="importlib.metadata._itertools" finding="absolute" line="22" />
      <module_usage name="importlib.metadata._meta" finding="absolute" line="23" />
      <module_usage name="contextlib" finding="absolute" line="25" />
      <module_usage name="importlib" finding="absolute" line="26" />
      <module_usage name="importlib.abc" finding="absolute" line="27" />
      <module_usage name="itertools" finding="absolute" line="28" />
      <module_usage name="typing" finding="absolute" line="29" />
      <module_usage name="functools" finding="absolute" line="140" />
      <module_usage name="ntpath" finding="absolute" line="849" />
      <module_usage name="functools" finding="absolute" line="892" />
    </module_usages>
  </module>
  <module name="importlib.metadata._adapters" kind="UncompiledPythonModule" usage="import fromlist" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\importlib\metadata\_adapters.py">
    <optimization-time pass="1" time="0.69" micro_passes="5" max_branch_merge="36" merged_total="799" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="functools" finding="absolute" line="1" />
      <module_usage name="warnings" finding="absolute" line="2" />
      <module_usage name="re" finding="absolute" line="3" />
      <module_usage name="textwrap" finding="absolute" line="4" />
      <module_usage name="email.message" finding="absolute" line="5" />
      <module_usage name="importlib.metadata._text" finding="absolute" line="7" />
      <module_usage name="functools" finding="absolute" line="11" />
    </module_usages>
  </module>
  <module name="importlib.metadata._collections" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\importlib\metadata\_collections.py">
    <optimization-time pass="1" time="0.06" micro_passes="5" max_branch_merge="17" merged_total="404" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="collections" finding="absolute" line="1" />
    </module_usages>
  </module>
  <module name="importlib.metadata._functools" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\importlib\metadata\_functools.py">
    <optimization-time pass="1" time="0.03" micro_passes="5" max_branch_merge="12" merged_total="126" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="types" finding="absolute" line="1" />
      <module_usage name="functools" finding="absolute" line="2" />
    </module_usages>
  </module>
  <module name="importlib.metadata._itertools" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\importlib\metadata\_itertools.py">
    <optimization-time pass="1" time="0.05" micro_passes="5" max_branch_merge="13" merged_total="228" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="itertools" finding="absolute" line="1" />
    </module_usages>
  </module>
  <module name="importlib.metadata._meta" kind="UncompiledPythonModule" usage="import fromlist" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\importlib\metadata\_meta.py">
    <optimization-time pass="1" time="0.09" micro_passes="4" max_branch_merge="101" merged_total="1158" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="typing" finding="absolute" line="1" />
      <module_usage name="typing" finding="absolute" line="2" />
    </module_usages>
  </module>
  <module name="importlib.metadata._text" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\importlib\metadata\_text.py">
    <optimization-time pass="1" time="0.07" micro_passes="6" max_branch_merge="19" merged_total="471" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="1" />
      <module_usage name="importlib.metadata._functools" finding="absolute" line="3" />
    </module_usages>
  </module>
  <module name="importlib.readers" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\importlib\readers.py">
    <optimization-time pass="1" time="0.08" micro_passes="3" max_branch_merge="4" merged_total="12" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="importlib.resources.readers" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="importlib.resources" kind="UncompiledPythonPackage" usage="import path parent" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\importlib\resources\__init__.py">
    <optimization-time pass="1" time="0.11" micro_passes="3" max_branch_merge="8" merged_total="33" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="importlib.resources._common" finding="absolute" line="3" />
      <module_usage name="importlib.resources._legacy" finding="absolute" line="9" />
      <module_usage name="importlib.resources.abc" finding="absolute" line="20" />
    </module_usages>
  </module>
  <module name="importlib.resources._adapters" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\importlib\resources\_adapters.py">
    <optimization-time pass="1" time="0.22" micro_passes="5" max_branch_merge="18" merged_total="1384" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="contextlib" finding="absolute" line="1" />
      <module_usage name="io" finding="absolute" line="2" />
      <module_usage name="importlib.resources" finding="absolute" line="4" />
      <module_usage name="importlib.resources.abc" finding="relative" line="4" />
    </module_usages>
  </module>
  <module name="importlib.resources._common" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\importlib\resources\_common.py">
    <optimization-time pass="1" time="0.42" micro_passes="5" max_branch_merge="19" merged_total="1127" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="pathlib" finding="absolute" line="2" />
      <module_usage name="tempfile" finding="absolute" line="3" />
      <module_usage name="functools" finding="absolute" line="4" />
      <module_usage name="contextlib" finding="absolute" line="5" />
      <module_usage name="types" finding="absolute" line="6" />
      <module_usage name="importlib" finding="absolute" line="7" />
      <module_usage name="inspect" finding="absolute" line="8" />
      <module_usage name="warnings" finding="absolute" line="9" />
      <module_usage name="itertools" finding="absolute" line="10" />
      <module_usage name="typing" finding="absolute" line="12" />
      <module_usage name="importlib.resources.abc" finding="absolute" line="13" />
      <module_usage name="importlib.resources._adapters" finding="absolute" line="15" />
    </module_usages>
  </module>
  <module name="importlib.resources._itertools" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\importlib\resources\_itertools.py">
    <optimization-time pass="1" time="0.02" micro_passes="4" max_branch_merge="11" merged_total="68" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="importlib.resources._legacy" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\importlib\resources\_legacy.py">
    <optimization-time pass="1" time="0.10" micro_passes="5" max_branch_merge="11" merged_total="568" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="functools" finding="absolute" line="1" />
      <module_usage name="os" finding="absolute" line="2" />
      <module_usage name="pathlib" finding="absolute" line="3" />
      <module_usage name="types" finding="absolute" line="4" />
      <module_usage name="warnings" finding="absolute" line="5" />
      <module_usage name="typing" finding="absolute" line="7" />
      <module_usage name="importlib.resources" finding="absolute" line="9" />
      <module_usage name="importlib.resources._common" finding="relative" line="9" />
      <module_usage name="ntpath" finding="absolute" line="36" />
    </module_usages>
  </module>
  <module name="importlib.resources.abc" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\importlib\resources\abc.py">
    <optimization-time pass="1" time="0.27" micro_passes="6" max_branch_merge="60" merged_total="2646" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="abc" finding="absolute" line="1" />
      <module_usage name="io" finding="absolute" line="2" />
      <module_usage name="itertools" finding="absolute" line="3" />
      <module_usage name="os" finding="absolute" line="4" />
      <module_usage name="pathlib" finding="absolute" line="5" />
      <module_usage name="typing" finding="absolute" line="6" />
      <module_usage name="typing" finding="absolute" line="7" />
      <module_usage name="typing" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="importlib.resources.readers" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\importlib\resources\readers.py">
    <optimization-time pass="1" time="0.34" micro_passes="7" max_branch_merge="23" merged_total="2400" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="collections" finding="absolute" line="1" />
      <module_usage name="itertools" finding="absolute" line="2" />
      <module_usage name="pathlib" finding="absolute" line="3" />
      <module_usage name="operator" finding="absolute" line="4" />
      <module_usage name="zipfile" finding="absolute" line="5" />
      <module_usage name="importlib.resources" finding="absolute" line="7" />
      <module_usage name="importlib.resources.abc" finding="relative" line="7" />
      <module_usage name="importlib.resources._itertools" finding="absolute" line="9" />
    </module_usages>
  </module>
  <module name="importlib.resources.simple" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\importlib\resources\simple.py">
    <optimization-time pass="1" time="0.17" micro_passes="5" max_branch_merge="49" merged_total="1341" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="abc" finding="absolute" line="5" />
      <module_usage name="io" finding="absolute" line="6" />
      <module_usage name="itertools" finding="absolute" line="7" />
      <module_usage name="typing" finding="absolute" line="8" />
      <module_usage name="importlib.resources.abc" finding="absolute" line="10" />
    </module_usages>
  </module>
  <module name="importlib.simple" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\importlib\simple.py">
    <optimization-time pass="1" time="0.01" micro_passes="3" max_branch_merge="4" merged_total="12" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="importlib.resources.simple" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="importlib.util" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\importlib\util.py">
    <optimization-time pass="1" time="0.23" micro_passes="5" max_branch_merge="44" merged_total="1571" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="importlib._abc" finding="absolute" line="2" />
      <module_usage name="importlib._bootstrap" finding="absolute" line="3" />
      <module_usage name="importlib._bootstrap" finding="absolute" line="4" />
      <module_usage name="importlib._bootstrap" finding="absolute" line="5" />
      <module_usage name="importlib._bootstrap" finding="absolute" line="6" />
      <module_usage name="importlib._bootstrap_external" finding="absolute" line="7" />
      <module_usage name="importlib._bootstrap_external" finding="absolute" line="8" />
      <module_usage name="importlib._bootstrap_external" finding="absolute" line="9" />
      <module_usage name="importlib._bootstrap_external" finding="absolute" line="10" />
      <module_usage name="importlib._bootstrap_external" finding="absolute" line="11" />
      <module_usage name="importlib._bootstrap_external" finding="absolute" line="12" />
      <module_usage name="_imp" finding="absolute" line="14" />
      <module_usage name="sys" finding="absolute" line="15" />
      <module_usage name="types" finding="absolute" line="16" />
      <module_usage name="threading" finding="absolute" line="257" />
    </module_usages>
  </module>
  <module name="inspect" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\inspect.py">
    <optimization-time pass="1" time="5.78" micro_passes="7" max_branch_merge="189" merged_total="34119" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="abc" finding="absolute" line="144" />
      <module_usage name="ast" finding="absolute" line="145" />
      <module_usage name="dis" finding="absolute" line="146" />
      <module_usage name="collections.abc" finding="absolute" line="147" />
      <module_usage name="enum" finding="absolute" line="148" />
      <module_usage name="importlib.machinery" finding="absolute" line="149" />
      <module_usage name="itertools" finding="absolute" line="150" />
      <module_usage name="linecache" finding="absolute" line="151" />
      <module_usage name="os" finding="absolute" line="152" />
      <module_usage name="re" finding="absolute" line="153" />
      <module_usage name="sys" finding="absolute" line="154" />
      <module_usage name="tokenize" finding="absolute" line="155" />
      <module_usage name="token" finding="absolute" line="156" />
      <module_usage name="types" finding="absolute" line="157" />
      <module_usage name="functools" finding="absolute" line="158" />
      <module_usage name="builtins" finding="absolute" line="159" />
      <module_usage name="keyword" finding="absolute" line="160" />
      <module_usage name="operator" finding="absolute" line="161" />
      <module_usage name="collections" finding="absolute" line="162" />
      <module_usage name="collections.namedtuple" finding="not-found" line="162" />
      <module_usage name="collections.OrderedDict" finding="not-found" line="162" />
      <module_usage name="weakref" finding="absolute" line="163" />
      <module_usage name="functools" finding="absolute" line="273" />
      <module_usage name="ntpath" finding="absolute" line="959" />
      <module_usage name="ntpath" finding="absolute" line="983" />
      <module_usage name="ntpath" finding="absolute" line="1016" />
      <module_usage name="functools" finding="absolute" line="2509" />
      <module_usage name="functools" finding="absolute" line="2601" />
    </module_usages>
  </module>
  <module name="io" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\io.py">
    <optimization-time pass="1" time="0.15" micro_passes="5" max_branch_merge="18" merged_total="952" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_io" finding="absolute" line="52" />
      <module_usage name="abc" finding="absolute" line="53" />
      <module_usage name="_io" finding="absolute" line="55" />
      <module_usage name="_io" finding="absolute" line="95" />
    </module_usages>
  </module>
  <module name="ipaddress" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\ipaddress.py">
    <optimization-time pass="1" time="2.37" micro_passes="6" max_branch_merge="114" merged_total="18099" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="functools" finding="absolute" line="14" />
      <module_usage name="re" finding="absolute" line="641" />
    </module_usages>
  </module>
  <module name="json" kind="UncompiledPythonPackage" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\json\__init__.py">
    <optimization-time pass="1" time="0.10" micro_passes="5" max_branch_merge="41" merged_total="990" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="json.decoder" finding="absolute" line="106" />
      <module_usage name="json.encoder" finding="absolute" line="107" />
      <module_usage name="codecs" finding="absolute" line="108" />
    </module_usages>
  </module>
  <module name="json.decoder" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\json\decoder.py">
    <plugin-influence name="anti-bloat" influence="condition-used" condition="not has_builtin_module(&quot;_json&quot;)" tags_used="has_builtin_module" result="false" />
    <optimization-time pass="1" time="0.42" micro_passes="6" max_branch_merge="88" merged_total="3241" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="3" />
      <module_usage name="json" finding="absolute" line="5" />
      <module_usage name="json.scanner" finding="relative" line="5" />
      <module_usage name="_json" finding="absolute" line="7" />
    </module_usages>
  </module>
  <module name="json.encoder" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\json\encoder.py">
    <plugin-influence name="anti-bloat" influence="condition-used" condition="not has_builtin_module(&quot;_json&quot;)" tags_used="has_builtin_module" result="false" />
    <optimization-time pass="1" time="0.54" micro_passes="6" max_branch_merge="72" merged_total="3588" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="3" />
      <module_usage name="_json" finding="absolute" line="6" />
      <module_usage name="_json" finding="absolute" line="10" />
      <module_usage name="_json" finding="absolute" line="14" />
    </module_usages>
  </module>
  <module name="json.scanner" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\json\scanner.py">
    <plugin-influence name="anti-bloat" influence="condition-used" condition="not has_builtin_module(&quot;_json&quot;)" tags_used="has_builtin_module" result="false" />
    <optimization-time pass="1" time="0.11" micro_passes="4" max_branch_merge="83" merged_total="646" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="3" />
      <module_usage name="_json" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="keyword" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\keyword.py">
    <optimization-time pass="1" time="0.01" micro_passes="4" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="linecache" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\linecache.py">
    <optimization-time pass="1" time="0.21" micro_passes="5" max_branch_merge="32" merged_total="1478" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="functools" finding="absolute" line="8" />
      <module_usage name="sys" finding="absolute" line="9" />
      <module_usage name="os" finding="absolute" line="10" />
      <module_usage name="tokenize" finding="absolute" line="11" />
      <module_usage name="ntpath" finding="absolute" line="119" />
      <module_usage name="ntpath" finding="absolute" line="124" />
      <module_usage name="functools" finding="absolute" line="179" />
    </module_usages>
  </module>
  <module name="locale" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\locale.py">
    <optimization-time pass="1" time="0.64" micro_passes="5" max_branch_merge="89" merged_total="5642" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="sys" finding="absolute" line="13" />
      <module_usage name="encodings" finding="absolute" line="14" />
      <module_usage name="encodings.aliases" finding="absolute" line="15" />
      <module_usage name="re" finding="absolute" line="16" />
      <module_usage name="_collections_abc" finding="absolute" line="17" />
      <module_usage name="builtins" finding="absolute" line="18" />
      <module_usage name="functools" finding="absolute" line="19" />
      <module_usage name="_locale" finding="absolute" line="47" />
      <module_usage name="warnings" finding="absolute" line="543" />
      <module_usage name="_locale" finding="absolute" line="555" />
      <module_usage name="os" finding="absolute" line="569" />
      <module_usage name="warnings" finding="absolute" line="625" />
      <module_usage name="_locale" finding="absolute" line="639" />
      <module_usage name="warnings" finding="absolute" line="658" />
      <module_usage name="warnings" finding="absolute" line="672" />
    </module_usages>
  </module>
  <module name="logging" kind="UncompiledPythonPackage" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\logging\__init__.py">
    <optimization-time pass="1" time="3.57" micro_passes="7" max_branch_merge="82" merged_total="18386" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="sys" finding="absolute" line="26" />
      <module_usage name="os" finding="absolute" line="26" />
      <module_usage name="time" finding="absolute" line="26" />
      <module_usage name="io" finding="absolute" line="26" />
      <module_usage name="re" finding="absolute" line="26" />
      <module_usage name="traceback" finding="absolute" line="26" />
      <module_usage name="warnings" finding="absolute" line="26" />
      <module_usage name="weakref" finding="absolute" line="26" />
      <module_usage name="collections.abc" finding="absolute" line="26" />
      <module_usage name="types" finding="absolute" line="28" />
      <module_usage name="string" finding="absolute" line="29" />
      <module_usage name="string" finding="absolute" line="30" />
      <module_usage name="threading" finding="absolute" line="44" />
      <module_usage name="ntpath" finding="absolute" line="191" />
      <module_usage name="ntpath" finding="absolute" line="202" />
      <module_usage name="ntpath" finding="absolute" line="333" />
      <module_usage name="io" finding="absolute" line="656" />
      <module_usage name="io" finding="absolute" line="1637" />
      <module_usage name="pickle" finding="absolute" line="1856" />
      <module_usage name="atexit" finding="absolute" line="2280" />
    </module_usages>
  </module>
  <module name="lzma" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\lzma.py">
    <optimization-time pass="1" time="0.16" micro_passes="5" max_branch_merge="66" merged_total="1617" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="builtins" finding="absolute" line="24" />
      <module_usage name="io" finding="absolute" line="25" />
      <module_usage name="os" finding="absolute" line="26" />
      <module_usage name="_lzma" finding="absolute" line="27" />
      <module_usage name="_lzma" finding="absolute" line="28" />
      <module_usage name="_compression" finding="absolute" line="29" />
    </module_usages>
  </module>
  <module name="mailcap" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\mailcap.py">
    <optimization-time pass="1" time="0.74" micro_passes="9" max_branch_merge="76" merged_total="4887" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="3" />
      <module_usage name="warnings" finding="absolute" line="4" />
      <module_usage name="re" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="mimetypes" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\mimetypes.py">
    <optimization-time pass="1" time="0.74" micro_passes="7" max_branch_merge="71" merged_total="3680" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="26" />
      <module_usage name="sys" finding="absolute" line="27" />
      <module_usage name="posixpath" finding="absolute" line="28" />
      <module_usage name="urllib.parse" finding="absolute" line="29" />
      <module_usage name="_winapi" finding="absolute" line="32" />
      <module_usage name="winreg" finding="absolute" line="37" />
      <module_usage name="ntpath" finding="absolute" line="129" />
    </module_usages>
  </module>
  <module name="modulefinder" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\modulefinder.py">
    <optimization-time pass="1" time="1.22" micro_passes="7" max_branch_merge="67" merged_total="8428" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="dis" finding="absolute" line="3" />
      <module_usage name="importlib._bootstrap_external" finding="absolute" line="4" />
      <module_usage name="importlib.machinery" finding="absolute" line="5" />
      <module_usage name="marshal" finding="absolute" line="6" />
      <module_usage name="os" finding="absolute" line="7" />
      <module_usage name="io" finding="absolute" line="8" />
      <module_usage name="sys" finding="absolute" line="9" />
      <module_usage name="ntpath" finding="absolute" line="85" />
      <module_usage name="ntpath" finding="absolute" line="156" />
      <module_usage name="ntpath" finding="absolute" line="157" />
    </module_usages>
  </module>
  <module name="netrc" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\netrc.py">
    <optimization-time pass="1" time="0.57" micro_passes="7" max_branch_merge="124" merged_total="2911" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="5" />
      <module_usage name="stat" finding="absolute" line="5" />
      <module_usage name="ntpath" finding="absolute" line="70" />
    </module_usages>
  </module>
  <module name="ntpath" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\ntpath.py">
    <optimization-time pass="1" time="1.21" micro_passes="7" max_branch_merge="139" merged_total="10896" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="20" />
      <module_usage name="sys" finding="absolute" line="21" />
      <module_usage name="stat" finding="absolute" line="22" />
      <module_usage name="genericpath" finding="absolute" line="23" />
      <module_usage name="genericpath" finding="absolute" line="24" />
      <module_usage name="_winapi" finding="absolute" line="46" />
      <module_usage name="nt" finding="absolute" line="318" />
      <module_usage name="string" finding="absolute" line="419" />
      <module_usage name="string" finding="absolute" line="430" />
      <module_usage name="nt" finding="absolute" line="524" />
      <module_usage name="nt" finding="absolute" line="582" />
      <module_usage name="nt" finding="absolute" line="596" />
      <module_usage name="nt" finding="absolute" line="867" />
      <module_usage name="nt" finding="absolute" line="868" />
      <module_usage name="nt" finding="absolute" line="869" />
      <module_usage name="nt" finding="absolute" line="870" />
      <module_usage name="nt" finding="absolute" line="877" />
    </module_usages>
  </module>
  <module name="ntsecuritycon" kind="CompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\venv\Lib\site-packages\win32\lib\ntsecuritycon.py">
    <optimization-time pass="1" time="0.06" micro_passes="3" />
    <optimization-time pass="2" time="0.02" micro_passes="1" />
    <module_usages />
  </module>
  <module name="nturl2path" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\nturl2path.py">
    <optimization-time pass="1" time="0.08" micro_passes="5" max_branch_merge="61" merged_total="715" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="string" finding="absolute" line="17" />
      <module_usage name="urllib.parse" finding="absolute" line="17" />
      <module_usage name="urllib.parse" finding="absolute" line="52" />
    </module_usages>
  </module>
  <module name="numbers" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\numbers.py">
    <optimization-time pass="1" time="0.23" micro_passes="4" max_branch_merge="86" merged_total="2176" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="abc" finding="absolute" line="33" />
    </module_usages>
  </module>
  <module name="opcode" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\opcode.py">
    <plugin-influence name="implicit-imports" influence="condition-used" condition="python313_or_higher" tags_used="python313_or_higher" result="false" />
    <optimization-time pass="1" time="0.22" micro_passes="7" max_branch_merge="13" merged_total="897" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_opcode" finding="absolute" line="19" />
    </module_usages>
  </module>
  <module name="operator" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\operator.py">
    <optimization-time pass="1" time="0.30" micro_passes="5" max_branch_merge="30" merged_total="1850" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="builtins" finding="absolute" line="22" />
      <module_usage name="functools" finding="absolute" line="333" />
      <module_usage name="_operator" finding="absolute" line="414" />
      <module_usage name="_operator" finding="absolute" line="418" />
    </module_usages>
  </module>
  <module name="os" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\os.py">
    <optimization-time pass="1" time="2.41" micro_passes="6" max_branch_merge="79" merged_total="8368" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="abc" finding="absolute" line="25" />
      <module_usage name="sys" finding="absolute" line="26" />
      <module_usage name="stat" finding="absolute" line="27" />
      <module_usage name="_collections_abc" finding="absolute" line="29" />
      <module_usage name="nt" finding="absolute" line="75" />
      <module_usage name="nt" finding="absolute" line="77" />
      <module_usage name="ntpath" finding="absolute" line="81" />
      <module_usage name="nt" finding="absolute" line="83" />
      <module_usage name="nt" finding="absolute" line="88" />
      <module_usage name="ntpath" finding="absolute" line="96" />
      <module_usage name="warnings" finding="absolute" line="664" />
      <module_usage name="_collections_abc" finding="absolute" line="699" />
      <module_usage name="subprocess" finding="absolute" line="1020" />
      <module_usage name="io" finding="absolute" line="1063" />
      <module_usage name="nt" finding="absolute" line="1153" />
    </module_usages>
  </module>
  <module name="pathlib" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\pathlib.py">
    <optimization-time pass="1" time="2.03" micro_passes="7" max_branch_merge="103" merged_total="17093" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="fnmatch" finding="absolute" line="8" />
      <module_usage name="functools" finding="absolute" line="9" />
      <module_usage name="io" finding="absolute" line="10" />
      <module_usage name="ntpath" finding="absolute" line="11" />
      <module_usage name="os" finding="absolute" line="12" />
      <module_usage name="posixpath" finding="absolute" line="13" />
      <module_usage name="re" finding="absolute" line="14" />
      <module_usage name="sys" finding="absolute" line="15" />
      <module_usage name="warnings" finding="absolute" line="16" />
      <module_usage name="_collections_abc" finding="absolute" line="17" />
      <module_usage name="errno" finding="absolute" line="18" />
      <module_usage name="stat" finding="absolute" line="19" />
      <module_usage name="urllib.parse" finding="absolute" line="20" />
      <module_usage name="pwd" finding="not-found" line="1260" />
      <module_usage name="grp" finding="not-found" line="1271" />
    </module_usages>
  </module>
  <module name="payload_extractor" kind="CompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\payload_extractor.py">
    <optimization-time pass="1" time="0.17" micro_passes="5" max_branch_merge="77" merged_total="1518" />
    <optimization-time pass="2" time="0.03" micro_passes="1" max_branch_merge="62" merged_total="227" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="subprocess" finding="absolute" line="2" />
      <module_usage name="sys" finding="absolute" line="3" />
      <module_usage name="pathlib" finding="absolute" line="4" />
      <module_usage name="locale" finding="absolute" line="5" />
      <module_usage name="shutil" finding="absolute" line="6" />
      <module_usage name="time" finding="absolute" line="7" />
      <module_usage name="ntpath" finding="absolute" line="191" />
      <module_usage name="ntpath" finding="absolute" line="225" />
      <module_usage name="ntpath" finding="absolute" line="258" />
    </module_usages>
  </module>
  <module name="pickle" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\pickle.py">
    <optimization-time pass="1" time="2.13" micro_passes="6" max_branch_merge="352" merged_total="17602" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="types" finding="absolute" line="26" />
      <module_usage name="copyreg" finding="absolute" line="27" />
      <module_usage name="copyreg" finding="absolute" line="28" />
      <module_usage name="itertools" finding="absolute" line="29" />
      <module_usage name="functools" finding="absolute" line="30" />
      <module_usage name="sys" finding="absolute" line="31" />
      <module_usage name="struct" finding="absolute" line="33" />
      <module_usage name="re" finding="absolute" line="34" />
      <module_usage name="io" finding="absolute" line="35" />
      <module_usage name="codecs" finding="absolute" line="36" />
      <module_usage name="_compat_pickle" finding="absolute" line="37" />
      <module_usage name="_pickle" finding="absolute" line="43" />
      <module_usage name="io" finding="absolute" line="204" />
      <module_usage name="io" finding="absolute" line="233" />
      <module_usage name="io" finding="absolute" line="311" />
      <module_usage name="io" finding="absolute" line="1796" />
      <module_usage name="io" finding="absolute" line="1812" />
      <module_usage name="_pickle" finding="absolute" line="1818" />
    </module_usages>
  </module>
  <module name="pickletools" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\pickletools.py">
    <optimization-time pass="1" time="1.88" micro_passes="6" max_branch_merge="167" merged_total="6837" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="13" />
      <module_usage name="io" finding="absolute" line="14" />
      <module_usage name="pickle" finding="absolute" line="15" />
      <module_usage name="re" finding="absolute" line="16" />
      <module_usage name="sys" finding="absolute" line="17" />
      <module_usage name="struct" finding="absolute" line="210" />
      <module_usage name="pickle" finding="absolute" line="871" />
      <module_usage name="io" finding="absolute" line="2270" />
      <module_usage name="io" finding="absolute" line="2364" />
    </module_usages>
  </module>
  <module name="pipes" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\pipes.py">
    <optimization-time pass="1" time="0.22" micro_passes="5" max_branch_merge="62" merged_total="1763" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="60" />
      <module_usage name="os" finding="absolute" line="61" />
      <module_usage name="tempfile" finding="absolute" line="62" />
      <module_usage name="warnings" finding="absolute" line="63" />
      <module_usage name="shlex" finding="absolute" line="66" />
    </module_usages>
  </module>
  <module name="pkgutil" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\pkgutil.py">
    <optimization-time pass="1" time="0.56" micro_passes="7" max_branch_merge="51" merged_total="4708" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="collections" finding="absolute" line="3" />
      <module_usage name="collections.namedtuple" finding="not-found" line="3" />
      <module_usage name="functools" finding="absolute" line="4" />
      <module_usage name="importlib" finding="absolute" line="5" />
      <module_usage name="importlib.util" finding="absolute" line="6" />
      <module_usage name="importlib.machinery" finding="absolute" line="7" />
      <module_usage name="os" finding="absolute" line="8" />
      <module_usage name="ntpath" finding="absolute" line="9" />
      <module_usage name="sys" finding="absolute" line="10" />
      <module_usage name="types" finding="absolute" line="11" />
      <module_usage name="warnings" finding="absolute" line="12" />
      <module_usage name="marshal" finding="absolute" line="29" />
      <module_usage name="inspect" finding="absolute" line="135" />
      <module_usage name="zipimport" finding="absolute" line="175" />
      <module_usage name="zipimport" finding="absolute" line="176" />
      <module_usage name="inspect" finding="absolute" line="183" />
      <module_usage name="re" finding="absolute" line="493" />
    </module_usages>
  </module>
  <module name="platform" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\platform.py">
    <plugin-influence name="anti-bloat" influence="condition-used" condition="not macos" tags_used="macos" result="true" />
    <optimization-time pass="1" time="1.22" micro_passes="7" max_branch_merge="76" merged_total="9880" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="collections" finding="absolute" line="116" />
      <module_usage name="os" finding="absolute" line="117" />
      <module_usage name="re" finding="absolute" line="118" />
      <module_usage name="sys" finding="absolute" line="119" />
      <module_usage name="functools" finding="absolute" line="120" />
      <module_usage name="itertools" finding="absolute" line="121" />
      <module_usage name="ntpath" finding="absolute" line="199" />
      <module_usage name="subprocess" finding="absolute" line="280" />
      <module_usage name="_wmi" finding="absolute" line="317" />
      <module_usage name="winreg" finding="absolute" line="369" />
      <module_usage name="_winreg" finding="not-found" line="371" />
      <module_usage name="sys" finding="absolute" line="406" />
      <module_usage name="winreg" finding="absolute" line="431" />
      <module_usage name="_winreg" finding="not-found" line="433" />
      <module_usage name="java.lang" finding="not-found" line="502" />
      <module_usage name="java.lang" finding="not-found" line="525" />
      <module_usage name="socket" finding="absolute" line="625" />
      <module_usage name="ntpath" finding="absolute" line="641" />
      <module_usage name="ntpath" finding="absolute" line="643" />
      <module_usage name="struct" finding="absolute" line="715" />
      <module_usage name="vms_lib" finding="not-found" line="811" />
      <module_usage name="subprocess" finding="absolute" line="823" />
    </module_usages>
  </module>
  <module name="poplib" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\poplib.py">
    <optimization-time pass="1" time="0.40" micro_passes="6" max_branch_merge="43" merged_total="2825" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="errno" finding="absolute" line="16" />
      <module_usage name="re" finding="absolute" line="17" />
      <module_usage name="socket" finding="absolute" line="18" />
      <module_usage name="sys" finding="absolute" line="19" />
      <module_usage name="ssl" finding="absolute" line="22" />
      <module_usage name="hashlib" finding="absolute" line="333" />
    </module_usages>
  </module>
  <module name="posixpath" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\posixpath.py">
    <optimization-time pass="1" time="0.68" micro_passes="7" max_branch_merge="58" merged_total="5783" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="25" />
      <module_usage name="sys" finding="absolute" line="26" />
      <module_usage name="stat" finding="absolute" line="27" />
      <module_usage name="genericpath" finding="absolute" line="28" />
      <module_usage name="genericpath" finding="absolute" line="29" />
      <module_usage name="pwd" finding="not-found" line="273" />
      <module_usage name="pwd" finding="not-found" line="287" />
      <module_usage name="re" finding="absolute" line="329" />
      <module_usage name="re" finding="absolute" line="339" />
      <module_usage name="posix" finding="not-found" line="374" />
    </module_usages>
  </module>
  <module name="pprint" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\pprint.py">
    <optimization-time pass="1" time="1.22" micro_passes="8" max_branch_merge="114" merged_total="9435" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="collections" finding="absolute" line="37" />
      <module_usage name="dataclasses" finding="absolute" line="38" />
      <module_usage name="re" finding="absolute" line="39" />
      <module_usage name="sys" finding="absolute" line="40" />
      <module_usage name="types" finding="absolute" line="41" />
      <module_usage name="io" finding="absolute" line="42" />
    </module_usages>
  </module>
  <module name="pstats" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\pstats.py">
    <optimization-time pass="1" time="1.48" micro_passes="7" max_branch_merge="60" merged_total="10850" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="sys" finding="absolute" line="23" />
      <module_usage name="os" finding="absolute" line="24" />
      <module_usage name="time" finding="absolute" line="25" />
      <module_usage name="marshal" finding="absolute" line="26" />
      <module_usage name="re" finding="absolute" line="27" />
      <module_usage name="enum" finding="absolute" line="29" />
      <module_usage name="functools" finding="absolute" line="30" />
      <module_usage name="dataclasses" finding="absolute" line="31" />
      <module_usage name="typing" finding="absolute" line="32" />
    </module_usages>
  </module>
  <module name="psutil" kind="CompiledPythonPackage" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\venv\Lib\site-packages\psutil\__init__.py" distribution="psutil">
    <optimization-time pass="1" time="3.98" micro_passes="7" max_branch_merge="177" merged_total="18249" />
    <optimization-time pass="2" time="0.28" micro_passes="1" max_branch_merge="169" merged_total="2403" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="__future__" finding="absolute" line="23" />
      <module_usage name="collections" finding="absolute" line="25" />
      <module_usage name="contextlib" finding="absolute" line="26" />
      <module_usage name="datetime" finding="absolute" line="27" />
      <module_usage name="functools" finding="absolute" line="28" />
      <module_usage name="os" finding="absolute" line="29" />
      <module_usage name="signal" finding="absolute" line="30" />
      <module_usage name="subprocess" finding="absolute" line="31" />
      <module_usage name="sys" finding="absolute" line="32" />
      <module_usage name="threading" finding="absolute" line="33" />
      <module_usage name="time" finding="absolute" line="34" />
      <module_usage name="pwd" finding="not-found" line="38" />
      <module_usage name="psutil" finding="absolute" line="42" />
      <module_usage name="psutil._common" finding="relative" line="42" />
      <module_usage name="psutil._common" finding="relative" line="45" />
      <module_usage name="psutil._common" finding="relative" line="46" />
      <module_usage name="psutil._common" finding="relative" line="47" />
      <module_usage name="psutil._common" finding="relative" line="48" />
      <module_usage name="psutil._common" finding="relative" line="49" />
      <module_usage name="psutil._common" finding="relative" line="50" />
      <module_usage name="psutil._common" finding="relative" line="51" />
      <module_usage name="psutil._common" finding="relative" line="52" />
      <module_usage name="psutil._common" finding="relative" line="53" />
      <module_usage name="psutil._common" finding="relative" line="54" />
      <module_usage name="psutil._common" finding="relative" line="55" />
      <module_usage name="psutil._common" finding="relative" line="56" />
      <module_usage name="psutil._common" finding="relative" line="61" />
      <module_usage name="psutil._common" finding="relative" line="62" />
      <module_usage name="psutil._common" finding="relative" line="63" />
      <module_usage name="psutil._common" finding="relative" line="67" />
      <module_usage name="psutil._common" finding="relative" line="68" />
      <module_usage name="psutil._common" finding="relative" line="69" />
      <module_usage name="psutil._common" finding="relative" line="70" />
      <module_usage name="psutil._common" finding="relative" line="71" />
      <module_usage name="psutil._common" finding="relative" line="72" />
      <module_usage name="psutil._common" finding="relative" line="73" />
      <module_usage name="psutil._common" finding="relative" line="74" />
      <module_usage name="psutil._common" finding="relative" line="75" />
      <module_usage name="psutil._common" finding="relative" line="76" />
      <module_usage name="psutil._common" finding="relative" line="77" />
      <module_usage name="psutil._common" finding="relative" line="78" />
      <module_usage name="psutil._common" finding="relative" line="79" />
      <module_usage name="psutil._common" finding="relative" line="80" />
      <module_usage name="psutil._common" finding="relative" line="83" />
      <module_usage name="psutil._common" finding="relative" line="84" />
      <module_usage name="psutil._common" finding="relative" line="85" />
      <module_usage name="psutil._common" finding="relative" line="86" />
      <module_usage name="psutil._common" finding="relative" line="87" />
      <module_usage name="psutil._common" finding="relative" line="88" />
      <module_usage name="psutil._common" finding="relative" line="89" />
      <module_usage name="psutil._common" finding="relative" line="90" />
      <module_usage name="psutil._compat" finding="relative" line="91" />
      <module_usage name="psutil._compat" finding="relative" line="92" />
      <module_usage name="psutil._compat" finding="relative" line="93" />
      <module_usage name="psutil._compat" finding="relative" line="94" />
      <module_usage name="psutil._compat" finding="relative" line="95" />
      <module_usage name="psutil" finding="absolute" line="103" />
      <module_usage name="psutil._pslinux" finding="relative" line="103" />
      <module_usage name="psutil._pslinux" finding="relative" line="104" />
      <module_usage name="psutil._pslinux" finding="relative" line="105" />
      <module_usage name="psutil._pslinux" finding="relative" line="106" />
      <module_usage name="psutil._pslinux" finding="relative" line="107" />
      <module_usage name="psutil" finding="absolute" line="110" />
      <module_usage name="psutil._pswindows" finding="relative" line="110" />
      <module_usage name="psutil._psutil_windows" finding="relative" line="111" />
      <module_usage name="psutil._psutil_windows" finding="relative" line="112" />
      <module_usage name="psutil._psutil_windows" finding="relative" line="113" />
      <module_usage name="psutil._psutil_windows" finding="relative" line="114" />
      <module_usage name="psutil._psutil_windows" finding="relative" line="115" />
      <module_usage name="psutil._psutil_windows" finding="relative" line="116" />
      <module_usage name="psutil._pswindows" finding="relative" line="117" />
      <module_usage name="psutil._pswindows" finding="relative" line="118" />
      <module_usage name="psutil._pswindows" finding="relative" line="119" />
      <module_usage name="psutil._pswindows" finding="relative" line="120" />
      <module_usage name="psutil._pswindows" finding="relative" line="121" />
      <module_usage name="psutil" finding="absolute" line="124" />
      <module_usage name="psutil._psosx" finding="relative" line="124" />
      <module_usage name="psutil" finding="absolute" line="127" />
      <module_usage name="psutil._psbsd" finding="relative" line="127" />
      <module_usage name="psutil" finding="absolute" line="130" />
      <module_usage name="psutil._pssunos" finding="relative" line="130" />
      <module_usage name="psutil._pssunos" finding="relative" line="131" />
      <module_usage name="psutil._pssunos" finding="relative" line="132" />
      <module_usage name="psutil" finding="absolute" line="139" />
      <module_usage name="psutil._psaix" finding="relative" line="139" />
      <module_usage name="psutil" finding="absolute" line="204" />
      <module_usage name="psutil._psutil_posix" finding="not-found" line="204" />
      <module_usage name="ntpath" finding="absolute" line="717" />
      <module_usage name="functools" finding="absolute" line="2123" />
      <module_usage name="functools" finding="absolute" line="2173" />
      <module_usage name="socket" finding="absolute" line="2228" />
      <module_usage name="psutil._common" finding="absolute" line="2401" />
      <module_usage name="psutil._common" finding="relative" line="2408" />
      <module_usage name="psutil._compat" finding="relative" line="2409" />
    </module_usages>
  </module>
  <module name="psutil._common" kind="CompiledPythonModule" usage="import fromlist" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\venv\Lib\site-packages\psutil\_common.py" distribution="psutil">
    <optimization-time pass="1" time="1.09" micro_passes="7" max_branch_merge="34" merged_total="7784" />
    <optimization-time pass="2" time="0.14" micro_passes="1" max_branch_merge="30" merged_total="1029" />
    <module_usages>
      <module_usage name="__future__" finding="absolute" line="10" />
      <module_usage name="__future__" finding="absolute" line="11" />
      <module_usage name="collections" finding="absolute" line="13" />
      <module_usage name="contextlib" finding="absolute" line="14" />
      <module_usage name="errno" finding="absolute" line="15" />
      <module_usage name="functools" finding="absolute" line="16" />
      <module_usage name="os" finding="absolute" line="17" />
      <module_usage name="socket" finding="absolute" line="18" />
      <module_usage name="stat" finding="absolute" line="19" />
      <module_usage name="sys" finding="absolute" line="20" />
      <module_usage name="threading" finding="absolute" line="21" />
      <module_usage name="warnings" finding="absolute" line="22" />
      <module_usage name="collections" finding="absolute" line="23" />
      <module_usage name="collections.namedtuple" finding="not-found" line="23" />
      <module_usage name="socket" finding="absolute" line="24" />
      <module_usage name="socket" finding="absolute" line="25" />
      <module_usage name="socket" finding="absolute" line="26" />
      <module_usage name="socket" finding="absolute" line="30" />
      <module_usage name="socket" finding="absolute" line="34" />
      <module_usage name="enum" finding="absolute" line="42" />
      <module_usage name="ctypes" finding="absolute" line="948" />
      <module_usage name="inspect" finding="absolute" line="981" />
    </module_usages>
  </module>
  <module name="psutil._compat" kind="CompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\venv\Lib\site-packages\psutil\_compat.py" distribution="psutil">
    <optimization-time pass="1" time="0.58" micro_passes="6" max_branch_merge="50" merged_total="3699" />
    <optimization-time pass="2" time="0.09" micro_passes="1" max_branch_merge="31" merged_total="536" />
    <module_usages>
      <module_usage name="collections" finding="absolute" line="10" />
      <module_usage name="contextlib" finding="absolute" line="11" />
      <module_usage name="errno" finding="absolute" line="12" />
      <module_usage name="functools" finding="absolute" line="13" />
      <module_usage name="os" finding="absolute" line="14" />
      <module_usage name="sys" finding="absolute" line="15" />
      <module_usage name="types" finding="absolute" line="16" />
      <module_usage name="functools" finding="absolute" line="217" />
      <module_usage name="threading" finding="absolute" line="220" />
      <module_usage name="dummy_threading" finding="not-found" line="222" />
      <module_usage name="shutil" finding="absolute" line="378" />
      <module_usage name="ntpath" finding="absolute" line="423" />
      <module_usage name="ntpath" finding="absolute" line="427" />
      <module_usage name="shutil" finding="absolute" line="435" />
      <module_usage name="fcntl" finding="not-found" line="440" />
      <module_usage name="struct" finding="absolute" line="441" />
      <module_usage name="termios" finding="not-found" line="442" />
      <module_usage name="subprocess" finding="absolute" line="458" />
      <module_usage name="contextlib" finding="absolute" line="467" />
    </module_usages>
  </module>
  <module name="psutil._psaix" kind="CompiledPythonModule" usage="import fromlist" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\venv\Lib\site-packages\psutil\_psaix.py" distribution="psutil">
    <optimization-time pass="1" time="0.86" micro_passes="7" max_branch_merge="116" merged_total="6219" />
    <optimization-time pass="2" time="0.12" micro_passes="1" max_branch_merge="54" merged_total="797" />
    <module_usages>
      <module_usage name="functools" finding="absolute" line="9" />
      <module_usage name="glob" finding="absolute" line="10" />
      <module_usage name="os" finding="absolute" line="11" />
      <module_usage name="re" finding="absolute" line="12" />
      <module_usage name="subprocess" finding="absolute" line="13" />
      <module_usage name="sys" finding="absolute" line="14" />
      <module_usage name="collections" finding="absolute" line="15" />
      <module_usage name="collections.namedtuple" finding="not-found" line="15" />
      <module_usage name="psutil" finding="absolute" line="17" />
      <module_usage name="psutil._common" finding="relative" line="17" />
      <module_usage name="psutil" finding="absolute" line="18" />
      <module_usage name="psutil._psposix" finding="relative" line="18" />
      <module_usage name="psutil" finding="absolute" line="19" />
      <module_usage name="psutil._psutil_aix" finding="not-found" line="19" />
      <module_usage name="psutil" finding="absolute" line="20" />
      <module_usage name="psutil._psutil_posix" finding="not-found" line="20" />
      <module_usage name="psutil._common" finding="relative" line="21" />
      <module_usage name="psutil._common" finding="relative" line="22" />
      <module_usage name="psutil._common" finding="relative" line="23" />
      <module_usage name="psutil._common" finding="relative" line="24" />
      <module_usage name="psutil._common" finding="relative" line="25" />
      <module_usage name="psutil._common" finding="relative" line="26" />
      <module_usage name="psutil._common" finding="relative" line="27" />
      <module_usage name="psutil._common" finding="relative" line="28" />
      <module_usage name="psutil._common" finding="relative" line="29" />
      <module_usage name="psutil._common" finding="relative" line="30" />
      <module_usage name="psutil._compat" finding="relative" line="31" />
      <module_usage name="psutil._compat" finding="relative" line="32" />
      <module_usage name="psutil._compat" finding="relative" line="33" />
      <module_usage name="psutil._compat" finding="relative" line="34" />
      <module_usage name="ntpath" finding="absolute" line="323" />
      <module_usage name="ntpath" finding="absolute" line="393" />
      <module_usage name="ntpath" finding="absolute" line="395" />
      <module_usage name="ntpath" finding="absolute" line="397" />
      <module_usage name="ntpath" finding="absolute" line="399" />
      <module_usage name="ntpath" finding="absolute" line="408" />
    </module_usages>
  </module>
  <module name="psutil._psbsd" kind="CompiledPythonModule" usage="import fromlist" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\venv\Lib\site-packages\psutil\_psbsd.py" distribution="psutil">
    <optimization-time pass="1" time="1.31" micro_passes="6" max_branch_merge="148" merged_total="8394" />
    <optimization-time pass="2" time="0.17" micro_passes="1" max_branch_merge="69" merged_total="1298" />
    <module_usages>
      <module_usage name="contextlib" finding="absolute" line="7" />
      <module_usage name="errno" finding="absolute" line="8" />
      <module_usage name="functools" finding="absolute" line="9" />
      <module_usage name="os" finding="absolute" line="10" />
      <module_usage name="collections" finding="absolute" line="11" />
      <module_usage name="collections.defaultdict" finding="not-found" line="11" />
      <module_usage name="collections" finding="absolute" line="12" />
      <module_usage name="collections.namedtuple" finding="not-found" line="12" />
      <module_usage name="xml.etree" finding="absolute" line="13" />
      <module_usage name="xml.etree.ElementTree" finding="relative" line="13" />
      <module_usage name="psutil" finding="absolute" line="15" />
      <module_usage name="psutil._common" finding="relative" line="15" />
      <module_usage name="psutil" finding="absolute" line="16" />
      <module_usage name="psutil._psposix" finding="relative" line="16" />
      <module_usage name="psutil" finding="absolute" line="17" />
      <module_usage name="psutil._psutil_bsd" finding="not-found" line="17" />
      <module_usage name="psutil" finding="absolute" line="18" />
      <module_usage name="psutil._psutil_posix" finding="not-found" line="18" />
      <module_usage name="psutil._common" finding="relative" line="19" />
      <module_usage name="psutil._common" finding="relative" line="20" />
      <module_usage name="psutil._common" finding="relative" line="21" />
      <module_usage name="psutil._common" finding="relative" line="22" />
      <module_usage name="psutil._common" finding="relative" line="23" />
      <module_usage name="psutil._common" finding="relative" line="24" />
      <module_usage name="psutil._common" finding="relative" line="25" />
      <module_usage name="psutil._common" finding="relative" line="26" />
      <module_usage name="psutil._common" finding="relative" line="27" />
      <module_usage name="psutil._common" finding="relative" line="28" />
      <module_usage name="psutil._common" finding="relative" line="29" />
      <module_usage name="psutil._common" finding="relative" line="30" />
      <module_usage name="psutil._compat" finding="relative" line="31" />
      <module_usage name="psutil._compat" finding="relative" line="32" />
      <module_usage name="psutil._compat" finding="relative" line="33" />
      <module_usage name="psutil._compat" finding="relative" line="34" />
    </module_usages>
  </module>
  <module name="psutil._pslinux" kind="CompiledPythonModule" usage="import fromlist" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\venv\Lib\site-packages\psutil\_pslinux.py" distribution="psutil">
    <optimization-time pass="1" time="4.16" micro_passes="7" max_branch_merge="215" merged_total="31219" />
    <optimization-time pass="2" time="0.54" micro_passes="1" max_branch_merge="110" merged_total="4267" />
    <module_usages>
      <module_usage name="__future__" finding="absolute" line="7" />
      <module_usage name="base64" finding="absolute" line="9" />
      <module_usage name="collections" finding="absolute" line="10" />
      <module_usage name="errno" finding="absolute" line="11" />
      <module_usage name="functools" finding="absolute" line="12" />
      <module_usage name="glob" finding="absolute" line="13" />
      <module_usage name="os" finding="absolute" line="14" />
      <module_usage name="re" finding="absolute" line="15" />
      <module_usage name="socket" finding="absolute" line="16" />
      <module_usage name="struct" finding="absolute" line="17" />
      <module_usage name="sys" finding="absolute" line="18" />
      <module_usage name="warnings" finding="absolute" line="19" />
      <module_usage name="collections" finding="absolute" line="20" />
      <module_usage name="collections.defaultdict" finding="not-found" line="20" />
      <module_usage name="collections" finding="absolute" line="21" />
      <module_usage name="collections.namedtuple" finding="not-found" line="21" />
      <module_usage name="psutil" finding="absolute" line="23" />
      <module_usage name="psutil._common" finding="relative" line="23" />
      <module_usage name="psutil" finding="absolute" line="24" />
      <module_usage name="psutil._psposix" finding="relative" line="24" />
      <module_usage name="psutil" finding="absolute" line="25" />
      <module_usage name="psutil._psutil_linux" finding="not-found" line="25" />
      <module_usage name="psutil" finding="absolute" line="26" />
      <module_usage name="psutil._psutil_posix" finding="not-found" line="26" />
      <module_usage name="psutil._common" finding="relative" line="27" />
      <module_usage name="psutil._common" finding="relative" line="28" />
      <module_usage name="psutil._common" finding="relative" line="29" />
      <module_usage name="psutil._common" finding="relative" line="30" />
      <module_usage name="psutil._common" finding="relative" line="31" />
      <module_usage name="psutil._common" finding="relative" line="32" />
      <module_usage name="psutil._common" finding="relative" line="33" />
      <module_usage name="psutil._common" finding="relative" line="34" />
      <module_usage name="psutil._common" finding="relative" line="35" />
      <module_usage name="psutil._common" finding="relative" line="36" />
      <module_usage name="psutil._common" finding="relative" line="37" />
      <module_usage name="psutil._common" finding="relative" line="38" />
      <module_usage name="psutil._common" finding="relative" line="39" />
      <module_usage name="psutil._common" finding="relative" line="40" />
      <module_usage name="psutil._common" finding="relative" line="41" />
      <module_usage name="psutil._common" finding="relative" line="42" />
      <module_usage name="psutil._common" finding="relative" line="43" />
      <module_usage name="psutil._common" finding="relative" line="44" />
      <module_usage name="psutil._common" finding="relative" line="45" />
      <module_usage name="psutil._common" finding="relative" line="46" />
      <module_usage name="psutil._compat" finding="relative" line="47" />
      <module_usage name="psutil._compat" finding="relative" line="48" />
      <module_usage name="psutil._compat" finding="relative" line="49" />
      <module_usage name="psutil._compat" finding="relative" line="50" />
      <module_usage name="psutil._compat" finding="relative" line="51" />
      <module_usage name="psutil._compat" finding="relative" line="52" />
      <module_usage name="enum" finding="absolute" line="56" />
      <module_usage name="resource" finding="not-found" line="309" />
      <module_usage name="ctypes" finding="absolute" line="311" />
      <module_usage name="ntpath" finding="absolute" line="769" />
      <module_usage name="ntpath" finding="absolute" line="1206" />
      <module_usage name="ntpath" finding="absolute" line="1209" />
      <module_usage name="ntpath" finding="absolute" line="1351" />
      <module_usage name="ntpath" finding="absolute" line="1353" />
      <module_usage name="ntpath" finding="absolute" line="1415" />
      <module_usage name="ntpath" finding="absolute" line="1452" />
      <module_usage name="ntpath" finding="absolute" line="1454" />
      <module_usage name="ntpath" finding="absolute" line="1468" />
      <module_usage name="ntpath" finding="absolute" line="1472" />
      <module_usage name="ntpath" finding="absolute" line="1476" />
      <module_usage name="ntpath" finding="absolute" line="1519" />
      <module_usage name="ntpath" finding="absolute" line="1558" />
      <module_usage name="ntpath" finding="absolute" line="1583" />
      <module_usage name="ntpath" finding="absolute" line="1584" />
      <module_usage name="ntpath" finding="absolute" line="1851" />
    </module_usages>
  </module>
  <module name="psutil._psosx" kind="CompiledPythonModule" usage="import fromlist" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\venv\Lib\site-packages\psutil\_psosx.py" distribution="psutil">
    <optimization-time pass="1" time="0.61" micro_passes="6" max_branch_merge="114" merged_total="4506" />
    <optimization-time pass="2" time="0.11" micro_passes="1" max_branch_merge="54" merged_total="686" />
    <module_usages>
      <module_usage name="errno" finding="absolute" line="7" />
      <module_usage name="functools" finding="absolute" line="8" />
      <module_usage name="os" finding="absolute" line="9" />
      <module_usage name="collections" finding="absolute" line="10" />
      <module_usage name="collections.namedtuple" finding="not-found" line="10" />
      <module_usage name="psutil" finding="absolute" line="12" />
      <module_usage name="psutil._common" finding="relative" line="12" />
      <module_usage name="psutil" finding="absolute" line="13" />
      <module_usage name="psutil._psposix" finding="relative" line="13" />
      <module_usage name="psutil" finding="absolute" line="14" />
      <module_usage name="psutil._psutil_osx" finding="not-found" line="14" />
      <module_usage name="psutil" finding="absolute" line="15" />
      <module_usage name="psutil._psutil_posix" finding="not-found" line="15" />
      <module_usage name="psutil._common" finding="relative" line="16" />
      <module_usage name="psutil._common" finding="relative" line="17" />
      <module_usage name="psutil._common" finding="relative" line="18" />
      <module_usage name="psutil._common" finding="relative" line="19" />
      <module_usage name="psutil._common" finding="relative" line="20" />
      <module_usage name="psutil._common" finding="relative" line="21" />
      <module_usage name="psutil._common" finding="relative" line="22" />
      <module_usage name="psutil._common" finding="relative" line="23" />
      <module_usage name="psutil._common" finding="relative" line="24" />
      <module_usage name="psutil._compat" finding="relative" line="25" />
      <module_usage name="psutil._compat" finding="relative" line="26" />
      <module_usage name="ntpath" finding="absolute" line="204" />
    </module_usages>
  </module>
  <module name="psutil._psposix" kind="CompiledPythonModule" usage="import fromlist" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\venv\Lib\site-packages\psutil\_psposix.py" distribution="psutil">
    <optimization-time pass="1" time="0.15" micro_passes="5" max_branch_merge="45" merged_total="1028" />
    <optimization-time pass="2" time="0.07" micro_passes="1" max_branch_merge="33" merged_total="188" />
    <module_usages>
      <module_usage name="glob" finding="absolute" line="7" />
      <module_usage name="os" finding="absolute" line="8" />
      <module_usage name="signal" finding="absolute" line="9" />
      <module_usage name="sys" finding="absolute" line="10" />
      <module_usage name="time" finding="absolute" line="11" />
      <module_usage name="psutil._common" finding="relative" line="13" />
      <module_usage name="psutil._common" finding="relative" line="14" />
      <module_usage name="psutil._common" finding="relative" line="15" />
      <module_usage name="psutil._common" finding="relative" line="16" />
      <module_usage name="psutil._common" finding="relative" line="17" />
      <module_usage name="psutil._compat" finding="relative" line="18" />
      <module_usage name="psutil._compat" finding="relative" line="19" />
      <module_usage name="psutil._compat" finding="relative" line="20" />
      <module_usage name="psutil._compat" finding="relative" line="21" />
      <module_usage name="psutil._compat" finding="relative" line="22" />
      <module_usage name="psutil._compat" finding="relative" line="23" />
      <module_usage name="psutil._compat" finding="relative" line="24" />
      <module_usage name="psutil" finding="absolute" line="28" />
      <module_usage name="psutil._psutil_osx" finding="not-found" line="28" />
      <module_usage name="enum" finding="absolute" line="32" />
    </module_usages>
  </module>
  <module name="psutil._pssunos" kind="CompiledPythonModule" usage="import fromlist" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\venv\Lib\site-packages\psutil\_pssunos.py" distribution="psutil">
    <optimization-time pass="1" time="0.99" micro_passes="7" max_branch_merge="136" merged_total="7728" />
    <optimization-time pass="2" time="0.15" micro_passes="1" max_branch_merge="64" merged_total="1011" />
    <module_usages>
      <module_usage name="errno" finding="absolute" line="7" />
      <module_usage name="functools" finding="absolute" line="8" />
      <module_usage name="os" finding="absolute" line="9" />
      <module_usage name="socket" finding="absolute" line="10" />
      <module_usage name="subprocess" finding="absolute" line="11" />
      <module_usage name="sys" finding="absolute" line="12" />
      <module_usage name="collections" finding="absolute" line="13" />
      <module_usage name="collections.namedtuple" finding="not-found" line="13" />
      <module_usage name="socket" finding="absolute" line="14" />
      <module_usage name="psutil" finding="absolute" line="16" />
      <module_usage name="psutil._common" finding="relative" line="16" />
      <module_usage name="psutil" finding="absolute" line="17" />
      <module_usage name="psutil._psposix" finding="relative" line="17" />
      <module_usage name="psutil" finding="absolute" line="18" />
      <module_usage name="psutil._psutil_posix" finding="not-found" line="18" />
      <module_usage name="psutil" finding="absolute" line="19" />
      <module_usage name="psutil._psutil_sunos" finding="not-found" line="19" />
      <module_usage name="psutil._common" finding="relative" line="20" />
      <module_usage name="psutil._common" finding="relative" line="21" />
      <module_usage name="psutil._common" finding="relative" line="22" />
      <module_usage name="psutil._common" finding="relative" line="23" />
      <module_usage name="psutil._common" finding="relative" line="24" />
      <module_usage name="psutil._common" finding="relative" line="25" />
      <module_usage name="psutil._common" finding="relative" line="26" />
      <module_usage name="psutil._common" finding="relative" line="27" />
      <module_usage name="psutil._common" finding="relative" line="28" />
      <module_usage name="psutil._common" finding="relative" line="29" />
      <module_usage name="psutil._common" finding="relative" line="30" />
      <module_usage name="psutil._compat" finding="relative" line="31" />
      <module_usage name="psutil._compat" finding="relative" line="32" />
      <module_usage name="psutil._compat" finding="relative" line="33" />
      <module_usage name="psutil._compat" finding="relative" line="34" />
      <module_usage name="psutil._compat" finding="relative" line="35" />
      <module_usage name="ntpath" finding="absolute" line="620" />
      <module_usage name="ntpath" finding="absolute" line="621" />
    </module_usages>
  </module>
  <module name="psutil._psutil_windows" kind="PythonExtensionModule" usage="import" reason="Extension module needed for standalone mode." source_path="${sys.real_prefix}\shuajishangyong\shuaji\venv\Lib\site-packages\psutil\_psutil_windows.pyd" distribution="psutil">
    <optimization-time pass="1" time="0.00" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="psutil._pswindows" kind="CompiledPythonModule" usage="import fromlist" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\venv\Lib\site-packages\psutil\_pswindows.py" distribution="psutil">
    <optimization-time pass="1" time="1.18" micro_passes="6" max_branch_merge="140" merged_total="9883" />
    <optimization-time pass="2" time="0.19" micro_passes="1" max_branch_merge="67" merged_total="1497" />
    <module_usages>
      <module_usage name="contextlib" finding="absolute" line="7" />
      <module_usage name="errno" finding="absolute" line="8" />
      <module_usage name="functools" finding="absolute" line="9" />
      <module_usage name="os" finding="absolute" line="10" />
      <module_usage name="signal" finding="absolute" line="11" />
      <module_usage name="sys" finding="absolute" line="12" />
      <module_usage name="time" finding="absolute" line="13" />
      <module_usage name="collections" finding="absolute" line="14" />
      <module_usage name="collections.namedtuple" finding="not-found" line="14" />
      <module_usage name="psutil" finding="absolute" line="16" />
      <module_usage name="psutil._common" finding="relative" line="16" />
      <module_usage name="psutil._common" finding="relative" line="17" />
      <module_usage name="psutil._common" finding="relative" line="18" />
      <module_usage name="psutil._common" finding="relative" line="19" />
      <module_usage name="psutil._common" finding="relative" line="20" />
      <module_usage name="psutil._common" finding="relative" line="21" />
      <module_usage name="psutil._common" finding="relative" line="22" />
      <module_usage name="psutil._common" finding="relative" line="23" />
      <module_usage name="psutil._common" finding="relative" line="24" />
      <module_usage name="psutil._common" finding="relative" line="25" />
      <module_usage name="psutil._common" finding="relative" line="26" />
      <module_usage name="psutil._common" finding="relative" line="27" />
      <module_usage name="psutil._common" finding="relative" line="28" />
      <module_usage name="psutil._common" finding="relative" line="29" />
      <module_usage name="psutil._compat" finding="relative" line="30" />
      <module_usage name="psutil._compat" finding="relative" line="31" />
      <module_usage name="psutil._compat" finding="relative" line="32" />
      <module_usage name="psutil._compat" finding="relative" line="33" />
      <module_usage name="psutil._compat" finding="relative" line="34" />
      <module_usage name="psutil._psutil_windows" finding="relative" line="35" />
      <module_usage name="psutil._psutil_windows" finding="relative" line="36" />
      <module_usage name="psutil._psutil_windows" finding="relative" line="37" />
      <module_usage name="psutil._psutil_windows" finding="relative" line="38" />
      <module_usage name="psutil._psutil_windows" finding="relative" line="39" />
      <module_usage name="psutil._psutil_windows" finding="relative" line="40" />
      <module_usage name="psutil" finding="absolute" line="44" />
      <module_usage name="psutil._psutil_windows" finding="relative" line="44" />
      <module_usage name="enum" finding="absolute" line="62" />
      <module_usage name="ntpath" finding="absolute" line="212" />
    </module_usages>
  </module>
  <module name="py_compile" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\py_compile.py">
    <optimization-time pass="1" time="1.22" micro_passes="6" max_branch_merge="79" merged_total="1759" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="enum" finding="absolute" line="6" />
      <module_usage name="importlib._bootstrap_external" finding="absolute" line="7" />
      <module_usage name="importlib.machinery" finding="absolute" line="8" />
      <module_usage name="importlib.util" finding="absolute" line="9" />
      <module_usage name="os" finding="absolute" line="10" />
      <module_usage name="ntpath" finding="absolute" line="11" />
      <module_usage name="sys" finding="absolute" line="12" />
      <module_usage name="traceback" finding="absolute" line="13" />
      <module_usage name="argparse" finding="absolute" line="177" />
    </module_usages>
  </module>
  <module name="pyclbr" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\pyclbr.py">
    <optimization-time pass="1" time="0.32" micro_passes="5" max_branch_merge="59" merged_total="2307" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="ast" finding="absolute" line="44" />
      <module_usage name="sys" finding="absolute" line="45" />
      <module_usage name="importlib.util" finding="absolute" line="46" />
    </module_usages>
  </module>
  <module name="pyexpat" kind="PythonExtensionModule" usage="import" reason="Extension module needed for standalone mode." source_path="${sys.real_prefix}\DLLs\pyexpat.pyd">
    <optimization-time pass="1" time="0.00" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="quopri" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\quopri.py">
    <optimization-time pass="1" time="0.30" micro_passes="7" max_branch_merge="122" merged_total="2821" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="binascii" finding="absolute" line="15" />
      <module_usage name="io" finding="absolute" line="106" />
      <module_usage name="io" finding="absolute" line="158" />
    </module_usages>
  </module>
  <module name="random" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\random.py">
    <optimization-time pass="1" time="1.14" micro_passes="6" max_branch_merge="148" merged_total="6739" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="warnings" finding="absolute" line="53" />
      <module_usage name="math" finding="absolute" line="54" />
      <module_usage name="math" finding="absolute" line="55" />
      <module_usage name="math" finding="absolute" line="56" />
      <module_usage name="math" finding="absolute" line="57" />
      <module_usage name="os" finding="absolute" line="58" />
      <module_usage name="_collections_abc" finding="absolute" line="59" />
      <module_usage name="operator" finding="absolute" line="60" />
      <module_usage name="itertools" finding="absolute" line="61" />
      <module_usage name="bisect" finding="absolute" line="62" />
      <module_usage name="os" finding="absolute" line="63" />
      <module_usage name="_random" finding="absolute" line="64" />
      <module_usage name="_sha2" finding="absolute" line="68" />
      <module_usage name="hashlib" finding="absolute" line="71" />
      <module_usage name="statistics" finding="absolute" line="951" />
      <module_usage name="time" finding="absolute" line="952" />
    </module_usages>
  </module>
  <module name="re" kind="UncompiledPythonPackage" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\re\__init__.py">
    <optimization-time pass="1" time="0.78" micro_passes="5" max_branch_merge="59" merged_total="1646" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="enum" finding="absolute" line="124" />
      <module_usage name="re" finding="absolute" line="125" />
      <module_usage name="re._compiler" finding="relative" line="125" />
      <module_usage name="re._parser" finding="relative" line="125" />
      <module_usage name="functools" finding="absolute" line="126" />
      <module_usage name="_sre" finding="absolute" line="127" />
      <module_usage name="warnings" finding="absolute" line="238" />
      <module_usage name="warnings" finding="absolute" line="301" />
      <module_usage name="copyreg" finding="absolute" line="338" />
      <module_usage name="re._constants" finding="absolute" line="350" />
    </module_usages>
  </module>
  <module name="re._casefix" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\re\_casefix.py">
    <optimization-time pass="1" time="0.01" micro_passes="3" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="re._compiler" kind="UncompiledPythonModule" usage="import fromlist" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\re\_compiler.py">
    <optimization-time pass="1" time="2.10" micro_passes="7" max_branch_merge="419" merged_total="13653" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_sre" finding="absolute" line="13" />
      <module_usage name="re" finding="absolute" line="14" />
      <module_usage name="re._parser" finding="relative" line="14" />
      <module_usage name="re._constants" finding="absolute" line="15" />
      <module_usage name="re._casefix" finding="absolute" line="16" />
      <module_usage name="sys" finding="absolute" line="592" />
    </module_usages>
  </module>
  <module name="re._constants" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\re\_constants.py">
    <optimization-time pass="1" time="0.13" micro_passes="6" max_branch_merge="25" merged_total="860" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_sre" finding="absolute" line="18" />
    </module_usages>
  </module>
  <module name="re._parser" kind="UncompiledPythonModule" usage="import fromlist" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\re\_parser.py">
    <optimization-time pass="1" time="2.23" micro_passes="7" max_branch_merge="727" merged_total="17608" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re._constants" finding="absolute" line="15" />
      <module_usage name="unicodedata" finding="absolute" line="343" />
      <module_usage name="unicodedata" finding="absolute" line="403" />
      <module_usage name="warnings" finding="absolute" line="558" />
      <module_usage name="warnings" finding="absolute" line="576" />
      <module_usage name="warnings" finding="absolute" line="603" />
      <module_usage name="warnings" finding="absolute" line="810" />
      <module_usage name="warnings" finding="absolute" line="1041" />
    </module_usages>
  </module>
  <module name="reprlib" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\reprlib.py">
    <optimization-time pass="1" time="0.19" micro_passes="5" max_branch_merge="38" merged_total="1753" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="builtins" finding="absolute" line="5" />
      <module_usage name="itertools" finding="absolute" line="6" />
      <module_usage name="_thread" finding="absolute" line="7" />
    </module_usages>
  </module>
  <module name="rlcompleter" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\rlcompleter.py">
    <optimization-time pass="1" time="0.34" micro_passes="8" max_branch_merge="39" merged_total="2048" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="atexit" finding="absolute" line="32" />
      <module_usage name="builtins" finding="absolute" line="33" />
      <module_usage name="inspect" finding="absolute" line="34" />
      <module_usage name="keyword" finding="absolute" line="35" />
      <module_usage name="re" finding="absolute" line="36" />
      <module_usage name="__main__" finding="not-found" line="37" />
      <module_usage name="readline" finding="not-found" line="210" />
    </module_usages>
  </module>
  <module name="sched" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\sched.py">
    <optimization-time pass="1" time="0.17" micro_passes="5" max_branch_merge="26" merged_total="1100" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="time" finding="absolute" line="26" />
      <module_usage name="heapq" finding="absolute" line="27" />
      <module_usage name="collections" finding="absolute" line="28" />
      <module_usage name="collections.namedtuple" finding="not-found" line="28" />
      <module_usage name="itertools" finding="absolute" line="29" />
      <module_usage name="threading" finding="absolute" line="30" />
      <module_usage name="time" finding="absolute" line="31" />
    </module_usages>
  </module>
  <module name="select" kind="PythonExtensionModule" usage="import" reason="Extension module needed for standalone mode." source_path="${sys.real_prefix}\DLLs\select.pyd">
    <optimization-time pass="1" time="0.00" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="selectors" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\selectors.py">
    <optimization-time pass="1" time="1.43" micro_passes="5" max_branch_merge="32" merged_total="5444" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="abc" finding="absolute" line="8" />
      <module_usage name="collections" finding="absolute" line="9" />
      <module_usage name="collections.namedtuple" finding="not-found" line="9" />
      <module_usage name="collections.abc" finding="absolute" line="10" />
      <module_usage name="math" finding="absolute" line="11" />
      <module_usage name="select" finding="absolute" line="12" />
      <module_usage name="sys" finding="absolute" line="13" />
    </module_usages>
  </module>
  <module name="shlex" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\shlex.py">
    <optimization-time pass="1" time="0.29" micro_passes="5" max_branch_merge="309" merged_total="3956" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="10" />
      <module_usage name="re" finding="absolute" line="11" />
      <module_usage name="sys" finding="absolute" line="12" />
      <module_usage name="collections" finding="absolute" line="13" />
      <module_usage name="collections.deque" finding="not-found" line="13" />
      <module_usage name="io" finding="absolute" line="15" />
      <module_usage name="ntpath" finding="absolute" line="284" />
      <module_usage name="ntpath" finding="absolute" line="285" />
    </module_usages>
  </module>
  <module name="shutil" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\shutil.py">
    <optimization-time pass="1" time="3.15" micro_passes="7" max_branch_merge="84" merged_total="18059" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="7" />
      <module_usage name="sys" finding="absolute" line="8" />
      <module_usage name="stat" finding="absolute" line="9" />
      <module_usage name="fnmatch" finding="absolute" line="10" />
      <module_usage name="collections" finding="absolute" line="11" />
      <module_usage name="errno" finding="absolute" line="12" />
      <module_usage name="warnings" finding="absolute" line="13" />
      <module_usage name="zlib" finding="absolute" line="16" />
      <module_usage name="bz2" finding="absolute" line="23" />
      <module_usage name="lzma" finding="absolute" line="30" />
      <module_usage name="nt" finding="absolute" line="41" />
      <module_usage name="_winapi" finding="absolute" line="44" />
      <module_usage name="ntpath" finding="absolute" line="208" />
      <module_usage name="ntpath" finding="absolute" line="210" />
      <module_usage name="ntpath" finding="absolute" line="214" />
      <module_usage name="ntpath" finding="absolute" line="216" />
      <module_usage name="ntpath" finding="absolute" line="221" />
      <module_usage name="ntpath" finding="absolute" line="222" />
      <module_usage name="ntpath" finding="absolute" line="228" />
      <module_usage name="ntpath" finding="absolute" line="304" />
      <module_usage name="os" finding="absolute" line="306" />
      <module_usage name="ntpath" finding="absolute" line="312" />
      <module_usage name="ntpath" finding="absolute" line="313" />
      <module_usage name="ntpath" finding="absolute" line="365" />
      <module_usage name="ntpath" finding="absolute" line="392" />
      <module_usage name="ntpath" finding="absolute" line="393" />
      <module_usage name="ntpath" finding="absolute" line="434" />
      <module_usage name="ntpath" finding="absolute" line="451" />
      <module_usage name="ntpath" finding="absolute" line="505" />
      <module_usage name="ntpath" finding="absolute" line="506" />
      <module_usage name="ntpath" finding="absolute" line="616" />
      <module_usage name="ntpath" finding="absolute" line="625" />
      <module_usage name="ntpath" finding="absolute" line="631" />
      <module_usage name="os" finding="absolute" line="667" />
      <module_usage name="ntpath" finding="absolute" line="676" />
      <module_usage name="ntpath" finding="absolute" line="678" />
      <module_usage name="ntpath" finding="absolute" line="689" />
      <module_usage name="os" finding="absolute" line="693" />
      <module_usage name="os" finding="absolute" line="705" />
      <module_usage name="os" finding="absolute" line="708" />
      <module_usage name="os" finding="absolute" line="756" />
      <module_usage name="ntpath" finding="absolute" line="778" />
      <module_usage name="ntpath" finding="absolute" line="802" />
      <module_usage name="ntpath" finding="absolute" line="834" />
      <module_usage name="ntpath" finding="absolute" line="842" />
      <module_usage name="ntpath" finding="absolute" line="849" />
      <module_usage name="ntpath" finding="absolute" line="874" />
      <module_usage name="ntpath" finding="absolute" line="875" />
      <module_usage name="ntpath" finding="absolute" line="876" />
      <module_usage name="ntpath" finding="absolute" line="877" />
      <module_usage name="grp" finding="not-found" line="891" />
      <module_usage name="pwd" finding="not-found" line="909" />
      <module_usage name="tarfile" finding="absolute" line="949" />
      <module_usage name="ntpath" finding="absolute" line="981" />
      <module_usage name="zipfile" finding="absolute" line="998" />
      <module_usage name="ntpath" finding="absolute" line="1018" />
      <module_usage name="ntpath" finding="absolute" line="1027" />
      <module_usage name="ntpath" finding="absolute" line="1030" />
      <module_usage name="ntpath" finding="absolute" line="1031" />
      <module_usage name="ntpath" finding="absolute" line="1036" />
      <module_usage name="ntpath" finding="absolute" line="1039" />
      <module_usage name="zipfile" finding="absolute" line="1235" />
      <module_usage name="ntpath" finding="absolute" line="1249" />
      <module_usage name="tarfile" finding="absolute" line="1265" />
      <module_usage name="ntpath" finding="absolute" line="1501" />
      <module_usage name="ntpath" finding="absolute" line="1549" />
      <module_usage name="ntpath" finding="absolute" line="1559" />
      <module_usage name="ntpath" finding="absolute" line="1563" />
    </module_usages>
  </module>
  <module name="signal" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\signal.py">
    <optimization-time pass="1" time="0.09" micro_passes="5" max_branch_merge="11" merged_total="604" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_signal" finding="absolute" line="1" />
      <module_usage name="_signal" finding="absolute" line="2" />
      <module_usage name="enum" finding="absolute" line="3" />
    </module_usages>
  </module>
  <module name="sndhdr" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\sndhdr.py">
    <optimization-time pass="1" time="0.18" micro_passes="5" max_branch_merge="32" merged_total="1566" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="warnings" finding="absolute" line="30" />
      <module_usage name="collections" finding="absolute" line="39" />
      <module_usage name="collections.namedtuple" finding="not-found" line="39" />
      <module_usage name="aifc" finding="absolute" line="83" />
      <module_usage name="wave" finding="absolute" line="168" />
      <module_usage name="sys" finding="absolute" line="250" />
      <module_usage name="os" finding="absolute" line="251" />
      <module_usage name="glob" finding="absolute" line="257" />
      <module_usage name="ntpath" finding="absolute" line="258" />
    </module_usages>
  </module>
  <module name="socket" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\socket.py">
    <optimization-time pass="1" time="0.70" micro_passes="5" max_branch_merge="52" merged_total="6281" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_socket" finding="absolute" line="52" />
      <module_usage name="_socket" finding="absolute" line="53" />
      <module_usage name="os" finding="absolute" line="55" />
      <module_usage name="sys" finding="absolute" line="55" />
      <module_usage name="io" finding="absolute" line="55" />
      <module_usage name="selectors" finding="absolute" line="55" />
      <module_usage name="enum" finding="absolute" line="56" />
      <module_usage name="errno" finding="absolute" line="59" />
      <module_usage name="array" finding="absolute" line="552" />
      <module_usage name="array" finding="absolute" line="564" />
    </module_usages>
  </module>
  <module name="socketserver" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\socketserver.py">
    <optimization-time pass="1" time="1.07" micro_passes="6" max_branch_merge="31" merged_total="6908" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="socket" finding="absolute" line="126" />
      <module_usage name="selectors" finding="absolute" line="127" />
      <module_usage name="os" finding="absolute" line="128" />
      <module_usage name="sys" finding="absolute" line="129" />
      <module_usage name="threading" finding="absolute" line="130" />
      <module_usage name="io" finding="absolute" line="131" />
      <module_usage name="time" finding="absolute" line="132" />
      <module_usage name="traceback" finding="absolute" line="381" />
      <module_usage name="io" finding="absolute" line="852" />
    </module_usages>
  </module>
  <module name="sre_compile" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\sre_compile.py">
    <optimization-time pass="1" time="0.03" micro_passes="5" max_branch_merge="7" merged_total="122" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="warnings" finding="absolute" line="1" />
      <module_usage name="re" finding="absolute" line="6" />
      <module_usage name="re._compiler" finding="relative" line="6" />
    </module_usages>
  </module>
  <module name="sre_constants" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\sre_constants.py">
    <optimization-time pass="1" time="0.03" micro_passes="5" max_branch_merge="7" merged_total="122" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="warnings" finding="absolute" line="1" />
      <module_usage name="re" finding="absolute" line="6" />
      <module_usage name="re._constants" finding="relative" line="6" />
    </module_usages>
  </module>
  <module name="sre_parse" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\sre_parse.py">
    <optimization-time pass="1" time="0.03" micro_passes="5" max_branch_merge="7" merged_total="122" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="warnings" finding="absolute" line="1" />
      <module_usage name="re" finding="absolute" line="6" />
      <module_usage name="re._parser" finding="relative" line="6" />
    </module_usages>
  </module>
  <module name="stat" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\stat.py">
    <optimization-time pass="1" time="0.17" micro_passes="8" max_branch_merge="9" merged_total="425" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_stat" finding="absolute" line="193" />
    </module_usages>
  </module>
  <module name="statistics" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\statistics.py">
    <optimization-time pass="1" time="1.64" micro_passes="7" max_branch_merge="113" merged_total="11445" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="math" finding="absolute" line="130" />
      <module_usage name="numbers" finding="absolute" line="131" />
      <module_usage name="random" finding="absolute" line="132" />
      <module_usage name="sys" finding="absolute" line="133" />
      <module_usage name="fractions" finding="absolute" line="135" />
      <module_usage name="decimal" finding="absolute" line="136" />
      <module_usage name="itertools" finding="absolute" line="137" />
      <module_usage name="bisect" finding="absolute" line="138" />
      <module_usage name="math" finding="absolute" line="139" />
      <module_usage name="functools" finding="absolute" line="140" />
      <module_usage name="operator" finding="absolute" line="141" />
      <module_usage name="collections" finding="absolute" line="142" />
      <module_usage name="collections.Counter" finding="not-found" line="142" />
      <module_usage name="collections.namedtuple" finding="not-found" line="142" />
      <module_usage name="collections.defaultdict" finding="not-found" line="142" />
      <module_usage name="_statistics" finding="absolute" line="1231" />
    </module_usages>
  </module>
  <module name="string" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\string.py">
    <optimization-time pass="1" time="0.24" micro_passes="5" max_branch_merge="37" merged_total="1941" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_string" finding="absolute" line="21" />
      <module_usage name="re" finding="absolute" line="52" />
      <module_usage name="collections" finding="absolute" line="53" />
      <module_usage name="collections.ChainMap" finding="not-found" line="53" />
    </module_usages>
  </module>
  <module name="stringprep" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\stringprep.py">
    <optimization-time pass="1" time="0.09" micro_passes="5" max_branch_merge="11" merged_total="588" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="unicodedata" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="struct" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\struct.py">
    <optimization-time pass="1" time="0.01" micro_passes="3" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_struct" finding="absolute" line="13" />
      <module_usage name="_struct" finding="absolute" line="14" />
      <module_usage name="_struct" finding="absolute" line="15" />
    </module_usages>
  </module>
  <module name="subprocess" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\subprocess.py">
    <optimization-time pass="1" time="4.05" micro_passes="8" max_branch_merge="172" merged_total="26073" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="builtins" finding="absolute" line="43" />
      <module_usage name="errno" finding="absolute" line="44" />
      <module_usage name="io" finding="absolute" line="45" />
      <module_usage name="locale" finding="absolute" line="46" />
      <module_usage name="os" finding="absolute" line="47" />
      <module_usage name="time" finding="absolute" line="48" />
      <module_usage name="signal" finding="absolute" line="49" />
      <module_usage name="sys" finding="absolute" line="50" />
      <module_usage name="threading" finding="absolute" line="51" />
      <module_usage name="warnings" finding="absolute" line="52" />
      <module_usage name="contextlib" finding="absolute" line="53" />
      <module_usage name="time" finding="absolute" line="54" />
      <module_usage name="types" finding="absolute" line="55" />
      <module_usage name="fcntl" finding="not-found" line="58" />
      <module_usage name="msvcrt" finding="absolute" line="71" />
      <module_usage name="_winapi" finding="absolute" line="81" />
      <module_usage name="_winapi" finding="absolute" line="82" />
      <module_usage name="_posixsubprocess" finding="not-found" line="104" />
      <module_usage name="select" finding="absolute" line="118" />
      <module_usage name="selectors" finding="absolute" line="119" />
      <module_usage name="grp" finding="not-found" line="906" />
      <module_usage name="grp" finding="not-found" line="934" />
      <module_usage name="pwd" finding="not-found" line="962" />
      <module_usage name="ntpath" finding="absolute" line="1521" />
      <module_usage name="ntpath" finding="absolute" line="1522" />
      <module_usage name="ntpath" finding="absolute" line="1524" />
      <module_usage name="ntpath" finding="absolute" line="1881" />
    </module_usages>
  </module>
  <module name="symtable" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\symtable.py">
    <optimization-time pass="1" time="0.38" micro_passes="6" max_branch_merge="39" merged_total="2923" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_symtable" finding="absolute" line="3" />
      <module_usage name="_symtable" finding="absolute" line="4" />
      <module_usage name="weakref" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="sysconfig" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\sysconfig.py">
    <plugin-influence name="anti-bloat" influence="condition-used" condition="not win32" tags_used="win32" result="false" />
    <optimization-time pass="1" time="0.80" micro_passes="8" max_branch_merge="95" merged_total="7275" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="3" />
      <module_usage name="sys" finding="absolute" line="4" />
      <module_usage name="threading" finding="absolute" line="5" />
      <module_usage name="ntpath" finding="absolute" line="6" />
      <module_usage name="ntpath" finding="absolute" line="120" />
      <module_usage name="warnings" finding="absolute" line="226" />
      <module_usage name="ntpath" finding="absolute" line="230" />
      <module_usage name="ntpath" finding="absolute" line="279" />
      <module_usage name="re" finding="absolute" line="327" />
      <module_usage name="ntpath" finding="absolute" line="451" />
      <module_usage name="ntpath" finding="absolute" line="458" />
      <module_usage name="pprint" finding="absolute" line="471" />
      <module_usage name="ntpath" finding="absolute" line="520" />
      <module_usage name="_imp" finding="absolute" line="543" />
      <module_usage name="re" finding="absolute" line="572" />
      <module_usage name="ntpath" finding="absolute" line="601" />
      <module_usage name="ntpath" finding="absolute" line="606" />
      <module_usage name="re" finding="absolute" line="836" />
    </module_usages>
  </module>
  <module name="tarfile" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\tarfile.py">
    <optimization-time pass="1" time="4.87" micro_passes="8" max_branch_merge="138" merged_total="40203" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="sys" finding="absolute" line="40" />
      <module_usage name="os" finding="absolute" line="41" />
      <module_usage name="io" finding="absolute" line="42" />
      <module_usage name="shutil" finding="absolute" line="43" />
      <module_usage name="stat" finding="absolute" line="44" />
      <module_usage name="time" finding="absolute" line="45" />
      <module_usage name="struct" finding="absolute" line="46" />
      <module_usage name="copy" finding="absolute" line="47" />
      <module_usage name="re" finding="absolute" line="48" />
      <module_usage name="warnings" finding="absolute" line="49" />
      <module_usage name="pwd" finding="not-found" line="52" />
      <module_usage name="grp" finding="not-found" line="56" />
      <module_usage name="zlib" finding="absolute" line="370" />
      <module_usage name="bz2" finding="absolute" line="383" />
      <module_usage name="lzma" finding="absolute" line="395" />
      <module_usage name="ntpath" finding="absolute" line="758" />
      <module_usage name="ntpath" finding="absolute" line="763" />
      <module_usage name="ntpath" finding="absolute" line="768" />
      <module_usage name="ntpath" finding="absolute" line="769" />
      <module_usage name="ntpath" finding="absolute" line="804" />
      <module_usage name="ntpath" finding="absolute" line="807" />
      <module_usage name="ntpath" finding="absolute" line="811" />
      <module_usage name="ntpath" finding="absolute" line="813" />
      <module_usage name="ntpath" finding="absolute" line="814" />
      <module_usage name="gzip" finding="absolute" line="1898" />
      <module_usage name="bz2" finding="absolute" line="1931" />
      <module_usage name="lzma" finding="absolute" line="1959" />
      <module_usage name="ntpath" finding="absolute" line="2058" />
      <module_usage name="ntpath" finding="absolute" line="2219" />
      <module_usage name="ntpath" finding="absolute" line="2310" />
      <module_usage name="ntpath" finding="absolute" line="2357" />
      <module_usage name="ntpath" finding="absolute" line="2365" />
      <module_usage name="ntpath" finding="absolute" line="2546" />
      <module_usage name="zlib" finding="absolute" line="2666" />
    </module_usages>
  </module>
  <module name="tempfile" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\tempfile.py">
    <optimization-time pass="1" time="1.43" micro_passes="6" max_branch_merge="65" merged_total="5786" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="functools" finding="absolute" line="39" />
      <module_usage name="warnings" finding="absolute" line="40" />
      <module_usage name="io" finding="absolute" line="41" />
      <module_usage name="os" finding="absolute" line="42" />
      <module_usage name="shutil" finding="absolute" line="43" />
      <module_usage name="errno" finding="absolute" line="44" />
      <module_usage name="random" finding="absolute" line="45" />
      <module_usage name="sys" finding="absolute" line="46" />
      <module_usage name="types" finding="absolute" line="47" />
      <module_usage name="weakref" finding="absolute" line="48" />
      <module_usage name="_thread" finding="absolute" line="49" />
      <module_usage name="ntpath" finding="absolute" line="169" />
      <module_usage name="ntpath" finding="absolute" line="170" />
      <module_usage name="ntpath" finding="absolute" line="201" />
      <module_usage name="ntpath" finding="absolute" line="253" />
      <module_usage name="ntpath" finding="absolute" line="381" />
      <module_usage name="ntpath" finding="absolute" line="424" />
      <module_usage name="io" finding="absolute" line="696" />
      <module_usage name="io" finding="absolute" line="699" />
      <module_usage name="ntpath" finding="absolute" line="916" />
    </module_usages>
  </module>
  <module name="textwrap" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\textwrap.py">
    <optimization-time pass="1" time="0.26" micro_passes="5" max_branch_merge="203" merged_total="2658" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="8" />
    </module_usages>
  </module>
  <module name="threading" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\threading.py">
    <optimization-time pass="1" time="1.45" micro_passes="7" max_branch_merge="62" merged_total="12698" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="3" />
      <module_usage name="sys" finding="absolute" line="4" />
      <module_usage name="_thread" finding="absolute" line="5" />
      <module_usage name="functools" finding="absolute" line="6" />
      <module_usage name="time" finding="absolute" line="8" />
      <module_usage name="_weakrefset" finding="absolute" line="9" />
      <module_usage name="itertools" finding="absolute" line="10" />
      <module_usage name="_collections" finding="absolute" line="12" />
      <module_usage name="collections" finding="absolute" line="14" />
      <module_usage name="collections.deque" finding="not-found" line="14" />
      <module_usage name="warnings" finding="absolute" line="439" />
      <module_usage name="warnings" finding="absolute" line="611" />
      <module_usage name="warnings" finding="absolute" line="1267" />
      <module_usage name="warnings" finding="absolute" line="1278" />
      <module_usage name="warnings" finding="absolute" line="1289" />
      <module_usage name="warnings" finding="absolute" line="1300" />
      <module_usage name="_thread" finding="absolute" line="1307" />
      <module_usage name="traceback" finding="absolute" line="1311" />
      <module_usage name="collections" finding="absolute" line="1312" />
      <module_usage name="collections.namedtuple" finding="not-found" line="1312" />
      <module_usage name="warnings" finding="absolute" line="1503" />
      <module_usage name="warnings" finding="absolute" line="1526" />
      <module_usage name="functools" finding="absolute" line="1563" />
      <module_usage name="_thread" finding="absolute" line="1567" />
      <module_usage name="_thread" finding="absolute" line="1644" />
      <module_usage name="_threading_local" finding="absolute" line="1646" />
    </module_usages>
  </module>
  <module name="timeit" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\timeit.py">
    <optimization-time pass="1" time="0.36" micro_passes="7" max_branch_merge="63" merged_total="2673" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="gc" finding="absolute" line="52" />
      <module_usage name="itertools" finding="absolute" line="53" />
      <module_usage name="sys" finding="absolute" line="54" />
      <module_usage name="time" finding="absolute" line="55" />
      <module_usage name="linecache" finding="absolute" line="156" />
      <module_usage name="traceback" finding="absolute" line="156" />
      <module_usage name="getopt" finding="absolute" line="265" />
      <module_usage name="os" finding="absolute" line="314" />
      <module_usage name="warnings" finding="absolute" line="371" />
    </module_usages>
  </module>
  <module name="token" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\token.py">
    <optimization-time pass="1" time="0.08" micro_passes="5" max_branch_merge="10" merged_total="162" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="tokenize" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\tokenize.py">
    <optimization-time pass="1" time="1.15" micro_passes="9" max_branch_merge="94" merged_total="7455" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="codecs" finding="absolute" line="28" />
      <module_usage name="collections" finding="absolute" line="29" />
      <module_usage name="functools" finding="absolute" line="30" />
      <module_usage name="io" finding="absolute" line="31" />
      <module_usage name="itertools" finding="absolute" line="32" />
      <module_usage name="re" finding="absolute" line="33" />
      <module_usage name="sys" finding="absolute" line="34" />
      <module_usage name="token" finding="absolute" line="35" />
      <module_usage name="token" finding="absolute" line="36" />
      <module_usage name="_tokenize" finding="absolute" line="37" />
      <module_usage name="token" finding="absolute" line="42" />
    </module_usages>
  </module>
  <module name="tomllib" kind="UncompiledPythonPackage" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\tomllib\__init__.py">
    <optimization-time pass="1" time="0.02" micro_passes="5" max_branch_merge="3" merged_total="15" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="tomllib._parser" finding="absolute" line="7" />
    </module_usages>
  </module>
  <module name="tomllib._parser" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\tomllib\_parser.py">
    <optimization-time pass="1" time="0.79" micro_passes="6" max_branch_merge="78" merged_total="7613" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="__future__" finding="absolute" line="5" />
      <module_usage name="collections.abc" finding="absolute" line="7" />
      <module_usage name="string" finding="absolute" line="8" />
      <module_usage name="types" finding="absolute" line="9" />
      <module_usage name="typing" finding="absolute" line="10" />
      <module_usage name="tomllib._re" finding="absolute" line="12" />
      <module_usage name="tomllib._types" finding="absolute" line="20" />
    </module_usages>
  </module>
  <module name="tomllib._re" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\tomllib\_re.py">
    <optimization-time pass="1" time="0.08" micro_passes="4" max_branch_merge="21" merged_total="465" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="__future__" finding="absolute" line="5" />
      <module_usage name="datetime" finding="absolute" line="7" />
      <module_usage name="functools" finding="absolute" line="8" />
      <module_usage name="re" finding="absolute" line="9" />
      <module_usage name="typing" finding="absolute" line="10" />
      <module_usage name="tomllib._types" finding="absolute" line="12" />
    </module_usages>
  </module>
  <module name="tomllib._types" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\tomllib\_types.py">
    <optimization-time pass="1" time="0.01" micro_passes="4" max_branch_merge="3" merged_total="12" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="typing" finding="absolute" line="5" />
    </module_usages>
  </module>
  <module name="trace" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\trace.py">
    <optimization-time pass="1" time="0.94" micro_passes="6" max_branch_merge="53" merged_total="6918" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="io" finding="absolute" line="52" />
      <module_usage name="linecache" finding="absolute" line="53" />
      <module_usage name="os" finding="absolute" line="54" />
      <module_usage name="sys" finding="absolute" line="55" />
      <module_usage name="sysconfig" finding="absolute" line="56" />
      <module_usage name="token" finding="absolute" line="57" />
      <module_usage name="tokenize" finding="absolute" line="58" />
      <module_usage name="inspect" finding="absolute" line="59" />
      <module_usage name="gc" finding="absolute" line="60" />
      <module_usage name="dis" finding="absolute" line="61" />
      <module_usage name="pickle" finding="absolute" line="62" />
      <module_usage name="time" finding="absolute" line="63" />
      <module_usage name="threading" finding="absolute" line="65" />
      <module_usage name="ntpath" finding="absolute" line="123" />
      <module_usage name="ntpath" finding="absolute" line="134" />
      <module_usage name="ntpath" finding="absolute" line="137" />
      <module_usage name="ntpath" finding="absolute" line="147" />
      <module_usage name="ntpath" finding="absolute" line="151" />
      <module_usage name="ntpath" finding="absolute" line="271" />
      <module_usage name="__main__" finding="not-found" line="439" />
    </module_usages>
  </module>
  <module name="traceback" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\traceback.py">
    <optimization-time pass="1" time="1.42" micro_passes="7" max_branch_merge="168" merged_total="13207" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="collections.abc" finding="absolute" line="3" />
      <module_usage name="itertools" finding="absolute" line="4" />
      <module_usage name="linecache" finding="absolute" line="5" />
      <module_usage name="sys" finding="absolute" line="6" />
      <module_usage name="textwrap" finding="absolute" line="7" />
      <module_usage name="contextlib" finding="absolute" line="8" />
      <module_usage name="ast" finding="absolute" line="590" />
      <module_usage name="unicodedata" finding="absolute" line="647" />
    </module_usages>
  </module>
  <module name="tracemalloc" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\tracemalloc.py">
    <optimization-time pass="1" time="0.99" micro_passes="7" max_branch_merge="28" merged_total="6954" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="collections.abc" finding="absolute" line="1" />
      <module_usage name="functools" finding="absolute" line="2" />
      <module_usage name="fnmatch" finding="absolute" line="3" />
      <module_usage name="linecache" finding="absolute" line="4" />
      <module_usage name="ntpath" finding="absolute" line="5" />
      <module_usage name="pickle" finding="absolute" line="6" />
      <module_usage name="_tracemalloc" finding="absolute" line="9" />
      <module_usage name="_tracemalloc" finding="absolute" line="10" />
    </module_usages>
  </module>
  <module name="types" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\types.py">
    <optimization-time pass="1" time="0.27" micro_passes="6" max_branch_merge="37" merged_total="1839" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="sys" finding="absolute" line="4" />
      <module_usage name="functools" finding="absolute" line="305" />
      <module_usage name="_collections_abc" finding="absolute" line="306" />
    </module_usages>
  </module>
  <module name="typing" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\typing.py">
    <optimization-time pass="1" time="7.81" micro_passes="7" max_branch_merge="148" merged_total="34738" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="abc" finding="absolute" line="22" />
      <module_usage name="collections" finding="absolute" line="23" />
      <module_usage name="collections" finding="absolute" line="24" />
      <module_usage name="collections.defaultdict" finding="not-found" line="24" />
      <module_usage name="collections.abc" finding="absolute" line="25" />
      <module_usage name="copyreg" finding="absolute" line="26" />
      <module_usage name="contextlib" finding="absolute" line="27" />
      <module_usage name="functools" finding="absolute" line="28" />
      <module_usage name="operator" finding="absolute" line="29" />
      <module_usage name="re" finding="absolute" line="30" />
      <module_usage name="sys" finding="absolute" line="31" />
      <module_usage name="types" finding="absolute" line="32" />
      <module_usage name="warnings" finding="absolute" line="33" />
      <module_usage name="types" finding="absolute" line="34" />
      <module_usage name="_typing" finding="absolute" line="36" />
      <module_usage name="warnings" finding="absolute" line="1517" />
      <module_usage name="inspect" finding="absolute" line="1828" />
      <module_usage name="functools" finding="absolute" line="2502" />
    </module_usages>
  </module>
  <module name="unicodedata" kind="PythonExtensionModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\DLLs\unicodedata.pyd">
    <optimization-time pass="1" time="0.00" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="urllib" kind="UncompiledPythonPackage" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\urllib\__init__.py">
    <optimization-time pass="1" time="0.01" micro_passes="3" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
    </module_usages>
  </module>
  <module name="urllib.parse" kind="UncompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\urllib\parse.py">
    <optimization-time pass="1" time="2.26" micro_passes="7" max_branch_merge="69" merged_total="14475" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="collections" finding="absolute" line="34" />
      <module_usage name="collections.namedtuple" finding="not-found" line="34" />
      <module_usage name="functools" finding="absolute" line="35" />
      <module_usage name="math" finding="absolute" line="36" />
      <module_usage name="re" finding="absolute" line="37" />
      <module_usage name="types" finding="absolute" line="38" />
      <module_usage name="warnings" finding="absolute" line="39" />
      <module_usage name="ipaddress" finding="absolute" line="40" />
      <module_usage name="unicodedata" finding="absolute" line="426" />
    </module_usages>
  </module>
  <module name="utils" kind="CompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\utils.py">
    <optimization-time pass="1" time="0.40" micro_passes="6" max_branch_merge="48" merged_total="2571" />
    <optimization-time pass="2" time="0.04" micro_passes="1" max_branch_merge="31" merged_total="385" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="sys" finding="absolute" line="2" />
      <module_usage name="platform" finding="absolute" line="3" />
      <module_usage name="subprocess" finding="absolute" line="4" />
      <module_usage name="time" finding="absolute" line="5" />
      <module_usage name="shutil" finding="absolute" line="6" />
      <module_usage name="ntpath" finding="absolute" line="18" />
      <module_usage name="ntpath" finding="absolute" line="19" />
      <module_usage name="ntpath" finding="absolute" line="34" />
      <module_usage name="ntpath" finding="absolute" line="37" />
      <module_usage name="ntpath" finding="absolute" line="38" />
    </module_usages>
  </module>
  <module name="uu" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\uu.py">
    <optimization-time pass="1" time="0.18" micro_passes="6" max_branch_merge="98" merged_total="1740" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="binascii" finding="absolute" line="33" />
      <module_usage name="os" finding="absolute" line="34" />
      <module_usage name="sys" finding="absolute" line="35" />
      <module_usage name="warnings" finding="absolute" line="36" />
    </module_usages>
  </module>
  <module name="warnings" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\warnings.py">
    <optimization-time pass="1" time="1.35" micro_passes="6" max_branch_merge="85" merged_total="5574" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="sys" finding="absolute" line="3" />
      <module_usage name="linecache" finding="absolute" line="43" />
      <module_usage name="tracemalloc" finding="absolute" line="58" />
      <module_usage name="re" finding="absolute" line="155" />
      <module_usage name="re" finding="absolute" line="227" />
      <module_usage name="builtins" finding="absolute" line="258" />
      <module_usage name="linecache" finding="absolute" line="384" />
      <module_usage name="linecache" finding="absolute" line="541" />
      <module_usage name="traceback" finding="absolute" line="541" />
      <module_usage name="_warnings" finding="absolute" line="567" />
    </module_usages>
  </module>
  <module name="weakref" kind="UncompiledPythonModule" usage="import" reason="Technically required for CPython library startup." source_path="${sys.real_prefix}\Lib\weakref.py">
    <optimization-time pass="1" time="1.08" micro_passes="6" max_branch_merge="39" merged_total="8625" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="_weakref" finding="absolute" line="12" />
      <module_usage name="_weakrefset" finding="absolute" line="22" />
      <module_usage name="_collections_abc" finding="absolute" line="24" />
      <module_usage name="sys" finding="absolute" line="25" />
      <module_usage name="itertools" finding="absolute" line="26" />
      <module_usage name="copy" finding="absolute" line="183" />
      <module_usage name="copy" finding="absolute" line="442" />
      <module_usage name="atexit" finding="absolute" line="572" />
      <module_usage name="gc" finding="absolute" line="649" />
    </module_usages>
  </module>
  <module name="webbrowser" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\webbrowser.py">
    <optimization-time pass="1" time="0.85" micro_passes="6" max_branch_merge="123" merged_total="7235" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="5" />
      <module_usage name="shlex" finding="absolute" line="6" />
      <module_usage name="shutil" finding="absolute" line="7" />
      <module_usage name="sys" finding="absolute" line="8" />
      <module_usage name="subprocess" finding="absolute" line="9" />
      <module_usage name="threading" finding="absolute" line="10" />
      <module_usage name="warnings" finding="absolute" line="11" />
      <module_usage name="copy" finding="absolute" line="132" />
      <module_usage name="ntpath" finding="absolute" line="489" />
      <module_usage name="ntpath" finding="absolute" line="492" />
      <module_usage name="getopt" finding="absolute" line="661" />
    </module_usages>
  </module>
  <module name="win32security" kind="PythonExtensionModule" usage="import" reason="Extension module needed for standalone mode." source_path="${sys.real_prefix}\shuajishangyong\shuaji\venv\Lib\site-packages\win32\win32security.pyd" distribution="pywin32">
    <optimization-time pass="1" time="0.00" />
    <optimization-time pass="2" time="0.00" />
    <module_usages />
  </module>
  <module name="xdrlib" kind="UncompiledPythonModule" usage="stdlib" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\xdrlib.py">
    <optimization-time pass="1" time="0.28" micro_passes="6" max_branch_merge="36" merged_total="2210" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="struct" finding="absolute" line="7" />
      <module_usage name="io" finding="absolute" line="8" />
      <module_usage name="functools" finding="absolute" line="9" />
      <module_usage name="warnings" finding="absolute" line="10" />
    </module_usages>
  </module>
  <module name="xml" kind="UncompiledPythonPackage" usage="import path parent" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\xml\__init__.py">
    <optimization-time pass="1" time="0.01" micro_passes="3" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
    </module_usages>
  </module>
  <module name="xml.etree" kind="UncompiledPythonPackage" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\xml\etree\__init__.py">
    <optimization-time pass="1" time="0.01" micro_passes="3" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
    </module_usages>
  </module>
  <module name="xml.etree.ElementPath" kind="UncompiledPythonModule" usage="import fromlist" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\xml\etree\ElementPath.py">
    <optimization-time pass="1" time="0.69" micro_passes="7" max_branch_merge="75" merged_total="5207" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="59" />
    </module_usages>
  </module>
  <module name="xml.etree.ElementTree" kind="UncompiledPythonModule" usage="import fromlist" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\xml\etree\ElementTree.py">
    <optimization-time pass="1" time="2.29" micro_passes="7" max_branch_merge="70" merged_total="19993" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="sys" finding="absolute" line="95" />
      <module_usage name="re" finding="absolute" line="96" />
      <module_usage name="warnings" finding="absolute" line="97" />
      <module_usage name="io" finding="absolute" line="98" />
      <module_usage name="collections" finding="absolute" line="99" />
      <module_usage name="collections.abc" finding="absolute" line="100" />
      <module_usage name="contextlib" finding="absolute" line="101" />
      <module_usage name="weakref" finding="absolute" line="102" />
      <module_usage name="xml.etree" finding="absolute" line="104" />
      <module_usage name="xml.etree.ElementPath" finding="relative" line="104" />
      <module_usage name="io" finding="absolute" line="1083" />
      <module_usage name="xml.parsers" finding="absolute" line="1515" />
      <module_usage name="xml.parsers.expat" finding="relative" line="1515" />
      <module_usage name="pyexpat" finding="absolute" line="1518" />
      <module_usage name="xml.parsers" finding="absolute" line="1660" />
      <module_usage name="xml.parsers.expat" finding="relative" line="1660" />
      <module_usage name="io" finding="absolute" line="1757" />
      <module_usage name="_elementtree" finding="absolute" line="2080" />
      <module_usage name="_elementtree" finding="absolute" line="2081" />
    </module_usages>
  </module>
  <module name="xml.parsers" kind="UncompiledPythonPackage" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\xml\parsers\__init__.py">
    <optimization-time pass="1" time="0.01" micro_passes="3" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
    </module_usages>
  </module>
  <module name="xml.parsers.expat" kind="UncompiledPythonModule" usage="import fromlist" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\Lib\xml\parsers\expat.py">
    <optimization-time pass="1" time="0.01" micro_passes="3" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="sys" finding="absolute" line="2" />
      <module_usage name="pyexpat" finding="absolute" line="4" />
    </module_usages>
  </module>
  <module name="zhidinyishuaxie" kind="CompiledPythonModule" usage="import" reason="Instructed by user to follow to all modules." source_path="${sys.real_prefix}\shuajishangyong\shuaji\zhidinyishuaxie.py">
    <optimization-time pass="1" time="0.39" micro_passes="7" max_branch_merge="182" merged_total="4450" />
    <optimization-time pass="2" time="0.05" micro_passes="1" max_branch_merge="157" merged_total="586" />
    <distribution-usages>
      <distribution-usage name="PyQt6" />
      <distribution-usage name="PyQt6-Qt6" />
      <distribution-usage name="PyQt6_sip" />
    </distribution-usages>
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="subprocess" finding="absolute" line="2" />
      <module_usage name="sys" finding="absolute" line="3" />
      <module_usage name="threading" finding="absolute" line="4" />
      <module_usage name="random" finding="absolute" line="5" />
      <module_usage name="PyQt6.QtWidgets" finding="absolute" line="6" />
      <module_usage name="PyQt6.QtCore" finding="absolute" line="8" />
      <module_usage name="utils" finding="absolute" line="9" />
      <module_usage name="custom_messagebox" finding="absolute" line="10" />
      <module_usage name="ntpath" finding="absolute" line="317" />
    </module_usages>
  </module>
  <module name="zipfile" kind="UncompiledPythonPackage" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\zipfile\__init__.py">
    <optimization-time pass="1" time="2.70" micro_passes="6" max_branch_merge="179" merged_total="24109" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="binascii" finding="absolute" line="6" />
      <module_usage name="importlib.util" finding="absolute" line="7" />
      <module_usage name="io" finding="absolute" line="8" />
      <module_usage name="os" finding="absolute" line="9" />
      <module_usage name="shutil" finding="absolute" line="10" />
      <module_usage name="stat" finding="absolute" line="11" />
      <module_usage name="struct" finding="absolute" line="12" />
      <module_usage name="sys" finding="absolute" line="13" />
      <module_usage name="threading" finding="absolute" line="14" />
      <module_usage name="time" finding="absolute" line="15" />
      <module_usage name="zlib" finding="absolute" line="18" />
      <module_usage name="bz2" finding="absolute" line="25" />
      <module_usage name="lzma" finding="absolute" line="30" />
      <module_usage name="warnings" finding="absolute" line="536" />
      <module_usage name="ntpath" finding="absolute" line="568" />
      <module_usage name="ntpath" finding="absolute" line="591" />
      <module_usage name="ntpath" finding="absolute" line="592" />
      <module_usage name="io" finding="absolute" line="1438" />
      <module_usage name="warnings" finding="absolute" line="1557" />
      <module_usage name="ntpath" finding="absolute" line="1770" />
      <module_usage name="ntpath" finding="absolute" line="1772" />
      <module_usage name="ntpath" finding="absolute" line="1773" />
      <module_usage name="ntpath" finding="absolute" line="1776" />
      <module_usage name="ntpath" finding="absolute" line="1777" />
      <module_usage name="ntpath" finding="absolute" line="1778" />
      <module_usage name="ntpath" finding="absolute" line="1780" />
      <module_usage name="ntpath" finding="absolute" line="1782" />
      <module_usage name="ntpath" finding="absolute" line="1787" />
      <module_usage name="warnings" finding="absolute" line="1809" />
      <module_usage name="ntpath" finding="absolute" line="2094" />
      <module_usage name="ntpath" finding="absolute" line="2096" />
      <module_usage name="ntpath" finding="absolute" line="2113" />
      <module_usage name="ntpath" finding="absolute" line="2114" />
      <module_usage name="ntpath" finding="absolute" line="2116" />
      <module_usage name="ntpath" finding="absolute" line="2135" />
      <module_usage name="ntpath" finding="absolute" line="2136" />
      <module_usage name="py_compile" finding="absolute" line="2164" />
      <module_usage name="ntpath" finding="absolute" line="2233" />
      <module_usage name="zipfile._path" finding="absolute" line="2309" />
    </module_usages>
  </module>
  <module name="zipfile._path" kind="UncompiledPythonPackage" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\zipfile\_path\__init__.py">
    <optimization-time pass="1" time="0.33" micro_passes="5" max_branch_merge="51" merged_total="2615" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="os" finding="absolute" line="1" />
      <module_usage name="io" finding="absolute" line="10" />
      <module_usage name="posixpath" finding="absolute" line="11" />
      <module_usage name="zipfile" finding="absolute" line="12" />
      <module_usage name="itertools" finding="absolute" line="13" />
      <module_usage name="contextlib" finding="absolute" line="14" />
      <module_usage name="pathlib" finding="absolute" line="15" />
      <module_usage name="re" finding="absolute" line="16" />
      <module_usage name="zipfile._path.glob" finding="absolute" line="18" />
    </module_usages>
  </module>
  <module name="zipfile._path.glob" kind="UncompiledPythonModule" usage="import" reason="Including as part of the non-excluded parts of standard library." source_path="${sys.real_prefix}\Lib\zipfile\_path\glob.py">
    <optimization-time pass="1" time="0.02" micro_passes="4" max_branch_merge="14" merged_total="82" />
    <optimization-time pass="2" time="0.00" />
    <module_usages>
      <module_usage name="re" finding="absolute" line="1" />
    </module_usages>
  </module>
  <performance>
    <memory_usage name="after_launch" value="56631296" />
    <memory_usage name="optimization/1/PyQt6.QtWidgets" value="58621952" />
    <memory_usage name="optimization/1/datetime" value="70148096" />
    <memory_usage name="optimization/1/_pydatetime" value="75821056" />
    <memory_usage name="optimization/1/_strptime" value="75104256" />
    <memory_usage name="optimization/1/re" value="77291520" />
    <memory_usage name="optimization/1/re._constants" value="75108352" />
    <memory_usage name="optimization/1/copyreg" value="75108352" />
    <memory_usage name="optimization/1/functools" value="75108352" />
    <memory_usage name="optimization/1/typing" value="77414400" />
    <memory_usage name="optimization/1/inspect" value="79904768" />
    <memory_usage name="optimization/1/ntpath" value="82894848" />
    <memory_usage name="optimization/1/string" value="82894848" />
    <memory_usage name="optimization/1/genericpath" value="82894848" />
    <memory_usage name="optimization/1/stat" value="82894848" />
    <memory_usage name="optimization/1/keyword" value="82894848" />
    <memory_usage name="optimization/1/token" value="82894848" />
    <memory_usage name="optimization/1/tokenize" value="82894848" />
    <memory_usage name="optimization/1/io" value="82894848" />
    <memory_usage name="optimization/1/codecs" value="82894848" />
    <memory_usage name="optimization/1/linecache" value="82894848" />
    <memory_usage name="optimization/1/importlib.machinery" value="82894848" />
    <memory_usage name="optimization/1/importlib._bootstrap_external" value="84082688" />
    <memory_usage name="optimization/1/importlib.metadata" value="84082688" />
    <memory_usage name="optimization/1/importlib.abc" value="81661952" />
    <memory_usage name="optimization/1/importlib.resources.abc" value="81661952" />
    <memory_usage name="optimization/1/importlib.resources" value="81661952" />
    <memory_usage name="optimization/1/importlib.resources._legacy" value="81661952" />
    <memory_usage name="optimization/1/importlib.resources._common" value="81661952" />
    <memory_usage name="optimization/1/importlib.resources._adapters" value="82788352" />
    <memory_usage name="optimization/1/tempfile" value="82788352" />
    <memory_usage name="optimization/1/random" value="84369408" />
    <memory_usage name="optimization/1/statistics" value="84729856" />
    <memory_usage name="optimization/1/decimal" value="84754432" />
    <memory_usage name="optimization/1/_pydecimal" value="95875072" />
    <memory_usage name="optimization/1/contextvars" value="95875072" />
    <memory_usage name="optimization/1/_decimal" value="95875072" />
    <memory_usage name="optimization/1/fractions" value="95875072" />
    <memory_usage name="optimization/1/numbers" value="95875072" />
    <memory_usage name="optimization/1/hashlib" value="95875072" />
    <memory_usage name="optimization/1/logging" value="93024256" />
    <memory_usage name="optimization/1/pickle" value="89505792" />
    <memory_usage name="optimization/1/_compat_pickle" value="89825280" />
    <memory_usage name="optimization/1/struct" value="89825280" />
    <memory_usage name="optimization/1/threading" value="89825280" />
    <memory_usage name="optimization/1/_threading_local" value="89870336" />
    <memory_usage name="optimization/1/_weakrefset" value="89870336" />
    <memory_usage name="optimization/1/traceback" value="89870336" />
    <memory_usage name="optimization/1/unicodedata" value="89870336" />
    <memory_usage name="optimization/1/_hashlib" value="89870336" />
    <memory_usage name="optimization/1/bisect" value="89870336" />
    <memory_usage name="optimization/1/_collections_abc" value="89870336" />
    <memory_usage name="optimization/1/shutil" value="89870336" />
    <memory_usage name="optimization/1/tarfile" value="91586560" />
    <memory_usage name="optimization/1/gzip" value="92123136" />
    <memory_usage name="optimization/1/_compression" value="92123136" />
    <memory_usage name="optimization/1/copy" value="92123136" />
    <memory_usage name="optimization/1/lzma" value="92123136" />
    <memory_usage name="optimization/1/_lzma" value="92123136" />
    <memory_usage name="optimization/1/bz2" value="92123136" />
    <memory_usage name="optimization/1/_bz2" value="92123136" />
    <memory_usage name="optimization/1/fnmatch" value="92123136" />
    <memory_usage name="optimization/1/importlib._abc" value="92123136" />
    <memory_usage name="optimization/1/importlib.metadata._itertools" value="92123136" />
    <memory_usage name="optimization/1/importlib.metadata._functools" value="92123136" />
    <memory_usage name="optimization/1/importlib.metadata._collections" value="92123136" />
    <memory_usage name="optimization/1/importlib.metadata._meta" value="92123136" />
    <memory_usage name="optimization/1/importlib.metadata._adapters" value="92123136" />
    <memory_usage name="optimization/1/importlib.metadata._text" value="93921280" />
    <memory_usage name="optimization/1/email.message" value="93921280" />
    <memory_usage name="optimization/1/email.policy" value="94797824" />
    <memory_usage name="optimization/1/email.contentmanager" value="94797824" />
    <memory_usage name="optimization/1/email.quoprimime" value="94797824" />
    <memory_usage name="optimization/1/email.headerregistry" value="94797824" />
    <memory_usage name="optimization/1/email._header_value_parser" value="97533952" />
    <memory_usage name="optimization/1/urllib" value="97533952" />
    <memory_usage name="optimization/1/email.iterators" value="97533952" />
    <memory_usage name="optimization/1/email.generator" value="97533952" />
    <memory_usage name="optimization/1/email._encoded_words" value="97533952" />
    <memory_usage name="optimization/1/base64" value="98234368" />
    <memory_usage name="optimization/1/email.charset" value="98234368" />
    <memory_usage name="optimization/1/email.encoders" value="98234368" />
    <memory_usage name="optimization/1/email.base64mime" value="98234368" />
    <memory_usage name="optimization/1/email._policybase" value="98234368" />
    <memory_usage name="optimization/1/email.header" value="98279424" />
    <memory_usage name="optimization/1/email.errors" value="98279424" />
    <memory_usage name="optimization/1/email.utils" value="98279424" />
    <memory_usage name="optimization/1/email._parseaddr" value="99778560" />
    <memory_usage name="optimization/1/urllib.parse" value="99778560" />
    <memory_usage name="optimization/1/ipaddress" value="99778560" />
    <memory_usage name="optimization/1/quopri" value="99778560" />
    <memory_usage name="optimization/1/posixpath" value="99778560" />
    <memory_usage name="optimization/1/textwrap" value="99778560" />
    <memory_usage name="optimization/1/zipfile" value="99778560" />
    <memory_usage name="optimization/1/zipfile._path" value="99799040" />
    <memory_usage name="optimization/1/zipfile._path.glob" value="99799040" />
    <memory_usage name="optimization/1/py_compile" value="99799040" />
    <memory_usage name="optimization/1/argparse" value="94388224" />
    <memory_usage name="optimization/1/gettext" value="95150080" />
    <memory_usage name="optimization/1/importlib.util" value="95150080" />
    <memory_usage name="optimization/1/pathlib" value="95150080" />
    <memory_usage name="optimization/1/email" value="95150080" />
    <memory_usage name="optimization/1/email.parser" value="95162368" />
    <memory_usage name="optimization/1/email.feedparser" value="95162368" />
    <memory_usage name="optimization/1/csv" value="95162368" />
    <memory_usage name="optimization/1/importlib.readers" value="95162368" />
    <memory_usage name="optimization/1/importlib.resources.readers" value="95162368" />
    <memory_usage name="optimization/1/importlib.resources._itertools" value="95162368" />
    <memory_usage name="optimization/1/importlib._bootstrap" value="95162368" />
    <memory_usage name="optimization/1/importlib" value="95162368" />
    <memory_usage name="optimization/1/dis" value="95162368" />
    <memory_usage name="optimization/1/opcode" value="95223808" />
    <memory_usage name="optimization/1/ast" value="95223808" />
    <memory_usage name="optimization/1/contextlib" value="95223808" />
    <memory_usage name="optimization/1/collections.abc" value="95223808" />
    <memory_usage name="optimization/1/weakref" value="95223808" />
    <memory_usage name="optimization/1/types" value="95223808" />
    <memory_usage name="optimization/1/reprlib" value="95223808" />
    <memory_usage name="optimization/1/collections" value="95223808" />
    <memory_usage name="optimization/1/heapq" value="95485952" />
    <memory_usage name="optimization/1/abc" value="95485952" />
    <memory_usage name="optimization/1/_py_abc" value="95485952" />
    <memory_usage name="optimization/1/re._parser" value="95485952" />
    <memory_usage name="optimization/1/re._compiler" value="95485952" />
    <memory_usage name="optimization/1/re._casefix" value="95485952" />
    <memory_usage name="optimization/1/os" value="95485952" />
    <memory_usage name="optimization/1/subprocess" value="93396992" />
    <memory_usage name="optimization/1/selectors" value="94101504" />
    <memory_usage name="optimization/1/select" value="94101504" />
    <memory_usage name="optimization/1/signal" value="94101504" />
    <memory_usage name="optimization/1/calendar" value="94101504" />
    <memory_usage name="optimization/1/locale" value="94101504" />
    <memory_usage name="optimization/1/encodings.aliases" value="94269440" />
    <memory_usage name="optimization/1/encodings" value="94269440" />
    <memory_usage name="optimization/1/encodings.mbcs" value="94269440" />
    <memory_usage name="optimization/1/warnings" value="94269440" />
    <memory_usage name="optimization/1/tracemalloc" value="94269440" />
    <memory_usage name="optimization/1/operator" value="94269440" />
    <memory_usage name="optimization/1/PyQt6.sip" value="94269440" />
    <memory_usage name="optimization/1/enum" value="94269440" />
    <memory_usage name="optimization/1/PyQt6" value="94269440" />
    <memory_usage name="optimization/1/PyQt6-preLoad" value="94285824" />
    <memory_usage name="optimization/1/pkgutil" value="94285824" />
    <memory_usage name="optimization/1/PyQt6.QtGui" value="94285824" />
    <memory_usage name="optimization/1/PyQt6.QtCore" value="95633408" />
    <memory_usage name="optimization/1/PyQt6.QtCore-postLoad" value="95633408" />
    <memory_usage name="optimization/1/__future__" value="95633408" />
    <memory_usage name="optimization/1/__main__" value="95633408" />
    <memory_usage name="optimization/1/glob" value="98004992" />
    <memory_usage name="optimization/1/flash_tool" value="98004992" />
    <memory_usage name="optimization/1/genduodakhd" value="99676160" />
    <memory_usage name="optimization/1/font_extractor" value="100208640" />
    <memory_usage name="optimization/1/webbrowser" value="100208640" />
    <memory_usage name="optimization/1/getopt" value="101355520" />
    <memory_usage name="optimization/1/shlex" value="101355520" />
    <memory_usage name="optimization/1/fastboodt" value="101355520" />
    <memory_usage name="optimization/1/custom_messagebox" value="101355520" />
    <memory_usage name="optimization/1/zhidinyishuaxie" value="101355520" />
    <memory_usage name="optimization/1/payload_extractor" value="101355520" />
    <memory_usage name="optimization/1/coloros" value="101355520" />
    <memory_usage name="optimization/1/coloros15" value="101355520" />
    <memory_usage name="optimization/1/utils" value="102125568" />
    <memory_usage name="optimization/1/platform" value="109056000" />
    <memory_usage name="optimization/1/socket" value="109330432" />
    <memory_usage name="optimization/1/_socket" value="109330432" />
    <memory_usage name="optimization/1/_wmi" value="109330432" />
    <memory_usage name="optimization/1/ntsecuritycon" value="109330432" />
    <memory_usage name="optimization/1/win32security" value="109330432" />
    <memory_usage name="optimization/1/ctypes" value="109330432" />
    <memory_usage name="optimization/1/ctypes._endian" value="109330432" />
    <memory_usage name="optimization/1/_ctypes" value="109330432" />
    <memory_usage name="optimization/1/psutil" value="109330432" />
    <memory_usage name="optimization/1/psutil._psaix" value="126177280" />
    <memory_usage name="optimization/1/psutil._psposix" value="127635456" />
    <memory_usage name="optimization/1/psutil._pssunos" value="127795200" />
    <memory_usage name="optimization/1/psutil._psbsd" value="130072576" />
    <memory_usage name="optimization/1/xml.etree.ElementTree" value="142802944" />
    <memory_usage name="optimization/1/_elementtree" value="142802944" />
    <memory_usage name="optimization/1/pyexpat" value="142802944" />
    <memory_usage name="optimization/1/xml.parsers.expat" value="142802944" />
    <memory_usage name="optimization/1/xml.parsers" value="142802944" />
    <memory_usage name="optimization/1/xml.etree.ElementPath" value="142802944" />
    <memory_usage name="optimization/1/xml.etree" value="142802944" />
    <memory_usage name="optimization/1/xml" value="142802944" />
    <memory_usage name="optimization/1/psutil._psosx" value="142802944" />
    <memory_usage name="optimization/1/psutil._psutil_windows" value="142802944" />
    <memory_usage name="optimization/1/psutil._pswindows" value="142802944" />
    <memory_usage name="optimization/1/psutil._pslinux" value="142802944" />
    <memory_usage name="optimization/1/psutil._compat" value="146681856" />
    <memory_usage name="optimization/1/psutil._common" value="148844544" />
    <memory_usage name="optimization/1/xdrlib" value="172576768" />
    <memory_usage name="optimization/1/uu" value="172576768" />
    <memory_usage name="optimization/1/trace" value="172576768" />
    <memory_usage name="optimization/1/tomllib._types" value="172576768" />
    <memory_usage name="optimization/1/tomllib._re" value="172576768" />
    <memory_usage name="optimization/1/tomllib._parser" value="172576768" />
    <memory_usage name="optimization/1/tomllib" value="172576768" />
    <memory_usage name="optimization/1/timeit" value="172576768" />
    <memory_usage name="optimization/1/sysconfig" value="172576768" />
    <memory_usage name="optimization/1/symtable" value="172576768" />
    <memory_usage name="optimization/1/stringprep" value="172576768" />
    <memory_usage name="optimization/1/sre_parse" value="172576768" />
    <memory_usage name="optimization/1/sre_constants" value="172576768" />
    <memory_usage name="optimization/1/sre_compile" value="172576768" />
    <memory_usage name="optimization/1/socketserver" value="172576768" />
    <memory_usage name="optimization/1/sndhdr" value="172576768" />
    <memory_usage name="optimization/1/sched" value="172576768" />
    <memory_usage name="optimization/1/rlcompleter" value="172576768" />
    <memory_usage name="optimization/1/pyclbr" value="172576768" />
    <memory_usage name="optimization/1/pstats" value="172576768" />
    <memory_usage name="optimization/1/pprint" value="172576768" />
    <memory_usage name="optimization/1/poplib" value="172576768" />
    <memory_usage name="optimization/1/pipes" value="172576768" />
    <memory_usage name="optimization/1/pickletools" value="172576768" />
    <memory_usage name="optimization/1/nturl2path" value="172576768" />
    <memory_usage name="optimization/1/netrc" value="172576768" />
    <memory_usage name="optimization/1/modulefinder" value="172576768" />
    <memory_usage name="optimization/1/mimetypes" value="172576768" />
    <memory_usage name="optimization/1/mailcap" value="172576768" />
    <memory_usage name="optimization/1/json.scanner" value="172576768" />
    <memory_usage name="optimization/1/json.encoder" value="172576768" />
    <memory_usage name="optimization/1/json.decoder" value="172576768" />
    <memory_usage name="optimization/1/json" value="172576768" />
    <memory_usage name="optimization/1/importlib.simple" value="172576768" />
    <memory_usage name="optimization/1/importlib.resources.simple" value="172576768" />
    <memory_usage name="optimization/1/imghdr" value="172576768" />
    <memory_usage name="optimization/1/imaplib" value="172576768" />
    <memory_usage name="optimization/1/html.parser" value="172576768" />
    <memory_usage name="optimization/1/html.entities" value="172576768" />
    <memory_usage name="optimization/1/html" value="172576768" />
    <memory_usage name="optimization/1/graphlib" value="172576768" />
    <memory_usage name="optimization/1/ftplib" value="172576768" />
    <memory_usage name="optimization/1/fileinput" value="172576768" />
    <memory_usage name="optimization/1/filecmp" value="172576768" />
    <memory_usage name="optimization/1/encodings.rot_13" value="172576768" />
    <memory_usage name="optimization/1/encodings.punycode" value="172576768" />
    <memory_usage name="optimization/1/encodings.idna" value="172576768" />
    <memory_usage name="optimization/1/encodings.hex_codec" value="172576768" />
    <memory_usage name="optimization/1/encodings.bz2_codec" value="172576768" />
    <memory_usage name="optimization/1/encodings.base64_codec" value="172576768" />
    <memory_usage name="optimization/1/difflib" value="172576768" />
    <memory_usage name="optimization/1/dataclasses" value="172576768" />
    <memory_usage name="optimization/1/configparser" value="172576768" />
    <memory_usage name="optimization/1/colorsys" value="172576768" />
    <memory_usage name="optimization/1/codeop" value="172576768" />
    <memory_usage name="optimization/1/code" value="172576768" />
    <memory_usage name="optimization/1/cmd" value="172576768" />
    <memory_usage name="optimization/1/chunk" value="172576768" />
    <memory_usage name="optimization/1/cgitb" value="172576768" />
    <memory_usage name="optimization/1/cgi" value="172576768" />
    <memory_usage name="optimization/1/_sitebuiltins" value="172576768" />
    <memory_usage name="optimization/1/_pylong" value="172576768" />
    <memory_usage name="optimization/1/_pyio" value="172576768" />
    <memory_usage name="optimization/1/_osx_support" value="172576768" />
    <memory_usage name="optimization/1/_markupbase" value="172576768" />
    <memory_usage name="optimization/1/_aix_support" value="172576768" />
    <memory_usage name="optimization/1/__phello__.spam" value="172576768" />
    <memory_usage name="optimization/1/__phello__.ham.eggs" value="172576768" />
    <memory_usage name="optimization/1/__phello__.ham" value="172576768" />
    <memory_usage name="optimization/1/__phello__" value="172576768" />
    <memory_usage name="optimization/1/__hello__" value="172576768" />
    <memory_usage name="optimization/1/encodings.zlib_codec" value="172576768" />
    <memory_usage name="optimization/1/encodings.uu_codec" value="172576768" />
    <memory_usage name="optimization/1/encodings.utf_8_sig" value="172576768" />
    <memory_usage name="optimization/1/encodings.utf_8" value="172576768" />
    <memory_usage name="optimization/1/encodings.utf_7" value="172576768" />
    <memory_usage name="optimization/1/encodings.utf_32_le" value="172576768" />
    <memory_usage name="optimization/1/encodings.utf_32_be" value="172576768" />
    <memory_usage name="optimization/1/encodings.utf_32" value="172576768" />
    <memory_usage name="optimization/1/encodings.utf_16_le" value="172576768" />
    <memory_usage name="optimization/1/encodings.utf_16_be" value="172576768" />
    <memory_usage name="optimization/1/encodings.utf_16" value="172576768" />
    <memory_usage name="optimization/1/encodings.unicode_escape" value="172576768" />
    <memory_usage name="optimization/1/encodings.undefined" value="172576768" />
    <memory_usage name="optimization/1/encodings.tis_620" value="172576768" />
    <memory_usage name="optimization/1/encodings.shift_jisx0213" value="172576768" />
    <memory_usage name="optimization/1/encodings.shift_jis_2004" value="172576768" />
    <memory_usage name="optimization/1/encodings.shift_jis" value="172576768" />
    <memory_usage name="optimization/1/encodings.raw_unicode_escape" value="172576768" />
    <memory_usage name="optimization/1/encodings.quopri_codec" value="172576768" />
    <memory_usage name="optimization/1/encodings.ptcp154" value="172576768" />
    <memory_usage name="optimization/1/encodings.palmos" value="172576768" />
    <memory_usage name="optimization/1/encodings.oem" value="172576768" />
    <memory_usage name="optimization/1/encodings.mac_turkish" value="172576768" />
    <memory_usage name="optimization/1/encodings.mac_romanian" value="172576768" />
    <memory_usage name="optimization/1/encodings.mac_roman" value="172576768" />
    <memory_usage name="optimization/1/encodings.mac_latin2" value="172576768" />
    <memory_usage name="optimization/1/encodings.mac_iceland" value="172576768" />
    <memory_usage name="optimization/1/encodings.mac_greek" value="172576768" />
    <memory_usage name="optimization/1/encodings.mac_farsi" value="172576768" />
    <memory_usage name="optimization/1/encodings.mac_cyrillic" value="172576768" />
    <memory_usage name="optimization/1/encodings.mac_croatian" value="172576768" />
    <memory_usage name="optimization/1/encodings.mac_arabic" value="172576768" />
    <memory_usage name="optimization/1/encodings.latin_1" value="172576768" />
    <memory_usage name="optimization/1/encodings.kz1048" value="172576768" />
    <memory_usage name="optimization/1/encodings.koi8_u" value="172576768" />
    <memory_usage name="optimization/1/encodings.koi8_t" value="172576768" />
    <memory_usage name="optimization/1/encodings.koi8_r" value="172576768" />
    <memory_usage name="optimization/1/encodings.johab" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso8859_9" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso8859_8" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso8859_7" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso8859_6" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso8859_5" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso8859_4" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso8859_3" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso8859_2" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso8859_16" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso8859_15" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso8859_14" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso8859_13" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso8859_11" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso8859_10" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso8859_1" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso2022_kr" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso2022_jp_ext" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso2022_jp_3" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso2022_jp_2004" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso2022_jp_2" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso2022_jp_1" value="172576768" />
    <memory_usage name="optimization/1/encodings.iso2022_jp" value="172576768" />
    <memory_usage name="optimization/1/encodings.hz" value="172576768" />
    <memory_usage name="optimization/1/encodings.hp_roman8" value="172576768" />
    <memory_usage name="optimization/1/encodings.gbk" value="172576768" />
    <memory_usage name="optimization/1/encodings.gb2312" value="172576768" />
    <memory_usage name="optimization/1/encodings.gb18030" value="172576768" />
    <memory_usage name="optimization/1/encodings.euc_kr" value="172576768" />
    <memory_usage name="optimization/1/encodings.euc_jp" value="172576768" />
    <memory_usage name="optimization/1/encodings.euc_jisx0213" value="172576768" />
    <memory_usage name="optimization/1/encodings.euc_jis_2004" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp950" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp949" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp932" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp875" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp874" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp869" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp866" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp865" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp864" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp863" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp862" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp861" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp860" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp858" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp857" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp856" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp855" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp852" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp850" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp775" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp737" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp720" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp500" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp437" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp424" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp273" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp1258" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp1257" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp1256" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp1255" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp1254" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp1253" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp1252" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp1251" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp1250" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp1140" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp1125" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp1026" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp1006" value="172576768" />
    <memory_usage name="optimization/1/encodings.cp037" value="172576768" />
    <memory_usage name="optimization/1/encodings.charmap" value="172576768" />
    <memory_usage name="optimization/1/encodings.big5hkscs" value="172576768" />
    <memory_usage name="optimization/1/encodings.big5" value="172576768" />
    <memory_usage name="optimization/1/encodings.ascii" value="172576768" />
    <memory_usage name="optimization/2/PyQt6.QtWidgets" value="172576768" />
    <memory_usage name="optimization/2/datetime" value="172576768" />
    <memory_usage name="optimization/2/_pydatetime" value="172576768" />
    <memory_usage name="optimization/2/_strptime" value="172576768" />
    <memory_usage name="optimization/2/re" value="172576768" />
    <memory_usage name="optimization/2/re._constants" value="172576768" />
    <memory_usage name="optimization/2/copyreg" value="172576768" />
    <memory_usage name="optimization/2/functools" value="172576768" />
    <memory_usage name="optimization/2/typing" value="172576768" />
    <memory_usage name="optimization/2/inspect" value="172576768" />
    <memory_usage name="optimization/2/ntpath" value="172576768" />
    <memory_usage name="optimization/2/string" value="172576768" />
    <memory_usage name="optimization/2/genericpath" value="172576768" />
    <memory_usage name="optimization/2/stat" value="172576768" />
    <memory_usage name="optimization/2/keyword" value="172576768" />
    <memory_usage name="optimization/2/token" value="172576768" />
    <memory_usage name="optimization/2/tokenize" value="172576768" />
    <memory_usage name="optimization/2/io" value="172576768" />
    <memory_usage name="optimization/2/codecs" value="172576768" />
    <memory_usage name="optimization/2/linecache" value="172576768" />
    <memory_usage name="optimization/2/importlib.machinery" value="172576768" />
    <memory_usage name="optimization/2/importlib._bootstrap_external" value="172576768" />
    <memory_usage name="optimization/2/importlib.metadata" value="172576768" />
    <memory_usage name="optimization/2/importlib.abc" value="172576768" />
    <memory_usage name="optimization/2/importlib.resources.abc" value="172576768" />
    <memory_usage name="optimization/2/importlib.resources" value="172576768" />
    <memory_usage name="optimization/2/importlib.resources._legacy" value="172576768" />
    <memory_usage name="optimization/2/importlib.resources._common" value="172576768" />
    <memory_usage name="optimization/2/importlib.resources._adapters" value="172576768" />
    <memory_usage name="optimization/2/tempfile" value="172576768" />
    <memory_usage name="optimization/2/random" value="172576768" />
    <memory_usage name="optimization/2/statistics" value="172576768" />
    <memory_usage name="optimization/2/decimal" value="172576768" />
    <memory_usage name="optimization/2/_pydecimal" value="172576768" />
    <memory_usage name="optimization/2/contextvars" value="172576768" />
    <memory_usage name="optimization/2/_decimal" value="172576768" />
    <memory_usage name="optimization/2/fractions" value="172576768" />
    <memory_usage name="optimization/2/numbers" value="172576768" />
    <memory_usage name="optimization/2/hashlib" value="172576768" />
    <memory_usage name="optimization/2/logging" value="172576768" />
    <memory_usage name="optimization/2/pickle" value="172576768" />
    <memory_usage name="optimization/2/_compat_pickle" value="172576768" />
    <memory_usage name="optimization/2/struct" value="172576768" />
    <memory_usage name="optimization/2/threading" value="172576768" />
    <memory_usage name="optimization/2/_threading_local" value="172576768" />
    <memory_usage name="optimization/2/_weakrefset" value="172576768" />
    <memory_usage name="optimization/2/traceback" value="172576768" />
    <memory_usage name="optimization/2/unicodedata" value="172576768" />
    <memory_usage name="optimization/2/_hashlib" value="172576768" />
    <memory_usage name="optimization/2/bisect" value="172576768" />
    <memory_usage name="optimization/2/_collections_abc" value="172576768" />
    <memory_usage name="optimization/2/shutil" value="172576768" />
    <memory_usage name="optimization/2/tarfile" value="172576768" />
    <memory_usage name="optimization/2/gzip" value="172576768" />
    <memory_usage name="optimization/2/_compression" value="172576768" />
    <memory_usage name="optimization/2/copy" value="172576768" />
    <memory_usage name="optimization/2/lzma" value="172576768" />
    <memory_usage name="optimization/2/_lzma" value="172576768" />
    <memory_usage name="optimization/2/bz2" value="172576768" />
    <memory_usage name="optimization/2/_bz2" value="172576768" />
    <memory_usage name="optimization/2/fnmatch" value="172576768" />
    <memory_usage name="optimization/2/importlib._abc" value="172576768" />
    <memory_usage name="optimization/2/importlib.metadata._itertools" value="172576768" />
    <memory_usage name="optimization/2/importlib.metadata._functools" value="172576768" />
    <memory_usage name="optimization/2/importlib.metadata._collections" value="172576768" />
    <memory_usage name="optimization/2/importlib.metadata._meta" value="172576768" />
    <memory_usage name="optimization/2/importlib.metadata._adapters" value="172576768" />
    <memory_usage name="optimization/2/importlib.metadata._text" value="172576768" />
    <memory_usage name="optimization/2/email.message" value="172576768" />
    <memory_usage name="optimization/2/email.policy" value="172576768" />
    <memory_usage name="optimization/2/email.contentmanager" value="172576768" />
    <memory_usage name="optimization/2/email.quoprimime" value="172576768" />
    <memory_usage name="optimization/2/email.headerregistry" value="172576768" />
    <memory_usage name="optimization/2/email._header_value_parser" value="172576768" />
    <memory_usage name="optimization/2/urllib" value="172576768" />
    <memory_usage name="optimization/2/email.iterators" value="172576768" />
    <memory_usage name="optimization/2/email.generator" value="172576768" />
    <memory_usage name="optimization/2/email._encoded_words" value="172576768" />
    <memory_usage name="optimization/2/base64" value="172576768" />
    <memory_usage name="optimization/2/email.charset" value="172576768" />
    <memory_usage name="optimization/2/email.encoders" value="172576768" />
    <memory_usage name="optimization/2/email.base64mime" value="172576768" />
    <memory_usage name="optimization/2/email._policybase" value="172576768" />
    <memory_usage name="optimization/2/email.header" value="172576768" />
    <memory_usage name="optimization/2/email.errors" value="172576768" />
    <memory_usage name="optimization/2/email.utils" value="172576768" />
    <memory_usage name="optimization/2/email._parseaddr" value="172576768" />
    <memory_usage name="optimization/2/urllib.parse" value="172576768" />
    <memory_usage name="optimization/2/ipaddress" value="172576768" />
    <memory_usage name="optimization/2/quopri" value="172576768" />
    <memory_usage name="optimization/2/posixpath" value="172576768" />
    <memory_usage name="optimization/2/textwrap" value="172576768" />
    <memory_usage name="optimization/2/zipfile" value="172576768" />
    <memory_usage name="optimization/2/zipfile._path" value="172576768" />
    <memory_usage name="optimization/2/zipfile._path.glob" value="172576768" />
    <memory_usage name="optimization/2/py_compile" value="172576768" />
    <memory_usage name="optimization/2/argparse" value="172576768" />
    <memory_usage name="optimization/2/gettext" value="172576768" />
    <memory_usage name="optimization/2/importlib.util" value="172576768" />
    <memory_usage name="optimization/2/pathlib" value="172576768" />
    <memory_usage name="optimization/2/email" value="172576768" />
    <memory_usage name="optimization/2/email.parser" value="172576768" />
    <memory_usage name="optimization/2/email.feedparser" value="172576768" />
    <memory_usage name="optimization/2/csv" value="172576768" />
    <memory_usage name="optimization/2/importlib.readers" value="172576768" />
    <memory_usage name="optimization/2/importlib.resources.readers" value="172576768" />
    <memory_usage name="optimization/2/importlib.resources._itertools" value="172576768" />
    <memory_usage name="optimization/2/importlib._bootstrap" value="172576768" />
    <memory_usage name="optimization/2/importlib" value="172576768" />
    <memory_usage name="optimization/2/dis" value="172576768" />
    <memory_usage name="optimization/2/opcode" value="172576768" />
    <memory_usage name="optimization/2/ast" value="172576768" />
    <memory_usage name="optimization/2/contextlib" value="172576768" />
    <memory_usage name="optimization/2/collections.abc" value="172576768" />
    <memory_usage name="optimization/2/weakref" value="172576768" />
    <memory_usage name="optimization/2/types" value="172576768" />
    <memory_usage name="optimization/2/reprlib" value="172576768" />
    <memory_usage name="optimization/2/collections" value="172576768" />
    <memory_usage name="optimization/2/heapq" value="172576768" />
    <memory_usage name="optimization/2/abc" value="172576768" />
    <memory_usage name="optimization/2/_py_abc" value="172576768" />
    <memory_usage name="optimization/2/re._parser" value="172576768" />
    <memory_usage name="optimization/2/re._compiler" value="172576768" />
    <memory_usage name="optimization/2/re._casefix" value="172576768" />
    <memory_usage name="optimization/2/os" value="172576768" />
    <memory_usage name="optimization/2/subprocess" value="172576768" />
    <memory_usage name="optimization/2/selectors" value="172576768" />
    <memory_usage name="optimization/2/select" value="172576768" />
    <memory_usage name="optimization/2/signal" value="172576768" />
    <memory_usage name="optimization/2/calendar" value="172576768" />
    <memory_usage name="optimization/2/locale" value="172576768" />
    <memory_usage name="optimization/2/encodings.aliases" value="172576768" />
    <memory_usage name="optimization/2/encodings" value="172576768" />
    <memory_usage name="optimization/2/encodings.mbcs" value="172576768" />
    <memory_usage name="optimization/2/warnings" value="172576768" />
    <memory_usage name="optimization/2/tracemalloc" value="172576768" />
    <memory_usage name="optimization/2/operator" value="172576768" />
    <memory_usage name="optimization/2/PyQt6.sip" value="172576768" />
    <memory_usage name="optimization/2/enum" value="172576768" />
    <memory_usage name="optimization/2/PyQt6" value="172576768" />
    <memory_usage name="optimization/2/PyQt6-preLoad" value="172576768" />
    <memory_usage name="optimization/2/pkgutil" value="172576768" />
    <memory_usage name="optimization/2/PyQt6.QtGui" value="172576768" />
    <memory_usage name="optimization/2/PyQt6.QtCore" value="172576768" />
    <memory_usage name="optimization/2/PyQt6.QtCore-postLoad" value="172576768" />
    <memory_usage name="optimization/2/__future__" value="172576768" />
    <memory_usage name="optimization/2/__main__" value="172576768" />
    <memory_usage name="optimization/2/xdrlib" value="172576768" />
    <memory_usage name="optimization/2/webbrowser" value="172576768" />
    <memory_usage name="optimization/2/uu" value="172576768" />
    <memory_usage name="optimization/2/trace" value="172576768" />
    <memory_usage name="optimization/2/tomllib._types" value="172576768" />
    <memory_usage name="optimization/2/tomllib._re" value="172576768" />
    <memory_usage name="optimization/2/tomllib._parser" value="172576768" />
    <memory_usage name="optimization/2/tomllib" value="172576768" />
    <memory_usage name="optimization/2/timeit" value="172576768" />
    <memory_usage name="optimization/2/sysconfig" value="172576768" />
    <memory_usage name="optimization/2/symtable" value="172576768" />
    <memory_usage name="optimization/2/stringprep" value="172576768" />
    <memory_usage name="optimization/2/sre_parse" value="172576768" />
    <memory_usage name="optimization/2/sre_constants" value="172576768" />
    <memory_usage name="optimization/2/sre_compile" value="172576768" />
    <memory_usage name="optimization/2/socketserver" value="172576768" />
    <memory_usage name="optimization/2/sndhdr" value="172576768" />
    <memory_usage name="optimization/2/shlex" value="172576768" />
    <memory_usage name="optimization/2/sched" value="172576768" />
    <memory_usage name="optimization/2/rlcompleter" value="172576768" />
    <memory_usage name="optimization/2/pyclbr" value="172576768" />
    <memory_usage name="optimization/2/pstats" value="172576768" />
    <memory_usage name="optimization/2/pprint" value="172576768" />
    <memory_usage name="optimization/2/poplib" value="172576768" />
    <memory_usage name="optimization/2/platform" value="172576768" />
    <memory_usage name="optimization/2/socket" value="172576768" />
    <memory_usage name="optimization/2/_socket" value="172576768" />
    <memory_usage name="optimization/2/pipes" value="172576768" />
    <memory_usage name="optimization/2/pickletools" value="172576768" />
    <memory_usage name="optimization/2/nturl2path" value="172576768" />
    <memory_usage name="optimization/2/netrc" value="172576768" />
    <memory_usage name="optimization/2/modulefinder" value="172576768" />
    <memory_usage name="optimization/2/mimetypes" value="172576768" />
    <memory_usage name="optimization/2/mailcap" value="172576768" />
    <memory_usage name="optimization/2/json.scanner" value="172576768" />
    <memory_usage name="optimization/2/json.encoder" value="172576768" />
    <memory_usage name="optimization/2/json.decoder" value="172576768" />
    <memory_usage name="optimization/2/json" value="172576768" />
    <memory_usage name="optimization/2/importlib.simple" value="172576768" />
    <memory_usage name="optimization/2/importlib.resources.simple" value="172576768" />
    <memory_usage name="optimization/2/imghdr" value="172576768" />
    <memory_usage name="optimization/2/imaplib" value="172576768" />
    <memory_usage name="optimization/2/html.parser" value="172576768" />
    <memory_usage name="optimization/2/html.entities" value="172576768" />
    <memory_usage name="optimization/2/html" value="172576768" />
    <memory_usage name="optimization/2/graphlib" value="172576768" />
    <memory_usage name="optimization/2/getopt" value="172576768" />
    <memory_usage name="optimization/2/ftplib" value="172576768" />
    <memory_usage name="optimization/2/fileinput" value="172576768" />
    <memory_usage name="optimization/2/filecmp" value="172576768" />
    <memory_usage name="optimization/2/encodings.rot_13" value="172576768" />
    <memory_usage name="optimization/2/encodings.punycode" value="172576768" />
    <memory_usage name="optimization/2/encodings.idna" value="172576768" />
    <memory_usage name="optimization/2/encodings.hex_codec" value="172576768" />
    <memory_usage name="optimization/2/encodings.bz2_codec" value="172576768" />
    <memory_usage name="optimization/2/encodings.base64_codec" value="172576768" />
    <memory_usage name="optimization/2/difflib" value="172576768" />
    <memory_usage name="optimization/2/dataclasses" value="172576768" />
    <memory_usage name="optimization/2/configparser" value="172576768" />
    <memory_usage name="optimization/2/colorsys" value="172576768" />
    <memory_usage name="optimization/2/codeop" value="172576768" />
    <memory_usage name="optimization/2/code" value="172576768" />
    <memory_usage name="optimization/2/cmd" value="172576768" />
    <memory_usage name="optimization/2/chunk" value="172576768" />
    <memory_usage name="optimization/2/cgitb" value="172576768" />
    <memory_usage name="optimization/2/cgi" value="172576768" />
    <memory_usage name="optimization/2/_wmi" value="172576768" />
    <memory_usage name="optimization/2/_sitebuiltins" value="172576768" />
    <memory_usage name="optimization/2/_pylong" value="172576768" />
    <memory_usage name="optimization/2/_pyio" value="172576768" />
    <memory_usage name="optimization/2/_osx_support" value="172576768" />
    <memory_usage name="optimization/2/_markupbase" value="172576768" />
    <memory_usage name="optimization/2/_aix_support" value="172576768" />
    <memory_usage name="optimization/2/__phello__.spam" value="172576768" />
    <memory_usage name="optimization/2/__phello__.ham.eggs" value="172576768" />
    <memory_usage name="optimization/2/__phello__.ham" value="172576768" />
    <memory_usage name="optimization/2/__phello__" value="172576768" />
    <memory_usage name="optimization/2/__hello__" value="172576768" />
    <memory_usage name="optimization/2/encodings.zlib_codec" value="172576768" />
    <memory_usage name="optimization/2/encodings.uu_codec" value="172576768" />
    <memory_usage name="optimization/2/encodings.utf_8_sig" value="172576768" />
    <memory_usage name="optimization/2/encodings.utf_8" value="172576768" />
    <memory_usage name="optimization/2/encodings.utf_7" value="172576768" />
    <memory_usage name="optimization/2/encodings.utf_32_le" value="172576768" />
    <memory_usage name="optimization/2/encodings.utf_32_be" value="172576768" />
    <memory_usage name="optimization/2/encodings.utf_32" value="172576768" />
    <memory_usage name="optimization/2/encodings.utf_16_le" value="172576768" />
    <memory_usage name="optimization/2/encodings.utf_16_be" value="172576768" />
    <memory_usage name="optimization/2/encodings.utf_16" value="172576768" />
    <memory_usage name="optimization/2/encodings.unicode_escape" value="172576768" />
    <memory_usage name="optimization/2/encodings.undefined" value="172576768" />
    <memory_usage name="optimization/2/encodings.tis_620" value="172576768" />
    <memory_usage name="optimization/2/encodings.shift_jisx0213" value="172576768" />
    <memory_usage name="optimization/2/encodings.shift_jis_2004" value="172576768" />
    <memory_usage name="optimization/2/encodings.shift_jis" value="172576768" />
    <memory_usage name="optimization/2/encodings.raw_unicode_escape" value="172576768" />
    <memory_usage name="optimization/2/encodings.quopri_codec" value="172576768" />
    <memory_usage name="optimization/2/encodings.ptcp154" value="172576768" />
    <memory_usage name="optimization/2/encodings.palmos" value="172576768" />
    <memory_usage name="optimization/2/encodings.oem" value="172576768" />
    <memory_usage name="optimization/2/encodings.mac_turkish" value="172576768" />
    <memory_usage name="optimization/2/encodings.mac_romanian" value="172576768" />
    <memory_usage name="optimization/2/encodings.mac_roman" value="172576768" />
    <memory_usage name="optimization/2/encodings.mac_latin2" value="172576768" />
    <memory_usage name="optimization/2/encodings.mac_iceland" value="172576768" />
    <memory_usage name="optimization/2/encodings.mac_greek" value="172576768" />
    <memory_usage name="optimization/2/encodings.mac_farsi" value="172576768" />
    <memory_usage name="optimization/2/encodings.mac_cyrillic" value="172576768" />
    <memory_usage name="optimization/2/encodings.mac_croatian" value="172576768" />
    <memory_usage name="optimization/2/encodings.mac_arabic" value="172576768" />
    <memory_usage name="optimization/2/encodings.latin_1" value="172576768" />
    <memory_usage name="optimization/2/encodings.kz1048" value="172576768" />
    <memory_usage name="optimization/2/encodings.koi8_u" value="172576768" />
    <memory_usage name="optimization/2/encodings.koi8_t" value="172576768" />
    <memory_usage name="optimization/2/encodings.koi8_r" value="172576768" />
    <memory_usage name="optimization/2/encodings.johab" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso8859_9" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso8859_8" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso8859_7" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso8859_6" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso8859_5" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso8859_4" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso8859_3" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso8859_2" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso8859_16" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso8859_15" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso8859_14" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso8859_13" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso8859_11" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso8859_10" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso8859_1" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso2022_kr" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso2022_jp_ext" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso2022_jp_3" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso2022_jp_2004" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso2022_jp_2" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso2022_jp_1" value="172576768" />
    <memory_usage name="optimization/2/encodings.iso2022_jp" value="172576768" />
    <memory_usage name="optimization/2/encodings.hz" value="172576768" />
    <memory_usage name="optimization/2/encodings.hp_roman8" value="172576768" />
    <memory_usage name="optimization/2/encodings.gbk" value="172576768" />
    <memory_usage name="optimization/2/encodings.gb2312" value="172576768" />
    <memory_usage name="optimization/2/encodings.gb18030" value="172576768" />
    <memory_usage name="optimization/2/encodings.euc_kr" value="172576768" />
    <memory_usage name="optimization/2/encodings.euc_jp" value="172576768" />
    <memory_usage name="optimization/2/encodings.euc_jisx0213" value="172576768" />
    <memory_usage name="optimization/2/encodings.euc_jis_2004" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp950" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp949" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp932" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp875" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp874" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp869" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp866" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp865" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp864" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp863" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp862" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp861" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp860" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp858" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp857" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp856" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp855" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp852" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp850" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp775" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp737" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp720" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp500" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp437" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp424" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp273" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp1258" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp1257" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp1256" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp1255" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp1254" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp1253" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp1252" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp1251" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp1250" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp1140" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp1125" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp1026" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp1006" value="172576768" />
    <memory_usage name="optimization/2/encodings.cp037" value="172576768" />
    <memory_usage name="optimization/2/encodings.charmap" value="172576768" />
    <memory_usage name="optimization/2/encodings.big5hkscs" value="172576768" />
    <memory_usage name="optimization/2/encodings.big5" value="172576768" />
    <memory_usage name="optimization/2/encodings.ascii" value="172576768" />
    <memory_usage name="optimization/2/glob" value="172576768" />
    <memory_usage name="optimization/2/flash_tool" value="172576768" />
    <memory_usage name="optimization/2/genduodakhd" value="172576768" />
    <memory_usage name="optimization/2/font_extractor" value="172576768" />
    <memory_usage name="optimization/2/fastboodt" value="172576768" />
    <memory_usage name="optimization/2/custom_messagebox" value="172576768" />
    <memory_usage name="optimization/2/zhidinyishuaxie" value="172576768" />
    <memory_usage name="optimization/2/payload_extractor" value="172576768" />
    <memory_usage name="optimization/2/coloros" value="172576768" />
    <memory_usage name="optimization/2/coloros15" value="172576768" />
    <memory_usage name="optimization/2/utils" value="172576768" />
    <memory_usage name="optimization/2/ntsecuritycon" value="172576768" />
    <memory_usage name="optimization/2/win32security" value="172576768" />
    <memory_usage name="optimization/2/ctypes" value="172576768" />
    <memory_usage name="optimization/2/ctypes._endian" value="172576768" />
    <memory_usage name="optimization/2/_ctypes" value="172576768" />
    <memory_usage name="optimization/2/psutil" value="172576768" />
    <memory_usage name="optimization/2/psutil._psaix" value="173191168" />
    <memory_usage name="optimization/2/psutil._psposix" value="173191168" />
    <memory_usage name="optimization/2/psutil._pssunos" value="173191168" />
    <memory_usage name="optimization/2/psutil._psbsd" value="173191168" />
    <memory_usage name="optimization/2/xml.etree.ElementTree" value="173191168" />
    <memory_usage name="optimization/2/_elementtree" value="173191168" />
    <memory_usage name="optimization/2/pyexpat" value="173191168" />
    <memory_usage name="optimization/2/xml.parsers.expat" value="173191168" />
    <memory_usage name="optimization/2/xml.parsers" value="173191168" />
    <memory_usage name="optimization/2/xml.etree.ElementPath" value="173191168" />
    <memory_usage name="optimization/2/xml.etree" value="173191168" />
    <memory_usage name="optimization/2/xml" value="173191168" />
    <memory_usage name="optimization/2/psutil._psosx" value="173191168" />
    <memory_usage name="optimization/2/psutil._psutil_windows" value="173191168" />
    <memory_usage name="optimization/2/psutil._pswindows" value="173191168" />
    <memory_usage name="optimization/2/psutil._pslinux" value="173191168" />
    <memory_usage name="optimization/2/psutil._compat" value="173740032" />
    <memory_usage name="optimization/2/psutil._common" value="173740032" />
    <memory_usage name="before_c_code_generation" value="173740032" />
    <memory_usage name="before_running_scons" value="180695040" />
  </performance>
  <data_file name="ADBTools\AdbWinApi.dll" source="${sys.real_prefix}\shuajishangyong\shuaji\temp_build\ADBTools\AdbWinApi.dll" size="108128" reason="specified data file 'temp_build/ADBTools/**/*.*=ADBTools/' on command line" tags="user,copy" />
  <data_file name="ADBTools\AdbWinUsbApi.dll" source="${sys.real_prefix}\shuajishangyong\shuaji\temp_build\ADBTools\AdbWinUsbApi.dll" size="73312" reason="specified data file 'temp_build/ADBTools/**/*.*=ADBTools/' on command line" tags="user,copy" />
  <data_file name="ADBTools\adb.exe" source="${sys.real_prefix}\shuajishangyong\shuaji\temp_build\ADBTools\adb.exe" size="5920544" reason="specified data file 'temp_build/ADBTools/**/*.*=ADBTools/' on command line" tags="user,copy" />
  <data_file name="ADBTools\fastboot.exe" source="${sys.real_prefix}\shuajishangyong\shuaji\temp_build\ADBTools\fastboot.exe" size="2050144" reason="specified data file 'temp_build/ADBTools/**/*.*=ADBTools/' on command line" tags="user,copy" />
  <data_file name="ADBTools\libwinpthread-1.dll" source="${sys.real_prefix}\shuajishangyong\shuaji\temp_build\ADBTools\libwinpthread-1.dll" size="242960" reason="specified data file 'temp_build/ADBTools/**/*.*=ADBTools/' on command line" tags="user,copy" />
  <data_file name="ADBTools\pay.exe" source="${sys.real_prefix}\shuajishangyong\shuaji\temp_build\ADBTools\pay.exe" size="7519792" reason="specified data file 'temp_build/ADBTools/**/*.*=ADBTools/' on command line" tags="user,copy" />
  <data_file name="ico\icon.ico" source="${sys.real_prefix}\shuajishangyong\shuaji\temp_build\ico\icon.ico" size="10076" reason="specified data file 'temp_build/ico/icon.ico=ico/' on command line" tags="user,copy" />
  <data_file name="tup\1.png" source="${sys.real_prefix}\shuajishangyong\shuaji\temp_build\tup\1.png" size="36576" reason="specified data file 'temp_build/tup/1.png=tup/' on command line" tags="user,copy" />
  <data_file name="tup\2.png" source="${sys.real_prefix}\shuajishangyong\shuaji\temp_build\tup\2.png" size="34793" reason="specified data file 'temp_build/tup/2.png=tup/' on command line" tags="user,copy" />
  <included_extension name="QtCore.pyd" dest_path="PyQt6\QtCore.pyd" source_path="${sys.real_prefix}\shuajishangyong\shuaji\PyQt6\QtCore.pyd" package="PyQt6" ignored="no" reason="used extension module" />
  <included_extension name="QtGui.pyd" dest_path="PyQt6\QtGui.pyd" source_path="${sys.real_prefix}\shuajishangyong\shuaji\PyQt6\QtGui.pyd" package="PyQt6" ignored="no" reason="used extension module" />
  <included_extension name="QtWidgets.pyd" dest_path="PyQt6\QtWidgets.pyd" source_path="${sys.real_prefix}\shuajishangyong\shuaji\PyQt6\QtWidgets.pyd" package="PyQt6" ignored="no" reason="used extension module" />
  <included_extension name="sip.pyd" dest_path="PyQt6\sip.pyd" source_path="${sys.real_prefix}\shuajishangyong\shuaji\PyQt6\sip.cp312-win_amd64.pyd" package="PyQt6" ignored="no" reason="used extension module" />
  <included_extension name="_bz2.pyd" dest_path="_bz2.pyd" source_path="${sys.real_prefix}\DLLs\_bz2.pyd" package="" ignored="no" reason="used extension module" />
  <included_extension name="_ctypes.pyd" dest_path="_ctypes.pyd" source_path="${sys.real_prefix}\DLLs\_ctypes.pyd" package="" ignored="no" reason="used extension module" />
  <included_extension name="_decimal.pyd" dest_path="_decimal.pyd" source_path="${sys.real_prefix}\DLLs\_decimal.pyd" package="" ignored="no" reason="used extension module" />
  <included_extension name="_elementtree.pyd" dest_path="_elementtree.pyd" source_path="${sys.real_prefix}\DLLs\_elementtree.pyd" package="" ignored="no" reason="used extension module" />
  <included_extension name="_hashlib.pyd" dest_path="_hashlib.pyd" source_path="${sys.real_prefix}\DLLs\_hashlib.pyd" package="" ignored="no" reason="used extension module" />
  <included_extension name="_lzma.pyd" dest_path="_lzma.pyd" source_path="${sys.real_prefix}\DLLs\_lzma.pyd" package="" ignored="no" reason="used extension module" />
  <included_extension name="_socket.pyd" dest_path="_socket.pyd" source_path="${sys.real_prefix}\DLLs\_socket.pyd" package="" ignored="no" reason="used extension module" />
  <included_extension name="_wmi.pyd" dest_path="_wmi.pyd" source_path="${sys.real_prefix}\DLLs\_wmi.pyd" package="" ignored="no" reason="used extension module" />
  <included_extension name="_psutil_windows.pyd" dest_path="psutil\_psutil_windows.pyd" source_path="${sys.real_prefix}\shuajishangyong\shuaji\venv\Lib\site-packages\psutil\_psutil_windows.pyd" package="psutil" ignored="no" reason="used extension module" />
  <included_extension name="pyexpat.pyd" dest_path="pyexpat.pyd" source_path="${sys.real_prefix}\DLLs\pyexpat.pyd" package="" ignored="no" reason="used extension module" />
  <included_extension name="select.pyd" dest_path="select.pyd" source_path="${sys.real_prefix}\DLLs\select.pyd" package="" ignored="no" reason="used extension module" />
  <included_extension name="unicodedata.pyd" dest_path="unicodedata.pyd" source_path="${sys.real_prefix}\DLLs\unicodedata.pyd" package="" ignored="no" reason="used extension module" />
  <included_extension name="win32security.pyd" dest_path="win32security.pyd" source_path="${sys.real_prefix}\shuajishangyong\shuaji\venv\Lib\site-packages\win32\win32security.pyd" package="" ignored="no" reason="used extension module" />
  <data_composer blob_size="6056554" total="8272">
    <module_data filename="__bytecode.const" blob_name=".bytecode" blob_size="5828468" input_size="5851492" />
    <module_data filename="__constants.const" blob_name="" blob_size="866" input_size="2206" />
    <module_data filename="module.PyQt6-preLoad.const" blob_name="PyQt6-preLoad" blob_size="188" input_size="411" />
    <module_data filename="module.PyQt6.QtCore-postLoad.const" blob_name="PyQt6.QtCore-postLoad" blob_size="254" input_size="472" />
    <module_data filename="module.PyQt6.const" blob_name="PyQt6" blob_size="371" input_size="740" />
    <module_data filename="module.__main__.const" blob_name="__main__" blob_size="2108" input_size="4026" />
    <module_data filename="module.coloros.const" blob_name="coloros" blob_size="3605" input_size="5435" />
    <module_data filename="module.coloros15.const" blob_name="coloros15" blob_size="4242" input_size="6189" />
    <module_data filename="module.custom_messagebox.const" blob_name="custom_messagebox" blob_size="1590" input_size="2245" />
    <module_data filename="module.fastboodt.const" blob_name="fastboodt" blob_size="1657" input_size="2721" />
    <module_data filename="module.flash_tool.const" blob_name="flash_tool" blob_size="34245" input_size="42078" />
    <module_data filename="module.font_extractor.const" blob_name="font_extractor" blob_size="6251" input_size="9018" />
    <module_data filename="module.genduodakhd.const" blob_name="genduodakhd" blob_size="16245" input_size="19705" />
    <module_data filename="module.ntsecuritycon.const" blob_name="ntsecuritycon" blob_size="15637" input_size="24637" />
    <module_data filename="module.payload_extractor.const" blob_name="payload_extractor" blob_size="7044" input_size="8911" />
    <module_data filename="module.psutil._common.const" blob_name="psutil._common" blob_size="11973" input_size="17897" />
    <module_data filename="module.psutil._compat.const" blob_name="psutil._compat" blob_size="4178" input_size="5872" />
    <module_data filename="module.psutil._psaix.const" blob_name="psutil._psaix" blob_size="6072" input_size="10313" />
    <module_data filename="module.psutil._psbsd.const" blob_name="psutil._psbsd" blob_size="8631" input_size="14016" />
    <module_data filename="module.psutil._pslinux.const" blob_name="psutil._pslinux" blob_size="23410" input_size="34864" />
    <module_data filename="module.psutil._psosx.const" blob_name="psutil._psosx" blob_size="5689" input_size="9522" />
    <module_data filename="module.psutil._psposix.const" blob_name="psutil._psposix" blob_size="2780" input_size="3853" />
    <module_data filename="module.psutil._pssunos.const" blob_name="psutil._pssunos" blob_size="7174" input_size="12058" />
    <module_data filename="module.psutil._pswindows.const" blob_name="psutil._pswindows" blob_size="12939" input_size="19635" />
    <module_data filename="module.psutil.const" blob_name="psutil" blob_size="42760" input_size="51773" />
    <module_data filename="module.utils.const" blob_name="utils" blob_size="2096" input_size="3301" />
    <module_data filename="module.zhidinyishuaxie.const" blob_name="zhidinyishuaxie" blob_size="5620" input_size="8596" />
  </data_composer>
  <command_line>
    <option value="--standalone" />
    <option value="--windows-console-mode=disable" />
    <option value="--windows-icon-from-ico=ico/icon.ico" />
    <option value="--include-package=PyQt6.QtCore" />
    <option value="--include-package=PyQt6.QtGui" />
    <option value="--include-package=PyQt6.QtWidgets" />
    <option value="--include-data-dir=temp_build/ADBTools=ADBTools" />
    <option value="--include-data-files=temp_build/ADBTools/**/*.*=ADBTools/" />
    <option value="--include-data-files=temp_build/ico/icon.ico=ico/" />
    <option value="--include-data-files=temp_build/tup/1.png=tup/" />
    <option value="--include-data-files=temp_build/tup/2.png=tup/" />
    <option value="--enable-plugin=pyqt6" />
    <option value="--remove-output" />
    <option value="--output-dir=dist" />
    <option value="--output-filename=益民固件刷写工具" />
    <option value="--jobs=6" />
    <option value="--show-progress" />
    <option value="--show-memory" />
    <option value="--assume-yes-for-downloads" />
    <option value="--onefile" />
    <option value="--onefile-tempdir-spec=\syiming\onefile_%PID%_%TIME%" />
    <option value="--windows-uac-admin" />
    <option value="--windows-company-name=益民科技" />
    <option value="--windows-product-name=益民固件刷写器" />
    <option value="--windows-file-version=5.0.0.0" />
    <option value="--windows-product-version=5.0.0.3" />
    <option value="--noinclude-qt-translations" />
    <option value="--remove-output" />
    <option value="temp_build/main.py" />
  </command_line>
  <plugins>
    <plugin name="anti-bloat" user_enabled="no" />
    <plugin name="data-files" user_enabled="no" />
    <plugin name="delvewheel" user_enabled="no" />
    <plugin name="dll-files" user_enabled="no" />
    <plugin name="eventlet" user_enabled="no" />
    <plugin name="gevent" user_enabled="no" />
    <plugin name="gi" user_enabled="no" />
    <plugin name="glfw" user_enabled="no" />
    <plugin name="implicit-imports" user_enabled="no" />
    <plugin name="kivy" user_enabled="no" />
    <plugin name="matplotlib" user_enabled="no" />
    <plugin name="multiprocessing" user_enabled="no" />
    <plugin name="options-nanny" user_enabled="no" />
    <plugin name="pbr-compat" user_enabled="no" />
    <plugin name="pkg-resources" user_enabled="no" />
    <plugin name="playwright" user_enabled="no" />
    <plugin name="pyqt6" user_enabled="yes" />
    <plugin name="pywebview" user_enabled="no" />
    <plugin name="spacy" user_enabled="no" />
    <plugin name="transformers" user_enabled="no" />
  </plugins>
  <distributions>
    <distribution name="PyQt6" version="6.7.1" installer="pip" />
    <distribution name="PyQt6-Qt6" version="6.7.3" installer="pip" />
    <distribution name="PyQt6_sip" version="13.9.0" installer="pip" />
    <distribution name="psutil" version="6.1.0" installer="pip" />
    <distribution name="pywin32" version="308" installer="pip" />
  </distributions>
  <python python_exe="${sys.real_prefix}\shuajishangyong\shuaji\venv\Scripts\python.exe" python_flavor="Unknown" python_version="3.12.7" os_name="Windows" os_release="10" arch_name="x86_64" filesystem_encoding="utf-8">
    <search_path>
      <path value="${sys.real_prefix}\shuajishangyong\shuaji" />
      <path value="${sys.real_prefix}\shuajishangyong\shuaji\temp_build" />
      <path value="${sys.real_prefix}\DLLs" />
      <path value="${sys.real_prefix}\Lib" />
      <path value="${sys.real_prefix}" />
      <path value="${sys.real_prefix}\shuajishangyong\shuaji\venv" />
      <path value="${sys.real_prefix}\shuajishangyong\shuaji\venv\Lib\site-packages" />
      <path value="${sys.real_prefix}\shuajishangyong\shuaji\venv\Lib\site-packages\win32" />
      <path value="${sys.real_prefix}\shuajishangyong\shuaji\venv\Lib\site-packages\win32\lib" />
      <path value="${sys.real_prefix}\shuajishangyong\shuaji\venv\Lib\site-packages\Pythonwin" />
      <path value="${sys.real_prefix}\shuajishangyong\shuaji\venv\Lib\site-packages\setuptools\_vendor" />
    </search_path>
  </python>
  <output run_filename="${sys.real_prefix}\shuajishangyong\shuaji\dist\益民固件刷写工具.exe" />
</nuitka-compilation-report>
