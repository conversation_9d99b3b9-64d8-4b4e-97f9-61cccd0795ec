// qmdisubwindow.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMdiSubWindow : public QWidget
{
%TypeHeaderCode
#include <qmdisubwindow.h>
%End

public:
    enum SubWindowOption /BaseType=Flag/
    {
        RubberBandResize,
        RubberBandMove,
    };

    typedef <PERSON>lag<PERSON><QMdiSubWindow::SubWindowOption> SubWindowOptions;
    QMdiSubWindow(QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    virtual ~QMdiSubWindow();
    virtual QSize sizeHint() const;
    virtual QSize minimumSizeHint() const;
    void setWidget(QWidget *widget /Transfer/);
%MethodCode
        // We have to implement /TransferBack/ on any existing widget.
        QWidget *w = sipCpp->widget();
        
        Py_BEGIN_ALLOW_THREADS
        sipCpp->setWidget(a0);
        Py_END_ALLOW_THREADS
        
        if (w)
        {
            PyObject *wo = sipGetPyObject(w, sipType_QWidget);
        
            if (wo)
                sipTransferBack(wo);
        }
%End

    QWidget *widget() const;
    bool isShaded() const;
    void setOption(QMdiSubWindow::SubWindowOption option, bool on = true);
    bool testOption(QMdiSubWindow::SubWindowOption) const;
    void setKeyboardSingleStep(int step);
    int keyboardSingleStep() const;
    void setKeyboardPageStep(int step);
    int keyboardPageStep() const;
    void setSystemMenu(QMenu *systemMenu /Transfer/);
%MethodCode
        // We have to break the parent association on any existing menu.
        QMenu *w = sipCpp->systemMenu();
        
        if (w)
        {
            PyObject *wo = sipGetPyObject(w, sipType_QMenu);
        
            if (wo)
                sipTransferTo(wo, 0);
        }
        
        Py_BEGIN_ALLOW_THREADS
        sipCpp->setSystemMenu(a0);
        Py_END_ALLOW_THREADS
%End

    QMenu *systemMenu() const;
    QMdiArea *mdiArea() const;

signals:
    void windowStateChanged(Qt::WindowStates oldState, Qt::WindowStates newState);
    void aboutToActivate();

public slots:
    void showSystemMenu();
    void showShaded();

protected:
    virtual bool eventFilter(QObject *object, QEvent *event);
    virtual bool event(QEvent *event);
    virtual void showEvent(QShowEvent *showEvent);
    virtual void hideEvent(QHideEvent *hideEvent);
    virtual void changeEvent(QEvent *changeEvent);
    virtual void closeEvent(QCloseEvent *closeEvent);
    virtual void leaveEvent(QEvent *leaveEvent);
    virtual void resizeEvent(QResizeEvent *resizeEvent);
    virtual void timerEvent(QTimerEvent *timerEvent);
    virtual void moveEvent(QMoveEvent *moveEvent);
    virtual void paintEvent(QPaintEvent *paintEvent);
    virtual void mousePressEvent(QMouseEvent *mouseEvent);
    virtual void mouseDoubleClickEvent(QMouseEvent *mouseEvent);
    virtual void mouseReleaseEvent(QMouseEvent *mouseEvent);
    virtual void mouseMoveEvent(QMouseEvent *mouseEvent);
    virtual void keyPressEvent(QKeyEvent *keyEvent);
    virtual void contextMenuEvent(QContextMenuEvent *contextMenuEvent);
    virtual void focusInEvent(QFocusEvent *focusInEvent);
    virtual void focusOutEvent(QFocusEvent *focusOutEvent);
    virtual void childEvent(QChildEvent *childEvent);
};
