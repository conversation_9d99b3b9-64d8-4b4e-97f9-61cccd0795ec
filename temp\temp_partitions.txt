frp
recovery_b
userdata
xbl_config_b
qupfw_b
dtbo_b
imagefv_b
fw_ufs1_b
oem_dycnvbk
devcfg_b
keystore
opproduct_b
vbmeta_b
sdc
devcfg_a
vbmeta_a
uefisecapp_b
uefisecapp_a
dip
cmnlib64_a
qupfw_a
dtbo_a
imagefv_a
LOGO_b
vbmeta_system_a
misc
mdm_oem_stanvbk
config
vbmeta_system_b
xbl_b
fw_ufs2_b
secdata
mdm1oemnvbktmp
fw_ufs5_b
catecontentfv
aging
reserve2
rawdump
op1
reserve4
storsec_a
modem_b
recovery_a
fw_ufs1_a
fw_ufs4_b
catefv
fw_ufs3_b
fw_ufs3_a
multiimgoem_b
fw_ufs5_a
reserve1
aging_mod
aop_b
reserve3
boot_a
devinfo
keymaster_a
fw_ufs2_a
core_nhlos
cmnlib_a
fw_ufs4_a
tz_a
sdd
multiimgoem_a
ALIGN_TO_128K_2
tz_b
apdp
mdm1m9kefs2
msadp
mdm1m9kefs3
xbl_a
mdmddr
modemst2
fsc
opproduct_a
ssd
mdm_oem_dycnvbk
param
splash
modem_a
oem_stanvbk
toolsfv
mdtpsecapp_a
cateloader
mdtp_b
abl_a
dsp_b
LOGO_a
storsec_b
boot_b
spunvm
hyp_a
aop_a
limits
bluetooth_a
mdtpsecapp_b
logfs
mdtp_a
abl_b
logdump
dsp_a
keymaster_b
hyp_b
uefivarstore
cmnlib_b
bluetooth_b
sde
cmnlib64_b
sdb
xbl_config_a
ddr
cdt
ALIGN_TO_128K_1
modemst1
fsg
mdm1m9kefsc
mdm1m9kefs1
sdf
super
metadata
ADB
adl
my_carrier
1
my_heytap
my_bigball_a
my_carrier_a
my_company_a
my_engineering_a
my_heytap_a
my_manifest_a
my_preload_a
my_product_a
my_region_a
my_stock_a
odm_a
product_a
system_a
system_ext_a
vendor_a