// qsgtextnode.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_7_0 -)

class QSGTextNode : public QSGTransformNode /NoDefaultCtors/
{
%TypeHeaderCode
#include <qsgtextnode.h>
%End

public:
    enum RenderType
    {
        QtRendering,
        NativeRendering,
        CurveRendering,
    };

    enum TextStyle
    {
        Normal,
        Outline,
        Raised,
        Sunken,
    };

    virtual ~QSGTextNode();
    void addTextDocument(QPointF position, QTextDocument *document, int selectionStart = -1, int selectionCount = -1);
    void addTextLayout(QPointF position, QTextLayout *layout, int selectionStart = -1, int selectionCount = -1, int lineStart = 0, int lineCount = -1);
    virtual void setColor(QColor color) = 0;
    virtual QColor color() const = 0;
    virtual void setTextStyle(QSGTextNode::TextStyle textStyle) = 0;
    virtual QSGTextNode::TextStyle textStyle() = 0;
    virtual void setStyleColor(QColor styleColor) = 0;
    virtual QColor styleColor() const = 0;
    virtual void setLinkColor(QColor linkColor) = 0;
    virtual QColor linkColor() const = 0;
    virtual void setSelectionColor(QColor selectionColor) = 0;
    virtual QColor selectionColor() const = 0;
    virtual void setSelectionTextColor(QColor selectionTextColor) = 0;
    virtual QColor selectionTextColor() const = 0;
    virtual void setRenderType(QSGTextNode::RenderType renderType) = 0;
    virtual QSGTextNode::RenderType renderType() const = 0;
    virtual void setRenderTypeQuality(int renderTypeQuality) = 0;
    virtual int renderTypeQuality() const = 0;
    virtual void setFiltering(QSGTexture::Filtering) = 0;
    virtual QSGTexture::Filtering filtering() const = 0;
    virtual void clear() = 0;
    virtual void setViewport(const QRectF &viewport) = 0;
    virtual QRectF viewport() const = 0;
};

%End
