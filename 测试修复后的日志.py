# -*- coding: utf-8 -*-
"""
测试修复后的日志显示
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_fixed_log_display():
    """测试修复后的日志显示"""
    print("🔍 测试修复后的日志显示...")
    
    try:
        from qz4n import FlashThread
        from wr3j import ColorOS15Function
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建刷机功能实例
        flash_function = ColorOS15Function()
        
        # 创建FlashThread
        flash_thread = FlashThread(flash_function, "test_folder")
        
        # 创建日志接收器
        received_logs = []
        def log_receiver(message, level):
            received_logs.append((message, level))
            print(f"📝 接收到日志: [{level}] {message}")
        
        # 连接信号
        flash_thread.log_signal.connect(log_receiver)
        
        # 测试handle_flash方法的类型
        print(f"\nhandle_flash方法: {flash_function.handle_flash}")
        
        # 检查修复
        with open('wr3j.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'threading.Thread(target=self._do_flash)' in content:
            print("❌ wr3j.py仍在使用threading.Thread")
        else:
            print("✅ wr3j.py已修复，不再使用threading.Thread")
        
        if 'return self._do_flash()' in content:
            print("✅ wr3j.py现在直接调用_do_flash()")
        else:
            print("❌ wr3j.py没有直接调用_do_flash()")
        
        # 测试日志处理器
        print("\n🧪 测试日志处理器...")
        flash_function.add_log("测试开始刷机日志", "info")
        
        # 等待信号处理
        app.processEvents()
        
        print(f"\n📊 接收到的日志数量: {len(received_logs)}")
        
        if len(received_logs) > 0:
            print("✅ 日志处理器工作正常!")
            print("\n💡 现在刷机日志应该能正常显示了:")
            print("1. FlashThread在初始化时设置log_handler")
            print("2. wr3j.py的handle_flash直接执行_do_flash")
            print("3. 所有add_log调用都会通过log_signal发送到UI")
        else:
            print("❌ 日志处理器仍有问题")
        
        app.quit()
        return len(received_logs) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_fixed_log_display()
