// qpoint.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPoint
{
%TypeHeaderCode
#include <qpoint.h>
%End

%PickleCode
    sipRes = Py_BuildValue("ii", sipCpp->x(), sipCpp->y());
%End

public:
    int manhattanLength() const;
    QPoint();
    QPoint(int xpos, int ypos);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        if (sipCpp->isNull())
        {
            sipRes = PyUnicode_FromString("PyQt6.QtCore.QPoint()");
        }
        else
        {
            sipRes = PyUnicode_FromFormat(
                    "PyQt6.QtCore.QPoint(%i, %i)", sipCpp->x(), sipCpp->y());
        }
%End

    bool isNull() const;
    int __bool__() const;
%MethodCode
        sipRes = !sipCpp->isNull();
%End

    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

    int x() const;
    int y() const;
    void setX(int xpos);
    void setY(int ypos);
    QPoint &operator+=(const QPoint &p);
    QPoint &operator-=(const QPoint &p);
    QPoint &operator*=(int c /Constrained/);
    QPoint &operator*=(double c);
    QPoint &operator/=(qreal c);
    static int dotProduct(const QPoint &p1, const QPoint &p2);
    QPoint transposed() const;
%If (Qt_6_4_0 -)
    QPointF toPointF() const;
%End
};

QDataStream &operator<<(QDataStream &, const QPoint &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QPoint & /Constrained/) /ReleaseGIL/;
bool operator==(const QPoint &p1, const QPoint &p2);
bool operator!=(const QPoint &p1, const QPoint &p2);
QPoint operator+(const QPoint &p);
QPoint operator+(const QPoint &p1, const QPoint &p2);
QPoint operator-(const QPoint &p);
QPoint operator-(const QPoint &p1, const QPoint &p2);
QPoint operator*(const QPoint &p, int factor /Constrained/);
QPoint operator*(int factor /Constrained/, const QPoint &p);
QPoint operator*(const QPoint &p, double factor);
QPoint operator*(double factor, const QPoint &p);
QPoint operator/(const QPoint &p, qreal c);

class QPointF
{
%TypeHeaderCode
#include <qpoint.h>
%End

%PickleCode
    sipRes = Py_BuildValue("dd", sipCpp->x(), sipCpp->y());
%End

public:
    QPointF();
    QPointF(qreal xpos, qreal ypos);
    QPointF(const QPoint &p);
    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        if (sipCpp->isNull())
        {
            sipRes = PyUnicode_FromString("PyQt6.QtCore.QPointF()");
        }
        else
        {
            PyObject *x = PyFloat_FromDouble(sipCpp->x());
            PyObject *y = PyFloat_FromDouble(sipCpp->y());
        
            if (x && y)
                sipRes = PyUnicode_FromFormat("PyQt6.QtCore.QPointF(%R, %R)", x, y);
        
            Py_XDECREF(x);
            Py_XDECREF(y);
        }
%End

    bool isNull() const;
    int __bool__() const;
%MethodCode
        sipRes = !sipCpp->isNull();
%End

    qreal x() const;
    qreal y() const;
    void setX(qreal xpos);
    void setY(qreal ypos);
    QPointF &operator+=(const QPointF &p);
    QPointF &operator-=(const QPointF &p);
    QPointF &operator*=(qreal c);
    QPointF &operator/=(qreal c);
    QPoint toPoint() const;
    qreal manhattanLength() const;
    static qreal dotProduct(const QPointF &p1, const QPointF &p2);
    QPointF transposed() const;
};

QDataStream &operator<<(QDataStream &, const QPointF &) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QPointF & /Constrained/) /ReleaseGIL/;
bool operator==(const QPointF &p1, const QPointF &p2);
bool operator!=(const QPointF &p1, const QPointF &p2);
QPointF operator+(const QPointF &p);
QPointF operator+(const QPointF &p1, const QPointF &p2);
QPointF operator-(const QPointF &p);
QPointF operator-(const QPointF &p1, const QPointF &p2);
QPointF operator*(const QPointF &p, qreal c);
QPointF operator*(qreal c, const QPointF &p);
QPointF operator/(const QPointF &p, qreal divisor);
