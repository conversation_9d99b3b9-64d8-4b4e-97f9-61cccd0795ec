// qpdfview.sip generated by MetaSIP
//
// This file is part of the QtPdfWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPdfView : public QAbstractScrollArea
{
%TypeHeaderCode
#include <qpdfview.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
    #if QT_VERSION >= 0x060600
        {sipName_QPdfPageSelector, &sipType_QPdfPageSelector, -1, 1},
    #else
        {0, 0, -1, 1},
    #endif
        {sipName_QPdfView, &sipType_QPdfView, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    enum class PageMode
    {
        SinglePage,
        MultiPage,
    };

    enum class ZoomMode
    {
        Custom,
        FitToWidth,
        FitInView,
    };

    explicit QPdfView(QWidget *parent /TransferThis/);
    virtual ~QPdfView();
    void setDocument(QPdfDocument *document);
    QPdfDocument *document() const;
    QPdfPageNavigator *pageNavigator() const;
    QPdfView::PageMode pageMode() const;
    QPdfView::ZoomMode zoomMode() const;
    qreal zoomFactor() const;
    int pageSpacing() const;
    void setPageSpacing(int spacing);
    QMargins documentMargins() const;
    void setDocumentMargins(QMargins margins);

public slots:
    void setPageMode(QPdfView::PageMode mode);
    void setZoomMode(QPdfView::ZoomMode mode);
    void setZoomFactor(qreal factor);

signals:
    void documentChanged(QPdfDocument *document);
    void pageModeChanged(QPdfView::PageMode pageMode);
    void zoomModeChanged(QPdfView::ZoomMode zoomMode);
    void zoomFactorChanged(qreal zoomFactor);
    void pageSpacingChanged(int pageSpacing);
    void documentMarginsChanged(QMargins documentMargins);

protected:
    virtual void paintEvent(QPaintEvent *event);
    virtual void resizeEvent(QResizeEvent *event);
    virtual void scrollContentsBy(int dx, int dy);

public:
%If (Qt_6_6_0 -)
    QPdfSearchModel *searchModel() const;
%End
%If (Qt_6_6_0 -)
    void setSearchModel(QPdfSearchModel *searchModel /KeepReference/);
%End
%If (Qt_6_6_0 -)
    int currentSearchResultIndex() const;
%End

public slots:
%If (Qt_6_6_0 -)
    void setCurrentSearchResultIndex(int currentResult);
%End

signals:
%If (Qt_6_6_0 -)
    void searchModelChanged(QPdfSearchModel *searchModel);
%End
%If (Qt_6_6_0 -)
    void currentSearchResultIndexChanged(int currentResult);
%End

protected:
%If (Qt_6_6_0 -)
    virtual void mousePressEvent(QMouseEvent *event);
%End
%If (Qt_6_6_0 -)
    virtual void mouseMoveEvent(QMouseEvent *event);
%End
%If (Qt_6_6_0 -)
    virtual void mouseReleaseEvent(QMouseEvent *event);
%End
};
