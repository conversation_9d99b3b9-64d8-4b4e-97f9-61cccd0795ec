# -*- coding: utf-8 -*-
"""
验证设备检查位置修改
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_device_check_position():
    """验证设备检查位置修改"""
    print("🔍 验证设备检查位置修改...")
    
    try:
        # 读取源代码文件
        with open('wr3j.py', 'r', encoding='utf-8') as f:
            wr3j_content = f.read()
            
        with open('bv8k.py', 'r', encoding='utf-8') as f:
            bv8k_content = f.read()
        
        # 检查设备检查的新位置
        print("\n📋 检查设备检查的新位置:")
        
        # 检查wr3j.py中的位置
        wr3j_lines = wr3j_content.split('\n')
        switch_mode_line = -1
        device_check_line = -1
        
        for i, line in enumerate(wr3j_lines):
            if "正在切换模式...请勿触碰设备" in line:
                switch_mode_line = i + 1
            if "✅ 设备在Fastboot模式，可以继续操作" in line and "current_mode" in wr3j_lines[i-1] if i > 0 else False:
                device_check_line = i + 1
        
        if switch_mode_line > 0 and device_check_line > 0:
            if device_check_line > switch_mode_line and device_check_line - switch_mode_line < 10:
                print(f"✅ wr3j.py: 设备检查在切换模式后 (第{switch_mode_line}行 → 第{device_check_line}行)")
            else:
                print(f"❌ wr3j.py: 设备检查位置不正确")
        else:
            print(f"❌ wr3j.py: 未找到正确的位置标记")
        
        # 检查bv8k.py中的位置
        bv8k_lines = bv8k_content.split('\n')
        switch_mode_line = -1
        device_check_line = -1
        
        for i, line in enumerate(bv8k_lines):
            if "切换模式中——请勿干扰,触碰" in line:
                switch_mode_line = i + 1
            if "✅ 设备在Fastboot模式，可以继续操作" in line and "current_mode" in bv8k_lines[i-1] if i > 0 else False:
                device_check_line = i + 1
        
        if switch_mode_line > 0 and device_check_line > 0:
            if device_check_line > switch_mode_line and device_check_line - switch_mode_line < 15:
                print(f"✅ bv8k.py: 设备检查在切换模式后 (第{switch_mode_line}行 → 第{device_check_line}行)")
            else:
                print(f"❌ bv8k.py: 设备检查位置不正确")
        else:
            print(f"❌ bv8k.py: 未找到正确的位置标记")
        
        # 检查是否移除了原来的刷机后检查
        print("\n📋 检查是否移除了原来的刷机后检查:")
        
        # 检查完成部分是否还有设备检查
        wr3j_has_old_check = "self.check_post_flash_mode()" in wr3j_content
        bv8k_has_old_check = "self.check_post_flash_mode()" in bv8k_content
        
        if not wr3j_has_old_check:
            print("✅ wr3j.py: 已移除完成后的设备检查")
        else:
            print("❌ wr3j.py: 仍有完成后的设备检查")
            
        if not bv8k_has_old_check:
            print("✅ bv8k.py: 已移除完成后的设备检查")
        else:
            print("❌ bv8k.py: 仍有完成后的设备检查")
        
        print("\n🎯 修改后的日志流程:")
        print("1. 开始刷机...")
        print("2. 载入刷机")
        print("3. 正在切换模式...请勿触碰设备")
        print("4. ✅ 设备在Fastboot模式，可以继续操作  ← 新位置")
        print("5. 在检查cow")
        print("6. Loading.. [X/Y](请勿断开设备) 进度：X%")
        print("7. partition OK")
        print("8. 进度：100%")
        print("9. 完成")
        
        # 总体检查
        all_good = (
            switch_mode_line > 0 and device_check_line > 0 and
            device_check_line > switch_mode_line and
            not wr3j_has_old_check and not bv8k_has_old_check
        )
        
        if all_good:
            print("\n🎉 设备检查位置修改成功!")
            print("现在设备检查会在切换模式后立即进行，而不是在刷机完成后")
        else:
            print("\n❌ 发现问题，请检查修改")
        
        return all_good
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    verify_device_check_position()
