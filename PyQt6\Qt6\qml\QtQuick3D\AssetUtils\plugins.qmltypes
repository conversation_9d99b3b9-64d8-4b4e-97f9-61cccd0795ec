import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquick3druntimeloader_p.h"
        name: "QQuick3DRuntimeLoader"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: [
            "QtQuick3D.AssetUtils/RuntimeLoader 6.2",
            "QtQuick3D.AssetUtils/RuntimeLoader 6.7"
        ]
        exportMetaObjectRevisions: [1538, 1543]
        Enum {
            name: "Status"
            values: ["Empty", "Success", "Error"]
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "errorString"
            type: "QString"
            read: "errorString"
            notify: "errorStringChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "bounds"
            type: "QQuick3DBounds3"
            read: "bounds"
            notify: "boundsChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "instancing"
            type: "QQuick3DInstancing"
            isPointer: true
            read: "instancing"
            write: "setInstancing"
            notify: "instancingChanged"
            index: 4
        }
        Property {
            name: "supportedExtensions"
            revision: 1543
            type: "QStringList"
            read: "supportedExtensions"
            index: 5
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "supportedMimeTypes"
            revision: 1543
            type: "QMimeType"
            isList: true
            read: "supportedMimeTypes"
            index: 6
            isReadonly: true
            isConstant: true
        }
        Signal { name: "sourceChanged" }
        Signal { name: "statusChanged" }
        Signal { name: "errorStringChanged" }
        Signal { name: "boundsChanged" }
        Signal { name: "instancingChanged" }
    }
}
