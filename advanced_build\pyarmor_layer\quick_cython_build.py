# -*- coding: utf-8 -*-
"""
快速Cython编译脚本 - 逐个编译核心算法
避免卡住，确保每个模块都能成功编译
"""

import os
import sys
import shutil
import subprocess
import time
from pathlib import Path

# 核心算法模块
CORE_MODULES = [
    "coloros.py",
    "coloros15.py", 
    "utils.py",
    "fastboodt.py",
    "zhidinyishuaxie.py",
    "payload_extractor.py",
    "custom_messagebox.py",
    "genduodakhd.py",
    "font_extractor.py",
]

def log(message):
    """简单日志"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def compile_single_module(module_file):
    """编译单个模块"""
    log(f"编译模块: {module_file}")
    
    if not os.path.exists(module_file):
        log(f"❌ 文件不存在: {module_file}")
        return False
    
    # 创建简单的setup.py
    module_name = module_file.replace('.py', '_cython')
    setup_content = f'''
from setuptools import setup, Extension
from Cython.Build import cythonize

ext = Extension(
    "{module_name}",
    ["{module_file}"],
    language='c++'
)

setup(
    ext_modules=cythonize([ext], language_level=3),
    zip_safe=False,
)
'''
    
    with open("temp_setup.py", "w", encoding="utf-8") as f:
        f.write(setup_content)
    
    try:
        # 执行编译
        cmd = [sys.executable, "temp_setup.py", "build_ext", "--inplace"]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        # 清理临时文件
        if os.path.exists("temp_setup.py"):
            os.remove("temp_setup.py")
        
        if result.returncode == 0:
            # 检查是否生成了.pyd文件
            pyd_file = f"{module_name}.pyd"
            if os.path.exists(pyd_file):
                size = os.path.getsize(pyd_file)
                log(f"✅ 编译成功: {pyd_file} ({size:,} 字节)")
                return True
            else:
                log(f"⚠️ 编译完成但未找到输出文件")
        else:
            log(f"❌ 编译失败: {result.stderr[:200]}")
        
        return False
        
    except subprocess.TimeoutExpired:
        log(f"❌ 编译超时: {module_file}")
        return False
    except Exception as e:
        log(f"❌ 编译异常: {e}")
        return False

def quick_compile_all():
    """快速编译所有核心模块"""
    log("🚀 开始快速Cython编译")
    log("=" * 50)
    
    # 准备编译目录
    build_dir = "quick_cython"
    if os.path.exists(build_dir):
        shutil.rmtree(build_dir)
    os.makedirs(build_dir)
    
    original_dir = os.getcwd()
    compiled_modules = {}
    
    try:
        os.chdir(build_dir)
        
        # 逐个编译模块
        for module in CORE_MODULES:
            src_path = os.path.join("..", module)
            if os.path.exists(src_path):
                # 复制到编译目录
                shutil.copy2(src_path, module)
                
                # 编译模块
                if compile_single_module(module):
                    pyd_file = f"{module.replace('.py', '_cython')}.pyd"
                    if os.path.exists(pyd_file):
                        compiled_modules[module] = pyd_file
                else:
                    log(f"⚠️ 跳过失败的模块: {module}")
            else:
                log(f"❌ 源文件不存在: {module}")
        
        os.chdir(original_dir)
        
        # 报告结果
        log("\n" + "=" * 50)
        log("📊 编译结果统计")
        log(f"总模块数: {len(CORE_MODULES)}")
        log(f"成功编译: {len(compiled_modules)}")
        log(f"成功率: {len(compiled_modules)/len(CORE_MODULES)*100:.1f}%")
        
        if compiled_modules:
            log("\n✅ 成功编译的模块:")
            for original, compiled in compiled_modules.items():
                compiled_path = os.path.join(build_dir, compiled)
                if os.path.exists(compiled_path):
                    size = os.path.getsize(compiled_path)
                    log(f"  {original} -> {compiled} ({size:,} 字节)")
            
            # 保存映射
            import json
            mapping_data = {
                "modules": compiled_modules,
                "total_modules": len(compiled_modules),
                "compile_time": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            mapping_file = os.path.join(build_dir, "cython_mapping.json")
            with open(mapping_file, 'w', encoding='utf-8') as f:
                json.dump(mapping_data, f, indent=2, ensure_ascii=False)
            
            log(f"✅ 映射文件已保存: {mapping_file}")
            return True, build_dir, compiled_modules
        else:
            log("❌ 没有成功编译任何模块")
            return False, None, {}
            
    except Exception as e:
        if 'original_dir' in locals():
            os.chdir(original_dir)
        log(f"❌ 编译过程异常: {e}")
        return False, None, {}

def create_nuitka_with_cython(build_dir, compiled_modules):
    """创建包含Cython模块的Nuitka版本"""
    log("🔧 创建Cython分层保护版")
    
    if not compiled_modules:
        log("❌ 没有可用的Cython模块")
        return False
    
    # 构建Nuitka命令
    nuitka_cmd = [
        sys.executable, "-m", "nuitka",
        "--standalone",
        "--onefile",
        "--onefile-tempdir-spec={TEMP}\\syiming\\cython_{PID}_{TIME}",
        "--windows-console-mode=disable",
        "--enable-plugin=pyqt6",
        "--assume-yes-for-downloads",
        "--show-progress",
        "--show-memory",
        "--remove-output",
        "--output-filename=益民欧加真固件刷写工具_Cython分层保护版.exe",
    ]
    
    # 添加图标
    if os.path.exists("ico/icon.ico"):
        nuitka_cmd.append("--windows-icon-from-ico=ico/icon.ico")
        log("✅ 添加图标文件")
    
    # 添加Cython编译模块
    for original, compiled in compiled_modules.items():
        compiled_path = os.path.join(build_dir, compiled)
        if os.path.exists(compiled_path):
            nuitka_cmd.append(f"--include-data-file={compiled_path}={compiled}")
            log(f"包含Cython模块: {compiled}")
    
    # 添加映射文件
    mapping_file = os.path.join(build_dir, "cython_mapping.json")
    if os.path.exists(mapping_file):
        nuitka_cmd.append(f"--include-data-file={mapping_file}=cython_mapping.json")
        log("包含Cython映射文件")
    
    # 添加ADBTools
    if os.path.exists("ADBTools"):
        for file in os.listdir("ADBTools"):
            file_path = os.path.join("ADBTools", file)
            if os.path.isfile(file_path):
                nuitka_cmd.append(f"--include-data-file={file_path}=ADBTools/{file}")
        log("✅ 包含ADBTools目录")
    
    # 添加其他资源
    for dir_name in ["ico", "tup"]:
        if os.path.exists(dir_name):
            nuitka_cmd.append(f"--include-data-dir={dir_name}={dir_name}")
            log(f"包含目录: {dir_name}")
    
    # 添加主脚本
    nuitka_cmd.append("main.py")
    
    log("🚀 开始Nuitka编译...")
    try:
        result = subprocess.run(nuitka_cmd, timeout=1800)
        
        if result.returncode == 0:
            output_file = "益民欧加真固件刷写工具_Cython分层保护版.exe"
            if os.path.exists(output_file):
                size = os.path.getsize(output_file)
                log(f"🎉 Cython分层保护版构建成功!")
                log(f"📁 输出文件: {output_file}")
                log(f"📦 文件大小: {size:,} 字节 ({size/1024/1024:.1f} MB)")
                
                # 移动到输出目录
                final_dir = "advanced_build/final_output"
                os.makedirs(final_dir, exist_ok=True)
                final_path = os.path.join(final_dir, output_file)
                shutil.move(output_file, final_path)
                log(f"📁 已移动到: {final_path}")
                
                return True
        
        log("❌ Nuitka编译失败")
        return False
        
    except Exception as e:
        log(f"❌ Nuitka编译异常: {e}")
        return False

def main():
    """主函数"""
    log("🔒 快速Cython编译工具")
    log("专门解决编译卡住问题")
    log("=" * 50)
    
    # 步骤1: 快速编译所有模块
    success, build_dir, compiled_modules = quick_compile_all()
    
    if not success:
        log("❌ Cython编译失败")
        return False
    
    log(f"\n🎉 Cython编译成功! 编译了 {len(compiled_modules)}/{len(CORE_MODULES)} 个模块")
    
    # 步骤2: 询问是否继续Nuitka编译
    choice = input("\n是否继续创建Cython分层保护版exe？(y/n): ").lower()
    if choice == 'y':
        if create_nuitka_with_cython(build_dir, compiled_modules):
            log("\n🎉 Cython分层保护版构建完成!")
            log("🔒 实现的保护:")
            log(f"   内层: {len(compiled_modules)} 个核心算法Cython编译")
            log("   外层: Nuitka编译")
        else:
            log("\n❌ Nuitka编译失败")
    
    return True

if __name__ == "__main__":
    main()
