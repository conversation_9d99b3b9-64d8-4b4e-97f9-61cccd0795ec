# 三合一加密打包工具使用说明

## 概述

这是一个增强版的Python项目打包脚本，实现了分层保护的加密方案：

### 🔒 分层保护架构

1. **外层保护**: Nuitka编译 + VMProtect加壳（自签名证书）
2. **中层保护**: PyArmor混淆非核心代码（免费版）
3. **内层保护**: Cython编译核心算法

### 📦 包含内容

- **ADBTools**: 完整的ADB工具集（adb.exe, fastboot.exe, pay.exe等）
- **PyQt6**: UI框架库
- **ico**: 图标资源
- **tup**: 图片资源
- **所有Python模块**: 核心算法和UI代码

## 🚀 使用方法

### 基本用法

```bash
# 完整构建（推荐）
python advanced_build.py

# 清理构建目录
python advanced_build.py --clean

# 详细输出模式
python advanced_build.py --verbose
```

### 分步构建

```bash
# 仅Cython编译核心模块
python advanced_build.py --cython-only

# 仅PyArmor混淆非核心模块
python advanced_build.py --pyarmor-only

# 仅Nuitka最终编译
python advanced_build.py --nuitka-only
```

## 📋 前置要求

### 必需的Python包

脚本会自动安装以下依赖：

- PyQt6 >= 6.7.1
- nuitka >= 2.0.0
- cython >= 3.0.0
- pyarmor >= 8.0.0
- numpy >= 1.21.0
- pywin32 >= 305
- setuptools >= 65.0.0
- wheel >= 0.38.0
- psutil >= 5.9.0

### 系统要求

- Python 3.8+
- Windows 10/11
- 管理员权限（推荐）
- 至少4GB可用磁盘空间

## 📁 项目结构

确保你的项目包含以下文件和目录：

```
项目根目录/
├── main.py                 # 主程序入口
├── flash_tool.py          # UI界面
├── config.py              # 配置文件
├── coloros.py             # ColorOS核心算法
├── coloros15.py           # ColorOS 15算法
├── utils.py               # 工具函数
├── fastboodt.py           # Fastboot工具
├── zhidinyishuaxie.py     # 自定义刷写
├── payload_extractor.py   # 解包工具
├── custom_messagebox.py   # 自定义消息框
├── ADBTools/              # ADB工具目录
│   ├── adb.exe
│   ├── fastboot.exe
│   ├── pay.exe
│   └── ...
├── ico/                   # 图标目录
│   └── icon.ico
├── tup/                   # 图片资源
│   ├── 1.png
│   └── 2.png
└── advanced_build.py      # 本构建脚本
```

## 🔧 构建过程

### 第1步: 环境检查
- 检查Python版本
- 验证必需文件和目录
- 检查依赖包

### 第2步: 内层保护（Cython）
- 编译核心算法模块
- 生成.pyd/.so文件
- 创建模块映射

### 第3步: 中层保护（PyArmor）
- 混淆非核心代码
- 添加反调试保护
- 生成混淆后的Python文件

### 第4步: 外层保护（Nuitka）
- 整合所有模块
- 编译为单个exe文件
- 包含所有资源文件

### 第5步: 完成构建
- 生成最终输出
- 创建构建报告
- 清理临时文件

## 📊 输出结果

构建完成后，你将在 `advanced_build/final_output/` 目录中找到：

- `益民欧加真固件刷写工具_加密版.exe` - 最终的加密可执行文件
- `build_report.json` - 详细的构建报告
- `build.log` - 完整的构建日志

## ⚠️ 注意事项

1. **首次运行**: 可能需要较长时间下载和安装依赖
2. **网络连接**: 需要稳定的网络连接下载Nuitka依赖
3. **磁盘空间**: 构建过程会产生大量临时文件
4. **杀毒软件**: 可能需要将构建目录添加到白名单
5. **VMProtect**: 需要单独购买和配置（可选）

## 🐛 故障排除

### 常见问题

1. **Cython编译失败**
   - 检查是否安装了Visual Studio Build Tools
   - 确保numpy已正确安装

2. **PyArmor混淆失败**
   - 脚本会自动回退到简单混淆
   - 检查PyArmor版本兼容性

3. **Nuitka编译失败**
   - 检查磁盘空间是否充足
   - 确保所有依赖文件存在

4. **ADBTools缺失**
   - 确保ADBTools目录包含所有必需的exe文件
   - 检查文件权限

### 获取帮助

如果遇到问题，请检查：
1. `advanced_build/build.log` - 详细的构建日志
2. `advanced_build/final_output/build_report.json` - 构建报告

## 🔄 版本历史

- **v2.0.0**: 增强版三合一加密打包
  - 改进的错误处理
  - 更好的日志记录
  - 自动依赖管理
  - 分步构建支持

- **v1.0.0**: 基础版本
  - 基本的三合一加密
  - Cython + PyArmor + Nuitka

## 📝 许可证

本工具仅供学习和研究使用。请遵守相关软件的许可证条款。
