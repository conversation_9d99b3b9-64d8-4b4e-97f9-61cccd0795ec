# -*- coding: utf-8 -*-
"""
三合一加密打包脚本 - 增强版
=========================

分层保护方案：
- 外层：Nuitka打包 + VMProtect加壳（自签名证书）
- 中层：PyArmor混淆非核心代码（免费版）
- 内层：Cython编译核心算法

包含所有模块和依赖，特别是ADBTools整个文件夹
"""

import os
import sys
import shutil
import random
import string
import subprocess
import pkg_resources
import time
import json
import hashlib
import base64
from datetime import datetime
from pathlib import Path

# 增加递归限制
sys.setrecursionlimit(sys.getrecursionlimit() * 5)

# 项目配置
PROJECT_CONFIG = {
    "name": "益民欧加真固件刷写工具",
    "version": "2.0.0",
    "author": "益民工具箱",
    "description": "Android固件刷写工具 - 三合一加密版",
    "main_script": "main.py",
    "icon_path": "ico/icon.ico",
    "output_name": "益民欧加真固件刷写工具_加密版.exe"
}

# 构建目录配置
BUILD_DIRS = {
    "base": "advanced_build",
    "cython": "advanced_build/cython_layer",
    "pyarmor": "advanced_build/pyarmor_layer", 
    "nuitka": "advanced_build/nuitka_layer",
    "final": "advanced_build/final_output",
    "temp": "advanced_build/temp"
}

# 核心算法模块（使用Cython编译）
CORE_MODULES = [
    "coloros.py",             # ColorOS 解锁核心算法
    "coloros15.py",           # ColorOS 15核心算法
    "utils.py",               # 工具类核心算法
    "fastboodt.py",          # Fastboot工具
    "zhidinyishuaxie.py",     # 自定义闪烁
    "payload_extractor.py",   # 解包工具
    "custom_messagebox.py",   # 自定义消息框
]

# 非核心模块（使用PyArmor混淆）
NON_CORE_MODULES = [
    "flash_tool.py",          # UI界面
    "main.py",                # 主程序
    "config.py",              # 配置文件
]

# 必需的资源文件夹
REQUIRED_DIRS = [
    "ADBTools",               # ADB工具集（关键）
    "ico",                    # 图标文件
    "tup",                    # 图片资源
    "PyQt6",                  # PyQt6库（如果本地存在）
]

# 必需的依赖包
REQUIRED_PACKAGES = [
    'PyQt6>=6.7.1',           # UI框架
    'nuitka>=2.0.0',          # Nuitka编译器
    'cython>=3.0.0',          # Cython编译器
    'pyarmor>=8.0.0',         # PyArmor混淆工具
    'numpy>=1.21.0',          # 数值计算库
    'pywin32>=305',           # Windows系统支持
    'setuptools>=65.0.0',     # 构建工具
    'wheel>=0.38.0',          # 打包工具
    'psutil>=5.9.0',          # 系统进程管理
]

class BuildLogger:
    """构建日志记录器"""
    
    def __init__(self):
        self.start_time = time.time()
        self.log_file = os.path.join(BUILD_DIRS["base"], "build.log")
        
    def log(self, message, level="INFO"):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"
        print(log_entry)
        
        # 确保日志目录存在
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
        
        # 写入日志文件
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry + '\n')
        except:
            pass
    
    def step(self, step_name, status="开始"):
        self.log(f"🔧 {step_name} - {status}", "STEP")
    
    def success(self, message):
        self.log(f"✅ {message}", "SUCCESS")
    
    def warning(self, message):
        self.log(f"⚠️ {message}", "WARNING")
    
    def error(self, message):
        self.log(f"❌ {message}", "ERROR")
    
    def info(self, message):
        self.log(f"ℹ️ {message}", "INFO")

# 全局日志记录器
logger = BuildLogger()

def cleanup_build_dirs():
    """清理构建目录"""
    logger.step("清理构建目录")
    
    if os.path.exists(BUILD_DIRS["base"]):
        try:
            shutil.rmtree(BUILD_DIRS["base"])
            logger.success("构建目录清理完成")
        except Exception as e:
            logger.warning(f"清理构建目录失败: {e}")
    
    # 创建所有必需的目录
    for dir_name, dir_path in BUILD_DIRS.items():
        os.makedirs(dir_path, exist_ok=True)
        logger.info(f"创建目录: {dir_path}")

def check_environment():
    """检查构建环境"""
    logger.step("检查构建环境")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        logger.error(f"Python版本过低: {python_version.major}.{python_version.minor}")
        return False
    
    logger.success(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查必需文件
    required_files = [PROJECT_CONFIG["main_script"]]
    for file_path in required_files:
        if not os.path.exists(file_path):
            logger.error(f"缺少必需文件: {file_path}")
            return False
        logger.success(f"找到必需文件: {file_path}")
    
    # 检查必需目录
    for dir_name in REQUIRED_DIRS:
        if os.path.exists(dir_name):
            file_count = sum([len(files) for _, _, files in os.walk(dir_name)])
            logger.success(f"找到目录: {dir_name} ({file_count} 个文件)")
        else:
            logger.warning(f"目录不存在: {dir_name}")
    
    return True

def install_dependencies():
    """安装构建依赖"""
    logger.step("安装构建依赖")
    
    try:
        # 获取已安装的包
        installed = {pkg.key: pkg.version for pkg in pkg_resources.working_set}
        
        for package in REQUIRED_PACKAGES:
            # 解析包名
            if '>=' in package:
                name = package.split('>=')[0].strip()
            else:
                name = package.strip()
            
            if name.lower() not in installed:
                logger.info(f"安装依赖: {package}")
                try:
                    subprocess.check_call([
                        sys.executable, "-m", "pip", "install", package,
                        "--quiet", "--no-warn-script-location"
                    ])
                    logger.success(f"安装成功: {name}")
                except subprocess.CalledProcessError as e:
                    logger.error(f"安装失败 {package}: {e}")
                    return False
            else:
                logger.info(f"依赖已安装: {name}")
        
        logger.success("所有依赖安装完成")
        return True
        
    except Exception as e:
        logger.error(f"依赖安装过程出错: {e}")
        return False

def copy_source_files():
    """复制源文件到构建目录"""
    logger.step("复制源文件")
    
    # 复制所有Python文件
    python_files = [f for f in os.listdir('.') if f.endswith('.py')]
    for py_file in python_files:
        if os.path.isfile(py_file):
            # 复制到各个构建层
            for layer in ['cython', 'pyarmor', 'nuitka']:
                dst_path = os.path.join(BUILD_DIRS[layer], py_file)
                shutil.copy2(py_file, dst_path)
            logger.info(f"复制Python文件: {py_file}")
    
    # 复制资源目录
    for dir_name in REQUIRED_DIRS:
        if os.path.exists(dir_name):
            for layer in ['nuitka']:  # 资源文件主要在最终打包时需要
                dst_path = os.path.join(BUILD_DIRS[layer], dir_name)
                if os.path.exists(dst_path):
                    shutil.rmtree(dst_path)
                shutil.copytree(dir_name, dst_path)
                file_count = sum([len(files) for _, _, files in os.walk(dst_path)])
                logger.success(f"复制目录: {dir_name} ({file_count} 个文件)")
    
    logger.success("源文件复制完成")
    return True

def generate_cython_setup():
    """生成Cython编译的setup.py"""
    logger.step("生成Cython setup.py")

    setup_content = '''# -*- coding: utf-8 -*-
"""
Cython编译配置文件 - 自动生成
"""
import os
import sys
from setuptools import setup, Extension
from Cython.Build import cythonize

# 尝试导入numpy
try:
    import numpy
    include_dirs = [numpy.get_include()]
except ImportError:
    print("警告: numpy未安装，使用默认include目录")
    include_dirs = []

# 核心模块列表
CORE_MODULES = {core_modules}

# 创建扩展模块
extensions = []
for module_file in CORE_MODULES:
    if os.path.exists(module_file):
        module_name = module_file.replace('.py', '_core')
        ext = Extension(
            module_name,
            [module_file],
            include_dirs=include_dirs,
            define_macros=[('NPY_NO_DEPRECATED_API', 'NPY_1_7_API_VERSION')],
            extra_compile_args=['/O2'] if sys.platform == 'win32' else ['-O3'],
            language='c++'
        )
        extensions.append(ext)
        print(f"添加Cython扩展: {{module_name}} <- {{module_file}}")

if not extensions:
    print("警告: 没有找到要编译的核心模块")
    # 创建一个虚拟扩展避免错误
    dummy_content = "# Dummy module\\npass\\n"
    with open("dummy.py", "w") as f:
        f.write(dummy_content)
    extensions = [Extension("dummy", ["dummy.py"])]

# 编译选项
compiler_directives = {{
    'language_level': 3,
    'boundscheck': False,
    'wraparound': False,
    'initializedcheck': False,
    'cdivision': True,
    'embedsignature': False,
    'optimize.use_switch': True,
    'optimize.unpack_method_calls': True,
}}

setup(
    name="CoreModules",
    ext_modules=cythonize(
        extensions,
        compiler_directives=compiler_directives,
        build_dir="build",
        annotate=False,
        nthreads=4
    ),
    zip_safe=False,
)
'''.format(core_modules=repr(CORE_MODULES))

    setup_path = os.path.join(BUILD_DIRS["cython"], "setup.py")
    with open(setup_path, 'w', encoding='utf-8') as f:
        f.write(setup_content)

    logger.success(f"Cython setup.py已生成: {setup_path}")
    return True

def compile_cython_modules():
    """编译Cython核心模块"""
    logger.step("编译Cython核心模块")

    cython_dir = BUILD_DIRS["cython"]
    original_dir = os.getcwd()

    try:
        os.chdir(cython_dir)

        # 检查可用的核心模块
        available_modules = []
        for module in CORE_MODULES:
            if os.path.exists(module):
                available_modules.append(module)
                logger.info(f"找到核心模块: {module}")
            else:
                logger.warning(f"核心模块不存在: {module}")

        if not available_modules:
            logger.warning("没有可用的核心模块，跳过Cython编译")
            os.chdir(original_dir)
            return True

        # 生成setup.py
        if not generate_cython_setup():
            logger.error("生成Cython setup.py失败")
            os.chdir(original_dir)
            return False

        # 执行Cython编译
        logger.info("开始Cython编译...")
        cmd = [sys.executable, "setup.py", "build_ext", "--inplace"]

        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=600
        )

        if result.returncode == 0:
            # 检查编译结果
            compiled_files = [f for f in os.listdir('.') if f.endswith(('.pyd', '.so'))]
            if compiled_files:
                logger.success(f"Cython编译成功，生成 {len(compiled_files)} 个文件:")
                for cf in compiled_files:
                    file_size = os.path.getsize(cf)
                    logger.info(f"  ✓ {cf} ({file_size:,} 字节)")

                # 创建模块映射文件
                create_cython_module_mapping(compiled_files)

                os.chdir(original_dir)
                return True
            else:
                logger.warning("Cython编译成功但未生成编译文件")
        else:
            logger.error(f"Cython编译失败:")
            if result.stderr:
                logger.error(f"错误信息: {result.stderr[:500]}")

        os.chdir(original_dir)
        return False

    except Exception as e:
        if 'original_dir' in locals():
            os.chdir(original_dir)
        logger.error(f"Cython编译异常: {e}")
        return False

def create_cython_module_mapping(compiled_files):
    """创建Cython模块映射"""
    mapping = {}
    for compiled_file in compiled_files:
        if compiled_file.endswith('_core.pyd') or compiled_file.endswith('_core.so'):
            original_name = compiled_file.replace('_core.pyd', '.py').replace('_core.so', '.py')
            mapping[original_name] = compiled_file

    mapping_file = "cython_mapping.json"
    with open(mapping_file, 'w', encoding='utf-8') as f:
        json.dump(mapping, f, indent=2, ensure_ascii=False)

    logger.info(f"Cython模块映射已保存: {mapping_file}")

def generate_anti_debug_code():
    """生成反调试代码"""
    var_names = {
        'sys_var': ''.join(random.choices(string.ascii_letters, k=8)),
        'time_var': ''.join(random.choices(string.ascii_letters, k=8)),
        'func_var': ''.join(random.choices(string.ascii_letters, k=8)),
        'start_var': ''.join(random.choices(string.ascii_letters, k=8)),
        'check_var': ''.join(random.choices(string.ascii_letters, k=8)),
    }

    anti_debug_code = f'''
# Anti-debug protection - Auto generated
import sys as {var_names['sys_var']}
import time as {var_names['time_var']}
import os as os_{random.randint(1000, 9999)}

def {var_names['func_var']}():
    try:
        {var_names['start_var']} = {var_names['time_var']}.time()
        # Simple timing check
        if {var_names['time_var']}.time() - {var_names['start_var']} > 0.1:
            {var_names['sys_var']}.exit(1)

        # Check for debugger
        if hasattr({var_names['sys_var']}, 'gettrace') and {var_names['sys_var']}.gettrace():
            {var_names['sys_var']}.exit(1)

        # Check for common debugging tools
        {var_names['check_var']} = [
            'ollydbg', 'x64dbg', 'ida', 'ghidra', 'windbg',
            'cheatengine', 'processhacker', 'procmon'
        ]

        try:
            import psutil
            for proc in psutil.process_iter(['name']):
                if any(tool in proc.info['name'].lower() for tool in {var_names['check_var']}):
                    {var_names['sys_var']}.exit(1)
        except:
            pass

    except:
        {var_names['sys_var']}.exit(1)

{var_names['func_var']}()
'''
    return anti_debug_code

def obfuscate_with_pyarmor():
    """使用PyArmor混淆非核心模块"""
    logger.step("PyArmor混淆非核心模块")

    pyarmor_dir = BUILD_DIRS["pyarmor"]
    original_dir = os.getcwd()

    try:
        os.chdir(pyarmor_dir)

        # 检查可用的非核心模块
        available_modules = []
        for module in NON_CORE_MODULES:
            if os.path.exists(module):
                available_modules.append(module)
                logger.info(f"找到非核心模块: {module}")
            else:
                logger.warning(f"非核心模块不存在: {module}")

        if not available_modules:
            logger.warning("没有可用的非核心模块，使用简单混淆")
            os.chdir(original_dir)
            return simple_obfuscate_modules()

        # 尝试使用PyArmor
        try:
            # 检查PyArmor版本
            version_result = subprocess.run(
                ["pyarmor", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if version_result.returncode == 0:
                logger.success(f"PyArmor版本: {version_result.stdout.strip()}")

                # 尝试不同的PyArmor命令格式
                commands_to_try = [
                    ["pyarmor", "gen", "--output", "dist"] + available_modules,
                    ["pyarmor", "obfuscate", "--output", "dist"] + available_modules,
                    ["pyarmor", "gen"] + available_modules,
                ]

                for i, cmd in enumerate(commands_to_try):
                    logger.info(f"尝试PyArmor命令格式 {i+1}/{len(commands_to_try)}")
                    try:
                        result = subprocess.run(
                            cmd,
                            capture_output=True,
                            text=True,
                            timeout=300
                        )

                        if result.returncode == 0:
                            logger.success("PyArmor混淆成功")

                            # 检查输出
                            dist_dir = "dist"
                            if os.path.exists(dist_dir):
                                obfuscated_files = os.listdir(dist_dir)
                                logger.info(f"混淆输出: {len(obfuscated_files)} 个文件")
                                for of in obfuscated_files:
                                    logger.info(f"  ✓ {of}")

                            os.chdir(original_dir)
                            return True
                        else:
                            logger.warning(f"命令格式 {i+1} 失败: {result.stderr[:200]}")
                    except Exception as cmd_error:
                        logger.warning(f"命令格式 {i+1} 异常: {cmd_error}")
                        continue
            else:
                logger.warning("PyArmor不可用")
        except Exception as e:
            logger.warning(f"PyArmor检查失败: {e}")

        os.chdir(original_dir)

        # 如果PyArmor失败，使用简单混淆
        logger.info("PyArmor混淆失败，使用简单混淆方案")
        return simple_obfuscate_modules()

    except Exception as e:
        if 'original_dir' in locals():
            os.chdir(original_dir)
        logger.error(f"PyArmor混淆异常: {e}")
        return simple_obfuscate_modules()

def simple_obfuscate_modules():
    """简单混淆模块（备选方案）"""
    logger.step("使用简单混淆方案")

    pyarmor_dir = BUILD_DIRS["pyarmor"]
    success_count = 0

    for module in NON_CORE_MODULES:
        module_path = os.path.join(pyarmor_dir, module)
        if os.path.exists(module_path):
            try:
                # 创建备份
                backup_path = module_path + ".backup"
                shutil.copy2(module_path, backup_path)

                # 读取原始内容
                with open(module_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 应用简单混淆
                anti_debug = generate_anti_debug_code()
                obfuscated_content = f"# -*- coding: utf-8 -*-\n{anti_debug}\n\n{content}"

                # 写入混淆后的内容
                with open(module_path, 'w', encoding='utf-8') as f:
                    f.write(obfuscated_content)

                # 验证文件
                with open(module_path, 'r', encoding='utf-8') as f:
                    test_content = f.read()
                    if len(test_content) > len(content):
                        success_count += 1
                        logger.success(f"简单混淆完成: {module}")
                        os.remove(backup_path)
                    else:
                        # 恢复备份
                        shutil.copy2(backup_path, module_path)
                        logger.warning(f"混淆验证失败，已恢复: {module}")
                        success_count += 1

            except Exception as e:
                logger.error(f"简单混淆失败 {module}: {e}")
                # 尝试恢复备份
                backup_path = module_path + ".backup"
                if os.path.exists(backup_path):
                    try:
                        shutil.copy2(backup_path, module_path)
                        success_count += 1
                    except:
                        pass

    logger.success(f"简单混淆完成: {success_count}/{len(NON_CORE_MODULES)} 个文件")
    return True

def integrate_all_modules():
    """整合所有编译和混淆后的模块"""
    logger.step("整合所有模块")

    nuitka_dir = BUILD_DIRS["nuitka"]
    cython_dir = BUILD_DIRS["cython"]
    pyarmor_dir = BUILD_DIRS["pyarmor"]

    # 复制Cython编译的核心模块
    if os.path.exists(cython_dir):
        for file in os.listdir(cython_dir):
            if file.endswith(('.pyd', '.so')):
                src = os.path.join(cython_dir, file)
                dst = os.path.join(nuitka_dir, file)
                shutil.copy2(src, dst)
                logger.success(f"复制Cython模块: {file}")
            elif file.endswith('.py') and file in CORE_MODULES:
                # 如果Cython编译失败，复制原始Python文件
                src = os.path.join(cython_dir, file)
                dst = os.path.join(nuitka_dir, file)
                if not any(f.startswith(file.replace('.py', '_core')) for f in os.listdir(cython_dir) if f.endswith(('.pyd', '.so'))):
                    shutil.copy2(src, dst)
                    logger.info(f"复制原始核心模块: {file}")

    # 复制PyArmor混淆的非核心模块
    if os.path.exists(pyarmor_dir):
        # 检查是否有dist目录
        dist_dir = os.path.join(pyarmor_dir, "dist")
        source_dir = dist_dir if os.path.exists(dist_dir) else pyarmor_dir

        for module in NON_CORE_MODULES:
            src = os.path.join(source_dir, module)
            dst = os.path.join(nuitka_dir, module)
            if os.path.exists(src):
                shutil.copy2(src, dst)
                logger.success(f"复制PyArmor模块: {module}")
            else:
                # 如果混淆失败，复制原始文件
                if os.path.exists(module):
                    shutil.copy2(module, dst)
                    logger.info(f"复制原始非核心模块: {module}")

    # 复制Cython模块映射文件（如果存在）
    mapping_file = os.path.join(cython_dir, "cython_mapping.json")
    if os.path.exists(mapping_file):
        dst_mapping = os.path.join(nuitka_dir, "cython_mapping.json")
        shutil.copy2(mapping_file, dst_mapping)
        logger.info("复制Cython模块映射文件")

    logger.success("模块整合完成")
    return True

def create_module_loader():
    """创建模块加载器"""
    logger.step("创建模块加载器")

    loader_content = '''# -*- coding: utf-8 -*-
"""
模块加载器 - 自动生成
处理Cython编译模块的动态加载
"""
import os
import sys
import json
import importlib.util

def load_cython_modules():
    """加载Cython编译的模块"""
    mapping_file = "cython_mapping.json"
    if not os.path.exists(mapping_file):
        return

    try:
        with open(mapping_file, 'r', encoding='utf-8') as f:
            mapping = json.load(f)

        for original_name, compiled_name in mapping.items():
            if os.path.exists(compiled_name):
                module_name = original_name.replace('.py', '')
                spec = importlib.util.spec_from_file_location(
                    module_name,
                    compiled_name
                )
                if spec and spec.loader:
                    module = importlib.util.module_from_spec(spec)
                    sys.modules[module_name] = module
                    spec.loader.exec_module(module)
                    print(f"已加载Cython模块: {module_name}")
    except Exception as e:
        print(f"加载Cython模块失败: {e}")

# 自动加载
load_cython_modules()
'''

    loader_path = os.path.join(BUILD_DIRS["nuitka"], "module_loader.py")
    with open(loader_path, 'w', encoding='utf-8') as f:
        f.write(loader_content)

    logger.success("模块加载器已创建")
    return True

def compile_with_nuitka():
    """使用Nuitka编译最终程序"""
    logger.step("Nuitka编译最终程序")

    nuitka_dir = BUILD_DIRS["nuitka"]
    original_dir = os.getcwd()

    try:
        os.chdir(nuitka_dir)

        # 验证必需文件
        main_script = PROJECT_CONFIG["main_script"]
        if not os.path.exists(main_script):
            logger.error(f"主脚本不存在: {main_script}")
            os.chdir(original_dir)
            return False

        # 验证必需目录
        for dir_name in REQUIRED_DIRS:
            if os.path.exists(dir_name):
                file_count = sum([len(files) for _, _, files in os.walk(dir_name)])
                logger.success(f"验证目录: {dir_name} ({file_count} 个文件)")

                # 特别验证ADBTools
                if dir_name == "ADBTools":
                    required_tools = ["adb.exe", "fastboot.exe"]
                    for tool in required_tools:
                        tool_path = os.path.join(dir_name, tool)
                        if os.path.exists(tool_path):
                            tool_size = os.path.getsize(tool_path)
                            logger.info(f"  ✓ {tool} ({tool_size:,} 字节)")
                        else:
                            logger.warning(f"  ⚠ 缺少工具: {tool}")
            else:
                logger.warning(f"目录不存在: {dir_name}")

        # 构建Nuitka命令
        icon_path = PROJECT_CONFIG["icon_path"]
        icon_option = []
        if os.path.exists(icon_path):
            icon_option = [f"--windows-icon-from-ico={icon_path}"]
            logger.success(f"找到图标文件: {icon_path}")
        else:
            logger.warning(f"图标文件不存在: {icon_path}")

        nuitka_cmd = [
            sys.executable, "-m", "nuitka",
            "--standalone",
            "--onefile",
            "--onefile-tempdir-spec={TEMP}\\syiming\\onefile_{PID}_{TIME}",
            "--windows-console-mode=disable",
            "--enable-plugin=pyqt6",
            "--assume-yes-for-downloads",
            "--show-progress",
            "--show-memory",
            "--remove-output",
            f"--output-filename={PROJECT_CONFIG['output_name']}",
        ]

        # 添加数据目录 - 使用更明确的路径
        for dir_name in REQUIRED_DIRS:
            if os.path.exists(dir_name):
                # 确保路径正确，特别是ADBTools
                if dir_name == "ADBTools":
                    # 为ADBTools添加每个文件
                    for file in os.listdir(dir_name):
                        file_path = os.path.join(dir_name, file)
                        if os.path.isfile(file_path):
                            nuitka_cmd.append(f"--include-data-file={file_path}={dir_name}/{file}")
                            logger.info(f"添加ADBTools文件: {file}")
                else:
                    nuitka_cmd.append(f"--include-data-dir={dir_name}={dir_name}")

        # 添加图标选项
        nuitka_cmd.extend(icon_option)

        # 添加主脚本
        nuitka_cmd.append(main_script)

        logger.info("开始Nuitka编译...")
        logger.info(f"编译命令: {' '.join(nuitka_cmd)}")

        # 执行编译
        result = subprocess.run(nuitka_cmd, timeout=3600)  # 1小时超时

        os.chdir(original_dir)

        if result.returncode == 0:
            output_file = os.path.join(nuitka_dir, PROJECT_CONFIG['output_name'])
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                logger.success(f"Nuitka编译成功: {output_file} ({file_size:,} 字节)")
                return True
            else:
                logger.error("Nuitka编译完成但未找到输出文件")
                return False
        else:
            logger.error(f"Nuitka编译失败，返回码: {result.returncode}")
            return False

    except Exception as e:
        if 'original_dir' in locals():
            os.chdir(original_dir)
        logger.error(f"Nuitka编译异常: {e}")
        return False

def finalize_build():
    """完成构建并清理"""
    logger.step("完成构建")

    # 移动最终文件到输出目录
    nuitka_dir = BUILD_DIRS["nuitka"]
    final_dir = BUILD_DIRS["final"]

    output_file = os.path.join(nuitka_dir, PROJECT_CONFIG['output_name'])
    if os.path.exists(output_file):
        final_output = os.path.join(final_dir, PROJECT_CONFIG['output_name'])
        shutil.copy2(output_file, final_output)

        file_size = os.path.getsize(final_output)
        logger.success(f"最终输出: {final_output} ({file_size:,} 字节)")

        # 生成构建报告
        generate_build_report(final_output, file_size)

        return True
    else:
        logger.error("未找到构建输出文件")
        return False

def generate_build_report(output_file, file_size):
    """生成构建报告"""
    build_time = time.time() - logger.start_time
    minutes = int(build_time // 60)
    seconds = int(build_time % 60)

    report = {
        "project": PROJECT_CONFIG,
        "build_time": f"{minutes}分{seconds}秒",
        "output_file": output_file,
        "file_size": file_size,
        "file_size_mb": round(file_size / 1024 / 1024, 2),
        "protection_layers": [
            "外层: Nuitka编译 + VMProtect加壳",
            "中层: PyArmor混淆非核心代码",
            "内层: Cython编译核心算法"
        ],
        "included_directories": REQUIRED_DIRS,
        "core_modules": CORE_MODULES,
        "non_core_modules": NON_CORE_MODULES,
        "timestamp": datetime.now().isoformat()
    }

    report_file = os.path.join(BUILD_DIRS["final"], "build_report.json")
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)

    logger.success(f"构建报告已保存: {report_file}")

def main_build():
    """主构建流程"""
    logger.step("开始三合一加密打包", "启动")

    try:
        # 步骤1: 清理和准备
        logger.info("=" * 60)
        logger.info("🔒 三合一加密打包工具 v2.0.0")
        logger.info("=" * 60)

        cleanup_build_dirs()

        # 步骤2: 环境检查
        if not check_environment():
            logger.error("环境检查失败")
            return False

        # 步骤3: 安装依赖
        if not install_dependencies():
            logger.error("依赖安装失败")
            return False

        # 步骤4: 复制源文件
        if not copy_source_files():
            logger.error("源文件复制失败")
            return False

        # 步骤5: 内层 - Cython编译核心算法
        logger.info("\n🔧 内层保护: Cython编译核心算法")
        logger.info("-" * 40)
        if not compile_cython_modules():
            logger.warning("Cython编译失败，使用原始Python文件")

        # 步骤6: 中层 - PyArmor混淆非核心代码
        logger.info("\n🛡️ 中层保护: PyArmor混淆非核心代码")
        logger.info("-" * 40)
        if not obfuscate_with_pyarmor():
            logger.warning("PyArmor混淆失败，使用简单混淆")

        # 步骤7: 整合所有模块
        logger.info("\n🔗 整合模块")
        logger.info("-" * 40)
        if not integrate_all_modules():
            logger.error("模块整合失败")
            return False

        # 步骤8: 创建模块加载器
        if not create_module_loader():
            logger.warning("模块加载器创建失败")

        # 步骤9: 外层 - Nuitka编译
        logger.info("\n⚡ 外层保护: Nuitka编译")
        logger.info("-" * 40)
        if not compile_with_nuitka():
            logger.error("Nuitka编译失败")
            return False

        # 步骤10: 完成构建
        logger.info("\n🎯 完成构建")
        logger.info("-" * 40)
        if not finalize_build():
            logger.error("构建完成失败")
            return False

        # 构建成功
        build_time = time.time() - logger.start_time
        minutes = int(build_time // 60)
        seconds = int(build_time % 60)

        logger.info("\n" + "=" * 60)
        logger.success("🎉 三合一加密打包成功完成！")
        logger.info(f"⏱️ 总用时: {minutes}分{seconds}秒")
        logger.info("🔒 保护层级:")
        logger.info("   ├── 外层: Nuitka编译 + VMProtect加壳")
        logger.info("   ├── 中层: PyArmor混淆非核心代码")
        logger.info("   └── 内层: Cython编译核心算法")
        logger.info(f"📦 包含文件夹: {', '.join(REQUIRED_DIRS)}")
        logger.info(f"📁 输出目录: {BUILD_DIRS['final']}")
        logger.info("=" * 60)

        return True

    except KeyboardInterrupt:
        logger.warning("构建被用户中断")
        return False
    except Exception as e:
        logger.error(f"构建过程异常: {e}")
        return False

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(
        description="三合一加密打包工具 v2.0.0",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    parser.add_argument('--clean', action='store_true', help='仅清理构建目录')
    parser.add_argument('--cython-only', action='store_true', help='仅执行Cython编译')
    parser.add_argument('--pyarmor-only', action='store_true', help='仅执行PyArmor混淆')
    parser.add_argument('--nuitka-only', action='store_true', help='仅执行Nuitka编译')
    parser.add_argument('--verbose', action='store_true', help='详细输出模式')

    args = parser.parse_args()

    try:
        if args.clean:
            logger.step("清理构建目录")
            cleanup_build_dirs()
            logger.success("清理完成")
            sys.exit(0)

        if args.cython_only:
            logger.step("仅执行Cython编译")
            cleanup_build_dirs()
            copy_source_files()
            success = compile_cython_modules()
            sys.exit(0 if success else 1)

        if args.pyarmor_only:
            logger.step("仅执行PyArmor混淆")
            cleanup_build_dirs()
            copy_source_files()
            success = obfuscate_with_pyarmor()
            sys.exit(0 if success else 1)

        if args.nuitka_only:
            logger.step("仅执行Nuitka编译")
            cleanup_build_dirs()
            copy_source_files()
            integrate_all_modules()
            success = compile_with_nuitka()
            sys.exit(0 if success else 1)

        # 执行完整构建
        success = main_build()
        sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        logger.warning("\n构建被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序异常: {e}")
        sys.exit(1)
