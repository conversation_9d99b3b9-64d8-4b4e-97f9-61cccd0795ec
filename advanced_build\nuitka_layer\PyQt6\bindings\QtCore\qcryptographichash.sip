// qcryptographichash.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCryptographicHash
{
%TypeHeaderCode
#include <qcryptographichash.h>
%End

public:
    enum Algorithm
    {
        Md4,
        <PERSON>d5,
        <PERSON>ha1,
        <PERSON>ha224,
        <PERSON>ha256,
        <PERSON>ha384,
        <PERSON>ha512,
        <PERSON>ha3_224,
        <PERSON>ha3_256,
        <PERSON>ha3_384,
        <PERSON>ha3_512,
        <PERSON>ccak_224,
        <PERSON>ccak_256,
        Keccak_384,
        Keccak_512,
        Blake2b_160,
        Blake2b_256,
        Blake2b_384,
        Blake2b_512,
        Blake2s_128,
        <PERSON>2s_160,
        Blake2s_224,
        Blake2s_256,
    };

    explicit QCryptographicHash(QCryptographicHash::Algorithm method);
    ~QCryptographicHash();
    void reset();
%If (Qt_6_3_0 -)
    void addData(QByteArrayView data);
%End
%If (- Qt_6_3_0)
    void addData(const QByteArray &data);
%End
    void addData(const char *data /Array/, qsizetype length /ArraySize/);
    bool addData(QIODevice *device);
    QByteArray result() const;
%If (Qt_6_3_0 -)
    QByteArrayView resultView() const;
%End
%If (Qt_6_3_0 -)
    static QByteArray hash(QByteArrayView data, QCryptographicHash::Algorithm method);
%End
%If (- Qt_6_3_0)
    static QByteArray hash(const QByteArray &data, QCryptographicHash::Algorithm method);
%End
    static int hashLength(QCryptographicHash::Algorithm method);
%If (Qt_6_5_0 -)
    void swap(QCryptographicHash &other /Constrained/);
%End
%If (Qt_6_5_0 -)
    QCryptographicHash::Algorithm algorithm() const;
%End
%If (Qt_6_5_0 -)
    static bool supportsAlgorithm(QCryptographicHash::Algorithm method);
%End

private:
    QCryptographicHash(const QCryptographicHash &);
};
