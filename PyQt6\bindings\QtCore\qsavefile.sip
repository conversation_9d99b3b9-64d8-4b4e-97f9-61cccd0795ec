// qsavefile.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSaveFile : public QFileDevice
{
%TypeHeaderCode
#include <qsavefile.h>
%End

public:
    explicit QSaveFile(const QString &name);
    explicit QSaveFile(QObject *parent /TransferThis/ = 0);
    QSaveFile(const QString &name, QObject *parent /TransferThis/);
    virtual ~QSaveFile();
    virtual QString fileName() const;
    void setFileName(const QString &name);
    virtual bool open(QIODeviceBase::OpenMode flags) /ReleaseGIL/;
    bool commit();
    void cancelWriting();
    void setDirectWriteFallback(bool enabled);
    bool directWriteFallback() const;

protected:
    virtual qint64 writeData(SIP_PYBUFFER) /ReleaseGIL/ [qint64 (const char *data, qint64 len)];
%MethodCode
        sipBufferInfoDef bi;
        
        if (sipGetBufferInfo(a0, &bi) > 0)
        {
            Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
            sipRes = sipSelfWasArg ?
                    sipCpp->QSaveFile::writeData(reinterpret_cast<char *>(bi.bi_buf), bi.bi_len) :
                    sipCpp->writeData(reinterpret_cast<char *>(bi.bi_buf), bi.bi_len);
        #else
            sipRes = sipCpp->sipProtectVirt_writeData(sipSelfWasArg, reinterpret_cast<char *>(bi.bi_buf),
                    bi.bi_len);
        #endif
            Py_END_ALLOW_THREADS
            
            sipReleaseBufferInfo(&bi);
        }
        else
        {
            sipIsErr = 1;
        }
%End

private:
    virtual void close();
};
