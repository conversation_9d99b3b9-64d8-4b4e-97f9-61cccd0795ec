# -*- coding: utf-8 -*-
"""
测试载入刷机状态显示
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_flash_status_display():
    """测试载入刷机状态显示"""
    print("🧪 测试载入刷机状态显示")
    
    try:
        from qz4n import FlashToolUI
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口实例
        window = FlashToolUI()
        window.show()
        
        print("✅ 窗口创建成功")
        print(f"初始设备状态: {window.device_status_label.text()}")
        
        # 测试停止设备检查
        print("\n🔧 测试停止设备检查...")
        window.stop_device_check()
        
        print(f"停止后设备状态: {window.device_status_label.text()}")
        print(f"should_check_device: {window.should_check_device}")
        
        if window.device_status_label.text() == "载入刷机":
            print("✅ 载入刷机状态显示正确")
        else:
            print("❌ 载入刷机状态显示错误")
        
        # 测试恢复设备检查
        print("\n🔧 测试恢复设备检查...")
        window.start_device_check()
        
        print(f"恢复后should_check_device: {window.should_check_device}")
        
        # 等待一下让设备检查生效
        QTimer.singleShot(3000, app.quit)
        
        print("\n📋 修复内容:")
        print("1. 修复了DeviceCheckThread.stop()的缩进错误")
        print("2. 改进了stop_device_check()方法，等待线程完全停止")
        print("3. 修改了载入刷机的显示样式（橙色背景，白色粗体文字）")
        print("4. 增加了update_device_status的保护，防止覆盖载入刷机状态")
        
        app.exec()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_flash_status_display()
