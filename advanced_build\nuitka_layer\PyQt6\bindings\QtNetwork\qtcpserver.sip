// qtcpserver.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTcpServer : public QObject
{
%TypeHeaderCode
#include <qtcpserver.h>
%End

public:
    explicit QTcpServer(QObject *parent /TransferThis/ = 0);
    virtual ~QTcpServer();
    bool listen(const QHostAddress &address = QHostAddress::Any, quint16 port = 0);
    void close();
    bool isListening() const;
    void setMaxPendingConnections(int numConnections);
    int maxPendingConnections() const;
    quint16 serverPort() const;
    QHostAddress serverAddress() const;
    qintptr socketDescriptor() const;
    bool setSocketDescriptor(qintptr socketDescriptor);
    bool waitForNewConnection(int msecs = 0, bool *timedOut = 0) /ReleaseGIL/;
    virtual bool hasPendingConnections() const;
    virtual QTcpSocket *nextPendingConnection();
    QAbstractSocket::SocketError serverError() const;
    QString errorString() const;
    void setProxy(const QNetworkProxy &networkProxy);
    QNetworkProxy proxy() const;
    void pauseAccepting();
    void resumeAccepting();

protected:
    virtual void incomingConnection(qintptr handle);
    void addPendingConnection(QTcpSocket *socket);

signals:
    void newConnection();
    void acceptError(QAbstractSocket::SocketError socketError);

public:
%If (Qt_6_3_0 -)
    void setListenBacklogSize(int size);
%End
%If (Qt_6_3_0 -)
    int listenBacklogSize() const;
%End

signals:
%If (Qt_6_4_0 -)
    void pendingConnectionAvailable();
%End
};
