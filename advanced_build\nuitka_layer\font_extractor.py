from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QLabel, QPushButton, QTextEdit, QCheckBox, QHBoxLayout, QMessageBox, QGridLayout, QWidget, QGroupBox, QScrollArea
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
import os
from utils import ADBTools
import subprocess

class AdbCheckThread(QThread):
    result_signal = pyqtSignal(bool, str)
    def run(self):
        try:
            adb_path, _ = ADBTools.get_adb_path()
            startupinfo = None
            creationflags = 0
            if os.name == 'nt':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creationflags = subprocess.CREATE_NO_WINDOW
            result = subprocess.run([adb_path, 'devices'], capture_output=True, text=True, encoding='utf-8', timeout=5, startupinfo=startupinfo, creationflags=creationflags)
            lines = [line for line in result.stdout.strip().split('\n') if '\tdevice' in line]
            if not lines:
                self.result_signal.emit(False, "[失败] 未检测到ADB设备，请连接设备并确保已开启USB调试！")
            else:
                self.result_signal.emit(True, "[成功] 检测到ADB设备")
        except Exception as e:
            self.result_signal.emit(False, f"[错误] 检查ADB设备失败: {e}")

class RootCheckThread(QThread):
    result_signal = pyqtSignal(bool, str, str)
    def run(self):
        try:
            adb_path, _ = ADBTools.get_adb_path()
            startupinfo = None
            creationflags = 0
            if os.name == 'nt':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creationflags = subprocess.CREATE_NO_WINDOW
            result = subprocess.run([adb_path, 'shell', 'su', '-c', 'whoami'], capture_output=True, text=True, encoding='utf-8', timeout=5, startupinfo=startupinfo, creationflags=creationflags)
            if result.stdout.strip() == 'root':
                self.result_signal.emit(True, "设备已root", "[成功] 设备已root")
            else:
                self.result_signal.emit(False, "设备未root", "[失败] 设备未root，无法提取分区")
        except Exception as e:
            self.result_signal.emit(False, "root检测失败", f"[错误] 检查root状态失败: {e}")

class PartitionLoadThread(QThread):
    result_signal = pyqtSignal(list, list, str)
    def run(self):
        try:
            adb_path, _ = ADBTools.get_adb_path()
            startupinfo = None
            creationflags = 0
            if os.name == 'nt':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                creationflags = subprocess.CREATE_NO_WINDOW
            result = subprocess.run([adb_path, 'shell', 'ls', '-1', '/dev/block/by-name/'], capture_output=True, text=True, encoding='utf-8', timeout=8, startupinfo=startupinfo, creationflags=creationflags)
            part_list = result.stdout.split()
            ab_parts = [p for p in part_list if p.endswith('_a') or p.endswith('_b')]
            normal_parts = [p for p in part_list if not (p.endswith('_a') or p.endswith('_b'))]
            log = f"[分区] 常规分区: {len(normal_parts)}，A/B分区: {len(ab_parts)}"
            self.result_signal.emit(normal_parts, ab_parts, log)
        except Exception as e:
            self.result_signal.emit([], [], f"[错误] 加载分区失败: {e}")

class PartitionExtractThread(QThread):
    log_signal = pyqtSignal(str)
    finish_signal = pyqtSignal()
    def __init__(self, adb_path, selected, parent=None):
        super().__init__(parent)
        self.adb_path = adb_path
        self.selected = selected
    def run(self):
        startupinfo = None
        creationflags = 0
        if os.name == 'nt':
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            creationflags = subprocess.CREATE_NO_WINDOW
        for part in self.selected:
            try:
                subprocess.run([self.adb_path, 'shell', 'mkdir', '-p', '/sdcard/backup'], capture_output=True, text=True, encoding='utf-8', timeout=8, startupinfo=startupinfo, creationflags=creationflags)
                tmp_img = f'/sdcard/backup/{part}.img'
                subprocess.run([self.adb_path, 'shell', 'su', '-c', f'dd if=/dev/block/by-name/{part} of={tmp_img}'], capture_output=True, text=True, encoding='utf-8', startupinfo=startupinfo, creationflags=creationflags)
                self.log_signal.emit(f"[成功] {part} 提取完成，已保存在设备 /sdcard/backup/")
            except Exception as e:
                self.log_signal.emit(f"[错误] {part} 提取失败: {e}")
        self.log_signal.emit("[流程] 分区提取流程结束")
        self.finish_signal.emit()

class FontExtractorDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("字库提取")
        self.setFixedSize(800, 700)
        self.setWindowFlags(Qt.WindowType.Tool | Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
                border-radius: 12px;
                border: 1px solid #ddd;
            }
            QLabel {
                font-size: 15px;
            }
            QTextEdit {
                background-color: #ffffff;
                border: 1px solid #ddd;
                border-radius: 8px;
                font-size: 14px;
                color: #333333;
                padding: 6px;
            }
            QPushButton {
                background-color: #8250DF;
                border: 1px solid #6E44BC;
                color: white;
                font-size: 15px;
                font-weight: bold;
                border-radius: 10px;
                min-height: 32px;
                min-width: 90px;
                max-width: 180px;
            }
            QPushButton:hover { background-color: #6E44BC; }
            QCheckBox {
                font-size: 13px;
                min-width: 90px;
                max-width: 120px;
                min-height: 24px;
                margin: 2px 6px 2px 0;
            }
            QGroupBox {
                font-size: 15px;
                font-weight: bold;
                border: 1px solid #bbb;
                border-radius: 8px;
                margin-top: 8px;
            }
            QGroupBox:title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px 0 3px;
            }
        """)
        self.adb_tools = ADBTools()
        self.partitions = []
        self.partition_checkboxes = []
        self.partition_checkboxes_ab = []
        self.is_root = False
        self.root_status = QLabel("正在检查root状态...")

        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 顶部状态栏
        status_layout = QHBoxLayout()
        status_layout.addWidget(self.root_status)
        self.refresh_root_btn = QPushButton("刷新root状态")
        self.refresh_root_btn.clicked.connect(self.check_root_status)
        status_layout.addWidget(self.refresh_root_btn)
        status_layout.addStretch()
        main_layout.addLayout(status_layout)

        # 常规分区分组
        self.group_normal = QGroupBox("常规分区")
        self.scroll_normal = QScrollArea()
        self.scroll_normal.setWidgetResizable(True)
        self.scroll_normal.setMinimumHeight(160)
        self.scroll_normal.setMaximumHeight(220)
        self.normal_widget = QWidget()
        self.normal_grid = QGridLayout(self.normal_widget)
        self.normal_grid.setSpacing(4)
        self.normal_grid.setContentsMargins(8, 8, 8, 8)
        self.scroll_normal.setWidget(self.normal_widget)
        group_normal_layout = QVBoxLayout(self.group_normal)
        group_normal_layout.addWidget(self.scroll_normal)
        main_layout.addWidget(self.group_normal)

        # A/B分区分组
        self.group_ab = QGroupBox("A/B 分区")
        self.scroll_ab = QScrollArea()
        self.scroll_ab.setWidgetResizable(True)
        self.scroll_ab.setMinimumHeight(160)
        self.scroll_ab.setMaximumHeight(220)
        self.ab_widget = QWidget()
        self.ab_grid = QGridLayout(self.ab_widget)
        self.ab_grid.setSpacing(4)
        self.ab_grid.setContentsMargins(8, 8, 8, 8)
        self.scroll_ab.setWidget(self.ab_widget)
        group_ab_layout = QVBoxLayout(self.group_ab)
        group_ab_layout.addWidget(self.scroll_ab)
        main_layout.addWidget(self.group_ab)

        # 操作按钮横排
        btn_layout = QHBoxLayout()
        self.extract_btn = QPushButton("提取选中分区")
        self.extract_btn.clicked.connect(self.extract_selected)
        btn_layout.addWidget(self.extract_btn)
        self.refresh_part_btn = QPushButton("刷新分区列表")
        self.refresh_part_btn.clicked.connect(self.load_partitions)
        btn_layout.addWidget(self.refresh_part_btn)
        btn_layout.addStretch()
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)
        btn_layout.addWidget(self.close_btn)
        main_layout.addLayout(btn_layout)

        # 日志区
        self.log_box = QTextEdit()
        self.log_box.setReadOnly(True)
        self.log_box.setMinimumHeight(120)
        main_layout.addWidget(QLabel("日志区"))
        main_layout.addWidget(self.log_box)

        # 启动时自动检查adb设备和root
        self.check_adb_device()
        self.check_root_status()
        self.load_partitions()

    def log(self, msg):
        self.log_box.append(msg)

    def run_cmd(self, cmd, shell=False):
        startupinfo = None
        creationflags = 0
        if os.name == 'nt':
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            creationflags = subprocess.CREATE_NO_WINDOW
        try:
            result = subprocess.run(cmd, shell=shell, capture_output=True, text=True, encoding='utf-8', startupinfo=startupinfo, creationflags=creationflags)
            return result.stdout.strip()
        except Exception as e:
            return f'[subprocess error] {e}'

    def check_adb_device(self):
        self.log("[流程] 检查ADB设备...")
        self.adb_thread = AdbCheckThread()
        self.adb_thread.result_signal.connect(self.on_adb_check_result)
        self.adb_thread.start()
    def on_adb_check_result(self, ok, msg):
        self.log(msg)
        self.extract_btn.setEnabled(ok)
        self.refresh_part_btn.setEnabled(ok)

    def check_root_status(self):
        self.log("[流程] 检查Root状态...")
        self.root_thread = RootCheckThread()
        self.root_thread.result_signal.connect(self.on_root_check_result)
        self.root_thread.start()
    def on_root_check_result(self, is_root, label, logmsg):
        self.is_root = is_root
        self.root_status.setText(label)
        self.log(logmsg)

    def load_partitions(self):
        self.log("[流程] 加载分区列表...")
        self.partition_checkboxes.clear()
        self.partition_checkboxes_ab.clear()
        for i in reversed(range(self.normal_grid.count())):
            widget = self.normal_grid.itemAt(i).widget()
            if widget:
                widget.setParent(None)
        for i in reversed(range(self.ab_grid.count())):
            widget = self.ab_grid.itemAt(i).widget()
            if widget:
                widget.setParent(None)
        self.partition_thread = PartitionLoadThread()
        self.partition_thread.result_signal.connect(self.on_partition_loaded)
        self.partition_thread.start()
    def on_partition_loaded(self, normal_parts, ab_parts, log):
        self.log(log)
        cols = 6
        for idx, part in enumerate(normal_parts):
                cb = QCheckBox(part)
                self.partition_checkboxes.append(cb)
                row = idx // cols
                col = idx % cols
                self.normal_grid.addWidget(cb, row, col)
        for idx, part in enumerate(ab_parts):
                cb = QCheckBox(part)
                self.partition_checkboxes_ab.append(cb)
                row = idx // cols
                col = idx % cols
                self.ab_grid.addWidget(cb, row, col)

    def extract_selected(self):
        if not self.is_root:
            QMessageBox.warning(self, "未Root", "设备未root，无法提取分区！")
            return
        selected = [cb.text() for cb in self.partition_checkboxes if cb.isChecked()]
        selected += [cb.text() for cb in self.partition_checkboxes_ab if cb.isChecked()]
        if not selected:
            QMessageBox.information(self, "未选择", "请先选择要提取的分区！")
            return
        self.log(f"[流程] 开始提取分区: {', '.join(selected)}")
        adb_path, _ = self.adb_tools.get_adb_path()
        self.extract_thread = PartitionExtractThread(adb_path, selected)
        self.extract_thread.log_signal.connect(self.log)
        self.extract_thread.finish_signal.connect(lambda: self.extract_btn.setEnabled(True))
        self.extract_btn.setEnabled(False)
        self.extract_thread.start()

    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self._drag_pos = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        if event.buttons() == Qt.MouseButton.LeftButton:
            self.move(event.globalPosition().toPoint() - self._drag_pos)
            event.accept() 