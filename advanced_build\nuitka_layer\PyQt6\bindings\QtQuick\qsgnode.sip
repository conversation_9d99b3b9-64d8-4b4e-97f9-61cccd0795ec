// qsgnode.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSGNode /Supertype=PyQt6.sip.wrapper/
{
%TypeHeaderCode
#include <qsgnode.h>
%End

%TypeCode
static sipErrorState qsgnode_handle_flags(QSGNode *node, PyObject *self, QSGNode::Flags old_flags)
{
    QSGNode::Flags new_flags = node->flags();
    
    if (node->parent())
    {
        if ((old_flags & QSGNode::OwnedByParent) != (new_flags & QSGNode::OwnedByParent))
        {
            if (old_flags & QSGNode::OwnedByParent)
            {
                sipTransferBack(self);
            }
            else
            {
                PyObject *parent = sipConvertFromType(node->parent(), sipType_QSGNode, 0);

                if (!parent)
                    return sipErrorFail;

                sipTransferTo(self, parent);
                Py_DECREF(parent);
            }
        }
    }
    
    QSGNode::NodeType ntype = node->type();
        
    if (ntype == QSGNode::BasicNodeType || ntype == QSGNode::GeometryNodeType || ntype == QSGNode::ClipNodeType)
    {
        QSGBasicGeometryNode *bg_node = (QSGBasicGeometryNode *)node;
            
        if (bg_node->geometry())
        {
            if ((old_flags & QSGNode::OwnsGeometry) != (new_flags & QSGNode::OwnsGeometry))
            {
                PyObject *geom = sipConvertFromType(bg_node->geometry(), sipType_QSGGeometry, 0);

                if (!geom)
                    return sipErrorFail;

                if (old_flags & QSGNode::OwnsGeometry)
                    sipTransferBack(geom);
                else
                    sipTransferTo(geom, self);

                Py_DECREF(geom);
            }
        }
    }
    
    if (ntype == QSGNode::GeometryNodeType)
    {
        QSGGeometryNode *g_node = (QSGGeometryNode *)node;
            
        if (g_node->material())
        {
            if ((old_flags & QSGNode::OwnsMaterial) != (new_flags & QSGNode::OwnsMaterial))
            {
                PyObject *mat = sipConvertFromType(g_node->material(), sipType_QSGMaterial, 0);

                if (!mat)
                    return sipErrorFail;

                if (old_flags & QSGNode::OwnsMaterial)
                    sipTransferBack(mat);
                else
                    sipTransferTo(mat, self);

                Py_DECREF(mat);
            }
        }

        if (g_node->opaqueMaterial())
        {
            if ((old_flags & QSGNode::OwnsOpaqueMaterial) != (new_flags & QSGNode::OwnsOpaqueMaterial))
            {
                PyObject *omat = sipConvertFromType(g_node->opaqueMaterial(), sipType_QSGMaterial, 0);

                if (!omat)
                    return sipErrorFail;

                if (old_flags & QSGNode::OwnsOpaqueMaterial)
                    sipTransferBack(omat);
                else
                    sipTransferTo(omat, self);

                Py_DECREF(omat);
            }
        }
    }

    return sipErrorNone;
}
%End

%ConvertToSubClassCode
    switch (sipCpp->type())
    {
    case QSGNode::BasicNodeType:
        sipType = sipType_QSGBasicGeometryNode;
        break;
    
    case QSGNode::GeometryNodeType:
        sipType = sipType_QSGGeometryNode;
        break;
    
    case QSGNode::TransformNodeType:
        sipType = sipType_QSGClipNode;
        break;
    
    case QSGNode::ClipNodeType:
        sipType = sipType_QSGTransformNode;
        break;
    
    case QSGNode::OpacityNodeType:
        sipType = sipType_QSGOpacityNode;
        break;
    
    default:
        sipType = 0;
    }
%End

public:
    enum NodeType
    {
        BasicNodeType,
        GeometryNodeType,
        TransformNodeType,
        ClipNodeType,
        OpacityNodeType,
    };

    enum Flag /BaseType=Flag/
    {
        OwnedByParent,
        UsePreprocess,
        OwnsGeometry,
        OwnsMaterial,
        OwnsOpaqueMaterial,
    };

    typedef QFlags<QSGNode::Flag> Flags;

    enum DirtyStateBit /BaseType=Flag/
    {
        DirtyMatrix,
        DirtyNodeAdded,
        DirtyNodeRemoved,
        DirtyGeometry,
        DirtyMaterial,
        DirtyOpacity,
    };

    typedef QFlags<QSGNode::DirtyStateBit> DirtyState;
    QSGNode();
    virtual ~QSGNode();
    QSGNode *parent() const;
    void removeChildNode(QSGNode *node);
    void removeAllChildNodes();
    void prependChildNode(QSGNode *node /GetWrapper/);
%MethodCode
        sipCpp->prependChildNode(a0);
        
        if (a0->flags() & QSGNode::OwnedByParent)
            sipTransferTo(a0Wrapper, sipSelf);
%End

    void appendChildNode(QSGNode *node /GetWrapper/);
%MethodCode
        sipCpp->appendChildNode(a0);
        
        if (a0->flags() & QSGNode::OwnedByParent)
            sipTransferTo(a0Wrapper, sipSelf);
%End

    void insertChildNodeBefore(QSGNode *node /GetWrapper/, QSGNode *before);
%MethodCode
        sipCpp->insertChildNodeBefore(a0, a1);
        
        if (a0->flags() & QSGNode::OwnedByParent)
            sipTransferTo(a0Wrapper, sipSelf);
%End

    void insertChildNodeAfter(QSGNode *node /GetWrapper/, QSGNode *after);
%MethodCode
        sipCpp->insertChildNodeAfter(a0, a1);
        
        if (a0->flags() & QSGNode::OwnedByParent)
            sipTransferTo(a0Wrapper, sipSelf);
%End

    int childCount() const /__len__/;
    QSGNode *childAtIndex(int i) const;
    QSGNode *firstChild() const;
    QSGNode *lastChild() const;
    QSGNode *nextSibling() const;
    QSGNode *previousSibling() const;
    QSGNode::NodeType type() const;
    void markDirty(QSGNode::DirtyState bits);
    virtual bool isSubtreeBlocked() const;
    QSGNode::Flags flags() const;
    void setFlag(QSGNode::Flag, bool enabled = true);
%MethodCode
        QSGNode::Flags old_flags = sipCpp->flags();
        
        sipCpp->setFlag(a0, a1);
        
        sipError = qsgnode_handle_flags(sipCpp, sipSelf, old_flags);
%End

    void setFlags(QSGNode::Flags, bool enabled = true);
    virtual void preprocess();

private:
    QSGNode(const QSGNode &);
};

class QSGBasicGeometryNode : public QSGNode /NoDefaultCtors/
{
%TypeHeaderCode
#include <qsgnode.h>
%End

public:
    virtual ~QSGBasicGeometryNode();
    void setGeometry(QSGGeometry *geometry /GetWrapper/);
%MethodCode
        sipCpp->setGeometry(a0);
        
        if (sipCpp->flags() & QSGNode::OwnsGeometry)
            sipTransferTo(a0Wrapper, sipSelf);
%End

    QSGGeometry *geometry();
};

class QSGGeometryNode : public QSGBasicGeometryNode
{
%TypeHeaderCode
#include <qsgnode.h>
%End

public:
    QSGGeometryNode();
    virtual ~QSGGeometryNode();
    void setMaterial(QSGMaterial *material /GetWrapper/);
%MethodCode
        sipCpp->setMaterial(a0);
        
        if (sipCpp->flags() & QSGNode::OwnsMaterial)
            sipTransferTo(a0Wrapper, sipSelf);
%End

    QSGMaterial *material() const;
    void setOpaqueMaterial(QSGMaterial *material /GetWrapper/);
%MethodCode
        sipCpp->setOpaqueMaterial(a0);
        
        if (sipCpp->flags() & QSGNode::OwnsOpaqueMaterial)
            sipTransferTo(a0Wrapper, sipSelf);
%End

    QSGMaterial *opaqueMaterial() const;
};

class QSGClipNode : public QSGBasicGeometryNode
{
%TypeHeaderCode
#include <qsgnode.h>
%End

public:
    QSGClipNode();
    virtual ~QSGClipNode();
    void setIsRectangular(bool rectHint);
    bool isRectangular() const;
    void setClipRect(const QRectF &);
    QRectF clipRect() const;
};

class QSGTransformNode : public QSGNode
{
%TypeHeaderCode
#include <qsgnode.h>
%End

public:
    QSGTransformNode();
    virtual ~QSGTransformNode();
    void setMatrix(const QMatrix4x4 &matrix);
    const QMatrix4x4 &matrix() const;
};

class QSGOpacityNode : public QSGNode
{
%TypeHeaderCode
#include <qsgnode.h>
%End

public:
    QSGOpacityNode();
    virtual ~QSGOpacityNode();
    void setOpacity(qreal opacity);
    qreal opacity() const;
};
