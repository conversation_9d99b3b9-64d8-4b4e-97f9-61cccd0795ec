// qcollator.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCollatorSortKey
{
%TypeHeaderCode
#include <qcollator.h>
%End

public:
    QCollatorSortKey(const QCollatorSortKey &other);
    ~QCollatorSortKey();
    void swap(QCollatorSortKey &other /Constrained/);
    int compare(const QCollatorSortKey &key) const;

private:
    QCollatorSortKey();
};

bool operator<(const QCollatorSortKey &lhs, const QCollatorSortKey &rhs);

class QCollator
{
%TypeHeaderCode
#include <qcollator.h>
%End

public:
    QCollator();
    explicit QCollator(const QLocale &locale);
    QCollator(const QCollator &);
    ~QCollator();
    void swap(QCollator &other /Constrained/);
    void setLocale(const QLocale &locale);
    QLocale locale() const;
    Qt::CaseSensitivity caseSensitivity() const;
    void setCaseSensitivity(Qt::CaseSensitivity cs);
    void setNumericMode(bool on);
    bool numericMode() const;
    void setIgnorePunctuation(bool on);
    bool ignorePunctuation() const;
    int compare(const QString &s1, const QString &s2) const;
    QCollatorSortKey sortKey(const QString &string) const;
%If (Qt_6_3_0 -)
    static int defaultCompare(QStringView s1, QStringView s2);
%End
%If (Qt_6_3_0 -)
    static QCollatorSortKey defaultSortKey(QStringView key);
%End
};
