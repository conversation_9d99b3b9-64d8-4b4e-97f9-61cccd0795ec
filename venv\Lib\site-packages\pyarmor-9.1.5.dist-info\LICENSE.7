     END USER LICENSE AGREEMENT

     The following agreement regarding PyArmor - referred to as "software" - is
     made between <PERSON><PERSON> - referred to as "licensor" - and anyone who is
     installing, accessing or in any other way using the software - referred to
     as "user".

  1. The author and holder of the copyright of the software is <PERSON><PERSON>. The software is distributed as Free To Use But Restricted:

     a. The trial version could not obfuscate the big scripts.

     b. The scripts obfuscated by trial version are not private. It
        means anyone could generate the license file which works for
        these obfuscated scripts.

     c. The trial version could not download the latest dynamic library
        of extra platforms, the old versions still are available.

     d. The super plus mode is not availaible in the trial version.

     e. Without permission the trial version may not be used for the Python
        scripts of any commercial product.

  3. There are 2 basic types of licenses issued for the software. These are:

     a. A personal license for home users. The user purchases one license to use
        the software on his own computer. When placing an order of this kind of
        license, please fill real name as registration name, this software is
        only authorized to this registration name.

        Home users may use their personal license to obfuscate all the python
        scripts which are property of the license owner, to generate private
        license files for the obfuscated scripts and distribute them and all the
        required files to any other machine or device.

        Home users could NOT obfuscate any python script which is NOT property
        of the license owner.

     b. A enterprise license for business users. The user purchases one license
        to use the software for one product of an organization. When placing an
        order of this kind of license, please fill orginization name plus
        product name, this software is only authorized to this registration
        name.

        One product include the current version and any other latter versions of
        the same product.

        Business users may use their enterprise license on all computers and
        embedded devices to obfuscate all the python scripts of this product, to
        generate private license files for these obfuscated scripts and
        distribute them and all the required files to any other machine and
        device.

        Without permission of "licensor" the license purchased for one product
        should not be used for other product. Business users should purchase new
        license for different product.

     A user who purchased a license, is granted a non-exclusive right to use
     the software on as many computers as defined by the licensing terms above
     according to the number of licenses purchased, for any legal purpose.

     In any case, the software is only used to obfuscate the Python scripts
     owned by the authorized person or enterprise.

  4. There are no additional license fees, apart from the cost of the license,
     associated with the creation and distribution of obfuscated python scripts.
     Owners of a license may use their copies of the software to produce
     obfuscated python scripts and to distribute those files free of any
     additional royalties.

  5. To buy a license, please run command

         pyarmor register --buy

     Or open the following url in any web browser

         https://order.shareit.com/cart/add?vendorid=200089125&PRODUCT[300871197]=1

     For personal license, please fill the registeration name with real name
     when placing an order.

     For enterprise license, please fill the registeration name with enterprise
     name, and also fill "License To Product" with the product name which will
     use this software.

     A registration file generally named "pyarmor-regcode-xxxx.txt" will be sent
     by email immediately after payment is completed successfully.

     Save it to disk, then run the following command to register PyArmor

         pyarmor register /path/to/pyarmor-regcode-xxxx.txt

     Check the registration information:

         pyarmor register

     After registration successfully, remove all the obfuscated scripts by trial
     version, then obfuscate them again.

     The registration code is valid forever, it can be used permanently. But it
     may not be rented or leased.

  6. The software's free version may be freely distributed, with exceptions
     noted below, provided the distribution package is not modified in any way.

     a. Nobody may distribute separate parts of the package, without written
        permission.

     b. The software's unlicensed free version may not be distributed inside of
        any other software package without written permission.  The software
        must remain in the original unmodified installation file for download
        without any barrier and conditions to the user such as collecting fees
        for the download or making the download conditional on the user giving
        his contact data.

     c. The unmodified installation file of PyArmor must be provided pure and
        unpaired. Any bundling is interdicted. In particular the use of any
        install or download software which is providing any kind of download
        bundles is prohibited unless granted by Jondy Zhao written form.

     d. Hacks/cracks, keys or key generators may not be included, pointed to
        or referred to by the distributor of the free version.

     e. In case of violation of the precedent conditions the allowance
        lapses immediately and automatically.

  7. The software is distributed "as is". No warranty of any kind is expressed
     or implied. You use at your own risk. Neither the author, the licensor
     nor the agents of the licensor will be liable for data loss, damages,
     loss of profits or any other kind of loss while using or misusing
     this software.

  8. The dynamic library of the software may not be used for reverse engineer to
     re-create the PyArmor obfuscated algorithm.

  9. The licensor shall be responsible for interpretation of the
     agreement. Anytime the licensor made any modifications to the agreement,
     the modified version shall be applicable to the user automatically.

 10. Installing and using the software signifies acceptance of these terms
     and conditions of the license. If you do not agree with the terms of this
     license, you must remove all software files from your storage devices
     and cease to use the software.
