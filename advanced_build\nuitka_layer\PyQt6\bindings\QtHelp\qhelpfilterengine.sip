// qhelpfilterengine.sip generated by MetaSIP
//
// This file is part of the QtHelp Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QHelpFilterEngine : public QObject /NoDefaultCtors/
{
%TypeHeaderCode
#include <qhelpfilterengine.h>
%End

public:
    QMap<QString, QString> namespaceToComponent() const;
    QMap<QString, QVersionNumber> namespaceToVersion() const;
    QStringList filters() const;
    QString activeFilter() const;
    bool setActiveFilter(const QString &filterName);
    QStringList availableComponents() const;
    QHelpFilterData filterData(const QString &filterName) const;
    bool setFilterData(const QString &filterName, const QHelpFilterData &filterData);
    bool removeFilter(const QString &filterName);
    QStringList namespacesForFilter(const QString &filterName) const;

signals:
    void filterActivated(const QString &newFilter);

protected:
    virtual ~QHelpFilterEngine();

public:
    QList<QVersionNumber> availableVersions() const;
    QStringList indices() const;
    QStringList indices(const QString &filterName) const;
};
