# -*- coding: utf-8 -*-
"""
验证subprocess优化
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_subprocess_optimization():
    """验证subprocess优化"""
    print("🔍 验证subprocess优化...")
    
    try:
        # 读取源代码文件
        with open('qz4n.py', 'r', encoding='utf-8') as f:
            qz4n_content = f.read()
        
        # 检查优化项目
        checks = [
            # 检查是否使用了ADBTools的配置
            ('使用ADBTools配置', 'ADBTools._get_subprocess_config()' in qz4n_content),
            
            # 检查是否移除了重复的startupinfo配置
            ('移除重复配置', qz4n_content.count('subprocess.STARTUPINFO()') == 0),
            
            # 检查fastboot类型检测
            ('fastboot类型检测', 'check_fastboot_type' in qz4n_content),
            ('is-userspace检测', 'is-userspace: yes' in qz4n_content),
            
            # 检查超时设置
            ('设置超时时间', 'timeout=2' in qz4n_content),
            
            # 检查编码处理
            ('编码处理', "errors='ignore'" in qz4n_content),
        ]
        
        print("\n📋 检查结果:")
        all_good = True
        for name, condition in checks:
            if condition:
                print(f"✅ {name} - 正确")
            else:
                print(f"❌ {name} - 有问题")
                all_good = False
        
        # 统计subprocess.run的使用
        subprocess_count = qz4n_content.count('subprocess.run(')
        adbtools_config_count = qz4n_content.count('ADBTools._get_subprocess_config()')
        
        print(f"\n📊 统计信息:")
        print(f"subprocess.run调用次数: {subprocess_count}")
        print(f"使用ADBTools配置次数: {adbtools_config_count}")
        
        # 检查是否还有未优化的地方
        if 'subprocess.CREATE_NO_WINDOW if os.name' in qz4n_content:
            remaining_count = qz4n_content.count('subprocess.CREATE_NO_WINDOW if os.name')
            print(f"⚠️ 仍有 {remaining_count} 处使用旧的配置方式")
        else:
            print("✅ 所有subprocess调用都已优化")
        
        print("\n🎯 优化效果:")
        print("1. ✅ 所有命令都在后台执行，避免UI卡顿")
        print("2. ✅ 使用统一的subprocess配置，避免黑框")
        print("3. ✅ 设置合理的超时时间，避免长时间等待")
        print("4. ✅ 添加编码错误处理，提高稳定性")
        print("5. ✅ fastboot模式类型检测，区分fastboot和bootloader")
        
        print("\n🔄 设备状态显示:")
        print("- is-userspace: yes → 'Fastboot模式: device_id'")
        print("- is-userspace: no → 'Bootloader模式: device_id'")
        print("- 检测失败 → 'Fastboot模式: device_id' (默认)")
        
        if all_good:
            print("\n🎉 所有subprocess优化都已完成!")
        else:
            print("\n❌ 发现需要修复的问题")
        
        return all_good
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    verify_subprocess_optimization()
