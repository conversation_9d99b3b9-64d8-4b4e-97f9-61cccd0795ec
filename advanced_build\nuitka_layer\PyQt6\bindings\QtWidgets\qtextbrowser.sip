// qtextbrowser.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTextBrowser : public QTextEdit
{
%TypeHeaderCode
#include <qtextbrowser.h>
%End

public:
    explicit QTextBrowser(QWidget *parent /TransferThis/ = 0);
    virtual ~QTextBrowser();
    QUrl source() const;
    QStringList searchPaths() const;
    void setSearchPaths(const QStringList &paths);
    virtual QVariant loadResource(int type, const QUrl &name);

public slots:
    void setSource(const QUrl &name, QTextDocument::ResourceType type = QTextDocument::UnknownResource);
    virtual void backward();
    virtual void forward();
    virtual void home();
    virtual void reload();

signals:
    void backwardAvailable(bool);
    void forwardAvailable(bool);
    void sourceChanged(const QUrl &);
    void highlighted(const QUrl &);
    void anchorClicked(const QUrl &);

protected:
    virtual bool event(QEvent *e);
    virtual void keyPressEvent(QKeyEvent *ev);
    virtual void mouseMoveEvent(QMouseEvent *ev);
    virtual void mousePressEvent(QMouseEvent *ev);
    virtual void mouseReleaseEvent(QMouseEvent *ev);
    virtual void focusOutEvent(QFocusEvent *ev);
    virtual bool focusNextPrevChild(bool next);
    virtual void paintEvent(QPaintEvent *e);

public:
    bool isBackwardAvailable() const;
    bool isForwardAvailable() const;
    void clearHistory();
    bool openExternalLinks() const;
    void setOpenExternalLinks(bool open);
    bool openLinks() const;
    void setOpenLinks(bool open);
    QString historyTitle(int) const;
    QUrl historyUrl(int) const;
    int backwardHistoryCount() const;
    int forwardHistoryCount() const;

signals:
    void historyChanged();

public:
    QTextDocument::ResourceType sourceType() const;

protected:
    virtual void doSetSource(const QUrl &name, QTextDocument::ResourceType type = QTextDocument::UnknownResource);
};
