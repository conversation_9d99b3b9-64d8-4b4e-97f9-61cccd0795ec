// qitemeditorfactory.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QItemEditorCreatorBase /Supertype=PyQt6.sip.wrapper/
{
%TypeHeaderCode
#include <qitemeditorfactory.h>
%End

public:
    virtual ~QItemEditorCreatorBase();
    virtual QWidget *createWidget(QWidget *parent /TransferThis/) const = 0 /Factory/;
    virtual QByteArray valuePropertyName() const = 0;
};

class QItemEditorFactory /Supertype=PyQt6.sip.wrapper/
{
%TypeHeaderCode
#include <qitemeditorfactory.h>
%End

public:
    QItemEditorFactory();
    virtual ~QItemEditorFactory();
    virtual QWidget *createEditor(int userType, QWidget *parent /TransferThis/) const;
    virtual QByteArray valuePropertyName(int userType) const;
    void registerEditor(int userType, QItemEditorCreatorBase *creator /Transfer/);
    static const QItemEditorFactory *defaultFactory();
    static void setDefaultFactory(QItemEditorFactory *factory /Transfer/);
};
