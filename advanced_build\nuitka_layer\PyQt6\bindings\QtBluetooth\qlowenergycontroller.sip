// qlowenergycontroller.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QLowEnergyController : public QObject
{
%TypeHeaderCode
#include <qlowenergycontroller.h>
%End

public:
    enum Error
    {
        NoError,
        Unknown<PERSON><PERSON><PERSON>,
        UnknownRemoteDeviceError,
        NetworkError,
        InvalidBluetoothAdapterError,
        ConnectionError,
        AdvertisingError,
        RemoteHostClosedError,
        AuthorizationError,
%If (Qt_6_4_0 -)
        MissingPermissionsError,
%End
%If (Qt_6_5_0 -)
        RssiReadError,
%End
    };

    enum ControllerState
    {
        UnconnectedState,
        ConnectingState,
        ConnectedState,
        DiscoveringState,
        DiscoveredState,
        ClosingState,
        AdvertisingState,
    };

    enum RemoteAddressType
    {
        PublicAddress,
        RandomAddress,
    };

    virtual ~QLowEnergyController();
    QBluetoothAddress localAddress() const;
    QBluetoothAddress remoteAddress() const;
    QLowEnergyController::ControllerState state() const;
    QLowEnergyController::RemoteAddressType remoteAddressType() const;
    void setRemoteAddressType(QLowEnergyController::RemoteAddressType type);
    void connectToDevice();
    void disconnectFromDevice();
    void discoverServices();
    QList<QBluetoothUuid> services() const;
    QLowEnergyService *createServiceObject(const QBluetoothUuid &service, QObject *parent /TransferThis/ = 0) /Factory/;
    QLowEnergyController::Error error() const;
    QString errorString() const;
    QString remoteName() const;
    int mtu() const;

signals:
    void connected();
    void disconnected();
    void stateChanged(QLowEnergyController::ControllerState state);
    void errorOccurred(QLowEnergyController::Error newError);
    void serviceDiscovered(const QBluetoothUuid &newService);
    void discoveryFinished();
    void connectionUpdated(const QLowEnergyConnectionParameters &parameters);
    void mtuChanged(int mtu);

public:
    enum Role
    {
        CentralRole,
        PeripheralRole,
    };

    static QLowEnergyController *createCentral(const QBluetoothDeviceInfo &remoteDevice, QObject *parent /TransferThis/ = 0) /Factory/;
    static QLowEnergyController *createCentral(const QBluetoothDeviceInfo &remoteDevice, const QBluetoothAddress &localDevice, QObject *parent /TransferThis/ = 0) /Factory/;
    static QLowEnergyController *createPeripheral(const QBluetoothAddress &localDevice, QObject *parent /TransferThis/ = 0) /Factory/;
    static QLowEnergyController *createPeripheral(QObject *parent /TransferThis/ = 0) /Factory/;
    void startAdvertising(const QLowEnergyAdvertisingParameters &parameters, const QLowEnergyAdvertisingData &advertisingData, const QLowEnergyAdvertisingData &scanResponseData = QLowEnergyAdvertisingData());
    void stopAdvertising();
    QLowEnergyService *addService(const QLowEnergyServiceData &service, QObject *parent /TransferThis/ = 0) /Factory/;
    void requestConnectionUpdate(const QLowEnergyConnectionParameters &parameters);
    QLowEnergyController::Role role() const;
    QBluetoothUuid remoteDeviceUuid() const;
%If (Qt_6_5_0 -)
    void readRssi();
%End

signals:
%If (Qt_6_5_0 -)
    void rssiRead(qint16 rssi);
%End

private:
    QLowEnergyController(const QBluetoothDeviceInfo &remoteDevice, const QBluetoothAddress &localDevice, QObject *parent = 0);
    QLowEnergyController(const QBluetoothAddress &localDevice, QObject *parent = 0);
};

%End
