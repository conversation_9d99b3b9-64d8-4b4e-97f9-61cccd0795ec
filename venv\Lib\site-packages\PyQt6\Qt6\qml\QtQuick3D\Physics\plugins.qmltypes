import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qabstractcollisionshape_p.h"
        name: "QAbstractCollisionShape"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D.Physics/CollisionShape 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Property {
            name: "enableDebugDraw"
            type: "bool"
            read: "enableDebugDraw"
            write: "setEnableDebugDraw"
            notify: "enableDebugDrawChanged"
            index: 0
        }
        Signal {
            name: "enableDebugDrawChanged"
            Parameter { name: "enableDebugDraw"; type: "bool" }
        }
        Signal {
            name: "needsRebuild"
            Parameter { type: "QObject"; isPointer: true }
        }
        Method {
            name: "setEnableDebugDraw"
            Parameter { name: "enableDebugDraw"; type: "bool" }
        }
        Method { name: "handleScaleChange" }
    }
    Component {
        file: "private/qabstractphysicsbody_p.h"
        name: "QAbstractPhysicsBody"
        accessSemantics: "reference"
        prototype: "QAbstractPhysicsNode"
        exports: [
            "QtQuick3D.Physics/PhysicsBody 6.0",
            "QtQuick3D.Physics/PhysicsBody 6.5",
            "QtQuick3D.Physics/PhysicsBody 6.7"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1536, 1541, 1543]
        Property {
            name: "physicsMaterial"
            type: "QPhysicsMaterial"
            isPointer: true
            read: "physicsMaterial"
            write: "setPhysicsMaterial"
            notify: "physicsMaterialChanged"
            index: 0
        }
        Property {
            name: "simulationEnabled"
            type: "bool"
            read: "simulationEnabled"
            write: "setSimulationEnabled"
            notify: "simulationEnabledChanged"
            index: 1
        }
        Signal { name: "physicsMaterialChanged" }
        Signal { name: "simulationEnabledChanged" }
    }
    Component {
        file: "private/qabstractphysicsnode_p.h"
        name: "QAbstractPhysicsNode"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: [
            "QtQuick3D.Physics/PhysicsNode 6.0",
            "QtQuick3D.Physics/PhysicsNode 6.5",
            "QtQuick3D.Physics/PhysicsNode 6.7"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1536, 1541, 1543]
        Property {
            name: "collisionShapes"
            type: "QAbstractCollisionShape"
            isList: true
            read: "collisionShapes"
            index: 0
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "sendContactReports"
            type: "bool"
            read: "sendContactReports"
            write: "setSendContactReports"
            notify: "sendContactReportsChanged"
            index: 1
        }
        Property {
            name: "receiveContactReports"
            type: "bool"
            read: "receiveContactReports"
            write: "setReceiveContactReports"
            notify: "receiveContactReportsChanged"
            index: 2
        }
        Property {
            name: "sendTriggerReports"
            revision: 1541
            type: "bool"
            read: "sendTriggerReports"
            write: "setSendTriggerReports"
            notify: "sendTriggerReportsChanged"
            index: 3
        }
        Property {
            name: "receiveTriggerReports"
            revision: 1541
            type: "bool"
            read: "receiveTriggerReports"
            write: "setReceiveTriggerReports"
            notify: "receiveTriggerReportsChanged"
            index: 4
        }
        Property {
            name: "filterGroup"
            revision: 1543
            type: "int"
            read: "filterGroup"
            write: "setfilterGroup"
            notify: "filterGroupChanged"
            index: 5
        }
        Property {
            name: "filterIgnoreGroups"
            revision: 1543
            type: "int"
            read: "filterIgnoreGroups"
            write: "setFilterIgnoreGroups"
            notify: "filterIgnoreGroupsChanged"
            index: 6
        }
        Signal {
            name: "bodyContact"
            Parameter { name: "body"; type: "QAbstractPhysicsNode"; isPointer: true }
            Parameter { name: "positions"; type: "QVector3D"; isList: true }
            Parameter { name: "impulses"; type: "QVector3D"; isList: true }
            Parameter { name: "normals"; type: "QVector3D"; isList: true }
        }
        Signal {
            name: "sendContactReportsChanged"
            Parameter { name: "sendContactReports"; type: "float" }
        }
        Signal {
            name: "receiveContactReportsChanged"
            Parameter { name: "receiveContactReports"; type: "float" }
        }
        Signal {
            name: "sendTriggerReportsChanged"
            revision: 1541
            Parameter { name: "sendTriggerReports"; type: "float" }
        }
        Signal {
            name: "receiveTriggerReportsChanged"
            revision: 1541
            Parameter { name: "receiveTriggerReports"; type: "float" }
        }
        Signal {
            name: "enteredTriggerBody"
            revision: 1541
            Parameter { name: "body"; type: "QAbstractPhysicsNode"; isPointer: true }
        }
        Signal {
            name: "exitedTriggerBody"
            revision: 1541
            Parameter { name: "body"; type: "QAbstractPhysicsNode"; isPointer: true }
        }
        Signal { name: "filterGroupChanged"; revision: 1543 }
        Signal { name: "filterIgnoreGroupsChanged"; revision: 1543 }
        Method {
            name: "onShapeDestroyed"
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "onShapeNeedsRebuild"
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
    }
    Component {
        file: "private/qboxshape_p.h"
        name: "QBoxShape"
        accessSemantics: "reference"
        prototype: "QAbstractCollisionShape"
        exports: ["QtQuick3D.Physics/BoxShape 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "extents"
            type: "QVector3D"
            read: "extents"
            write: "setExtents"
            notify: "extentsChanged"
            index: 0
        }
        Signal {
            name: "extentsChanged"
            Parameter { name: "extents"; type: "QVector3D" }
        }
        Method {
            name: "setExtents"
            Parameter { name: "extents"; type: "QVector3D" }
        }
    }
    Component {
        file: "private/qcapsuleshape_p.h"
        name: "QCapsuleShape"
        accessSemantics: "reference"
        prototype: "QAbstractCollisionShape"
        exports: ["QtQuick3D.Physics/CapsuleShape 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "diameter"
            type: "float"
            read: "diameter"
            write: "setDiameter"
            notify: "diameterChanged"
            index: 0
        }
        Property {
            name: "height"
            type: "float"
            read: "height"
            write: "setHeight"
            notify: "heightChanged"
            index: 1
        }
        Signal { name: "diameterChanged" }
        Signal { name: "heightChanged" }
    }
    Component {
        file: "private/qcharactercontroller_p.h"
        name: "QCharacterController"
        accessSemantics: "reference"
        prototype: "QAbstractPhysicsBody"
        exports: [
            "QtQuick3D.Physics/CharacterController 6.0",
            "QtQuick3D.Physics/CharacterController 6.5",
            "QtQuick3D.Physics/CharacterController 6.7"
        ]
        exportMetaObjectRevisions: [1536, 1541, 1543]
        Enum {
            name: "Collisions"
            alias: "Collision"
            isFlag: true
            values: ["None", "Side", "Up", "Down"]
        }
        Property {
            name: "movement"
            type: "QVector3D"
            read: "movement"
            write: "setMovement"
            notify: "movementChanged"
            index: 0
        }
        Property {
            name: "gravity"
            type: "QVector3D"
            read: "gravity"
            write: "setGravity"
            notify: "gravityChanged"
            index: 1
        }
        Property {
            name: "midAirControl"
            type: "bool"
            read: "midAirControl"
            write: "setMidAirControl"
            notify: "midAirControlChanged"
            index: 2
        }
        Property {
            name: "collisions"
            type: "Collisions"
            read: "collisions"
            notify: "collisionsChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "enableShapeHitCallback"
            type: "bool"
            read: "enableShapeHitCallback"
            write: "setEnableShapeHitCallback"
            notify: "enableShapeHitCallbackChanged"
            index: 4
        }
        Signal { name: "movementChanged" }
        Signal { name: "gravityChanged" }
        Signal { name: "midAirControlChanged" }
        Signal { name: "collisionsChanged" }
        Signal { name: "enableShapeHitCallbackChanged" }
        Signal {
            name: "shapeHit"
            Parameter { name: "body"; type: "QAbstractPhysicsNode"; isPointer: true }
            Parameter { name: "position"; type: "QVector3D" }
            Parameter { name: "impulse"; type: "QVector3D" }
            Parameter { name: "normal"; type: "QVector3D" }
        }
        Method {
            name: "teleport"
            Parameter { name: "position"; type: "QVector3D" }
        }
    }
    Component {
        file: "private/qconvexmeshshape_p.h"
        name: "QConvexMeshShape"
        accessSemantics: "reference"
        prototype: "QMeshShape"
        exports: [
            "QtQuick3D.Physics/ConvexMeshShape 6.0",
            "QtQuick3D.Physics/ConvexMeshShape 6.5",
            "QtQuick3D.Physics/ConvexMeshShape 6.7"
        ]
        exportMetaObjectRevisions: [1536, 1541, 1543]
    }
    Component {
        file: "private/qdynamicrigidbody_p.h"
        name: "QDynamicRigidBody"
        accessSemantics: "reference"
        prototype: "QAbstractPhysicsBody"
        exports: [
            "QtQuick3D.Physics/DynamicRigidBody 6.0",
            "QtQuick3D.Physics/DynamicRigidBody 6.5",
            "QtQuick3D.Physics/DynamicRigidBody 6.7"
        ]
        exportMetaObjectRevisions: [1536, 1541, 1543]
        Enum {
            name: "MassMode"
            values: [
                "DefaultDensity",
                "CustomDensity",
                "Mass",
                "MassAndInertiaTensor",
                "MassAndInertiaMatrix"
            ]
        }
        Enum {
            name: "AxisLock"
            values: ["LockNone", "LockX", "LockY", "LockZ"]
        }
        Property {
            name: "mass"
            type: "float"
            read: "mass"
            write: "setMass"
            notify: "massChanged"
            index: 0
        }
        Property {
            name: "density"
            type: "float"
            read: "density"
            write: "setDensity"
            notify: "densityChanged"
            index: 1
        }
        Property {
            name: "linearAxisLock"
            revision: 1541
            type: "AxisLock"
            read: "linearAxisLock"
            write: "setLinearAxisLock"
            notify: "linearAxisLockChanged"
            index: 2
        }
        Property {
            name: "angularAxisLock"
            revision: 1541
            type: "AxisLock"
            read: "angularAxisLock"
            write: "setAngularAxisLock"
            notify: "angularAxisLockChanged"
            index: 3
        }
        Property {
            name: "isKinematic"
            type: "bool"
            read: "isKinematic"
            write: "setIsKinematic"
            notify: "isKinematicChanged"
            index: 4
        }
        Property {
            name: "gravityEnabled"
            type: "bool"
            read: "gravityEnabled"
            write: "setGravityEnabled"
            notify: "gravityEnabledChanged"
            index: 5
        }
        Property {
            name: "massMode"
            type: "MassMode"
            read: "massMode"
            write: "setMassMode"
            notify: "massModeChanged"
            index: 6
        }
        Property {
            name: "inertiaTensor"
            type: "QVector3D"
            read: "inertiaTensor"
            write: "setInertiaTensor"
            notify: "inertiaTensorChanged"
            index: 7
        }
        Property {
            name: "centerOfMassPosition"
            type: "QVector3D"
            read: "centerOfMassPosition"
            write: "setCenterOfMassPosition"
            notify: "centerOfMassPositionChanged"
            index: 8
        }
        Property {
            name: "centerOfMassRotation"
            type: "QQuaternion"
            read: "centerOfMassRotation"
            write: "setCenterOfMassRotation"
            notify: "centerOfMassRotationChanged"
            index: 9
        }
        Property {
            name: "inertiaMatrix"
            type: "float"
            isList: true
            read: "readInertiaMatrix"
            write: "setInertiaMatrix"
            notify: "inertiaMatrixChanged"
            index: 10
        }
        Property {
            name: "kinematicPosition"
            revision: 1541
            type: "QVector3D"
            read: "kinematicPosition"
            write: "setKinematicPosition"
            notify: "kinematicPositionChanged"
            index: 11
        }
        Property {
            name: "kinematicEulerRotation"
            revision: 1541
            type: "QVector3D"
            read: "kinematicEulerRotation"
            write: "setKinematicEulerRotation"
            notify: "kinematicEulerRotationChanged"
            index: 12
        }
        Property {
            name: "kinematicRotation"
            revision: 1541
            type: "QQuaternion"
            read: "kinematicRotation"
            write: "setKinematicRotation"
            notify: "kinematicRotationChanged"
            index: 13
        }
        Property {
            name: "kinematicPivot"
            revision: 1541
            type: "QVector3D"
            read: "kinematicPivot"
            write: "setKinematicPivot"
            notify: "kinematicPivotChanged"
            index: 14
        }
        Signal {
            name: "massChanged"
            Parameter { name: "mass"; type: "float" }
        }
        Signal {
            name: "densityChanged"
            Parameter { name: "density"; type: "float" }
        }
        Signal {
            name: "isKinematicChanged"
            Parameter { name: "isKinematic"; type: "bool" }
        }
        Signal { name: "linearAxisLockChanged"; revision: 1541 }
        Signal { name: "angularAxisLockChanged"; revision: 1541 }
        Signal { name: "gravityEnabledChanged" }
        Signal { name: "massModeChanged" }
        Signal { name: "inertiaTensorChanged" }
        Signal { name: "centerOfMassPositionChanged" }
        Signal { name: "centerOfMassRotationChanged" }
        Signal { name: "inertiaMatrixChanged" }
        Signal {
            name: "kinematicPositionChanged"
            revision: 1541
            Parameter { name: "kinematicPosition"; type: "QVector3D" }
        }
        Signal {
            name: "kinematicRotationChanged"
            revision: 1541
            Parameter { name: "kinematicRotation"; type: "QQuaternion" }
        }
        Signal {
            name: "kinematicEulerRotationChanged"
            revision: 1541
            Parameter { name: "kinematicEulerRotation"; type: "QVector3D" }
        }
        Signal {
            name: "kinematicPivotChanged"
            revision: 1541
            Parameter { name: "kinematicPivot"; type: "QVector3D" }
        }
        Method {
            name: "applyCentralForce"
            Parameter { name: "force"; type: "QVector3D" }
        }
        Method {
            name: "applyForce"
            Parameter { name: "force"; type: "QVector3D" }
            Parameter { name: "position"; type: "QVector3D" }
        }
        Method {
            name: "applyTorque"
            Parameter { name: "torque"; type: "QVector3D" }
        }
        Method {
            name: "applyCentralImpulse"
            Parameter { name: "impulse"; type: "QVector3D" }
        }
        Method {
            name: "applyImpulse"
            Parameter { name: "impulse"; type: "QVector3D" }
            Parameter { name: "position"; type: "QVector3D" }
        }
        Method {
            name: "applyTorqueImpulse"
            Parameter { name: "impulse"; type: "QVector3D" }
        }
        Method {
            name: "setAngularVelocity"
            Parameter { name: "angularVelocity"; type: "QVector3D" }
        }
        Method {
            name: "setLinearVelocity"
            Parameter { name: "linearVelocity"; type: "QVector3D" }
        }
        Method {
            name: "reset"
            Parameter { name: "position"; type: "QVector3D" }
            Parameter { name: "eulerRotation"; type: "QVector3D" }
        }
    }
    Component {
        file: "private/qheightfieldshape_p.h"
        name: "QHeightFieldShape"
        accessSemantics: "reference"
        prototype: "QAbstractCollisionShape"
        exports: [
            "QtQuick3D.Physics/HeightFieldShape 6.0",
            "QtQuick3D.Physics/HeightFieldShape 6.5",
            "QtQuick3D.Physics/HeightFieldShape 6.7"
        ]
        exportMetaObjectRevisions: [1536, 1541, 1543]
        Property {
            name: "extents"
            type: "QVector3D"
            read: "extents"
            write: "setExtents"
            notify: "extentsChanged"
            index: 0
        }
        Property {
            name: "source"
            revision: 1541
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 1
        }
        Property {
            name: "image"
            revision: 1543
            type: "QQuickImage"
            isPointer: true
            read: "image"
            write: "setImage"
            notify: "imageChanged"
            index: 2
        }
        Signal { name: "sourceChanged"; revision: 1541 }
        Signal { name: "extentsChanged" }
        Signal { name: "imageChanged"; revision: 1543 }
        Method {
            name: "imageDestroyed"
            Parameter { name: "image"; type: "QObject"; isPointer: true }
        }
        Method { name: "imageGeometryChanged" }
    }
    Component {
        file: "private/qmeshshape_p.h"
        name: "QMeshShape"
        accessSemantics: "reference"
        prototype: "QAbstractCollisionShape"
        exports: [
            "QtQuick3D.Physics/MeshShape 6.0",
            "QtQuick3D.Physics/MeshShape 6.5",
            "QtQuick3D.Physics/MeshShape 6.7"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1536, 1541, 1543]
        Property {
            name: "source"
            revision: 1541
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "geometry"
            revision: 1543
            type: "QQuick3DGeometry"
            isPointer: true
            read: "geometry"
            write: "setGeometry"
            notify: "geometryChanged"
            index: 1
        }
        Signal { name: "sourceChanged"; revision: 1541 }
        Signal { name: "geometryChanged"; revision: 1543 }
        Method {
            name: "geometryDestroyed"
            Parameter { name: "geometry"; type: "QObject"; isPointer: true }
        }
        Method { name: "geometryContentChanged" }
    }
    Component {
        file: "private/qphysicsmaterial_p.h"
        name: "QPhysicsMaterial"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick3D.Physics/PhysicsMaterial 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "staticFriction"
            type: "float"
            read: "staticFriction"
            write: "setStaticFriction"
            notify: "staticFrictionChanged"
            index: 0
        }
        Property {
            name: "dynamicFriction"
            type: "float"
            read: "dynamicFriction"
            write: "setDynamicFriction"
            notify: "dynamicFrictionChanged"
            index: 1
        }
        Property {
            name: "restitution"
            type: "float"
            read: "restitution"
            write: "setRestitution"
            notify: "restitutionChanged"
            index: 2
        }
        Signal { name: "staticFrictionChanged" }
        Signal { name: "dynamicFrictionChanged" }
        Signal { name: "restitutionChanged" }
    }
    Component {
        file: "private/qphysicsworld_p.h"
        name: "QPhysicsWorld"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQuick3D.Physics/PhysicsWorld 6.0",
            "QtQuick3D.Physics/PhysicsWorld 6.5",
            "QtQuick3D.Physics/PhysicsWorld 6.7"
        ]
        exportMetaObjectRevisions: [1536, 1541, 1543]
        Property {
            name: "gravity"
            type: "QVector3D"
            read: "gravity"
            write: "setGravity"
            notify: "gravityChanged"
            index: 0
        }
        Property {
            name: "running"
            type: "bool"
            read: "running"
            write: "setRunning"
            notify: "runningChanged"
            index: 1
        }
        Property {
            name: "forceDebugDraw"
            type: "bool"
            read: "forceDebugDraw"
            write: "setForceDebugDraw"
            notify: "forceDebugDrawChanged"
            index: 2
        }
        Property {
            name: "enableCCD"
            type: "bool"
            read: "enableCCD"
            write: "setEnableCCD"
            notify: "enableCCDChanged"
            index: 3
        }
        Property {
            name: "typicalLength"
            type: "float"
            read: "typicalLength"
            write: "setTypicalLength"
            notify: "typicalLengthChanged"
            index: 4
        }
        Property {
            name: "typicalSpeed"
            type: "float"
            read: "typicalSpeed"
            write: "setTypicalSpeed"
            notify: "typicalSpeedChanged"
            index: 5
        }
        Property {
            name: "defaultDensity"
            type: "float"
            read: "defaultDensity"
            write: "setDefaultDensity"
            notify: "defaultDensityChanged"
            index: 6
        }
        Property {
            name: "viewport"
            revision: 1541
            type: "QQuick3DNode"
            isPointer: true
            read: "viewport"
            write: "setViewport"
            notify: "viewportChanged"
            index: 7
        }
        Property {
            name: "minimumTimestep"
            revision: 1541
            type: "float"
            read: "minimumTimestep"
            write: "setMinimumTimestep"
            notify: "minimumTimestepChanged"
            index: 8
        }
        Property {
            name: "maximumTimestep"
            revision: 1541
            type: "float"
            read: "maximumTimestep"
            write: "setMaximumTimestep"
            notify: "maximumTimestepChanged"
            index: 9
        }
        Property {
            name: "scene"
            revision: 1541
            type: "QQuick3DNode"
            isPointer: true
            read: "scene"
            write: "setScene"
            notify: "sceneChanged"
            index: 10
        }
        Property {
            name: "numThreads"
            revision: 1543
            type: "int"
            read: "numThreads"
            write: "setNumThreads"
            notify: "numThreadsChanged"
            index: 11
        }
        Property {
            name: "reportKinematicKinematicCollisions"
            revision: 1543
            type: "bool"
            read: "reportKinematicKinematicCollisions"
            write: "setReportKinematicKinematicCollisions"
            notify: "reportKinematicKinematicCollisionsChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "reportStaticKinematicCollisions"
            revision: 1543
            type: "bool"
            read: "reportStaticKinematicCollisions"
            write: "setReportStaticKinematicCollisions"
            notify: "reportStaticKinematicCollisionsChanged"
            index: 13
            isFinal: true
        }
        Signal {
            name: "gravityChanged"
            Parameter { name: "gravity"; type: "QVector3D" }
        }
        Signal {
            name: "runningChanged"
            Parameter { name: "running"; type: "bool" }
        }
        Signal {
            name: "enableCCDChanged"
            Parameter { name: "enableCCD"; type: "bool" }
        }
        Signal {
            name: "forceDebugDrawChanged"
            Parameter { name: "forceDebugDraw"; type: "bool" }
        }
        Signal {
            name: "typicalLengthChanged"
            Parameter { name: "typicalLength"; type: "float" }
        }
        Signal {
            name: "typicalSpeedChanged"
            Parameter { name: "typicalSpeed"; type: "float" }
        }
        Signal {
            name: "defaultDensityChanged"
            Parameter { name: "defaultDensity"; type: "float" }
        }
        Signal {
            name: "viewportChanged"
            revision: 1541
            Parameter { name: "viewport"; type: "QQuick3DNode"; isPointer: true }
        }
        Signal {
            name: "minimumTimestepChanged"
            revision: 1541
            Parameter { name: "minimumTimestep"; type: "float" }
        }
        Signal {
            name: "maximumTimestepChanged"
            revision: 1541
            Parameter { name: "maxTimestep"; type: "float" }
        }
        Signal {
            name: "simulateFrame"
            Parameter { name: "minTimestep"; type: "float" }
            Parameter { name: "maxTimestep"; type: "float" }
        }
        Signal {
            name: "frameDone"
            revision: 1541
            Parameter { name: "timestep"; type: "float" }
        }
        Signal { name: "sceneChanged"; revision: 1541 }
        Signal { name: "numThreadsChanged"; revision: 1543 }
        Signal { name: "reportKinematicKinematicCollisionsChanged"; revision: 1543 }
        Signal { name: "reportStaticKinematicCollisionsChanged"; revision: 1543 }
        Method {
            name: "setGravity"
            Parameter { name: "gravity"; type: "QVector3D" }
        }
        Method {
            name: "setRunning"
            Parameter { name: "running"; type: "bool" }
        }
        Method {
            name: "setForceDebugDraw"
            Parameter { name: "forceDebugDraw"; type: "bool" }
        }
        Method {
            name: "setEnableCCD"
            Parameter { name: "enableCCD"; type: "bool" }
        }
        Method {
            name: "setTypicalLength"
            Parameter { name: "typicalLength"; type: "float" }
        }
        Method {
            name: "setTypicalSpeed"
            Parameter { name: "typicalSpeed"; type: "float" }
        }
        Method {
            name: "setDefaultDensity"
            Parameter { name: "defaultDensity"; type: "float" }
        }
        Method {
            name: "setViewport"
            revision: 1541
            Parameter { name: "viewport"; type: "QQuick3DNode"; isPointer: true }
        }
        Method {
            name: "setMinimumTimestep"
            revision: 1541
            Parameter { name: "minTimestep"; type: "float" }
        }
        Method {
            name: "setMaximumTimestep"
            revision: 1541
            Parameter { name: "maxTimestep"; type: "float" }
        }
        Method {
            name: "setScene"
            revision: 1541
            Parameter { name: "newScene"; type: "QQuick3DNode"; isPointer: true }
        }
        Method {
            name: "setNumThreads"
            revision: 1543
            Parameter { name: "newNumThreads"; type: "int" }
        }
    }
    Component {
        file: "private/qplaneshape_p.h"
        name: "QPlaneShape"
        accessSemantics: "reference"
        prototype: "QAbstractCollisionShape"
        exports: ["QtQuick3D.Physics/PlaneShape 6.0"]
        exportMetaObjectRevisions: [1536]
    }
    Component {
        file: "private/qsphereshape_p.h"
        name: "QSphereShape"
        accessSemantics: "reference"
        prototype: "QAbstractCollisionShape"
        exports: ["QtQuick3D.Physics/SphereShape 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "diameter"
            type: "float"
            read: "diameter"
            write: "setDiameter"
            notify: "diameterChanged"
            index: 0
        }
        Signal {
            name: "diameterChanged"
            Parameter { name: "diameter"; type: "float" }
        }
        Method {
            name: "setDiameter"
            Parameter { name: "diameter"; type: "float" }
        }
    }
    Component {
        file: "private/qstaticrigidbody_p.h"
        name: "QStaticRigidBody"
        accessSemantics: "reference"
        prototype: "QAbstractPhysicsBody"
        exports: [
            "QtQuick3D.Physics/StaticRigidBody 6.0",
            "QtQuick3D.Physics/StaticRigidBody 6.5",
            "QtQuick3D.Physics/StaticRigidBody 6.7"
        ]
        exportMetaObjectRevisions: [1536, 1541, 1543]
    }
    Component {
        file: "private/qtrianglemeshshape_p.h"
        name: "QTriangleMeshShape"
        accessSemantics: "reference"
        prototype: "QMeshShape"
        exports: [
            "QtQuick3D.Physics/TriangleMeshShape 6.0",
            "QtQuick3D.Physics/TriangleMeshShape 6.5",
            "QtQuick3D.Physics/TriangleMeshShape 6.7"
        ]
        exportMetaObjectRevisions: [1536, 1541, 1543]
    }
    Component {
        file: "private/qtriggerbody_p.h"
        name: "QTriggerBody"
        accessSemantics: "reference"
        prototype: "QAbstractPhysicsNode"
        exports: [
            "QtQuick3D.Physics/TriggerBody 6.0",
            "QtQuick3D.Physics/TriggerBody 6.5",
            "QtQuick3D.Physics/TriggerBody 6.7"
        ]
        exportMetaObjectRevisions: [1536, 1541, 1543]
        Property {
            name: "collisionCount"
            type: "int"
            read: "collisionCount"
            notify: "collisionCountChanged"
            index: 0
            isReadonly: true
        }
        Signal {
            name: "bodyEntered"
            Parameter { name: "body"; type: "QAbstractPhysicsNode"; isPointer: true }
        }
        Signal {
            name: "bodyExited"
            Parameter { name: "body"; type: "QAbstractPhysicsNode"; isPointer: true }
        }
        Signal { name: "collisionCountChanged" }
    }
}
