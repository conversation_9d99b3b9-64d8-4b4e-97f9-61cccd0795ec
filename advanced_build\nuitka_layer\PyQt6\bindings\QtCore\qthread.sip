// qthread.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QThread : public QObject
{
%TypeHeaderCode
#include <qthread.h>
%End

public:
    static QThread *currentThread();
    static Qt::HANDLE currentThreadId();
    static int idealThreadCount();
    static void yieldCurrentThread() /ReleaseGIL/;
    explicit QThread(QObject *parent /TransferThis/ = 0);
    virtual ~QThread();

    enum Priority
    {
        IdlePriority,
        LowestPriority,
        LowPriority,
        NormalPriority,
        HighPriority,
        HighestPriority,
        TimeCriticalPriority,
        InheritPriority,
    };

    bool isFinished() const;
    bool isRunning() const;
    void setPriority(QThread::Priority priority);
    QThread::Priority priority() const;
    void setStackSize(uint stackSize);
    uint stackSize() const;
    void exit(int returnCode = 0) /ReleaseGIL/;

public slots:
    void start(QThread::Priority priority = QThread::InheritPriority) /ReleaseGIL/;
    void terminate();
    void quit();

public:
    bool wait(QDeadlineTimer deadline = QDeadlineTimer(QDeadlineTimer::Forever)) /ReleaseGIL/;
    bool wait(unsigned long time) /ReleaseGIL/;

signals:
    void started();
    void finished();

protected:
    virtual void run() /NewThread,ReleaseGIL/;
    int exec() /ReleaseGIL/;
    static void setTerminationEnabled(bool enabled = true);

public:
    virtual bool event(QEvent *event);
    static void sleep(unsigned long) /ReleaseGIL/;
    static void msleep(unsigned long) /ReleaseGIL/;
    static void usleep(unsigned long) /ReleaseGIL/;
    QAbstractEventDispatcher *eventDispatcher() const;
    void setEventDispatcher(QAbstractEventDispatcher *eventDispatcher /Transfer/);
    void requestInterruption();
    bool isInterruptionRequested() const;
    int loopLevel() const;
};
