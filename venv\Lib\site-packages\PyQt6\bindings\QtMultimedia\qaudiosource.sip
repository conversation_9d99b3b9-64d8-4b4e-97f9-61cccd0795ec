// qaudiosource.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QAudioSource : public QObject
{
%TypeHeaderCode
#include <qaudiosource.h>
%End

public:
    QAudioSource(const QAudioDevice &audioDeviceInfo, const QAudioFormat &format = QAudioFormat(), QObject *parent /TransferThis/ = 0);
    QAudioSource(const QAudioFormat &format = QAudioFormat(), QObject *parent /TransferThis/ = 0);
    virtual ~QAudioSource();
    QAudioFormat format() const;
    void start(QIODevice *device);
    QIODevice *start();
    void stop();
    void reset();
    void suspend();
    void resume();
    void setBufferSize(qsizetype bytes);
    qsizetype bufferSize() const;
    qsizetype bytesAvailable() const;
    void setVolume(qreal volume);
    qreal volume() const;
    qint64 processedUSecs() const;
    qint64 elapsedUSecs() const;
    QAudio::Error error() const;
    QAudio::State state() const;

signals:
    void stateChanged(QAudio::State state);
};

%End
