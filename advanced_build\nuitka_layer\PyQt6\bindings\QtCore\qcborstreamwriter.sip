// qcborstreamwriter.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCborStreamWriter
{
%TypeHeaderCode
#include <qcborstreamwriter.h>
%End

public:
    explicit QCborStreamWriter(QIODevice *device);
    explicit QCborStreamWriter(QByteArray *data /Constrained/);
    ~QCborStreamWriter();
    void setDevice(QIODevice *device);
    QIODevice *device() const;
    void append(const QByteArray &ba);
    void append(QStringView str);
    void append(QCborKnownTags tag);
    void append(QCborSimpleType st);
    void append(bool b /Constrained/);
    void append(double d /Constrained/);
    void append(SIP_PYOBJECT /TypeHint="int"/);
%MethodCode
        static PyObject *zero = 0;
        
        if (!zero)
            zero = PyLong_FromLong(0);
        
        if (PyObject_RichCompareBool(a0, zero, Py_LT) > 0)
        {
            PyErr_Clear();
            qint64 val = sipLong_AsLongLong(a0);
        
            if (PyErr_Occurred())
                sipError = sipErrorFail;
            else
                sipCpp->append(val);
        }
        else
        {
            PyErr_Clear();
            quint64 val = sipLong_AsUnsignedLongLong(a0);
        
            if (PyErr_Occurred())
                sipError = sipErrorFail;
            else
                sipCpp->append(val);
        }
%End

    void appendNull();
    void appendUndefined();
    void startArray();
    void startArray(quint64 count);
    bool endArray();
    void startMap();
    void startMap(quint64 count);
    bool endMap();

private:
    QCborStreamWriter(const QCborStreamWriter &);
};
