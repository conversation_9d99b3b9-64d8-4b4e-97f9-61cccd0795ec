# -*- coding: utf-8 -*-
"""
ADBTools资源管理器
确保ADBTools文件在打包后可用
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# ADBTools文件列表
ADBTOOLS_FILES = {
    "adb.exe": None,
    "AdbWinApi.dll": None,
    "AdbWinUsbApi.dll": None,
    "fastboot.exe": None,
    "libwinpthread-1.dll": None,
    "pay.exe": None,
}

def get_resource_path(relative_path):
    """获取资源文件路径"""
    if hasattr(sys, '_MEIPASS'):
        # 打包后的环境
        base_path = sys._MEIPASS
    else:
        # 开发环境
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

def extract_adbtools():
    """提取ADBTools到临时目录"""
    temp_dir = tempfile.mkdtemp(prefix="adbtools_")
    adbtools_temp = os.path.join(temp_dir, "ADBTools")
    os.makedirs(adbtools_temp, exist_ok=True)
    
    success_count = 0
    for filename in ADBTOOLS_FILES:
        src_path = get_resource_path(f"ADBTools/{filename}")
        dst_path = os.path.join(adbtools_temp, filename)
        
        if os.path.exists(src_path):
            try:
                shutil.copy2(src_path, dst_path)
                if os.path.exists(dst_path):
                    success_count += 1
                    print(f"✅ 提取成功: {filename}")
                else:
                    print(f"❌ 提取失败: {filename}")
            except Exception as e:
                print(f"❌ 提取异常 {filename}: {e}")
        else:
            print(f"❌ 源文件不存在: {filename}")
    
    if success_count > 0:
        print(f"✅ 成功提取 {success_count}/{len(ADBTOOLS_FILES)} 个ADBTools文件")
        return adbtools_temp
    else:
        print("❌ 没有成功提取任何ADBTools文件")
        return None

def get_adbtools_path():
    """获取ADBTools路径"""
    # 首先尝试当前目录
    local_path = "ADBTools"
    if os.path.exists(local_path):
        return local_path
    
    # 然后尝试从资源中提取
    return extract_adbtools()

# 自动初始化
_adbtools_path = None

def init_adbtools():
    """初始化ADBTools"""
    global _adbtools_path
    if _adbtools_path is None:
        _adbtools_path = get_adbtools_path()
    return _adbtools_path

def get_adb_exe():
    """获取adb.exe路径"""
    adbtools_path = init_adbtools()
    if adbtools_path:
        adb_path = os.path.join(adbtools_path, "adb.exe")
        if os.path.exists(adb_path):
            return adb_path
    return None

def get_fastboot_exe():
    """获取fastboot.exe路径"""
    adbtools_path = init_adbtools()
    if adbtools_path:
        fastboot_path = os.path.join(adbtools_path, "fastboot.exe")
        if os.path.exists(fastboot_path):
            return fastboot_path
    return None

# 在模块加载时自动初始化
init_adbtools()
