# -*- coding: utf-8 -*-
"""
验证三个模块的设备检查位置修改
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_all_modules():
    """验证所有三个模块的修改"""
    print("🔍 验证三个模块的设备检查位置修改...")
    
    try:
        # 读取源代码文件
        with open('wr3j.py', 'r', encoding='utf-8') as f:
            wr3j_content = f.read()
            
        with open('bv8k.py', 'r', encoding='utf-8') as f:
            bv8k_content = f.read()
            
        with open('ly6h.py', 'r', encoding='utf-8') as f:
            ly6h_content = f.read()
        
        print("\n📋 检查三个模块的设备检查位置:")
        
        # 检查ColorOS升降 (wr3j.py)
        wr3j_has_switch = "正在切换模式...请勿触碰设备" in wr3j_content
        wr3j_has_check = "✅ 设备在Fastboot模式，可以继续操作" in wr3j_content
        wr3j_check_position = "切换模式" in wr3j_content and "设备在Fastboot模式" in wr3j_content
        
        if wr3j_has_switch and wr3j_has_check:
            print("✅ ColorOS升降 (wr3j.py): 设备检查已添加到切换模式后")
        else:
            print("❌ ColorOS升降 (wr3j.py): 设备检查位置不正确")
        
        # 检查强解功能 (bv8k.py)
        bv8k_has_switch = "切换模式中——请勿干扰,触碰" in bv8k_content
        bv8k_has_check = "✅ 设备在Fastboot模式，可以继续操作" in bv8k_content
        
        if bv8k_has_switch and bv8k_has_check:
            print("✅ 强解功能 (bv8k.py): 设备检查已添加到切换模式后")
        else:
            print("❌ 强解功能 (bv8k.py): 设备检查位置不正确")
        
        # 检查FastbooDT修复 (ly6h.py)
        ly6h_has_restart = "正在测试重启" in ly6h_content
        ly6h_has_check = "✅ 设备在Fastboot模式，可以继续操作" in ly6h_content
        
        if ly6h_has_restart and ly6h_has_check:
            print("✅ FastbooDT修复 (ly6h.py): 设备检查已添加到重启后")
        else:
            print("❌ FastbooDT修复 (ly6h.py): 设备检查位置不正确")
        
        # 检查是否移除了原来的刷机后检查
        print("\n📋 检查是否移除了原来的刷机后检查:")
        
        wr3j_no_old_check = "self.check_post_flash_mode()" not in wr3j_content
        bv8k_no_old_check = "self.check_post_flash_mode()" not in bv8k_content
        
        if wr3j_no_old_check:
            print("✅ ColorOS升降: 已移除完成后的设备检查")
        else:
            print("❌ ColorOS升降: 仍有完成后的设备检查")
            
        if bv8k_no_old_check:
            print("✅ 强解功能: 已移除完成后的设备检查")
        else:
            print("❌ 强解功能: 仍有完成后的设备检查")
        
        # FastbooDT原本就没有完成后检查，所以不需要检查
        print("✅ FastbooDT修复: 原本就没有完成后检查")
        
        # 检查功能完整性
        print("\n🔧 检查功能完整性:")
        
        functionality_checks = [
            ('ColorOS模式检查方法', 'check_fastboot_type' in wr3j_content),
            ('强解模式检查方法', 'check_fastboot_type' in bv8k_content),
            ('FastbooDT模式检查方法', 'check_fastboot_type' in ly6h_content),
        ]
        
        all_functional = True
        for name, condition in functionality_checks:
            if condition:
                print(f"✅ {name} - 正常")
            else:
                print(f"❌ {name} - 缺失")
                all_functional = False
        
        print("\n🎯 修改后的三个模块日志流程:")
        print("\n1️⃣ ColorOS升降:")
        print("   - 开始刷机...")
        print("   - 载入刷机")
        print("   - 正在切换模式...请勿触碰设备")
        print("   - ✅ 设备在Fastboot模式，可以继续操作")
        print("   - 在检查cow")
        print("   - Loading.. [X/Y] 进度：X%")
        print("   - 完成")
        
        print("\n2️⃣ 强解功能:")
        print("   - 开始刷机...")
        print("   - 载入刷机")
        print("   - 切换模式中——请勿干扰,触碰")
        print("   - ✅ 设备在Fastboot模式，可以继续操作")
        print("   - Loading.. [X/Y] 进度：X%")
        print("   - 完成")
        
        print("\n3️⃣ FastbooDT修复:")
        print("   - fastbooDT刷机流程开始...")
        print("   - Loading.. [X/Y] 进度：X%")
        print("   - 正在测试重启")
        print("   - ✅ 设备在Fastboot模式，可以继续操作")
        print("   - 设备已重启到 fastboot 模式...")
        
        # 总体检查
        all_good = (
            wr3j_has_switch and wr3j_has_check and
            bv8k_has_switch and bv8k_has_check and
            ly6h_has_restart and ly6h_has_check and
            wr3j_no_old_check and bv8k_no_old_check and
            all_functional
        )
        
        if all_good:
            print("\n🎉 所有三个模块的设备检查位置修改都已完成!")
            print("现在所有模块都会在适当的时机进行设备检查")
        else:
            print("\n❌ 发现问题，请检查修改")
        
        return all_good
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    verify_all_modules()
