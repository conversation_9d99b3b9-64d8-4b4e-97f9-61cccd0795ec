# The PEP 484 type hints stub file for the QtMultimedia module.
#
# Generated by SIP 6.8.6
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQt6.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICEN<PERSON> included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import enum
import typing

import PyQt6.sip

from PyQt6 import QtCore
from PyQt6 import QtGui
from PyQt6 import QtNetwork

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[typing.Callable[..., Any], QtCore.pyqtBoundSignal]


class QtVideo(PyQt6.sip.simplewrapper):

    class Rotation(enum.Enum):
        None_ = ... # type: QtVideo.Rotation
        Clockwise90 = ... # type: QtVideo.Rotation
        Clockwise180 = ... # type: QtVideo.Rotation
        Clockwise270 = ... # type: QtVideo.Rotation


class QAudio(PyQt6.sip.simplewrapper):

    class VolumeScale(enum.Enum):
        LinearVolumeScale = ... # type: QAudio.VolumeScale
        CubicVolumeScale = ... # type: QAudio.VolumeScale
        LogarithmicVolumeScale = ... # type: QAudio.VolumeScale
        DecibelVolumeScale = ... # type: QAudio.VolumeScale

    class State(enum.Enum):
        ActiveState = ... # type: QAudio.State
        SuspendedState = ... # type: QAudio.State
        StoppedState = ... # type: QAudio.State
        IdleState = ... # type: QAudio.State

    class Error(enum.Enum):
        NoError = ... # type: QAudio.Error
        OpenError = ... # type: QAudio.Error
        IOError = ... # type: QAudio.Error
        UnderrunError = ... # type: QAudio.Error
        FatalError = ... # type: QAudio.Error

    def convertVolume(self, volume: float, from_: 'QAudio.VolumeScale', to: 'QAudio.VolumeScale') -> float: ...


class QAudioBuffer(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, data: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], format: 'QAudioFormat', startTime: int = ...) -> None: ...
    @typing.overload
    def __init__(self, numFrames: int, format: 'QAudioFormat', startTime: int = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QAudioBuffer') -> None: ...

    def data(self) -> PyQt6.sip.voidptr: ...
    def constData(self) -> PyQt6.sip.voidptr: ...
    def detach(self) -> None: ...
    def swap(self, other: 'QAudioBuffer') -> None: ...
    def startTime(self) -> int: ...
    def duration(self) -> int: ...
    def byteCount(self) -> int: ...
    def sampleCount(self) -> int: ...
    def frameCount(self) -> int: ...
    def format(self) -> 'QAudioFormat': ...
    def isValid(self) -> bool: ...


class QAudioDecoder(QtCore.QObject):

    class Error(enum.Enum):
        NoError = ... # type: QAudioDecoder.Error
        ResourceError = ... # type: QAudioDecoder.Error
        FormatError = ... # type: QAudioDecoder.Error
        AccessDeniedError = ... # type: QAudioDecoder.Error
        NotSupportedError = ... # type: QAudioDecoder.Error

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    formatChanged: typing.ClassVar[QtCore.pyqtSignal]
    durationChanged: typing.ClassVar[QtCore.pyqtSignal]
    positionChanged: typing.ClassVar[QtCore.pyqtSignal]
    sourceChanged: typing.ClassVar[QtCore.pyqtSignal]
    isDecodingChanged: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    bufferReady: typing.ClassVar[QtCore.pyqtSignal]
    bufferAvailableChanged: typing.ClassVar[QtCore.pyqtSignal]
    def stop(self) -> None: ...
    def start(self) -> None: ...
    def setAudioFormat(self, format: 'QAudioFormat') -> None: ...
    def audioFormat(self) -> 'QAudioFormat': ...
    def duration(self) -> int: ...
    def position(self) -> int: ...
    def bufferAvailable(self) -> bool: ...
    def read(self) -> QAudioBuffer: ...
    def errorString(self) -> str: ...
    error: typing.ClassVar[QtCore.pyqtSignal]
    def setSourceDevice(self, device: typing.Optional[QtCore.QIODevice]) -> None: ...
    def sourceDevice(self) -> typing.Optional[QtCore.QIODevice]: ...
    def setSource(self, fileName: QtCore.QUrl) -> None: ...
    def source(self) -> QtCore.QUrl: ...
    def isDecoding(self) -> bool: ...
    def isSupported(self) -> bool: ...


class QAudioDevice(PyQt6.sip.simplewrapper):

    class Mode(enum.Enum):
        Null = ... # type: QAudioDevice.Mode
        Input = ... # type: QAudioDevice.Mode
        Output = ... # type: QAudioDevice.Mode

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QAudioDevice') -> None: ...

    def channelConfiguration(self) -> 'QAudioFormat.ChannelConfig': ...
    def supportedSampleFormats(self) -> typing.List['QAudioFormat.SampleFormat']: ...
    def maximumChannelCount(self) -> int: ...
    def minimumChannelCount(self) -> int: ...
    def maximumSampleRate(self) -> int: ...
    def minimumSampleRate(self) -> int: ...
    def preferredFormat(self) -> 'QAudioFormat': ...
    def isFormatSupported(self, format: 'QAudioFormat') -> bool: ...
    def mode(self) -> 'QAudioDevice.Mode': ...
    def isDefault(self) -> bool: ...
    def description(self) -> str: ...
    def id(self) -> QtCore.QByteArray: ...
    def isNull(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def swap(self, other: 'QAudioDevice') -> None: ...


class QAudioFormat(PyQt6.sip.simplewrapper):

    class SampleFormat(enum.Enum):
        Unknown = ... # type: QAudioFormat.SampleFormat
        UInt8 = ... # type: QAudioFormat.SampleFormat
        Int16 = ... # type: QAudioFormat.SampleFormat
        Int32 = ... # type: QAudioFormat.SampleFormat
        Float = ... # type: QAudioFormat.SampleFormat

    class ChannelConfig(enum.Enum):
        ChannelConfigUnknown = ... # type: QAudioFormat.ChannelConfig
        ChannelConfigMono = ... # type: QAudioFormat.ChannelConfig
        ChannelConfigStereo = ... # type: QAudioFormat.ChannelConfig
        ChannelConfig2Dot1 = ... # type: QAudioFormat.ChannelConfig
        ChannelConfigSurround5Dot0 = ... # type: QAudioFormat.ChannelConfig
        ChannelConfigSurround5Dot1 = ... # type: QAudioFormat.ChannelConfig
        ChannelConfigSurround7Dot0 = ... # type: QAudioFormat.ChannelConfig
        ChannelConfigSurround7Dot1 = ... # type: QAudioFormat.ChannelConfig
        ChannelConfig3Dot0 = ... # type: QAudioFormat.ChannelConfig
        ChannelConfig3Dot1 = ... # type: QAudioFormat.ChannelConfig

    class AudioChannelPosition(enum.Enum):
        UnknownPosition = ... # type: QAudioFormat.AudioChannelPosition
        FrontLeft = ... # type: QAudioFormat.AudioChannelPosition
        FrontRight = ... # type: QAudioFormat.AudioChannelPosition
        FrontCenter = ... # type: QAudioFormat.AudioChannelPosition
        LFE = ... # type: QAudioFormat.AudioChannelPosition
        BackLeft = ... # type: QAudioFormat.AudioChannelPosition
        BackRight = ... # type: QAudioFormat.AudioChannelPosition
        FrontLeftOfCenter = ... # type: QAudioFormat.AudioChannelPosition
        FrontRightOfCenter = ... # type: QAudioFormat.AudioChannelPosition
        BackCenter = ... # type: QAudioFormat.AudioChannelPosition
        LFE2 = ... # type: QAudioFormat.AudioChannelPosition
        SideLeft = ... # type: QAudioFormat.AudioChannelPosition
        SideRight = ... # type: QAudioFormat.AudioChannelPosition
        TopFrontLeft = ... # type: QAudioFormat.AudioChannelPosition
        TopFrontRight = ... # type: QAudioFormat.AudioChannelPosition
        TopFrontCenter = ... # type: QAudioFormat.AudioChannelPosition
        TopCenter = ... # type: QAudioFormat.AudioChannelPosition
        TopBackLeft = ... # type: QAudioFormat.AudioChannelPosition
        TopBackRight = ... # type: QAudioFormat.AudioChannelPosition
        TopSideLeft = ... # type: QAudioFormat.AudioChannelPosition
        TopSideRight = ... # type: QAudioFormat.AudioChannelPosition
        TopBackCenter = ... # type: QAudioFormat.AudioChannelPosition
        BottomFrontCenter = ... # type: QAudioFormat.AudioChannelPosition
        BottomFrontLeft = ... # type: QAudioFormat.AudioChannelPosition
        BottomFrontRight = ... # type: QAudioFormat.AudioChannelPosition

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QAudioFormat') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    @staticmethod
    def defaultChannelConfigForChannelCount(channelCount: int) -> 'QAudioFormat.ChannelConfig': ...
    def normalizedSampleValue(self, sample: typing.Optional[PyQt6.sip.voidptr]) -> float: ...
    def bytesPerSample(self) -> int: ...
    def sampleFormat(self) -> 'QAudioFormat.SampleFormat': ...
    def setSampleFormat(self, f: 'QAudioFormat.SampleFormat') -> None: ...
    def channelOffset(self, channel: 'QAudioFormat.AudioChannelPosition') -> int: ...
    def channelConfig(self) -> 'QAudioFormat.ChannelConfig': ...
    def setChannelConfig(self, config: 'QAudioFormat.ChannelConfig') -> None: ...
    def bytesPerFrame(self) -> int: ...
    def durationForFrames(self, frameCount: int) -> int: ...
    def framesForDuration(self, duration: int) -> int: ...
    def framesForBytes(self, byteCount: int) -> int: ...
    def bytesForFrames(self, frameCount: int) -> int: ...
    def durationForBytes(self, byteCount: int) -> int: ...
    def bytesForDuration(self, duration: int) -> int: ...
    def channelCount(self) -> int: ...
    def setChannelCount(self, channelCount: int) -> None: ...
    def sampleRate(self) -> int: ...
    def setSampleRate(self, sampleRate: int) -> None: ...
    def isValid(self) -> bool: ...


class QAudioInput(QtCore.QObject):

    @typing.overload
    def __init__(self, deviceInfo: QAudioDevice, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    mutedChanged: typing.ClassVar[QtCore.pyqtSignal]
    volumeChanged: typing.ClassVar[QtCore.pyqtSignal]
    deviceChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setVolume(self, volume: float) -> None: ...
    def setMuted(self, muted: bool) -> None: ...
    def setDevice(self, device: QAudioDevice) -> None: ...
    def isMuted(self) -> bool: ...
    def volume(self) -> float: ...
    def device(self) -> QAudioDevice: ...


class QAudioOutput(QtCore.QObject):

    @typing.overload
    def __init__(self, device: QAudioDevice, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    mutedChanged: typing.ClassVar[QtCore.pyqtSignal]
    volumeChanged: typing.ClassVar[QtCore.pyqtSignal]
    deviceChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setMuted(self, muted: bool) -> None: ...
    def setVolume(self, volume: float) -> None: ...
    def setDevice(self, device: QAudioDevice) -> None: ...
    def isMuted(self) -> bool: ...
    def device(self) -> QAudioDevice: ...
    def volume(self) -> float: ...


class QAudioSink(QtCore.QObject):

    @typing.overload
    def __init__(self, audioDeviceInfo: QAudioDevice, format: QAudioFormat = ..., parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, format: QAudioFormat = ..., parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    def volume(self) -> float: ...
    def setVolume(self, a0: float) -> None: ...
    def state(self) -> QAudio.State: ...
    def error(self) -> QAudio.Error: ...
    def elapsedUSecs(self) -> int: ...
    def processedUSecs(self) -> int: ...
    def bytesFree(self) -> int: ...
    def bufferSize(self) -> int: ...
    def setBufferSize(self, bytes: int) -> None: ...
    def resume(self) -> None: ...
    def suspend(self) -> None: ...
    def reset(self) -> None: ...
    def stop(self) -> None: ...
    @typing.overload
    def start(self, device: typing.Optional[QtCore.QIODevice]) -> None: ...
    @typing.overload
    def start(self) -> typing.Optional[QtCore.QIODevice]: ...
    def format(self) -> QAudioFormat: ...


class QAudioSource(QtCore.QObject):

    @typing.overload
    def __init__(self, audioDeviceInfo: QAudioDevice, format: QAudioFormat = ..., parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, format: QAudioFormat = ..., parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    def state(self) -> QAudio.State: ...
    def error(self) -> QAudio.Error: ...
    def elapsedUSecs(self) -> int: ...
    def processedUSecs(self) -> int: ...
    def volume(self) -> float: ...
    def setVolume(self, volume: float) -> None: ...
    def bytesAvailable(self) -> int: ...
    def bufferSize(self) -> int: ...
    def setBufferSize(self, bytes: int) -> None: ...
    def resume(self) -> None: ...
    def suspend(self) -> None: ...
    def reset(self) -> None: ...
    def stop(self) -> None: ...
    @typing.overload
    def start(self, device: typing.Optional[QtCore.QIODevice]) -> None: ...
    @typing.overload
    def start(self) -> typing.Optional[QtCore.QIODevice]: ...
    def format(self) -> QAudioFormat: ...


class QCamera(QtCore.QObject):

    class Feature(enum.Enum):
        ColorTemperature = ... # type: QCamera.Feature
        ExposureCompensation = ... # type: QCamera.Feature
        IsoSensitivity = ... # type: QCamera.Feature
        ManualExposureTime = ... # type: QCamera.Feature
        CustomFocusPoint = ... # type: QCamera.Feature
        FocusDistance = ... # type: QCamera.Feature

    class WhiteBalanceMode(enum.Enum):
        WhiteBalanceAuto = ... # type: QCamera.WhiteBalanceMode
        WhiteBalanceManual = ... # type: QCamera.WhiteBalanceMode
        WhiteBalanceSunlight = ... # type: QCamera.WhiteBalanceMode
        WhiteBalanceCloudy = ... # type: QCamera.WhiteBalanceMode
        WhiteBalanceShade = ... # type: QCamera.WhiteBalanceMode
        WhiteBalanceTungsten = ... # type: QCamera.WhiteBalanceMode
        WhiteBalanceFluorescent = ... # type: QCamera.WhiteBalanceMode
        WhiteBalanceFlash = ... # type: QCamera.WhiteBalanceMode
        WhiteBalanceSunset = ... # type: QCamera.WhiteBalanceMode

    class ExposureMode(enum.Enum):
        ExposureAuto = ... # type: QCamera.ExposureMode
        ExposureManual = ... # type: QCamera.ExposureMode
        ExposurePortrait = ... # type: QCamera.ExposureMode
        ExposureNight = ... # type: QCamera.ExposureMode
        ExposureSports = ... # type: QCamera.ExposureMode
        ExposureSnow = ... # type: QCamera.ExposureMode
        ExposureBeach = ... # type: QCamera.ExposureMode
        ExposureAction = ... # type: QCamera.ExposureMode
        ExposureLandscape = ... # type: QCamera.ExposureMode
        ExposureNightPortrait = ... # type: QCamera.ExposureMode
        ExposureTheatre = ... # type: QCamera.ExposureMode
        ExposureSunset = ... # type: QCamera.ExposureMode
        ExposureSteadyPhoto = ... # type: QCamera.ExposureMode
        ExposureFireworks = ... # type: QCamera.ExposureMode
        ExposureParty = ... # type: QCamera.ExposureMode
        ExposureCandlelight = ... # type: QCamera.ExposureMode
        ExposureBarcode = ... # type: QCamera.ExposureMode

    class TorchMode(enum.Enum):
        TorchOff = ... # type: QCamera.TorchMode
        TorchOn = ... # type: QCamera.TorchMode
        TorchAuto = ... # type: QCamera.TorchMode

    class FlashMode(enum.Enum):
        FlashOff = ... # type: QCamera.FlashMode
        FlashOn = ... # type: QCamera.FlashMode
        FlashAuto = ... # type: QCamera.FlashMode

    class FocusMode(enum.Enum):
        FocusModeAuto = ... # type: QCamera.FocusMode
        FocusModeAutoNear = ... # type: QCamera.FocusMode
        FocusModeAutoFar = ... # type: QCamera.FocusMode
        FocusModeHyperfocal = ... # type: QCamera.FocusMode
        FocusModeInfinity = ... # type: QCamera.FocusMode
        FocusModeManual = ... # type: QCamera.FocusMode

    class Error(enum.Enum):
        NoError = ... # type: QCamera.Error
        CameraError = ... # type: QCamera.Error

    @typing.overload
    def __init__(self, cameraDevice: 'QCameraDevice', parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, position: 'QCameraDevice.Position', parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    manualIsoSensitivityChanged: typing.ClassVar[QtCore.pyqtSignal]
    manualExposureTimeChanged: typing.ClassVar[QtCore.pyqtSignal]
    focusPointChanged: typing.ClassVar[QtCore.pyqtSignal]
    colorTemperatureChanged: typing.ClassVar[QtCore.pyqtSignal]
    whiteBalanceModeChanged: typing.ClassVar[QtCore.pyqtSignal]
    exposureModeChanged: typing.ClassVar[QtCore.pyqtSignal]
    exposureCompensationChanged: typing.ClassVar[QtCore.pyqtSignal]
    isoSensitivityChanged: typing.ClassVar[QtCore.pyqtSignal]
    exposureTimeChanged: typing.ClassVar[QtCore.pyqtSignal]
    torchModeChanged: typing.ClassVar[QtCore.pyqtSignal]
    flashModeChanged: typing.ClassVar[QtCore.pyqtSignal]
    flashReady: typing.ClassVar[QtCore.pyqtSignal]
    customFocusPointChanged: typing.ClassVar[QtCore.pyqtSignal]
    focusDistanceChanged: typing.ClassVar[QtCore.pyqtSignal]
    maximumZoomFactorChanged: typing.ClassVar[QtCore.pyqtSignal]
    minimumZoomFactorChanged: typing.ClassVar[QtCore.pyqtSignal]
    zoomFactorChanged: typing.ClassVar[QtCore.pyqtSignal]
    focusModeChanged: typing.ClassVar[QtCore.pyqtSignal]
    supportedFeaturesChanged: typing.ClassVar[QtCore.pyqtSignal]
    cameraFormatChanged: typing.ClassVar[QtCore.pyqtSignal]
    cameraDeviceChanged: typing.ClassVar[QtCore.pyqtSignal]
    errorOccurred: typing.ClassVar[QtCore.pyqtSignal]
    errorChanged: typing.ClassVar[QtCore.pyqtSignal]
    activeChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setColorTemperature(self, colorTemperature: int) -> None: ...
    def setWhiteBalanceMode(self, mode: 'QCamera.WhiteBalanceMode') -> None: ...
    def setAutoExposureTime(self) -> None: ...
    def setManualExposureTime(self, seconds: float) -> None: ...
    def setAutoIsoSensitivity(self) -> None: ...
    def setManualIsoSensitivity(self, iso: int) -> None: ...
    def setExposureCompensation(self, ev: float) -> None: ...
    def setExposureMode(self, mode: 'QCamera.ExposureMode') -> None: ...
    def setTorchMode(self, mode: 'QCamera.TorchMode') -> None: ...
    def setFlashMode(self, mode: 'QCamera.FlashMode') -> None: ...
    def zoomTo(self, zoom: float, rate: float) -> None: ...
    def stop(self) -> None: ...
    def start(self) -> None: ...
    def setActive(self, active: bool) -> None: ...
    def colorTemperature(self) -> int: ...
    def isWhiteBalanceModeSupported(self, mode: 'QCamera.WhiteBalanceMode') -> bool: ...
    def whiteBalanceMode(self) -> 'QCamera.WhiteBalanceMode': ...
    def maximumExposureTime(self) -> float: ...
    def minimumExposureTime(self) -> float: ...
    def maximumIsoSensitivity(self) -> int: ...
    def minimumIsoSensitivity(self) -> int: ...
    def manualExposureTime(self) -> float: ...
    def exposureTime(self) -> float: ...
    def manualIsoSensitivity(self) -> int: ...
    def isoSensitivity(self) -> int: ...
    def exposureCompensation(self) -> float: ...
    def isExposureModeSupported(self, mode: 'QCamera.ExposureMode') -> bool: ...
    def exposureMode(self) -> 'QCamera.ExposureMode': ...
    def isTorchModeSupported(self, mode: 'QCamera.TorchMode') -> bool: ...
    def torchMode(self) -> 'QCamera.TorchMode': ...
    def isFlashReady(self) -> bool: ...
    def isFlashModeSupported(self, mode: 'QCamera.FlashMode') -> bool: ...
    def flashMode(self) -> 'QCamera.FlashMode': ...
    def setZoomFactor(self, factor: float) -> None: ...
    def zoomFactor(self) -> float: ...
    def maximumZoomFactor(self) -> float: ...
    def minimumZoomFactor(self) -> float: ...
    def focusDistance(self) -> float: ...
    def setFocusDistance(self, d: float) -> None: ...
    def setCustomFocusPoint(self, point: QtCore.QPointF) -> None: ...
    def customFocusPoint(self) -> QtCore.QPointF: ...
    def focusPoint(self) -> QtCore.QPointF: ...
    def isFocusModeSupported(self, mode: 'QCamera.FocusMode') -> bool: ...
    def setFocusMode(self, mode: 'QCamera.FocusMode') -> None: ...
    def focusMode(self) -> 'QCamera.FocusMode': ...
    def supportedFeatures(self) -> 'QCamera.Feature': ...
    def errorString(self) -> str: ...
    def error(self) -> 'QCamera.Error': ...
    def setCameraFormat(self, format: 'QCameraFormat') -> None: ...
    def cameraFormat(self) -> 'QCameraFormat': ...
    def setCameraDevice(self, cameraDevice: 'QCameraDevice') -> None: ...
    def cameraDevice(self) -> 'QCameraDevice': ...
    def captureSession(self) -> typing.Optional['QMediaCaptureSession']: ...
    def isActive(self) -> bool: ...
    def isAvailable(self) -> bool: ...


class QCameraFormat(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QCameraFormat') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def isNull(self) -> bool: ...
    def maxFrameRate(self) -> float: ...
    def minFrameRate(self) -> float: ...
    def resolution(self) -> QtCore.QSize: ...
    def pixelFormat(self) -> 'QVideoFrameFormat.PixelFormat': ...


class QCameraDevice(PyQt6.sip.simplewrapper):

    class Position(enum.Enum):
        UnspecifiedPosition = ... # type: QCameraDevice.Position
        BackFace = ... # type: QCameraDevice.Position
        FrontFace = ... # type: QCameraDevice.Position

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QCameraDevice') -> None: ...

    def correctionAngle(self) -> QtVideo.Rotation: ...
    def videoFormats(self) -> typing.List[QCameraFormat]: ...
    def photoResolutions(self) -> typing.List[QtCore.QSize]: ...
    def position(self) -> 'QCameraDevice.Position': ...
    def isDefault(self) -> bool: ...
    def description(self) -> str: ...
    def id(self) -> QtCore.QByteArray: ...
    def isNull(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QCapturableWindow(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QCapturableWindow') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def description(self) -> str: ...
    def isValid(self) -> bool: ...
    def swap(self, other: 'QCapturableWindow') -> None: ...


class QImageCapture(QtCore.QObject):

    class FileFormat(enum.Enum):
        UnspecifiedFormat = ... # type: QImageCapture.FileFormat
        JPEG = ... # type: QImageCapture.FileFormat
        PNG = ... # type: QImageCapture.FileFormat
        WebP = ... # type: QImageCapture.FileFormat
        Tiff = ... # type: QImageCapture.FileFormat

    class Quality(enum.Enum):
        VeryLowQuality = ... # type: QImageCapture.Quality
        LowQuality = ... # type: QImageCapture.Quality
        NormalQuality = ... # type: QImageCapture.Quality
        HighQuality = ... # type: QImageCapture.Quality
        VeryHighQuality = ... # type: QImageCapture.Quality

    class Error(enum.Enum):
        NoError = ... # type: QImageCapture.Error
        NotReadyError = ... # type: QImageCapture.Error
        ResourceError = ... # type: QImageCapture.Error
        OutOfSpaceError = ... # type: QImageCapture.Error
        NotSupportedFeatureError = ... # type: QImageCapture.Error
        FormatError = ... # type: QImageCapture.Error

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    imageSaved: typing.ClassVar[QtCore.pyqtSignal]
    imageAvailable: typing.ClassVar[QtCore.pyqtSignal]
    imageCaptured: typing.ClassVar[QtCore.pyqtSignal]
    imageExposed: typing.ClassVar[QtCore.pyqtSignal]
    resolutionChanged: typing.ClassVar[QtCore.pyqtSignal]
    qualityChanged: typing.ClassVar[QtCore.pyqtSignal]
    fileFormatChanged: typing.ClassVar[QtCore.pyqtSignal]
    metaDataChanged: typing.ClassVar[QtCore.pyqtSignal]
    readyForCaptureChanged: typing.ClassVar[QtCore.pyqtSignal]
    errorOccurred: typing.ClassVar[QtCore.pyqtSignal]
    errorChanged: typing.ClassVar[QtCore.pyqtSignal]
    def capture(self) -> int: ...
    def captureToFile(self, location: typing.Optional[str] = ...) -> int: ...
    def addMetaData(self, metaData: 'QMediaMetaData') -> None: ...
    def setMetaData(self, metaData: 'QMediaMetaData') -> None: ...
    def metaData(self) -> 'QMediaMetaData': ...
    def setQuality(self, quality: 'QImageCapture.Quality') -> None: ...
    def quality(self) -> 'QImageCapture.Quality': ...
    @typing.overload
    def setResolution(self, a0: QtCore.QSize) -> None: ...
    @typing.overload
    def setResolution(self, width: int, height: int) -> None: ...
    def resolution(self) -> QtCore.QSize: ...
    @staticmethod
    def fileFormatDescription(c: 'QImageCapture.FileFormat') -> str: ...
    @staticmethod
    def fileFormatName(c: 'QImageCapture.FileFormat') -> str: ...
    @staticmethod
    def supportedFormats() -> typing.List['QImageCapture.FileFormat']: ...
    def setFileFormat(self, format: 'QImageCapture.FileFormat') -> None: ...
    def fileFormat(self) -> 'QImageCapture.FileFormat': ...
    def isReadyForCapture(self) -> bool: ...
    def errorString(self) -> str: ...
    def error(self) -> 'QImageCapture.Error': ...
    def captureSession(self) -> typing.Optional['QMediaCaptureSession']: ...
    def isAvailable(self) -> bool: ...


class QMediaCaptureSession(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    windowCaptureChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setWindowCapture(self, windowCapture: typing.Optional['QWindowCapture']) -> None: ...
    def windowCapture(self) -> typing.Optional['QWindowCapture']: ...
    screenCaptureChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setScreenCapture(self, screenCapture: typing.Optional['QScreenCapture']) -> None: ...
    def screenCapture(self) -> typing.Optional['QScreenCapture']: ...
    audioOutputChanged: typing.ClassVar[QtCore.pyqtSignal]
    videoOutputChanged: typing.ClassVar[QtCore.pyqtSignal]
    recorderChanged: typing.ClassVar[QtCore.pyqtSignal]
    imageCaptureChanged: typing.ClassVar[QtCore.pyqtSignal]
    cameraChanged: typing.ClassVar[QtCore.pyqtSignal]
    audioInputChanged: typing.ClassVar[QtCore.pyqtSignal]
    def audioOutput(self) -> typing.Optional[QAudioOutput]: ...
    def setAudioOutput(self, output: typing.Optional[QAudioOutput]) -> None: ...
    def videoSink(self) -> typing.Optional['QVideoSink']: ...
    def setVideoSink(self, sink: typing.Optional['QVideoSink']) -> None: ...
    def videoOutput(self) -> typing.Optional[QtCore.QObject]: ...
    def setVideoOutput(self, output: typing.Optional[QtCore.QObject]) -> None: ...
    def setRecorder(self, recorder: typing.Optional['QMediaRecorder']) -> None: ...
    def recorder(self) -> typing.Optional['QMediaRecorder']: ...
    def setImageCapture(self, imageCapture: typing.Optional[QImageCapture]) -> None: ...
    def imageCapture(self) -> typing.Optional[QImageCapture]: ...
    def setCamera(self, camera: typing.Optional[QCamera]) -> None: ...
    def camera(self) -> typing.Optional[QCamera]: ...
    def setAudioInput(self, device: typing.Optional[QAudioInput]) -> None: ...
    def audioInput(self) -> typing.Optional[QAudioInput]: ...


class QMediaDevices(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    videoInputsChanged: typing.ClassVar[QtCore.pyqtSignal]
    audioOutputsChanged: typing.ClassVar[QtCore.pyqtSignal]
    audioInputsChanged: typing.ClassVar[QtCore.pyqtSignal]
    @staticmethod
    def defaultVideoInput() -> QCameraDevice: ...
    @staticmethod
    def defaultAudioOutput() -> QAudioDevice: ...
    @staticmethod
    def defaultAudioInput() -> QAudioDevice: ...
    @staticmethod
    def videoInputs() -> typing.List[QCameraDevice]: ...
    @staticmethod
    def audioOutputs() -> typing.List[QAudioDevice]: ...
    @staticmethod
    def audioInputs() -> typing.List[QAudioDevice]: ...


class QMediaFormat(PyQt6.sip.simplewrapper):

    class ResolveFlags(enum.Enum):
        NoFlags = ... # type: QMediaFormat.ResolveFlags
        RequiresVideo = ... # type: QMediaFormat.ResolveFlags

    class ConversionMode(enum.Enum):
        Encode = ... # type: QMediaFormat.ConversionMode
        Decode = ... # type: QMediaFormat.ConversionMode

    class VideoCodec(enum.Enum):
        Unspecified = ... # type: QMediaFormat.VideoCodec
        MPEG1 = ... # type: QMediaFormat.VideoCodec
        MPEG2 = ... # type: QMediaFormat.VideoCodec
        MPEG4 = ... # type: QMediaFormat.VideoCodec
        H264 = ... # type: QMediaFormat.VideoCodec
        H265 = ... # type: QMediaFormat.VideoCodec
        VP8 = ... # type: QMediaFormat.VideoCodec
        VP9 = ... # type: QMediaFormat.VideoCodec
        AV1 = ... # type: QMediaFormat.VideoCodec
        Theora = ... # type: QMediaFormat.VideoCodec
        WMV = ... # type: QMediaFormat.VideoCodec
        MotionJPEG = ... # type: QMediaFormat.VideoCodec

    class AudioCodec(enum.Enum):
        Unspecified = ... # type: QMediaFormat.AudioCodec
        MP3 = ... # type: QMediaFormat.AudioCodec
        AAC = ... # type: QMediaFormat.AudioCodec
        AC3 = ... # type: QMediaFormat.AudioCodec
        EAC3 = ... # type: QMediaFormat.AudioCodec
        FLAC = ... # type: QMediaFormat.AudioCodec
        DolbyTrueHD = ... # type: QMediaFormat.AudioCodec
        Opus = ... # type: QMediaFormat.AudioCodec
        Vorbis = ... # type: QMediaFormat.AudioCodec
        Wave = ... # type: QMediaFormat.AudioCodec
        WMA = ... # type: QMediaFormat.AudioCodec
        ALAC = ... # type: QMediaFormat.AudioCodec

    class FileFormat(enum.Enum):
        UnspecifiedFormat = ... # type: QMediaFormat.FileFormat
        WMV = ... # type: QMediaFormat.FileFormat
        AVI = ... # type: QMediaFormat.FileFormat
        Matroska = ... # type: QMediaFormat.FileFormat
        MPEG4 = ... # type: QMediaFormat.FileFormat
        Ogg = ... # type: QMediaFormat.FileFormat
        QuickTime = ... # type: QMediaFormat.FileFormat
        WebM = ... # type: QMediaFormat.FileFormat
        Mpeg4Audio = ... # type: QMediaFormat.FileFormat
        AAC = ... # type: QMediaFormat.FileFormat
        WMA = ... # type: QMediaFormat.FileFormat
        MP3 = ... # type: QMediaFormat.FileFormat
        FLAC = ... # type: QMediaFormat.FileFormat
        Wave = ... # type: QMediaFormat.FileFormat

    @typing.overload
    def __init__(self, format: 'QMediaFormat.FileFormat' = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QMediaFormat') -> None: ...

    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def resolveForEncoding(self, flags: 'QMediaFormat.ResolveFlags') -> None: ...
    @staticmethod
    def videoCodecDescription(c: 'QMediaFormat.VideoCodec') -> str: ...
    @staticmethod
    def audioCodecDescription(c: 'QMediaFormat.AudioCodec') -> str: ...
    @staticmethod
    def fileFormatDescription(c: 'QMediaFormat.FileFormat') -> str: ...
    @staticmethod
    def videoCodecName(c: 'QMediaFormat.VideoCodec') -> str: ...
    @staticmethod
    def audioCodecName(c: 'QMediaFormat.AudioCodec') -> str: ...
    @staticmethod
    def fileFormatName(c: 'QMediaFormat.FileFormat') -> str: ...
    def supportedAudioCodecs(self, m: 'QMediaFormat.ConversionMode') -> typing.List['QMediaFormat.AudioCodec']: ...
    def supportedVideoCodecs(self, m: 'QMediaFormat.ConversionMode') -> typing.List['QMediaFormat.VideoCodec']: ...
    def supportedFileFormats(self, m: 'QMediaFormat.ConversionMode') -> typing.List['QMediaFormat.FileFormat']: ...
    def mimeType(self) -> QtCore.QMimeType: ...
    def isSupported(self, mode: 'QMediaFormat.ConversionMode') -> bool: ...
    def audioCodec(self) -> 'QMediaFormat.AudioCodec': ...
    def setAudioCodec(self, codec: 'QMediaFormat.AudioCodec') -> None: ...
    def videoCodec(self) -> 'QMediaFormat.VideoCodec': ...
    def setVideoCodec(self, codec: 'QMediaFormat.VideoCodec') -> None: ...
    def setFileFormat(self, f: 'QMediaFormat.FileFormat') -> None: ...
    def fileFormat(self) -> 'QMediaFormat.FileFormat': ...
    def swap(self, other: 'QMediaFormat') -> None: ...


class QMediaMetaData(PyQt6.sip.simplewrapper):

    class Key(enum.Enum):
        Title = ... # type: QMediaMetaData.Key
        Author = ... # type: QMediaMetaData.Key
        Comment = ... # type: QMediaMetaData.Key
        Description = ... # type: QMediaMetaData.Key
        Genre = ... # type: QMediaMetaData.Key
        Date = ... # type: QMediaMetaData.Key
        Language = ... # type: QMediaMetaData.Key
        Publisher = ... # type: QMediaMetaData.Key
        Copyright = ... # type: QMediaMetaData.Key
        Url = ... # type: QMediaMetaData.Key
        Duration = ... # type: QMediaMetaData.Key
        MediaType = ... # type: QMediaMetaData.Key
        FileFormat = ... # type: QMediaMetaData.Key
        AudioBitRate = ... # type: QMediaMetaData.Key
        AudioCodec = ... # type: QMediaMetaData.Key
        VideoBitRate = ... # type: QMediaMetaData.Key
        VideoCodec = ... # type: QMediaMetaData.Key
        VideoFrameRate = ... # type: QMediaMetaData.Key
        AlbumTitle = ... # type: QMediaMetaData.Key
        AlbumArtist = ... # type: QMediaMetaData.Key
        ContributingArtist = ... # type: QMediaMetaData.Key
        TrackNumber = ... # type: QMediaMetaData.Key
        Composer = ... # type: QMediaMetaData.Key
        LeadPerformer = ... # type: QMediaMetaData.Key
        ThumbnailImage = ... # type: QMediaMetaData.Key
        CoverArtImage = ... # type: QMediaMetaData.Key
        Orientation = ... # type: QMediaMetaData.Key
        Resolution = ... # type: QMediaMetaData.Key

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QMediaMetaData') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    @staticmethod
    def keyType(key: 'QMediaMetaData.Key') -> QtCore.QMetaType: ...
    @staticmethod
    def metaDataKeyToString(k: 'QMediaMetaData.Key') -> str: ...
    def stringValue(self, k: 'QMediaMetaData.Key') -> str: ...
    def keys(self) -> typing.List['QMediaMetaData.Key']: ...
    def insert(self, k: 'QMediaMetaData.Key', value: typing.Any) -> None: ...
    def value(self, k: 'QMediaMetaData.Key') -> typing.Any: ...


class QMediaPlayer(QtCore.QObject):

    class Loops(enum.IntEnum):
        Infinite = ... # type: QMediaPlayer.Loops
        Once = ... # type: QMediaPlayer.Loops

    class Error(enum.Enum):
        NoError = ... # type: QMediaPlayer.Error
        ResourceError = ... # type: QMediaPlayer.Error
        FormatError = ... # type: QMediaPlayer.Error
        NetworkError = ... # type: QMediaPlayer.Error
        AccessDeniedError = ... # type: QMediaPlayer.Error

    class MediaStatus(enum.Enum):
        NoMedia = ... # type: QMediaPlayer.MediaStatus
        LoadingMedia = ... # type: QMediaPlayer.MediaStatus
        LoadedMedia = ... # type: QMediaPlayer.MediaStatus
        StalledMedia = ... # type: QMediaPlayer.MediaStatus
        BufferingMedia = ... # type: QMediaPlayer.MediaStatus
        BufferedMedia = ... # type: QMediaPlayer.MediaStatus
        EndOfMedia = ... # type: QMediaPlayer.MediaStatus
        InvalidMedia = ... # type: QMediaPlayer.MediaStatus

    class PlaybackState(enum.Enum):
        StoppedState = ... # type: QMediaPlayer.PlaybackState
        PlayingState = ... # type: QMediaPlayer.PlaybackState
        PausedState = ... # type: QMediaPlayer.PlaybackState

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    playingChanged: typing.ClassVar[QtCore.pyqtSignal]
    def isPlaying(self) -> bool: ...
    loopsChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setLoops(self, loops: int) -> None: ...
    def loops(self) -> int: ...
    errorOccurred: typing.ClassVar[QtCore.pyqtSignal]
    errorChanged: typing.ClassVar[QtCore.pyqtSignal]
    activeTracksChanged: typing.ClassVar[QtCore.pyqtSignal]
    tracksChanged: typing.ClassVar[QtCore.pyqtSignal]
    audioOutputChanged: typing.ClassVar[QtCore.pyqtSignal]
    videoOutputChanged: typing.ClassVar[QtCore.pyqtSignal]
    metaDataChanged: typing.ClassVar[QtCore.pyqtSignal]
    playbackRateChanged: typing.ClassVar[QtCore.pyqtSignal]
    seekableChanged: typing.ClassVar[QtCore.pyqtSignal]
    bufferProgressChanged: typing.ClassVar[QtCore.pyqtSignal]
    hasVideoChanged: typing.ClassVar[QtCore.pyqtSignal]
    hasAudioChanged: typing.ClassVar[QtCore.pyqtSignal]
    positionChanged: typing.ClassVar[QtCore.pyqtSignal]
    durationChanged: typing.ClassVar[QtCore.pyqtSignal]
    mediaStatusChanged: typing.ClassVar[QtCore.pyqtSignal]
    playbackStateChanged: typing.ClassVar[QtCore.pyqtSignal]
    sourceChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setSourceDevice(self, device: typing.Optional[QtCore.QIODevice], sourceUrl: QtCore.QUrl = ...) -> None: ...
    def setSource(self, source: QtCore.QUrl) -> None: ...
    def setPlaybackRate(self, rate: float) -> None: ...
    def setPosition(self, position: int) -> None: ...
    def stop(self) -> None: ...
    def pause(self) -> None: ...
    def play(self) -> None: ...
    def metaData(self) -> QMediaMetaData: ...
    def isAvailable(self) -> bool: ...
    def errorString(self) -> str: ...
    def error(self) -> 'QMediaPlayer.Error': ...
    def playbackRate(self) -> float: ...
    def isSeekable(self) -> bool: ...
    def bufferedTimeRange(self) -> 'QMediaTimeRange': ...
    def bufferProgress(self) -> float: ...
    def hasVideo(self) -> bool: ...
    def hasAudio(self) -> bool: ...
    def position(self) -> int: ...
    def duration(self) -> int: ...
    def mediaStatus(self) -> 'QMediaPlayer.MediaStatus': ...
    def playbackState(self) -> 'QMediaPlayer.PlaybackState': ...
    def sourceDevice(self) -> typing.Optional[QtCore.QIODevice]: ...
    def source(self) -> QtCore.QUrl: ...
    def videoSink(self) -> typing.Optional['QVideoSink']: ...
    def setVideoSink(self, sink: typing.Optional['QVideoSink']) -> None: ...
    def videoOutput(self) -> typing.Optional[QtCore.QObject]: ...
    def setVideoOutput(self, a0: typing.Optional[QtCore.QObject]) -> None: ...
    def audioOutput(self) -> typing.Optional[QAudioOutput]: ...
    def setAudioOutput(self, output: typing.Optional[QAudioOutput]) -> None: ...
    def setActiveSubtitleTrack(self, index: int) -> None: ...
    def setActiveVideoTrack(self, index: int) -> None: ...
    def setActiveAudioTrack(self, index: int) -> None: ...
    def activeSubtitleTrack(self) -> int: ...
    def activeVideoTrack(self) -> int: ...
    def activeAudioTrack(self) -> int: ...
    def subtitleTracks(self) -> typing.List[QMediaMetaData]: ...
    def videoTracks(self) -> typing.List[QMediaMetaData]: ...
    def audioTracks(self) -> typing.List[QMediaMetaData]: ...


class QMediaRecorder(QtCore.QObject):

    class Error(enum.Enum):
        NoError = ... # type: QMediaRecorder.Error
        ResourceError = ... # type: QMediaRecorder.Error
        FormatError = ... # type: QMediaRecorder.Error
        OutOfSpaceError = ... # type: QMediaRecorder.Error
        LocationNotWritable = ... # type: QMediaRecorder.Error

    class RecorderState(enum.Enum):
        StoppedState = ... # type: QMediaRecorder.RecorderState
        RecordingState = ... # type: QMediaRecorder.RecorderState
        PausedState = ... # type: QMediaRecorder.RecorderState

    class EncodingMode(enum.Enum):
        ConstantQualityEncoding = ... # type: QMediaRecorder.EncodingMode
        ConstantBitRateEncoding = ... # type: QMediaRecorder.EncodingMode
        AverageBitRateEncoding = ... # type: QMediaRecorder.EncodingMode
        TwoPassEncoding = ... # type: QMediaRecorder.EncodingMode

    class Quality(enum.Enum):
        VeryLowQuality = ... # type: QMediaRecorder.Quality
        LowQuality = ... # type: QMediaRecorder.Quality
        NormalQuality = ... # type: QMediaRecorder.Quality
        HighQuality = ... # type: QMediaRecorder.Quality
        VeryHighQuality = ... # type: QMediaRecorder.Quality

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    audioSampleRateChanged: typing.ClassVar[QtCore.pyqtSignal]
    audioChannelCountChanged: typing.ClassVar[QtCore.pyqtSignal]
    audioBitRateChanged: typing.ClassVar[QtCore.pyqtSignal]
    videoBitRateChanged: typing.ClassVar[QtCore.pyqtSignal]
    videoFrameRateChanged: typing.ClassVar[QtCore.pyqtSignal]
    videoResolutionChanged: typing.ClassVar[QtCore.pyqtSignal]
    qualityChanged: typing.ClassVar[QtCore.pyqtSignal]
    encodingModeChanged: typing.ClassVar[QtCore.pyqtSignal]
    mediaFormatChanged: typing.ClassVar[QtCore.pyqtSignal]
    metaDataChanged: typing.ClassVar[QtCore.pyqtSignal]
    errorChanged: typing.ClassVar[QtCore.pyqtSignal]
    errorOccurred: typing.ClassVar[QtCore.pyqtSignal]
    actualLocationChanged: typing.ClassVar[QtCore.pyqtSignal]
    durationChanged: typing.ClassVar[QtCore.pyqtSignal]
    recorderStateChanged: typing.ClassVar[QtCore.pyqtSignal]
    def stop(self) -> None: ...
    def pause(self) -> None: ...
    def record(self) -> None: ...
    def captureSession(self) -> typing.Optional[QMediaCaptureSession]: ...
    def setMetaData(self, metaData: QMediaMetaData) -> None: ...
    def metaData(self) -> QMediaMetaData: ...
    def setAudioSampleRate(self, sampleRate: int) -> None: ...
    def audioSampleRate(self) -> int: ...
    def setAudioChannelCount(self, channels: int) -> None: ...
    def audioChannelCount(self) -> int: ...
    def setAudioBitRate(self, bitRate: int) -> None: ...
    def audioBitRate(self) -> int: ...
    def setVideoBitRate(self, bitRate: int) -> None: ...
    def videoBitRate(self) -> int: ...
    def setVideoFrameRate(self, frameRate: float) -> None: ...
    def videoFrameRate(self) -> float: ...
    @typing.overload
    def setVideoResolution(self, a0: QtCore.QSize) -> None: ...
    @typing.overload
    def setVideoResolution(self, width: int, height: int) -> None: ...
    def videoResolution(self) -> QtCore.QSize: ...
    def setQuality(self, quality: 'QMediaRecorder.Quality') -> None: ...
    def quality(self) -> 'QMediaRecorder.Quality': ...
    def setEncodingMode(self, a0: 'QMediaRecorder.EncodingMode') -> None: ...
    def encodingMode(self) -> 'QMediaRecorder.EncodingMode': ...
    def setMediaFormat(self, format: QMediaFormat) -> None: ...
    def mediaFormat(self) -> QMediaFormat: ...
    def duration(self) -> int: ...
    def errorString(self) -> str: ...
    def error(self) -> 'QMediaRecorder.Error': ...
    def recorderState(self) -> 'QMediaRecorder.RecorderState': ...
    def actualLocation(self) -> QtCore.QUrl: ...
    def setOutputLocation(self, location: QtCore.QUrl) -> None: ...
    def outputLocation(self) -> QtCore.QUrl: ...
    def isAvailable(self) -> bool: ...


class QMediaTimeRange(PyQt6.sip.simplewrapper):

    class Interval(PyQt6.sip.simplewrapper):

        @typing.overload
        def __init__(self, start: int, end: int) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QMediaTimeRange.Interval') -> None: ...

        def __eq__(self, other: object): ...
        def __ne__(self, other: object): ...
        def translated(self, offset: int) -> 'QMediaTimeRange.Interval': ...
        def normalized(self) -> 'QMediaTimeRange.Interval': ...
        def isNormal(self) -> bool: ...
        def contains(self, time: int) -> bool: ...
        def end(self) -> int: ...
        def start(self) -> int: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QMediaTimeRange.Interval') -> None: ...
    @typing.overload
    def __init__(self, start: int, end: int) -> None: ...
    @typing.overload
    def __init__(self, range: 'QMediaTimeRange') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __add__(self, a0: 'QMediaTimeRange') -> 'QMediaTimeRange': ...
    def __sub__(self, a0: 'QMediaTimeRange') -> 'QMediaTimeRange': ...
    def clear(self) -> None: ...
    @typing.overload
    def __isub__(self, a0: 'QMediaTimeRange.Interval') -> 'QMediaTimeRange': ...
    @typing.overload
    def __isub__(self, a0: 'QMediaTimeRange') -> 'QMediaTimeRange': ...
    @typing.overload
    def __iadd__(self, a0: 'QMediaTimeRange.Interval') -> 'QMediaTimeRange': ...
    @typing.overload
    def __iadd__(self, a0: 'QMediaTimeRange') -> 'QMediaTimeRange': ...
    def removeTimeRange(self, a0: 'QMediaTimeRange') -> None: ...
    @typing.overload
    def removeInterval(self, interval: 'QMediaTimeRange.Interval') -> None: ...
    @typing.overload
    def removeInterval(self, start: int, end: int) -> None: ...
    def addTimeRange(self, a0: 'QMediaTimeRange') -> None: ...
    @typing.overload
    def addInterval(self, interval: 'QMediaTimeRange.Interval') -> None: ...
    @typing.overload
    def addInterval(self, start: int, end: int) -> None: ...
    def contains(self, time: int) -> bool: ...
    def isContinuous(self) -> bool: ...
    def isEmpty(self) -> bool: ...
    def intervals(self) -> typing.List['QMediaTimeRange.Interval']: ...
    def latestTime(self) -> int: ...
    def earliestTime(self) -> int: ...


class QScreenCapture(QtCore.QObject):

    class Error(enum.Enum):
        NoError = ... # type: QScreenCapture.Error
        InternalError = ... # type: QScreenCapture.Error
        CapturingNotSupported = ... # type: QScreenCapture.Error
        CaptureFailed = ... # type: QScreenCapture.Error
        NotFound = ... # type: QScreenCapture.Error

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    errorOccurred: typing.ClassVar[QtCore.pyqtSignal]
    screenChanged: typing.ClassVar[QtCore.pyqtSignal]
    errorChanged: typing.ClassVar[QtCore.pyqtSignal]
    activeChanged: typing.ClassVar[QtCore.pyqtSignal]
    def stop(self) -> None: ...
    def start(self) -> None: ...
    def setActive(self, active: bool) -> None: ...
    def errorString(self) -> str: ...
    def error(self) -> 'QScreenCapture.Error': ...
    def isActive(self) -> bool: ...
    def screen(self) -> typing.Optional[QtGui.QScreen]: ...
    def setScreen(self, screen: typing.Optional[QtGui.QScreen]) -> None: ...
    def captureSession(self) -> typing.Optional[QMediaCaptureSession]: ...


class QSoundEffect(QtCore.QObject):

    class Status(enum.Enum):
        Null = ... # type: QSoundEffect.Status
        Loading = ... # type: QSoundEffect.Status
        Ready = ... # type: QSoundEffect.Status
        Error = ... # type: QSoundEffect.Status

    class Loop(enum.Enum):
        Infinite = ... # type: QSoundEffect.Loop

    @typing.overload
    def __init__(self, audioDevice: QAudioDevice, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    statusChanged: typing.ClassVar[QtCore.pyqtSignal]
    playingChanged: typing.ClassVar[QtCore.pyqtSignal]
    loadedChanged: typing.ClassVar[QtCore.pyqtSignal]
    mutedChanged: typing.ClassVar[QtCore.pyqtSignal]
    volumeChanged: typing.ClassVar[QtCore.pyqtSignal]
    loopsRemainingChanged: typing.ClassVar[QtCore.pyqtSignal]
    loopCountChanged: typing.ClassVar[QtCore.pyqtSignal]
    sourceChanged: typing.ClassVar[QtCore.pyqtSignal]
    audioDeviceChanged: typing.ClassVar[QtCore.pyqtSignal]
    def stop(self) -> None: ...
    def play(self) -> None: ...
    def status(self) -> 'QSoundEffect.Status': ...
    def isPlaying(self) -> bool: ...
    def isLoaded(self) -> bool: ...
    def setMuted(self, muted: bool) -> None: ...
    def isMuted(self) -> bool: ...
    def setVolume(self, volume: float) -> None: ...
    def volume(self) -> float: ...
    def setLoopCount(self, loopCount: int) -> None: ...
    def loopsRemaining(self) -> int: ...
    def loopCount(self) -> int: ...
    def setSource(self, url: QtCore.QUrl) -> None: ...
    def source(self) -> QtCore.QUrl: ...
    def setAudioDevice(self, device: QAudioDevice) -> None: ...
    def audioDevice(self) -> QAudioDevice: ...
    @staticmethod
    def supportedMimeTypes() -> typing.List[str]: ...


class QVideoFrame(PyQt6.sip.simplewrapper):

    class RotationAngle(enum.Enum):
        Rotation0 = ... # type: QVideoFrame.RotationAngle
        Rotation90 = ... # type: QVideoFrame.RotationAngle
        Rotation180 = ... # type: QVideoFrame.RotationAngle
        Rotation270 = ... # type: QVideoFrame.RotationAngle

    class MapMode(enum.Enum):
        NotMapped = ... # type: QVideoFrame.MapMode
        ReadOnly = ... # type: QVideoFrame.MapMode
        WriteOnly = ... # type: QVideoFrame.MapMode
        ReadWrite = ... # type: QVideoFrame.MapMode

    class HandleType(enum.Enum):
        NoHandle = ... # type: QVideoFrame.HandleType
        RhiTextureHandle = ... # type: QVideoFrame.HandleType

    class PaintOptions(PyQt6.sip.simplewrapper):

        class PaintFlag(enum.Flag):
            DontDrawSubtitles = ... # type: QVideoFrame.PaintOptions.PaintFlag

        aspectRatioMode = ... # type: QtCore.Qt.AspectRatioMode
        backgroundColor = ... # type: typing.Union[QtGui.QColor, QtCore.Qt.GlobalColor, int]
        paintFlags = ... # type: 'QVideoFrame.PaintOptions.PaintFlag'

        @typing.overload
        def __init__(self) -> None: ...
        @typing.overload
        def __init__(self, a0: 'QVideoFrame.PaintOptions') -> None: ...

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, format: 'QVideoFrameFormat') -> None: ...
    @typing.overload
    def __init__(self, other: 'QVideoFrame') -> None: ...

    def rotation(self) -> QtVideo.Rotation: ...
    def setRotation(self, angle: QtVideo.Rotation) -> None: ...
    def mirrored(self) -> bool: ...
    def setMirrored(self, a0: bool) -> None: ...
    def rotationAngle(self) -> 'QVideoFrame.RotationAngle': ...
    def setRotationAngle(self, a0: 'QVideoFrame.RotationAngle') -> None: ...
    def paint(self, painter: typing.Optional[QtGui.QPainter], rect: QtCore.QRectF, options: 'QVideoFrame.PaintOptions') -> None: ...
    def setSubtitleText(self, text: typing.Optional[str]) -> None: ...
    def subtitleText(self) -> str: ...
    def toImage(self) -> QtGui.QImage: ...
    def surfaceFormat(self) -> 'QVideoFrameFormat': ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def planeCount(self) -> int: ...
    def setEndTime(self, time: int) -> None: ...
    def endTime(self) -> int: ...
    def setStartTime(self, time: int) -> None: ...
    def startTime(self) -> int: ...
    def mappedBytes(self, plane: int) -> int: ...
    def bits(self, plane: int) -> typing.Optional[PyQt6.sip.voidptr]: ...
    def bytesPerLine(self, plane: int) -> int: ...
    def unmap(self) -> None: ...
    def map(self, mode: 'QVideoFrame.MapMode') -> bool: ...
    def mapMode(self) -> 'QVideoFrame.MapMode': ...
    def isWritable(self) -> bool: ...
    def isReadable(self) -> bool: ...
    def isMapped(self) -> bool: ...
    def height(self) -> int: ...
    def width(self) -> int: ...
    def size(self) -> QtCore.QSize: ...
    def handleType(self) -> 'QVideoFrame.HandleType': ...
    def pixelFormat(self) -> 'QVideoFrameFormat.PixelFormat': ...
    def isValid(self) -> bool: ...


class QVideoFrameFormat(PyQt6.sip.simplewrapper):

    class ColorRange(enum.Enum):
        ColorRange_Unknown = ... # type: QVideoFrameFormat.ColorRange
        ColorRange_Video = ... # type: QVideoFrameFormat.ColorRange
        ColorRange_Full = ... # type: QVideoFrameFormat.ColorRange

    class ColorTransfer(enum.Enum):
        ColorTransfer_Unknown = ... # type: QVideoFrameFormat.ColorTransfer
        ColorTransfer_BT709 = ... # type: QVideoFrameFormat.ColorTransfer
        ColorTransfer_BT601 = ... # type: QVideoFrameFormat.ColorTransfer
        ColorTransfer_Linear = ... # type: QVideoFrameFormat.ColorTransfer
        ColorTransfer_Gamma22 = ... # type: QVideoFrameFormat.ColorTransfer
        ColorTransfer_Gamma28 = ... # type: QVideoFrameFormat.ColorTransfer
        ColorTransfer_ST2084 = ... # type: QVideoFrameFormat.ColorTransfer
        ColorTransfer_STD_B67 = ... # type: QVideoFrameFormat.ColorTransfer

    class ColorSpace(enum.Enum):
        ColorSpace_Undefined = ... # type: QVideoFrameFormat.ColorSpace
        ColorSpace_BT601 = ... # type: QVideoFrameFormat.ColorSpace
        ColorSpace_BT709 = ... # type: QVideoFrameFormat.ColorSpace
        ColorSpace_AdobeRgb = ... # type: QVideoFrameFormat.ColorSpace
        ColorSpace_BT2020 = ... # type: QVideoFrameFormat.ColorSpace

    class YCbCrColorSpace(enum.Enum):
        YCbCr_Undefined = ... # type: QVideoFrameFormat.YCbCrColorSpace
        YCbCr_BT601 = ... # type: QVideoFrameFormat.YCbCrColorSpace
        YCbCr_BT709 = ... # type: QVideoFrameFormat.YCbCrColorSpace
        YCbCr_xvYCC601 = ... # type: QVideoFrameFormat.YCbCrColorSpace
        YCbCr_xvYCC709 = ... # type: QVideoFrameFormat.YCbCrColorSpace
        YCbCr_JPEG = ... # type: QVideoFrameFormat.YCbCrColorSpace
        YCbCr_BT2020 = ... # type: QVideoFrameFormat.YCbCrColorSpace

    class Direction(enum.Enum):
        TopToBottom = ... # type: QVideoFrameFormat.Direction
        BottomToTop = ... # type: QVideoFrameFormat.Direction

    class PixelFormat(enum.Enum):
        Format_Invalid = ... # type: QVideoFrameFormat.PixelFormat
        Format_YUV420P = ... # type: QVideoFrameFormat.PixelFormat
        Format_YUV422P = ... # type: QVideoFrameFormat.PixelFormat
        Format_YUV420P10 = ... # type: QVideoFrameFormat.PixelFormat
        Format_YV12 = ... # type: QVideoFrameFormat.PixelFormat
        Format_UYVY = ... # type: QVideoFrameFormat.PixelFormat
        Format_YUYV = ... # type: QVideoFrameFormat.PixelFormat
        Format_NV12 = ... # type: QVideoFrameFormat.PixelFormat
        Format_NV21 = ... # type: QVideoFrameFormat.PixelFormat
        Format_IMC1 = ... # type: QVideoFrameFormat.PixelFormat
        Format_IMC2 = ... # type: QVideoFrameFormat.PixelFormat
        Format_IMC3 = ... # type: QVideoFrameFormat.PixelFormat
        Format_IMC4 = ... # type: QVideoFrameFormat.PixelFormat
        Format_Y8 = ... # type: QVideoFrameFormat.PixelFormat
        Format_Y16 = ... # type: QVideoFrameFormat.PixelFormat
        Format_P010 = ... # type: QVideoFrameFormat.PixelFormat
        Format_P016 = ... # type: QVideoFrameFormat.PixelFormat
        Format_Jpeg = ... # type: QVideoFrameFormat.PixelFormat
        Format_SamplerExternalOES = ... # type: QVideoFrameFormat.PixelFormat
        Format_ARGB8888 = ... # type: QVideoFrameFormat.PixelFormat
        Format_ARGB8888_Premultiplied = ... # type: QVideoFrameFormat.PixelFormat
        Format_XRGB8888 = ... # type: QVideoFrameFormat.PixelFormat
        Format_BGRA8888 = ... # type: QVideoFrameFormat.PixelFormat
        Format_BGRA8888_Premultiplied = ... # type: QVideoFrameFormat.PixelFormat
        Format_BGRX8888 = ... # type: QVideoFrameFormat.PixelFormat
        Format_ABGR8888 = ... # type: QVideoFrameFormat.PixelFormat
        Format_XBGR8888 = ... # type: QVideoFrameFormat.PixelFormat
        Format_RGBA8888 = ... # type: QVideoFrameFormat.PixelFormat
        Format_RGBX8888 = ... # type: QVideoFrameFormat.PixelFormat
        Format_AYUV = ... # type: QVideoFrameFormat.PixelFormat
        Format_AYUV_Premultiplied = ... # type: QVideoFrameFormat.PixelFormat
        Format_SamplerRect = ... # type: QVideoFrameFormat.PixelFormat

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, size: QtCore.QSize, pixelFormat: 'QVideoFrameFormat.PixelFormat') -> None: ...
    @typing.overload
    def __init__(self, format: 'QVideoFrameFormat') -> None: ...

    def setColorRange(self, range: 'QVideoFrameFormat.ColorRange') -> None: ...
    def colorRange(self) -> 'QVideoFrameFormat.ColorRange': ...
    def setColorTransfer(self, colorTransfer: 'QVideoFrameFormat.ColorTransfer') -> None: ...
    def colorTransfer(self) -> 'QVideoFrameFormat.ColorTransfer': ...
    def setColorSpace(self, colorSpace: 'QVideoFrameFormat.ColorSpace') -> None: ...
    def colorSpace(self) -> 'QVideoFrameFormat.ColorSpace': ...
    @staticmethod
    def pixelFormatToString(pixelFormat: 'QVideoFrameFormat.PixelFormat') -> str: ...
    @staticmethod
    def imageFormatFromPixelFormat(format: 'QVideoFrameFormat.PixelFormat') -> QtGui.QImage.Format: ...
    @staticmethod
    def pixelFormatFromImageFormat(format: QtGui.QImage.Format) -> 'QVideoFrameFormat.PixelFormat': ...
    def setMirrored(self, mirrored: bool) -> None: ...
    def isMirrored(self) -> bool: ...
    def setYCbCrColorSpace(self, colorSpace: 'QVideoFrameFormat.YCbCrColorSpace') -> None: ...
    def yCbCrColorSpace(self) -> 'QVideoFrameFormat.YCbCrColorSpace': ...
    def setFrameRate(self, rate: float) -> None: ...
    def frameRate(self) -> float: ...
    def setScanLineDirection(self, direction: 'QVideoFrameFormat.Direction') -> None: ...
    def scanLineDirection(self) -> 'QVideoFrameFormat.Direction': ...
    def setViewport(self, viewport: QtCore.QRect) -> None: ...
    def viewport(self) -> QtCore.QRect: ...
    def planeCount(self) -> int: ...
    def frameHeight(self) -> int: ...
    def frameWidth(self) -> int: ...
    @typing.overload
    def setFrameSize(self, size: QtCore.QSize) -> None: ...
    @typing.overload
    def setFrameSize(self, width: int, height: int) -> None: ...
    def frameSize(self) -> QtCore.QSize: ...
    def pixelFormat(self) -> 'QVideoFrameFormat.PixelFormat': ...
    def isValid(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QVideoSink(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    videoSizeChanged: typing.ClassVar[QtCore.pyqtSignal]
    subtitleTextChanged: typing.ClassVar[QtCore.pyqtSignal]
    videoFrameChanged: typing.ClassVar[QtCore.pyqtSignal]
    def videoFrame(self) -> QVideoFrame: ...
    def setVideoFrame(self, frame: QVideoFrame) -> None: ...
    def setSubtitleText(self, subtitle: typing.Optional[str]) -> None: ...
    def subtitleText(self) -> str: ...
    def videoSize(self) -> QtCore.QSize: ...


class QWindowCapture(QtCore.QObject):

    class Error(enum.Enum):
        NoError = ... # type: QWindowCapture.Error
        InternalError = ... # type: QWindowCapture.Error
        CapturingNotSupported = ... # type: QWindowCapture.Error
        CaptureFailed = ... # type: QWindowCapture.Error
        NotFound = ... # type: QWindowCapture.Error

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    errorOccurred: typing.ClassVar[QtCore.pyqtSignal]
    errorChanged: typing.ClassVar[QtCore.pyqtSignal]
    windowChanged: typing.ClassVar[QtCore.pyqtSignal]
    activeChanged: typing.ClassVar[QtCore.pyqtSignal]
    def stop(self) -> None: ...
    def start(self) -> None: ...
    def setActive(self, active: bool) -> None: ...
    def errorString(self) -> str: ...
    def error(self) -> 'QWindowCapture.Error': ...
    def isActive(self) -> bool: ...
    def window(self) -> QCapturableWindow: ...
    def setWindow(self, window: QCapturableWindow) -> None: ...
    @staticmethod
    def capturableWindows() -> typing.List[QCapturableWindow]: ...
