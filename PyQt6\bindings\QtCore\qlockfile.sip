// qlockfile.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QLockFile
{
%TypeHeaderCode
#include <qlockfile.h>
%End

public:
    QLockFile(const QString &fileName);
    ~QLockFile();
    bool lock() /ReleaseGIL/;
    bool tryLock(int timeout = 0) /ReleaseGIL/;
    void unlock() /ReleaseGIL/;
    void setStaleLockTime(int);
    int staleLockTime() const;
    bool isLocked() const /ReleaseGIL/;
    bool getLockInfo(qint64 *pid /Out/, QString *hostname /Out/, QString *appname /Out/) const;
    bool removeStaleLockFile() /ReleaseGIL/;

    enum LockError
    {
        NoError,
        LockFailedError,
        PermissionError,
        UnknownError,
    };

    QLockFile::LockError error() const;
    QString fileName() const;

private:
    QLockFile(const QLockFile &);
};
