// qwidget.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


const int QWIDGETSIZE_MAX;

class QWidget : public QObject, public QPaintDevice
{
%TypeHeaderCode
#include <qwidget.h>
%End

%TypeCode
// Transfer the ownership of a single widget to a parent.
static void qtgui_TransferWidget(QWidget *w, PyObject *py_parent)
{
    PyObject *py_w = sipGetPyObject(w, sipType_QWidget);

    if (py_w)
        sipTransferTo(py_w, py_parent);
}


// Transfer ownership of all widgets in a layout to their new parent.
static void qtwidgets_TransferLayoutWidgets(QLayout *lay, PyObject *pw)
{
    int n = lay->count();

    for (int i = 0; i < n; ++i)
    {
        QLayoutItem *item = lay->itemAt(i);
        QWidget *w = item->widget();

        if (w)
        {
            qtgui_TransferWidget(w, pw);
        }
        else
        {
            QLayout *l = item->layout();

            if (l)
                qtwidgets_TransferLayoutWidgets(l, pw);
        }
    }
    
    QWidget *mb = lay->menuBar();

    if (mb)
        qtgui_TransferWidget(mb, pw);
}
%End

public:
    QWidget(QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    virtual ~QWidget();
    virtual int devType() const;
    QStyle *style() const;
    void setStyle(QStyle * /KeepReference/);
    bool isEnabledTo(const QWidget *) const;

public slots:
    void setEnabled(bool);
    void setDisabled(bool);
    void setWindowModified(bool);

public:
    QRect frameGeometry() const;
    QRect normalGeometry() const;
    int x() const;
    int y() const;
    QPoint pos() const;
    QSize frameSize() const;
    QRect childrenRect() const;
    QRegion childrenRegion() const;
    QSize minimumSize() const;
    QSize maximumSize() const;
    void setMinimumSize(int minw, int minh);
    void setMaximumSize(int maxw, int maxh);
    void setMinimumWidth(int minw);
    void setMinimumHeight(int minh);
    void setMaximumWidth(int maxw);
    void setMaximumHeight(int maxh);
    QSize sizeIncrement() const;
    void setSizeIncrement(int w, int h);
    QSize baseSize() const;
    void setBaseSize(int basew, int baseh);
    void setFixedSize(const QSize &);
    void setFixedSize(int w, int h);
    void setFixedWidth(int w);
    void setFixedHeight(int h);
    QPoint mapToGlobal(const QPoint &) const;
    QPointF mapToGlobal(const QPointF &) const;
    QPoint mapFromGlobal(const QPoint &) const;
    QPointF mapFromGlobal(const QPointF &) const;
    QPoint mapToParent(const QPoint &) const;
    QPointF mapToParent(const QPointF &) const;
    QPoint mapFromParent(const QPoint &) const;
    QPointF mapFromParent(const QPointF &) const;
    QPoint mapTo(const QWidget *, const QPoint &) const;
    QPointF mapTo(const QWidget *, const QPointF &) const;
    QPoint mapFrom(const QWidget *, const QPoint &) const;
    QPointF mapFrom(const QWidget *, const QPointF &) const;
    QWidget *window() const;
    const QPalette &palette() const;
    void setPalette(const QPalette &);
    void setBackgroundRole(QPalette::ColorRole);
    QPalette::ColorRole backgroundRole() const;
    void setForegroundRole(QPalette::ColorRole);
    QPalette::ColorRole foregroundRole() const;
    void setFont(const QFont &);
    QCursor cursor() const;
    void setCursor(const QCursor &);
    void unsetCursor();
    void setMask(const QBitmap &);
    void setMask(const QRegion &);
    QRegion mask() const;
    void clearMask();
    void setWindowTitle(const QString &);
    QString windowTitle() const;
    void setWindowIcon(const QIcon &icon);
    QIcon windowIcon() const;
    void setWindowIconText(const QString &);
    QString windowIconText() const;
    void setWindowRole(const QString &);
    QString windowRole() const;
    void setWindowOpacity(qreal level);
    qreal windowOpacity() const;
    bool isWindowModified() const;
    void setToolTip(const QString &);
    QString toolTip() const;
    void setStatusTip(const QString &);
    QString statusTip() const;
    void setWhatsThis(const QString &);
    QString whatsThis() const;
%If (PyQt_Accessibility)
    QString accessibleName() const;
%End
%If (PyQt_Accessibility)
    void setAccessibleName(const QString &name);
%End
%If (PyQt_Accessibility)
    QString accessibleDescription() const;
%End
%If (PyQt_Accessibility)
    void setAccessibleDescription(const QString &description);
%End
    void setLayoutDirection(Qt::LayoutDirection direction);
    Qt::LayoutDirection layoutDirection() const;
    void unsetLayoutDirection();
    bool isRightToLeft() const;
    bool isLeftToRight() const;

public slots:
    void setFocus();

public:
    bool isActiveWindow() const;
    void activateWindow();
    void clearFocus();
    void setFocus(Qt::FocusReason reason);
    Qt::FocusPolicy focusPolicy() const;
    void setFocusPolicy(Qt::FocusPolicy policy);
    bool hasFocus() const;
    static void setTabOrder(QWidget *, QWidget *);
    void setFocusProxy(QWidget * /KeepReference/);
    QWidget *focusProxy() const;
    Qt::ContextMenuPolicy contextMenuPolicy() const;
    void setContextMenuPolicy(Qt::ContextMenuPolicy policy);
    void grabMouse();
    void grabMouse(const QCursor &);
    void releaseMouse();
    void grabKeyboard();
    void releaseKeyboard();
    int grabShortcut(const QKeySequence &key, Qt::ShortcutContext context = Qt::WindowShortcut);
    void releaseShortcut(int id);
    void setShortcutEnabled(int id, bool enabled = true);
    static QWidget *mouseGrabber();
    static QWidget *keyboardGrabber();
    void setUpdatesEnabled(bool enable);

public slots:
    void update();
    void repaint();

public:
    void update(const QRect &);
    void update(const QRegion &);
    void repaint(int x, int y, int w, int h);
    void repaint(const QRect &);
    void repaint(const QRegion &);

public slots:
    virtual void setVisible(bool visible);
    void setHidden(bool hidden);
    void show();
    void hide();
    void showMinimized();
    void showMaximized();
    void showFullScreen();
    void showNormal();
    bool close();
    void raise();
    void lower();

public:
    void stackUnder(QWidget *);
    void move(const QPoint &);
    void resize(const QSize &);
    void setGeometry(const QRect &);
    void adjustSize();
    bool isVisibleTo(const QWidget *) const;
    bool isMinimized() const;
    bool isMaximized() const;
    bool isFullScreen() const;
    Qt::WindowStates windowState() const;
    void setWindowState(Qt::WindowStates state);
    void overrideWindowState(Qt::WindowStates state);
    virtual QSize sizeHint() const;
    virtual QSize minimumSizeHint() const;
    QSizePolicy sizePolicy() const;
    void setSizePolicy(QSizePolicy);
    virtual int heightForWidth(int) const;
    QRegion visibleRegion() const;
    void setContentsMargins(int left, int top, int right, int bottom);
    QRect contentsRect() const;
    QLayout *layout() const;
    void setLayout(QLayout * /Transfer/);
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipCpp->setLayout(a0);
        Py_END_ALLOW_THREADS
        
        // Internally Qt has reparented all of the widgets in the layout, so we need
        // to update the ownership hierachy.
        qtwidgets_TransferLayoutWidgets(a0, sipSelf);
%End

    void updateGeometry();
    void setParent(QWidget *parent /TransferThis/);
    void setParent(QWidget *parent /TransferThis/, Qt::WindowFlags f);
    void scroll(int dx, int dy);
    void scroll(int dx, int dy, const QRect &);
    QWidget *focusWidget() const;
    QWidget *nextInFocusChain() const;
    bool acceptDrops() const;
    void setAcceptDrops(bool on);
%If (Qt_6_3_0 -)
    QAction *addAction(const QIcon &icon, const QString &text) /Transfer/;
%End
%If (Qt_6_3_0 -)
    QAction *addAction(const QIcon &icon, const QString &text, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/, Qt::ConnectionType type = Qt::AutoConnection) /Transfer/;
%MethodCode
        QObject *receiver;
        QByteArray slot_signature;
        
        if ((sipError = pyqt6_qtwidgets_get_connection_parts(a2, sipCpp, "()", false, &receiver, slot_signature)) == sipErrorNone)
        {
            sipRes = sipCpp->addAction(*a0, *a1, receiver, slot_signature.constData(), a3);
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(2, a2);
        }
%End

%End
%If (Qt_6_3_0 -)
    QAction *addAction(const QIcon &icon, const QString &text, const QKeySequence &shortcut) /Transfer/;
%End
%If (Qt_6_3_0 -)
    QAction *addAction(const QIcon &icon, const QString &text, const QKeySequence &shortcut, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/, Qt::ConnectionType type = Qt::AutoConnection) /Transfer/;
%MethodCode
        QObject *receiver;
        QByteArray slot_signature;
        
        if ((sipError = pyqt6_qtwidgets_get_connection_parts(a3, sipCpp, "()", false, &receiver, slot_signature)) == sipErrorNone)
        {
            sipRes = sipCpp->addAction(*a0, *a1, *a2, receiver, slot_signature.constData(), a4);
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(3, a3);
        }
%End

%End
%If (Qt_6_3_0 -)
    QAction *addAction(const QString &text) /Transfer/;
%End
%If (Qt_6_3_0 -)
    QAction *addAction(const QString &text, const QKeySequence &shortcut) /Transfer/;
%End
%If (Qt_6_3_0 -)
    QAction *addAction(QString &text, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/, Qt::ConnectionType type = Qt::AutoConnection) /Transfer/;
%MethodCode
        QObject *receiver;
        QByteArray slot_signature;
        
        if ((sipError = pyqt6_qtwidgets_get_connection_parts(a1, sipCpp, "()", false, &receiver, slot_signature)) == sipErrorNone)
        {
            sipRes = sipCpp->addAction(*a0, receiver, slot_signature.constData(), a2);
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(1, a1);
        }
%End

%End
%If (Qt_6_3_0 -)
    QAction *addAction(const QString &text, const QKeySequence &shortcut, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/, Qt::ConnectionType type = Qt::AutoConnection) /Transfer/;
%MethodCode
        QObject *receiver;
        QByteArray slot_signature;
        
        if ((sipError = pyqt6_qtwidgets_get_connection_parts(a2, sipCpp, "()", false, &receiver, slot_signature)) == sipErrorNone)
        {
            sipRes = sipCpp->addAction(*a0, *a1, receiver, slot_signature.constData(), a3);
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(2, a2);
        }
%End

%End
    void addAction(QAction *action);
    void addActions(const QList<QAction *> &actions);
    void insertAction(QAction *before, QAction *action);
    void insertActions(QAction *before, const QList<QAction *> &actions);
    void removeAction(QAction *action);
    QList<QAction *> actions() const;
    void setWindowFlags(Qt::WindowFlags type);
    void overrideWindowFlags(Qt::WindowFlags type);
    static QWidget *find(WId);
    QWidget *childAt(const QPoint &p) const;
    void setAttribute(Qt::WidgetAttribute attribute, bool on = true);
    virtual QPaintEngine *paintEngine() const;
    void ensurePolished() const;
    bool isAncestorOf(const QWidget *child) const;

signals:
    void customContextMenuRequested(const QPoint &pos);

protected:
    virtual bool event(QEvent *);
    virtual void mousePressEvent(QMouseEvent *);
    virtual void mouseReleaseEvent(QMouseEvent *);
    virtual void mouseDoubleClickEvent(QMouseEvent *);
    virtual void mouseMoveEvent(QMouseEvent *);
    virtual void wheelEvent(QWheelEvent *);
    virtual void keyPressEvent(QKeyEvent *);
    virtual void keyReleaseEvent(QKeyEvent *);
    virtual void focusInEvent(QFocusEvent *);
    virtual void focusOutEvent(QFocusEvent *);
    virtual void enterEvent(QEnterEvent *event);
    virtual void leaveEvent(QEvent *);
    virtual void paintEvent(QPaintEvent *);
    virtual void moveEvent(QMoveEvent *);
    virtual void resizeEvent(QResizeEvent *);
    virtual void closeEvent(QCloseEvent *);
    virtual void contextMenuEvent(QContextMenuEvent *);
    virtual void tabletEvent(QTabletEvent *);
    virtual void actionEvent(QActionEvent *);
    virtual void dragEnterEvent(QDragEnterEvent *);
    virtual void dragMoveEvent(QDragMoveEvent *);
    virtual void dragLeaveEvent(QDragLeaveEvent *);
    virtual void dropEvent(QDropEvent *);
    virtual void showEvent(QShowEvent *);
    virtual void hideEvent(QHideEvent *);
    virtual void changeEvent(QEvent *);
    virtual int metric(QPaintDevice::PaintDeviceMetric) const;
    virtual void inputMethodEvent(QInputMethodEvent *);

public:
    virtual QVariant inputMethodQuery(Qt::InputMethodQuery) const;

protected:
    void updateMicroFocus(Qt::InputMethodQuery query = Qt::ImQueryAll);
    void create(WId window = 0, bool initializeWindow = true, bool destroyOldWindow = true);
    void destroy(bool destroyWindow = true, bool destroySubWindows = true);
    virtual bool focusNextPrevChild(bool next);
    bool focusNextChild();
    bool focusPreviousChild();

public:
    QWidget *childAt(int ax, int ay) const;
    Qt::WindowType windowType() const;
    Qt::WindowFlags windowFlags() const;
    WId winId() const;
    bool isWindow() const;
    bool isEnabled() const;
    bool isModal() const;
    int minimumWidth() const;
    int minimumHeight() const;
    int maximumWidth() const;
    int maximumHeight() const;
    void setMinimumSize(const QSize &s);
    void setMaximumSize(const QSize &s);
    void setSizeIncrement(const QSize &s);
    void setBaseSize(const QSize &s);
    const QFont &font() const;
    QFontMetrics fontMetrics() const;
    QFontInfo fontInfo() const;
    void setMouseTracking(bool enable);
    bool hasMouseTracking() const;
    bool underMouse() const;
    bool updatesEnabled() const;
    void update(int ax, int ay, int aw, int ah);
    bool isVisible() const;
    bool isHidden() const;
    void move(int ax, int ay);
    void resize(int w, int h);
    void setGeometry(int ax, int ay, int aw, int ah);
    QRect rect() const;
    const QRect &geometry() const;
    QSize size() const;
    int width() const;
    int height() const;
    QWidget *parentWidget() const;
    void setSizePolicy(QSizePolicy::Policy hor, QSizePolicy::Policy ver);
    bool testAttribute(Qt::WidgetAttribute attribute) const;
    Qt::WindowModality windowModality() const;
    void setWindowModality(Qt::WindowModality windowModality);
    bool autoFillBackground() const;
    void setAutoFillBackground(bool enabled);
    void setStyleSheet(const QString &styleSheet);
    QString styleSheet() const;
    void setShortcutAutoRepeat(int id, bool enabled = true);
    QByteArray saveGeometry() const;
    bool restoreGeometry(const QByteArray &geometry);

    enum RenderFlag /BaseType=Flag/
    {
        DrawWindowBackground,
        DrawChildren,
        IgnoreMask,
    };

    typedef QFlags<QWidget::RenderFlag> RenderFlags;
    void render(QPaintDevice *target, const QPoint &targetOffset = QPoint(), const QRegion &sourceRegion = QRegion(), QWidget::RenderFlags flags = QWidget::RenderFlags(QWidget::DrawWindowBackground | QWidget::DrawChildren));
    void render(QPainter *painter, const QPoint &targetOffset = QPoint(), const QRegion &sourceRegion = QRegion(), QWidget::RenderFlags flags = QWidget::RenderFlags(QWidget::DrawWindowBackground | QWidget::DrawChildren));
    void setLocale(const QLocale &locale);
    QLocale locale() const;
    void unsetLocale();
    WId effectiveWinId() const;
    QWidget *nativeParentWidget() const;
    void setWindowFilePath(const QString &filePath);
    QString windowFilePath() const;
    QGraphicsProxyWidget *graphicsProxyWidget() const;
    QGraphicsEffect *graphicsEffect() const;
    void setGraphicsEffect(QGraphicsEffect *effect /Transfer/);
    void grabGesture(Qt::GestureType type, Qt::GestureFlags flags = Qt::GestureFlags());
    void ungrabGesture(Qt::GestureType type);
    void setContentsMargins(const QMargins &margins);
    QMargins contentsMargins() const;
    QWidget *previousInFocusChain() const;
    Qt::InputMethodHints inputMethodHints() const;
    void setInputMethodHints(Qt::InputMethodHints hints);
    virtual bool hasHeightForWidth() const;
    QPixmap grab(const QRect &rectangle = QRect(QPoint(0, 0), QSize(-1, -1)));
    static SIP_PYOBJECT createWindowContainer(QWindow *window /GetWrapper/, QWidget *parent /GetWrapper/ = 0, Qt::WindowFlags flags = Qt::WindowFlags()) /Factory,TypeHint="QWidget"/;
%MethodCode
        // Ownersip issues are complicated so we handle them explicitly.
        
        QWidget *w = QWidget::createWindowContainer(a0, a1, *a2);
        
        sipRes = sipConvertFromNewType(w, sipType_QWidget, a1Wrapper);
        
        if (sipRes)
            sipTransferTo(a0Wrapper, sipRes);
%End

    QWindow *windowHandle() const;

protected:
    virtual bool nativeEvent(const QByteArray &eventType, void *message, qintptr *result /Out/);
    virtual QPainter *sharedPainter() const;
    virtual void initPainter(QPainter *painter) const;

public:
    void setToolTipDuration(int msec);
    int toolTipDuration() const;

signals:
    void windowTitleChanged(const QString &title);
    void windowIconChanged(const QIcon &icon);
    void windowIconTextChanged(const QString &iconText);

public:
    void setTabletTracking(bool enable);
    bool hasTabletTracking() const;
    void setWindowFlag(Qt::WindowType, bool on = true);
    QScreen *screen() const;
    void setScreen(QScreen *);
};
