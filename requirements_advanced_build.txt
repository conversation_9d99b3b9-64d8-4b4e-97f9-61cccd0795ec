# 三合一加密打包工具依赖包
# ================================

# UI框架
PyQt6>=6.7.1

# 编译和打包工具
nuitka>=2.0.0
cython>=3.0.0
pyarmor>=8.0.0

# 数值计算和系统支持
numpy>=1.21.0
pywin32>=305

# 构建工具
setuptools>=65.0.0
wheel>=0.38.0

# 系统进程管理
psutil>=5.9.0

# 可选的额外工具
# ================================

# 代码质量检查（可选）
# flake8>=6.0.0
# black>=23.0.0

# 测试工具（可选）
# pytest>=7.0.0
# pytest-qt>=4.0.0

# 文档生成（可选）
# sphinx>=6.0.0

# 注意事项：
# 1. 这些依赖会被advanced_build.py自动安装
# 2. 如果需要手动安装：pip install -r requirements_advanced_build.txt
# 3. 建议在虚拟环境中使用
# 4. Windows系统需要Visual Studio Build Tools用于Cython编译
