mdm_oem_stanvbk
vbmeta_system_b
fw_ufs5_b
aging
op1
storsec_a
fw_ufs4_b
frp
multiimgoem_b
recovery_b
userdata
xbl_config_b
vbmeta_system_a
qupfw_b
misc
oem_dycnvbk
config
dtbo_b
aging_mod
keystore
imagefv_b
xbl_b
fw_ufs1_b
secdata
opproduct_b
sdc
catecontentfv
devcfg_b
devcfg_a
vbmeta_b
reserve2
vbmeta_a
uefisecapp_b
devinfo
reserve4
uefisecapp_a
LOGO_b
modem_b
dip
catefv
cmnlib64_a
reserve1
qupfw_a
reserve3
dtbo_a
mdm1oemnvbktmp
keymaster_a
core_nhlos
imagefv_a
cmnlib_a
fw_ufs2_b
fw_ufs1_a
opproduct_a
fw_ufs3_b
mdm_oem_dycnvbk
tz_a
sdd
param
modem_a
mdtpsecapp_a
rawdump
mdm1m9kefs3
abl_a
ALIGN_TO_128K_2
storsec_b
recovery_a
hyp_a
mdm1m9kefs2
fw_ufs3_a
bluetooth_a
fw_ufs5_a
mdtp_a
modemst2
aop_b
dsp_a
boot_a
ssd
fw_ufs2_a
fw_ufs4_a
multiimgoem_a
splash
tz_b
toolsfv
apdp
cateloader
oem_stanvbk
msadp
LOGO_a
mdtp_b
xbl_a
spunvm
mdmddr
dsp_b
limits
fsc
boot_b
logfs
aop_a
logdump
mdtpsecapp_b
uefivarstore
abl_b
sde
sdb
keymaster_b
hyp_b
cmnlib_b
bluetooth_b
cmnlib64_b
xbl_config_a
ddr
cdt
ALIGN_TO_128K_1
modemst1
fsg
mdm1m9kefsc
mdm1m9kefs1
sdf
super
metadata
vendor_a
vendor_b
system_a
system_b
product_a
product_b
odm_a
odm_b
my_engineering_a
my_engineering_b
system_ext_a
system_ext_b
my_product_a
my_product_b
my_stock_a
my_stock_b
my_heytap_a
my_heytap_b
my_company_a
my_company_b
my_carrier_a
my_carrier_b
my_region_a
my_region_b
my_preload_a
my_preload_b
my_bigball_a
my_bigball_b
my_manifest_a
my_manifest_b
ADB
adl
my_carrier
1
my_heytap