// qjsprimitivevalue.sip generated by MetaSIP
//
// This file is part of the QtQml Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_1_0 -)

struct QJSPrimitiveUndefined
{
%TypeHeaderCode
#include <qjsprimitivevalue.h>
%End
};

%End
%If (Qt_6_1_0 -)

struct QJSPrimitiveNull
{
%TypeHeaderCode
#include <qjsprimitivevalue.h>
%End
};

%End
%If (Qt_6_1_0 -)

class QJSPrimitiveValue
{
%TypeHeaderCode
#include <qjsprimitivevalue.h>
%End

public:
    enum Type
    {
        Undefined,
        Null,
        Boolean,
        Integer,
        Double,
        String,
    };

    QJSPrimitiveValue();
    QJSPrimitiveValue(QJSPrimitiveUndefined undefined);
    QJSPrimitiveValue(QJSPrimitiveNull null);
    QJSPrimitiveValue(bool value /Constrained/);
    QJSPrimitiveValue(int value /Constrained/);
    QJSPrimitiveValue(double value /Constrained/);
    QJSPrimitiveValue(QString string);
    QJSPrimitiveValue::Type type() const;
    bool toBoolean() const;
    int toInteger() const;
    double toDouble() const;
    QString toString() const;
    bool strictlyEquals(const QJSPrimitiveValue &other) const;
    bool equals(const QJSPrimitiveValue &other) const;
%If (Qt_6_2_0 -)
    QJSPrimitiveValue operator+();
%End
%If (Qt_6_2_0 -)
    QJSPrimitiveValue operator-();
%End
%If (Qt_6_6_0 -)
    QMetaType metaType() const;
%End
%If (Qt_6_6_0 -)
    void *data();
%End
};

%End
%If (Qt_6_1_0 -)
QJSPrimitiveValue operator+(const QJSPrimitiveValue &lhs, const QJSPrimitiveValue &rhs);
%End
%If (Qt_6_1_0 -)
QJSPrimitiveValue operator-(const QJSPrimitiveValue &lhs, const QJSPrimitiveValue &rhs);
%End
%If (Qt_6_1_0 -)
QJSPrimitiveValue operator*(const QJSPrimitiveValue &lhs, const QJSPrimitiveValue &rhs);
%End
%If (Qt_6_1_0 -)
QJSPrimitiveValue operator/(const QJSPrimitiveValue &lhs, const QJSPrimitiveValue &rhs);
%End
%If (Qt_6_1_0 -)
QJSPrimitiveValue operator%(const QJSPrimitiveValue &lhs, const QJSPrimitiveValue &rhs);
%End
%If (Qt_6_1_0 -)
bool operator==(const QJSPrimitiveValue &lhs, const QJSPrimitiveValue &rhs);
%End
%If (Qt_6_1_0 -)
bool operator!=(const QJSPrimitiveValue &lhs, const QJSPrimitiveValue &rhs);
%End
%If (Qt_6_1_0 -)
bool operator<(const QJSPrimitiveValue &lhs, const QJSPrimitiveValue &rhs);
%End
%If (Qt_6_1_0 -)
bool operator>(const QJSPrimitiveValue &lhs, const QJSPrimitiveValue &rhs);
%End
%If (Qt_6_1_0 -)
bool operator<=(const QJSPrimitiveValue &lhs, const QJSPrimitiveValue &rhs);
%End
%If (Qt_6_1_0 -)
bool operator>=(const QJSPrimitiveValue &lhs, const QJSPrimitiveValue &rhs);
%End
