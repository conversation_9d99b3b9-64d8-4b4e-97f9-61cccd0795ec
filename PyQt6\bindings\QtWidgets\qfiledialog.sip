// qfiledialog.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QFileDialog : public QDialog
{
%TypeHeaderCode
#include <qfiledialog.h>
%End

public:
    enum ViewMode
    {
        Detail,
        List,
    };

    enum FileMode
    {
        AnyFile,
        ExistingFile,
        Directory,
        ExistingFiles,
    };

    enum AcceptMode
    {
        AcceptOpen,
        AcceptSave,
    };

    enum DialogLabel
    {
        LookIn,
        FileName,
        FileType,
        Accept,
        Reject,
    };

    enum Option /BaseType=Flag/
    {
        ShowDirsOnly,
        DontResolveSymlinks,
        DontConfirmOverwrite,
        DontUseNativeDialog,
        ReadOnly,
        HideNameFilterDetails,
        DontUseCustomDirectoryIcons,
    };

    typedef QFlags<QFileDialog::Option> Options;
    QFileDialog(QWidget *parent /TransferThis/, Qt::WindowFlags f);
    QFileDialog(QWidget *parent /TransferThis/ = 0, const QString &caption = QString(), const QString &directory = QString(), const QString &filter = QString());
    virtual ~QFileDialog();
    void setDirectory(const QString &directory);
    void setDirectory(const QDir &adirectory);
    QDir directory() const;
    void selectFile(const QString &filename);
    QStringList selectedFiles() const;
    void setViewMode(QFileDialog::ViewMode mode);
    QFileDialog::ViewMode viewMode() const;
    void setFileMode(QFileDialog::FileMode mode);
    QFileDialog::FileMode fileMode() const;
    void setAcceptMode(QFileDialog::AcceptMode mode);
    QFileDialog::AcceptMode acceptMode() const;
    void setDefaultSuffix(const QString &suffix);
    QString defaultSuffix() const;
    void setHistory(const QStringList &paths);
    QStringList history() const;
    void setItemDelegate(QAbstractItemDelegate *delegate /KeepReference/);
    QAbstractItemDelegate *itemDelegate() const;
    void setIconProvider(QAbstractFileIconProvider *provider /KeepReference/);
    QAbstractFileIconProvider *iconProvider() const;
    void setLabelText(QFileDialog::DialogLabel label, const QString &text);
    QString labelText(QFileDialog::DialogLabel label) const;

signals:
    void currentChanged(const QString &path);
    void directoryEntered(const QString &directory);
    void filesSelected(const QStringList &files);
    void filterSelected(const QString &filter);
    void fileSelected(const QString &file);

public:
    static QString getExistingDirectory(QWidget *parent = 0, const QString &caption = QString(), const QString &directory = QString(), QFileDialog::Options options = QFileDialog::ShowDirsOnly) /ReleaseGIL/;
    static QUrl getExistingDirectoryUrl(QWidget *parent = 0, const QString &caption = QString(), const QUrl &directory = QUrl(), QFileDialog::Options options = QFileDialog::ShowDirsOnly, const QStringList &supportedSchemes = QStringList()) /ReleaseGIL/;
    static SIP_PYTUPLE getOpenFileName(QWidget *parent = 0, const QString &caption = QString(), const QString &directory = QString(), const QString &filter = QString(), const QString &initialFilter = QString(), Options options = QFileDialog::Options()) /TypeHint="Tuple[QString, QString]", ReleaseGIL/;
%MethodCode
        QString *name;
        QString *filter = new QString(*a4);
        
        Py_BEGIN_ALLOW_THREADS
        
        name = new QString(QFileDialog::getOpenFileName(a0, *a1, *a2, *a3, filter, *a5));
        
        Py_END_ALLOW_THREADS
        
        PyObject *name_obj = sipConvertFromNewType(name, sipType_QString, NULL);
        PyObject *filter_obj = sipConvertFromNewType(filter, sipType_QString, NULL);
        
        if (name_obj && filter_obj)
            sipRes = PyTuple_Pack(2, name_obj, filter_obj);
        
        Py_XDECREF(name_obj);
        Py_XDECREF(filter_obj);
%End

    static SIP_PYTUPLE getOpenFileNames(QWidget *parent = 0, const QString &caption = QString(), const QString &directory = QString(), const QString &filter = QString(), const QString &initialFilter = QString(), Options options = QFileDialog::Options()) /TypeHint="Tuple[QStringList, QString]", ReleaseGIL/;
%MethodCode
        QStringList *names;
        QString *filter = new QString(*a4);
        
        Py_BEGIN_ALLOW_THREADS
        
        names = new QStringList(QFileDialog::getOpenFileNames(a0, *a1, *a2, *a3, filter, *a5));
        
        Py_END_ALLOW_THREADS
        
        PyObject *names_obj = sipConvertFromNewType(names, sipType_QStringList, NULL);
        PyObject *filter_obj = sipConvertFromNewType(filter, sipType_QString, NULL);
        
        if (names_obj && filter_obj)
            sipRes = PyTuple_Pack(2, names_obj, filter_obj);
        
        Py_XDECREF(names_obj);
        Py_XDECREF(filter_obj);
%End

    static SIP_PYTUPLE getSaveFileName(QWidget *parent = 0, const QString &caption = QString(), const QString &directory = QString(), const QString &filter = QString(), const QString &initialFilter = QString(), Options options = QFileDialog::Options()) /TypeHint="Tuple[QString, QString]", ReleaseGIL/;
%MethodCode
        QString *name;
        QString *filter = new QString(*a4);
        
        Py_BEGIN_ALLOW_THREADS
        
        name = new QString(QFileDialog::getSaveFileName(a0, *a1, *a2, *a3, filter, *a5));
        
        Py_END_ALLOW_THREADS
        
        PyObject *name_obj = sipConvertFromNewType(name, sipType_QString, NULL);
        PyObject *filter_obj = sipConvertFromNewType(filter, sipType_QString, NULL);
        
        if (name_obj && filter_obj)
            sipRes = PyTuple_Pack(2, name_obj, filter_obj);
        
        Py_XDECREF(name_obj);
        Py_XDECREF(filter_obj);
%End

protected:
    virtual void done(int result);
    virtual void accept();
    virtual void changeEvent(QEvent *e);

public:
    void setSidebarUrls(const QList<QUrl> &urls);
    QList<QUrl> sidebarUrls() const;
    QByteArray saveState() const;
    bool restoreState(const QByteArray &state);
    void setProxyModel(QAbstractProxyModel *model /Transfer/);
    QAbstractProxyModel *proxyModel() const;
    void setNameFilter(const QString &filter);
    void setNameFilters(const QStringList &filters);
    QStringList nameFilters() const;
    void selectNameFilter(const QString &filter);
    QString selectedNameFilter() const;
    QDir::Filters filter() const;
    void setFilter(QDir::Filters filters);
    void setOption(QFileDialog::Option option, bool on = true);
    bool testOption(QFileDialog::Option option) const;
    void setOptions(QFileDialog::Options options);
    QFileDialog::Options options() const;
    virtual void open();
    void open(SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/);
%MethodCode
        QObject *receiver;
        QByteArray slot_signature;
        
        if ((sipError = pyqt6_qtwidgets_get_connection_parts(a0, sipCpp, "()", false, &receiver, slot_signature)) == sipErrorNone)
        {
            sipCpp->open(receiver, slot_signature.constData());
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(0, a0);
        }
%End

    virtual void setVisible(bool visible);
    void setDirectoryUrl(const QUrl &directory);
    QUrl directoryUrl() const;
    void selectUrl(const QUrl &url);
    QList<QUrl> selectedUrls() const;
    void setMimeTypeFilters(const QStringList &filters);
    QStringList mimeTypeFilters() const;
    void selectMimeTypeFilter(const QString &filter);

signals:
    void urlSelected(const QUrl &url);
    void urlsSelected(const QList<QUrl> &urls);
    void currentUrlChanged(const QUrl &url);
    void directoryUrlEntered(const QUrl &directory);

public:
    static SIP_PYTUPLE getOpenFileUrl(QWidget *parent = 0, const QString &caption = QString(), const QUrl &directory = QUrl(), const QString &filter = QString(), const QString &initialFilter = QString(), Options options = QFileDialog::Options(), const QStringList &supportedSchemes = QStringList()) /TypeHint="Tuple[QUrl, QString]", ReleaseGIL/;
%MethodCode
        QUrl *url;
        QString *filter = new QString(*a4);
        
        Py_BEGIN_ALLOW_THREADS
        
        url = new QUrl(QFileDialog::getOpenFileUrl(a0, *a1, *a2, *a3, filter, *a5, *a6));
        
        Py_END_ALLOW_THREADS
        
        PyObject *url_obj = sipConvertFromNewType(url, sipType_QUrl, NULL);
        PyObject *filter_obj = sipConvertFromNewType(filter, sipType_QString, NULL);
        
        if (url_obj && filter_obj)
            sipRes = PyTuple_Pack(2, url_obj, filter_obj);
        
        Py_XDECREF(url_obj);
        Py_XDECREF(filter_obj);
%End

    static SIP_PYTUPLE getOpenFileUrls(QWidget *parent = 0, const QString &caption = QString(), const QUrl &directory = QUrl(), const QString &filter = QString(), const QString &initialFilter = QString(), Options options = QFileDialog::Options(), const QStringList &supportedSchemes = QStringList()) /TypeHint="Tuple[List[QUrl], QString]", ReleaseGIL/;
%MethodCode
        QList<QUrl> url_list;
        QString *filter = new QString(*a4);
        
        Py_BEGIN_ALLOW_THREADS
        
        url_list = QFileDialog::getOpenFileUrls(a0, *a1, *a2, *a3, filter, *a5, *a6);
        
        Py_END_ALLOW_THREADS
        
        PyObject *url_list_obj = PyList_New(url_list.size());
        
        if (url_list_obj)
        {
            for (int i = 0; i < url_list.size(); ++i)
            {
                QUrl *url = new QUrl(url_list.at(i));
                PyObject *url_obj = sipConvertFromNewType(url, sipType_QUrl, NULL);
                
                if (!url_obj)
                {
                    delete url;
                    Py_DECREF(url_list_obj);
                    url_list_obj = 0;
                    break;
                }
                
                PyList_SetItem(url_list_obj, i, url_obj);
            }
        }
        
        PyObject *filter_obj = sipConvertFromNewType(filter, sipType_QString, NULL);
        
        if (url_list_obj && filter_obj)
            sipRes = PyTuple_Pack(2, url_list_obj, filter_obj);
        
        Py_XDECREF(url_list_obj);
        Py_XDECREF(filter_obj);
%End

    static SIP_PYTUPLE getSaveFileUrl(QWidget *parent = 0, const QString &caption = QString(), const QUrl &directory = QUrl(), const QString &filter = QString(), const QString &initialFilter = QString(), Options options = QFileDialog::Options(), const QStringList &supportedSchemes = QStringList()) /TypeHint="Tuple[QUrl, QString]", ReleaseGIL/;
%MethodCode
        QUrl *url;
        QString *filter = new QString(*a4);
        
        Py_BEGIN_ALLOW_THREADS
        
        url = new QUrl(QFileDialog::getSaveFileUrl(a0, *a1, *a2, *a3, filter, *a5, *a6));
        
        Py_END_ALLOW_THREADS
        
        PyObject *url_obj = sipConvertFromNewType(url, sipType_QUrl, NULL);
        PyObject *filter_obj = sipConvertFromNewType(filter, sipType_QString, NULL);
        
        if (url_obj && filter_obj)
            sipRes = PyTuple_Pack(2, url_obj, filter_obj);
        
        Py_XDECREF(url_obj);
        Py_XDECREF(filter_obj);
%End

    void setSupportedSchemes(const QStringList &schemes);
    QStringList supportedSchemes() const;
    QString selectedMimeTypeFilter() const;
    static void saveFileContent(const QByteArray &fileContent, const QString &fileNameHint = QString()) /ReleaseGIL/;
%If (Qt_6_7_0 -)
    static void saveFileContent(const QByteArray &fileContent, const QString &fileNameHint, QWidget *parent = 0);
%End
};
