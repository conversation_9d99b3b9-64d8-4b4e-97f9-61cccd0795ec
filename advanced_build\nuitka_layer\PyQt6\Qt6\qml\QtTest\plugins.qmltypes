import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/quicktestevent_p.h"
        name: "QQuickTouchEventSequence"
        accessSemantics: "reference"
        prototype: "QObject"
        Method {
            name: "press"
            type: "QObject"
            isPointer: true
            Parameter { name: "touchId"; type: "int" }
            Parameter { name: "item"; type: "QObject"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "move"
            type: "QObject"
            isPointer: true
            Parameter { name: "touchId"; type: "int" }
            Parameter { name: "item"; type: "QObject"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "release"
            type: "QObject"
            isPointer: true
            Parameter { name: "touchId"; type: "int" }
            Parameter { name: "item"; type: "QObject"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "stationary"
            type: "QObject"
            isPointer: true
            Parameter { name: "touchId"; type: "int" }
        }
        Method { name: "commit"; type: "QObject"; isPointer: true }
    }
    Component {
        file: "private/quicktest_p.h"
        name: "QTestRootObject"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtTest/QTestRootObject 1.0", "QtTest/QTestRootObject 6.0"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "windowShown"
            type: "bool"
            read: "windowShown"
            notify: "windowShownChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "hasTestCase"
            type: "bool"
            read: "hasTestCase"
            write: "setHasTestCase"
            notify: "hasTestCaseChanged"
            index: 1
        }
        Property {
            name: "defined"
            type: "QObject"
            isPointer: true
            read: "defined"
            index: 2
            isReadonly: true
        }
        Signal { name: "windowShownChanged" }
        Signal { name: "hasTestCaseChanged" }
        Method { name: "quit" }
    }
    Component {
        file: "private/quicktestevent_p.h"
        name: "QuickTestEvent"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtTest/TestEvent 1.0",
            "QtTest/TestEvent 1.2",
            "QtTest/TestEvent 6.0"
        ]
        exportMetaObjectRevisions: [256, 258, 1536]
        Property {
            name: "defaultMouseDelay"
            type: "int"
            read: "defaultMouseDelay"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Method {
            name: "keyPress"
            type: "bool"
            Parameter { name: "key"; type: "int" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "keyRelease"
            type: "bool"
            Parameter { name: "key"; type: "int" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "keyClick"
            type: "bool"
            Parameter { name: "key"; type: "int" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "keyPressChar"
            type: "bool"
            Parameter { name: "character"; type: "QString" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "keyReleaseChar"
            type: "bool"
            Parameter { name: "character"; type: "QString" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "keyClickChar"
            type: "bool"
            Parameter { name: "character"; type: "QString" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "keySequence"
            revision: 258
            type: "bool"
            Parameter { name: "keySequence"; type: "QVariant" }
        }
        Method {
            name: "mousePress"
            type: "bool"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "button"; type: "int" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "mouseRelease"
            type: "bool"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "button"; type: "int" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "mouseClick"
            type: "bool"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "button"; type: "int" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "mouseDoubleClick"
            type: "bool"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "button"; type: "int" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "mouseDoubleClickSequence"
            type: "bool"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "button"; type: "int" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "mouseMove"
            type: "bool"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "delay"; type: "int" }
            Parameter { name: "buttons"; type: "int" }
            Parameter { name: "modifiers"; type: "int" }
        }
        Method {
            name: "mouseWheel"
            type: "bool"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "buttons"; type: "int" }
            Parameter { name: "modifiers"; type: "int" }
            Parameter { name: "xDelta"; type: "int" }
            Parameter { name: "yDelta"; type: "int" }
            Parameter { name: "delay"; type: "int" }
        }
        Method {
            name: "touchEvent"
            type: "QQuickTouchEventSequence"
            isPointer: true
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method { name: "touchEvent"; type: "QQuickTouchEventSequence"; isPointer: true; isCloned: true }
    }
    Component {
        file: "private/quicktestresult_p.h"
        name: "QuickTestResult"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtTest/TestResult 1.0",
            "QtTest/TestResult 1.1",
            "QtTest/TestResult 1.13",
            "QtTest/TestResult 6.0",
            "QtTest/TestResult 6.3",
            "QtTest/TestResult 6.5"
        ]
        exportMetaObjectRevisions: [256, 257, 269, 1536, 1539, 1541]
        Enum {
            name: "RunMode"
            values: ["RepeatUntilValidMeasurement", "RunOnce"]
        }
        Property {
            name: "testCaseName"
            type: "QString"
            read: "testCaseName"
            write: "setTestCaseName"
            notify: "testCaseNameChanged"
            index: 0
        }
        Property {
            name: "functionName"
            type: "QString"
            read: "functionName"
            write: "setFunctionName"
            notify: "functionNameChanged"
            index: 1
        }
        Property {
            name: "dataTag"
            type: "QString"
            read: "dataTag"
            write: "setDataTag"
            notify: "dataTagChanged"
            index: 2
        }
        Property { name: "failed"; type: "bool"; read: "isFailed"; index: 3; isReadonly: true }
        Property {
            name: "skipped"
            type: "bool"
            read: "isSkipped"
            write: "setSkipped"
            notify: "skippedChanged"
            index: 4
        }
        Property { name: "passCount"; type: "int"; read: "passCount"; index: 5; isReadonly: true }
        Property { name: "failCount"; type: "int"; read: "failCount"; index: 6; isReadonly: true }
        Property { name: "skipCount"; type: "int"; read: "skipCount"; index: 7; isReadonly: true }
        Property {
            name: "functionsToRun"
            type: "QStringList"
            read: "functionsToRun"
            index: 8
            isReadonly: true
        }
        Property { name: "tagsToRun"; type: "QStringList"; read: "tagsToRun"; index: 9; isReadonly: true }
        Signal { name: "programNameChanged" }
        Signal { name: "testCaseNameChanged" }
        Signal { name: "functionNameChanged" }
        Signal { name: "dataTagChanged" }
        Signal { name: "skippedChanged" }
        Method { name: "reset" }
        Method { name: "startLogging" }
        Method { name: "stopLogging" }
        Method { name: "initTestTable" }
        Method { name: "clearTestTable" }
        Method { name: "finishTestData" }
        Method { name: "finishTestDataCleanup" }
        Method { name: "finishTestFunction" }
        Method { name: "stringify"; isJavaScriptFunction: true }
        Method {
            name: "fail"
            Parameter { name: "message"; type: "QString" }
            Parameter { name: "location"; type: "QUrl" }
            Parameter { name: "line"; type: "int" }
        }
        Method {
            name: "verify"
            type: "bool"
            Parameter { name: "success"; type: "bool" }
            Parameter { name: "message"; type: "QString" }
            Parameter { name: "location"; type: "QUrl" }
            Parameter { name: "line"; type: "int" }
        }
        Method {
            name: "compare"
            type: "bool"
            Parameter { name: "success"; type: "bool" }
            Parameter { name: "message"; type: "QString" }
            Parameter { name: "val1"; type: "QVariant" }
            Parameter { name: "val2"; type: "QVariant" }
            Parameter { name: "location"; type: "QUrl" }
            Parameter { name: "line"; type: "int" }
        }
        Method {
            name: "fuzzyCompare"
            type: "bool"
            Parameter { name: "actual"; type: "QVariant" }
            Parameter { name: "expected"; type: "QVariant" }
            Parameter { name: "delta"; type: "double" }
        }
        Method {
            name: "skip"
            Parameter { name: "message"; type: "QString" }
            Parameter { name: "location"; type: "QUrl" }
            Parameter { name: "line"; type: "int" }
        }
        Method {
            name: "expectFail"
            type: "bool"
            Parameter { name: "tag"; type: "QString" }
            Parameter { name: "comment"; type: "QString" }
            Parameter { name: "location"; type: "QUrl" }
            Parameter { name: "line"; type: "int" }
        }
        Method {
            name: "expectFailContinue"
            type: "bool"
            Parameter { name: "tag"; type: "QString" }
            Parameter { name: "comment"; type: "QString" }
            Parameter { name: "location"; type: "QUrl" }
            Parameter { name: "line"; type: "int" }
        }
        Method {
            name: "warn"
            Parameter { name: "message"; type: "QString" }
            Parameter { name: "location"; type: "QUrl" }
            Parameter { name: "line"; type: "int" }
        }
        Method {
            name: "ignoreWarning"
            Parameter { name: "message"; type: "QJSValue" }
        }
        Method {
            name: "failOnWarning"
            revision: 1539
            Parameter { name: "message"; type: "QJSValue" }
        }
        Method {
            name: "wait"
            Parameter { name: "ms"; type: "int" }
        }
        Method {
            name: "sleep"
            Parameter { name: "ms"; type: "int" }
        }
        Method {
            name: "waitForRendering"
            type: "bool"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
            Parameter { name: "timeout"; type: "int" }
        }
        Method {
            name: "waitForRendering"
            type: "bool"
            isCloned: true
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method { name: "startMeasurement" }
        Method { name: "beginDataRun" }
        Method { name: "endDataRun" }
        Method { name: "measurementAccepted"; type: "bool" }
        Method { name: "needsMoreMeasurements"; type: "bool" }
        Method {
            name: "startBenchmark"
            Parameter { name: "runMode"; type: "RunMode" }
            Parameter { name: "tag"; type: "QString" }
        }
        Method { name: "isBenchmarkDone"; type: "bool" }
        Method { name: "nextBenchmark" }
        Method { name: "stopBenchmark" }
        Method {
            name: "grabImage"
            type: "QObject"
            isPointer: true
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "findChild"
            revision: 257
            type: "QObject"
            isPointer: true
            Parameter { name: "parent"; type: "QObject"; isPointer: true }
            Parameter { name: "objectName"; type: "QString" }
        }
        Method {
            name: "isPolishScheduled"
            revision: 269
            type: "bool"
            Parameter { name: "itemOrWindow"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "waitForPolish"
            revision: 1541
            type: "bool"
            Parameter { name: "itemOrWindow"; type: "QObject"; isPointer: true }
            Parameter { name: "timeout"; type: "int" }
        }
    }
    Component {
        file: "private/quicktestutil_p.h"
        name: "QuickTestUtil"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtTest/TestUtil 1.0",
            "QtTest/TestUtil 6.0",
            "QtTest/TestUtil 6.7"
        ]
        exportMetaObjectRevisions: [256, 1536, 1543]
        Property {
            name: "printAvailableFunctions"
            type: "bool"
            read: "printAvailableFunctions"
            notify: "printAvailableFunctionsChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "dragThreshold"
            type: "int"
            read: "dragThreshold"
            notify: "dragThresholdChanged"
            index: 1
            isReadonly: true
        }
        Signal { name: "printAvailableFunctionsChanged" }
        Signal { name: "dragThresholdChanged" }
        Method {
            name: "typeName"
            type: "QJSValue"
            Parameter { name: "v"; type: "QVariant" }
        }
        Method {
            name: "compare"
            type: "bool"
            Parameter { name: "act"; type: "QVariant" }
            Parameter { name: "exp"; type: "QVariant" }
        }
        Method {
            name: "callerFile"
            type: "QJSValue"
            Parameter { name: "frameIndex"; type: "int" }
        }
        Method { name: "callerFile"; type: "QJSValue"; isCloned: true }
        Method {
            name: "callerLine"
            type: "int"
            Parameter { name: "frameIndex"; type: "int" }
        }
        Method { name: "callerLine"; type: "int"; isCloned: true }
        Method {
            name: "signalHandlerName"
            revision: 1543
            type: "QString"
            Parameter { name: "signalName"; type: "QString" }
        }
        Method {
            name: "populateClipboardText"
            Parameter { name: "lineCount"; type: "int" }
        }
    }
}
