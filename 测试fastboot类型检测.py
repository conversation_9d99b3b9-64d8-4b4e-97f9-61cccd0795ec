# -*- coding: utf-8 -*-
"""
测试fastboot类型检测功能
"""

import sys
import os
import subprocess

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_fastboot_type_detection():
    """测试fastboot类型检测"""
    print("🔍 测试fastboot类型检测功能...")
    
    try:
        from mx9p import ADBTools
        
        # 创建ADB工具实例
        adb_tools = ADBTools()
        adb_path, fastboot_path = adb_tools.get_adb_path()
        
        print(f"Fastboot路径: {fastboot_path}")
        print(f"Fastboot存在: {os.path.exists(fastboot_path)}")
        
        if not os.path.exists(fastboot_path):
            print("❌ Fastboot工具不存在")
            return False
        
        # 检查是否有fastboot设备
        print("\n📱 检查fastboot设备...")
        try:
            result = subprocess.run(
                [fastboot_path, "devices"],
                capture_output=True,
                text=True,
                timeout=5,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            if result.stdout.strip():
                print(f"✅ 检测到fastboot设备:")
                print(result.stdout.strip())
                
                # 测试is-userspace命令
                print("\n🧪 测试is-userspace命令...")
                result = subprocess.run(
                    [fastboot_path, "getvar", "is-userspace"],
                    capture_output=True,
                    text=True,
                    timeout=3,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )
                
                print(f"返回码: {result.returncode}")
                print(f"stdout: {repr(result.stdout)}")
                print(f"stderr: {repr(result.stderr)}")
                
                # 分析输出
                output = result.stderr.lower()
                if "is-userspace: yes" in output:
                    fastboot_type = "Fastboot"
                    print("✅ 检测结果: Fastboot模式 (is-userspace: yes)")
                elif "is-userspace: no" in output:
                    fastboot_type = "Bootloader"
                    print("✅ 检测结果: Bootloader模式 (is-userspace: no)")
                else:
                    fastboot_type = "Fastboot"
                    print("⚠️ 无法确定类型，默认为Fastboot模式")
                
                print(f"\n🎯 最终显示: {fastboot_type}模式")
                
            else:
                print("⚠️ 未检测到fastboot设备")
                print("请确保设备已连接并处于fastboot模式")
                
                # 仍然测试命令格式
                print("\n🧪 测试命令格式（无设备）...")
                result = subprocess.run(
                    [fastboot_path, "getvar", "is-userspace"],
                    capture_output=True,
                    text=True,
                    timeout=3,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )
                print(f"无设备时返回码: {result.returncode}")
                print(f"无设备时stderr: {repr(result.stderr)}")
                
        except subprocess.TimeoutExpired:
            print("❌ 命令执行超时")
        except Exception as e:
            print(f"❌ 命令执行失败: {e}")
        
        # 测试DeviceCheckThread的方法
        print("\n🔧 测试DeviceCheckThread方法...")
        try:
            from qz4n import DeviceCheckThread
            
            device_check = DeviceCheckThread(adb_tools)
            fastboot_type = device_check.check_fastboot_type(fastboot_path)
            print(f"✅ check_fastboot_type返回: {fastboot_type}")
            
        except Exception as e:
            print(f"❌ DeviceCheckThread测试失败: {e}")
        
        print("\n📋 功能说明:")
        print("- is-userspace: yes → 显示为 'Fastboot模式'")
        print("- is-userspace: no → 显示为 'Bootloader模式'")
        print("- 命令失败或无法确定 → 默认显示 'Fastboot模式'")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_fastboot_type_detection()
