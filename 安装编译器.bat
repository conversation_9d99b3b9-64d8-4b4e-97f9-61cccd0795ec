@echo off
echo 正在下载并安装Visual Studio Build Tools...
echo 这将安装C++编译器以支持Cython编译

REM 下载Visual Studio Build Tools
echo 下载Visual Studio Build Tools...
powershell -Command "Invoke-WebRequest -Uri 'https://aka.ms/vs/17/release/vs_buildtools.exe' -OutFile 'vs_buildtools.exe'"

if exist vs_buildtools.exe (
    echo 开始安装Visual Studio Build Tools...
    echo 请在安装界面中选择 "C++ build tools" 工作负载
    vs_buildtools.exe --wait --add Microsoft.VisualStudio.Workload.VCTools --includeRecommended
    
    echo 安装完成！请重新启动命令行窗口
    pause
) else (
    echo 下载失败，请手动下载并安装Visual Studio Build Tools
    echo 下载地址: https://visualstudio.microsoft.com/visual-cpp-build-tools/
    pause
)
