// qpdfpagenavigator.sip generated by MetaSIP
//
// This file is part of the QtPdf Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPdfPageNavigator : public QObject
{
%TypeHeaderCode
#include <qpdfpagenavigator.h>
%End

public:
    explicit QPdfPageNavigator(QObject *parent /TransferThis/);
    virtual ~QPdfPageNavigator();
    int currentPage() const;
    QPointF currentLocation() const;
    qreal currentZoom() const;
    bool backAvailable() const;
    bool forwardAvailable() const;

public slots:
    void clear();
    void jump(QPdfLink destination);
    void jump(int page, const QPointF &location, qreal zoom = 0);
    void update(int page, const QPointF &location, qreal zoom);
    void forward();
    void back();

signals:
    void currentPageChanged(int page);
    void currentLocationChanged(QPointF location);
    void currentZoomChanged(qreal zoom);
    void backAvailableChanged(bool available);
    void forwardAvailableChanged(bool available);
    void jumped(QPdfLink current);
};
