# safe_build_final.py
import PyInstaller.__main__
import os
import shutil

def build():
    # 1. 确保使用原始PyInstaller
    os.system("pip install --force-reinstall pyinstaller")
    
    # 2. 使用官方加密方式打包
    PyInstaller.__main__.run([
        'your_script.py',
        '--onefile',
        '--key=YourStrongPassword!',  # AES加密
        '--upx-dir=upx',
        '--add-data=custom_loader.py;.',
        '--runtime-hook=custom_loader.py',
        '--clean',
        '--noconfirm',
        '--hidden-import=hashlib'
    ])

if __name__ == "__main__":
    print("🚀 开始安全打包...")
    build()
    print("🎉 打包完成！建议进行数字签名")