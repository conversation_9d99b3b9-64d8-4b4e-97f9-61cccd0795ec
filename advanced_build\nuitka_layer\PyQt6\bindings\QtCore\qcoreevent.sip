// qcoreevent.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QEvent /NoDefaultCtors,Supertype=PyQt6.sip.wrapper/
{
%TypeHeaderCode
#include <qcoreevent.h>
%End

%ConvertToSubClassCode
    switch (sipCpp->type())
    {
    case QEvent::Timer:
        sipType = sipType_QTimerEvent;
        break;
    
    case QEvent::ChildAdded:
    case QEvent::ChildPolished:
    case QEvent::ChildRemoved:
        sipType = sipType_QChildEvent;
        break;
    
    case QEvent::DynamicPropertyChange:
        sipType = sipType_QDynamicPropertyChangeEvent;
        break;
    
    default:
        sipType = 0;
    }
%End

public:
    enum Type /BaseType=IntEnum/
    {
        None,
        Timer,
        MouseButtonPress,
        MouseButtonRelease,
        MouseButtonDblClick,
        MouseMove,
        KeyPress,
        KeyRelease,
        FocusIn,
        FocusOut,
        Enter,
        Leave,
        Paint,
        Move,
        Resize,
        Show,
        Hide,
        Close,
        Quit,
        ParentChange,
        ParentAboutToChange,
        ThreadChange,
        WindowActivate,
        WindowDeactivate,
        ShowToParent,
        HideToParent,
        Wheel,
        WindowTitleChange,
        WindowIconChange,
        ApplicationWindowIconChange,
        ApplicationFontChange,
        ApplicationLayoutDirectionChange,
        ApplicationPaletteChange,
        PaletteChange,
        Clipboard,
        MetaCall,
        SockAct,
        WinEventAct,
        DeferredDelete,
        DragEnter,
        DragMove,
        DragLeave,
        Drop,
        ChildAdded,
        ChildPolished,
        ChildRemoved,
        PolishRequest,
        Polish,
        LayoutRequest,
        UpdateRequest,
        UpdateLater,
        ContextMenu,
        InputMethod,
        TabletMove,
        LocaleChange,
        LanguageChange,
        LayoutDirectionChange,
        TabletPress,
        TabletRelease,
        OkRequest,
        IconDrag,
        FontChange,
        EnabledChange,
        ActivationChange,
        StyleChange,
        IconTextChange,
        ModifiedChange,
        MouseTrackingChange,
        WindowBlocked,
        WindowUnblocked,
        WindowStateChange,
        ToolTip,
        WhatsThis,
        StatusTip,
        ActionChanged,
        ActionAdded,
        ActionRemoved,
        FileOpen,
        Shortcut,
        ShortcutOverride,
        WhatsThisClicked,
        ToolBarChange,
        ApplicationActivate,
        ApplicationActivated,
        ApplicationDeactivate,
        ApplicationDeactivated,
        QueryWhatsThis,
        EnterWhatsThisMode,
        LeaveWhatsThisMode,
        ZOrderChange,
        HoverEnter,
        HoverLeave,
        HoverMove,
        GraphicsSceneMouseMove,
        GraphicsSceneMousePress,
        GraphicsSceneMouseRelease,
        GraphicsSceneMouseDoubleClick,
        GraphicsSceneContextMenu,
        GraphicsSceneHoverEnter,
        GraphicsSceneHoverMove,
        GraphicsSceneHoverLeave,
        GraphicsSceneHelp,
        GraphicsSceneDragEnter,
        GraphicsSceneDragMove,
        GraphicsSceneDragLeave,
        GraphicsSceneDrop,
        GraphicsSceneWheel,
        GraphicsSceneResize,
        GraphicsSceneMove,
        KeyboardLayoutChange,
        DynamicPropertyChange,
        TabletEnterProximity,
        TabletLeaveProximity,
        NonClientAreaMouseMove,
        NonClientAreaMouseButtonPress,
        NonClientAreaMouseButtonRelease,
        NonClientAreaMouseButtonDblClick,
        MacSizeChange,
        ContentsRectChange,
        CursorChange,
        ToolTipChange,
        GrabMouse,
        UngrabMouse,
        GrabKeyboard,
        UngrabKeyboard,
        StateMachineSignal,
        StateMachineWrapped,
        TouchBegin,
        TouchUpdate,
        TouchEnd,
        NativeGesture,
        RequestSoftwareInputPanel,
        CloseSoftwareInputPanel,
        WinIdChange,
        Gesture,
        GestureOverride,
        FocusAboutToChange,
        ScrollPrepare,
        Scroll,
        Expose,
        InputMethodQuery,
        OrientationChange,
        TouchCancel,
        PlatformPanel,
        ApplicationStateChange,
        ReadOnlyChange,
        PlatformSurface,
        TabletTrackingChange,
        GraphicsSceneLeave,
        EnterEditFocus,
        LeaveEditFocus,
%If (Qt_6_6_0 -)
        DevicePixelRatioChange,
%End
%If (Qt_6_7_0 -)
        ChildWindowAdded,
%End
%If (Qt_6_7_0 -)
        ChildWindowRemoved,
%End
%If (Qt_6_7_0 -)
        ParentWindowAboutToChange,
%End
%If (Qt_6_7_0 -)
        ParentWindowChange,
%End
        User,
        MaxUser,
    };

    explicit QEvent(QEvent::Type type);
    QEvent(int type) /Deprecated, NoDerived/;
%MethodCode
        // This overload allows an int to be passed as is rather than being wrapped in
        // a QEvent.Type. This was usefull before enums supported missing members and
        // is now deprecated.
        
        sipCpp = new sipQEvent(static_cast<QEvent::Type>(a0));
%End

    virtual ~QEvent();
    QEvent::Type type() const;
    bool spontaneous() const;
    virtual void setAccepted(bool accepted);
    bool isAccepted() const;
    void accept();
    void ignore();
    static int registerEventType(int hint = -1);
    bool isInputEvent() const;
    bool isPointerEvent() const;
    bool isSinglePointEvent() const;
    virtual QEvent *clone() const /Factory/;
};

class QTimerEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qcoreevent.h>
%End

public:
    explicit QTimerEvent(int timerId);
    virtual ~QTimerEvent();
    int timerId() const;
    virtual QTimerEvent *clone() const /Factory/;
};

class QChildEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qcoreevent.h>
%End

public:
    QChildEvent(QEvent::Type type, QObject *child);
    QChildEvent(int type, QObject *child) /NoDerived/;
%MethodCode
        sipCpp = new sipQChildEvent(static_cast<QEvent::Type>(a0), a1);
%End

    virtual ~QChildEvent();
    QObject *child() const;
    bool added() const;
    bool polished() const;
    bool removed() const;
    virtual QChildEvent *clone() const /Factory/;
};

class QDynamicPropertyChangeEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qcoreevent.h>
%End

public:
    explicit QDynamicPropertyChangeEvent(const QByteArray &name);
    virtual ~QDynamicPropertyChangeEvent();
    QByteArray propertyName() const;
    virtual QDynamicPropertyChangeEvent *clone() const /Factory/;
};
