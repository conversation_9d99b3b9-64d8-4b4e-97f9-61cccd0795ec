oem_dycnvbk
keystore
opproduct_b
sdc
devcfg_a
vbmeta_a
uefisecapp_a
dip
frp
cmnlib64_a
recovery_b
qupfw_a
userdata
xbl_config_b
qupfw_b
dtbo_a
dtbo_b
imagefv_b
fw_ufs1_b
devcfg_b
vbmeta_b
imagefv_a
vbmeta_system_a
uefisecapp_b
mdm_oem_stanvbk
misc
LOGO_b
vbmeta_system_b
config
xbl_b
fw_ufs5_b
mdm1oemnvbktmp
fw_ufs2_b
secdata
aging
catecontentfv
op1
reserve2
storsec_a
reserve4
fw_ufs4_b
modem_b
multiimgoem_b
fw_ufs3_b
fw_ufs1_a
catefv
aging_mod
rawdump
reserve1
devinfo
op2
reserve3
core_nhlos
keymaster_a
recovery_a
tz_a
cmnlib_a
fw_ufs3_a
sdd
fw_ufs5_a
ALIGN_TO_128K_2
aop_b
mdm1m9kefs2
mdm1m9kefs3
modemst2
boot_a
fw_ufs2_a
fw_ufs4_a
ssd
opproduct_a
multiimgoem_a
mdm_oem_dycnvbk
tz_b
param
apdp
splash
msadp
oem_stanvbk
modem_a
toolsfv
xbl_a
mdtpsecapp_a
mdmddr
mdtp_b
cateloader
abl_a
fsc
dsp_b
LOGO_a
storsec_b
boot_b
spunvm
hyp_a
aop_a
limits
bluetooth_a
mdtpsecapp_b
logfs
mdtp_a
abl_b
logdump
dsp_a
keymaster_b
uefivarstore
hyp_b
cmnlib_b
bluetooth_b
sde
cmnlib64_b
sdb
xbl_config_a
ddr
cdt
ALIGN_TO_128K_1
modemst1
fsg
mdm1m9kefsc
mdm1m9kefs1
sdf
super
metadata
ADB
adl
my_carrier
1
my_heytap
my_bigball_a
my_carrier_a
my_company_a
my_engineering_a
my_heytap_a
my_manifest_a
my_preload_a
my_product_a
my_region_a
my_stock_a
odm_a
product_a
system_a
system_ext_a
vendor_a