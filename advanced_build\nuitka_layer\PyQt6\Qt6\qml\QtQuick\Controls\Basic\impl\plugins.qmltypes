import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquickbasicbusyindicator_p.h"
        name: "QQuickBasicBusyIndicator"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Controls.Basic.impl/BusyIndicatorImpl 2.0",
            "QtQuick.Controls.Basic.impl/BusyIndicatorImpl 2.1",
            "QtQuick.Controls.Basic.impl/BusyIndicatorImpl 2.4",
            "QtQuick.Controls.Basic.impl/BusyIndicatorImpl 2.7",
            "QtQuick.Controls.Basic.impl/BusyIndicatorImpl 2.11",
            "QtQuick.Controls.Basic.impl/BusyIndicatorImpl 6.0",
            "QtQuick.Controls.Basic.impl/BusyIndicatorImpl 6.3",
            "QtQuick.Controls.Basic.impl/BusyIndicatorImpl 6.7"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539, 1543]
        Property { name: "pen"; type: "QColor"; read: "pen"; write: "setPen"; index: 0; isFinal: true }
        Property { name: "fill"; type: "QColor"; read: "fill"; write: "setFill"; index: 1; isFinal: true }
        Property { name: "running"; type: "bool"; read: "isRunning"; write: "setRunning"; index: 2 }
    }
    Component {
        file: "private/qquickbasicdial_p.h"
        name: "QQuickBasicDial"
        accessSemantics: "reference"
        prototype: "QQuickPaintedItem"
        exports: [
            "QtQuick.Controls.Basic.impl/DialImpl 2.0",
            "QtQuick.Controls.Basic.impl/DialImpl 2.1",
            "QtQuick.Controls.Basic.impl/DialImpl 2.4",
            "QtQuick.Controls.Basic.impl/DialImpl 2.7",
            "QtQuick.Controls.Basic.impl/DialImpl 2.11",
            "QtQuick.Controls.Basic.impl/DialImpl 6.0",
            "QtQuick.Controls.Basic.impl/DialImpl 6.3",
            "QtQuick.Controls.Basic.impl/DialImpl 6.7"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539, 1543]
        Property {
            name: "progress"
            type: "double"
            read: "progress"
            write: "setProgress"
            index: 0
            isFinal: true
        }
        Property {
            name: "startAngle"
            type: "double"
            read: "startAngle"
            write: "setStartAngle"
            index: 1
            isFinal: true
        }
        Property {
            name: "endAngle"
            type: "double"
            read: "endAngle"
            write: "setEndAngle"
            index: 2
            isFinal: true
        }
        Property { name: "color"; type: "QColor"; read: "color"; write: "setColor"; index: 3; isFinal: true }
    }
    Component {
        file: "private/qquickbasicprogressbar_p.h"
        name: "QQuickBasicProgressBar"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Controls.Basic.impl/ProgressBarImpl 2.0",
            "QtQuick.Controls.Basic.impl/ProgressBarImpl 2.1",
            "QtQuick.Controls.Basic.impl/ProgressBarImpl 2.4",
            "QtQuick.Controls.Basic.impl/ProgressBarImpl 2.7",
            "QtQuick.Controls.Basic.impl/ProgressBarImpl 2.11",
            "QtQuick.Controls.Basic.impl/ProgressBarImpl 6.0",
            "QtQuick.Controls.Basic.impl/ProgressBarImpl 6.3",
            "QtQuick.Controls.Basic.impl/ProgressBarImpl 6.7"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539, 1543]
        Property {
            name: "indeterminate"
            type: "bool"
            read: "isIndeterminate"
            write: "setIndeterminate"
            index: 0
            isFinal: true
        }
        Property {
            name: "progress"
            type: "double"
            read: "progress"
            write: "setProgress"
            index: 1
            isFinal: true
        }
        Property { name: "color"; type: "QColor"; read: "color"; write: "setColor"; index: 2; isFinal: true }
    }
}
