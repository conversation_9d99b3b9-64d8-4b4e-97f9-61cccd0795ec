# The PEP 484 type hints stub file for the QtNetwork module.
#
# Generated by SIP 6.8.6
#
# <AUTHOR> <EMAIL>
# 
# This file is part of PyQt6.
# 
# This file may be used under the terms of the GNU General Public License
# version 3.0 as published by the Free Software Foundation and appearing in
# the file LICENSE included in the packaging of this file.  Please review the
# following information to ensure the GNU General Public License version 3.0
# requirements will be met: http://www.gnu.org/copyleft/gpl.html.
# 
# If you do not wish to use this file under the terms of the GPL version 3.0
# then you may purchase a commercial license.  For more information contact
# <EMAIL>.
# 
# This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
# WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


import enum
import typing

import PyQt6.sip

from PyQt6 import QtCore

# Support for QDate, QDateTime and QTime.
import datetime

# Convenient type aliases.
PYQT_SIGNAL = typing.Union[QtCore.pyqtSignal, QtCore.pyqtBoundSignal]
PYQT_SLOT = typing.Union[typing.Callable[..., Any], QtCore.pyqtBoundSignal]


class QOcspRevocationReason(enum.Enum):
    None_ = ... # type: QOcspRevocationReason
    Unspecified = ... # type: QOcspRevocationReason
    KeyCompromise = ... # type: QOcspRevocationReason
    CACompromise = ... # type: QOcspRevocationReason
    AffiliationChanged = ... # type: QOcspRevocationReason
    Superseded = ... # type: QOcspRevocationReason
    CessationOfOperation = ... # type: QOcspRevocationReason
    CertificateHold = ... # type: QOcspRevocationReason
    RemoveFromCRL = ... # type: QOcspRevocationReason


class QOcspCertificateStatus(enum.Enum):
    Good = ... # type: QOcspCertificateStatus
    Revoked = ... # type: QOcspCertificateStatus
    Unknown = ... # type: QOcspCertificateStatus


class QNetworkCacheMetaData(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QNetworkCacheMetaData') -> None: ...

    def swap(self, other: 'QNetworkCacheMetaData') -> None: ...
    def setAttributes(self, attributes: typing.Dict['QNetworkRequest.Attribute', typing.Any]) -> None: ...
    def attributes(self) -> typing.Dict['QNetworkRequest.Attribute', typing.Any]: ...
    def setSaveToDisk(self, allow: bool) -> None: ...
    def saveToDisk(self) -> bool: ...
    def setExpirationDate(self, dateTime: typing.Union[QtCore.QDateTime, datetime.datetime]) -> None: ...
    def expirationDate(self) -> QtCore.QDateTime: ...
    def setLastModified(self, dateTime: typing.Union[QtCore.QDateTime, datetime.datetime]) -> None: ...
    def lastModified(self) -> QtCore.QDateTime: ...
    def setRawHeaders(self, headers: typing.Iterable[typing.Tuple[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]]]) -> None: ...
    def rawHeaders(self) -> typing.List[typing.Tuple[QtCore.QByteArray, QtCore.QByteArray]]: ...
    def setUrl(self, url: QtCore.QUrl) -> None: ...
    def url(self) -> QtCore.QUrl: ...
    def isValid(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QAbstractNetworkCache(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def clear(self) -> None: ...
    def insert(self, device: typing.Optional[QtCore.QIODevice]) -> None: ...
    def prepare(self, metaData: QNetworkCacheMetaData) -> typing.Optional[QtCore.QIODevice]: ...
    def cacheSize(self) -> int: ...
    def remove(self, url: QtCore.QUrl) -> bool: ...
    def data(self, url: QtCore.QUrl) -> typing.Optional[QtCore.QIODevice]: ...
    def updateMetaData(self, metaData: QNetworkCacheMetaData) -> None: ...
    def metaData(self, url: QtCore.QUrl) -> QNetworkCacheMetaData: ...


class QAbstractSocket(QtCore.QIODevice):

    class PauseMode(enum.Flag):
        PauseNever = ... # type: QAbstractSocket.PauseMode
        PauseOnSslErrors = ... # type: QAbstractSocket.PauseMode

    class BindFlag(enum.Flag):
        DefaultForPlatform = ... # type: QAbstractSocket.BindFlag
        ShareAddress = ... # type: QAbstractSocket.BindFlag
        DontShareAddress = ... # type: QAbstractSocket.BindFlag
        ReuseAddressHint = ... # type: QAbstractSocket.BindFlag

    class SocketOption(enum.Enum):
        LowDelayOption = ... # type: QAbstractSocket.SocketOption
        KeepAliveOption = ... # type: QAbstractSocket.SocketOption
        MulticastTtlOption = ... # type: QAbstractSocket.SocketOption
        MulticastLoopbackOption = ... # type: QAbstractSocket.SocketOption
        TypeOfServiceOption = ... # type: QAbstractSocket.SocketOption
        SendBufferSizeSocketOption = ... # type: QAbstractSocket.SocketOption
        ReceiveBufferSizeSocketOption = ... # type: QAbstractSocket.SocketOption
        PathMtuSocketOption = ... # type: QAbstractSocket.SocketOption

    class SocketState(enum.Enum):
        UnconnectedState = ... # type: QAbstractSocket.SocketState
        HostLookupState = ... # type: QAbstractSocket.SocketState
        ConnectingState = ... # type: QAbstractSocket.SocketState
        ConnectedState = ... # type: QAbstractSocket.SocketState
        BoundState = ... # type: QAbstractSocket.SocketState
        ListeningState = ... # type: QAbstractSocket.SocketState
        ClosingState = ... # type: QAbstractSocket.SocketState

    class SocketError(enum.Enum):
        ConnectionRefusedError = ... # type: QAbstractSocket.SocketError
        RemoteHostClosedError = ... # type: QAbstractSocket.SocketError
        HostNotFoundError = ... # type: QAbstractSocket.SocketError
        SocketAccessError = ... # type: QAbstractSocket.SocketError
        SocketResourceError = ... # type: QAbstractSocket.SocketError
        SocketTimeoutError = ... # type: QAbstractSocket.SocketError
        DatagramTooLargeError = ... # type: QAbstractSocket.SocketError
        NetworkError = ... # type: QAbstractSocket.SocketError
        AddressInUseError = ... # type: QAbstractSocket.SocketError
        SocketAddressNotAvailableError = ... # type: QAbstractSocket.SocketError
        UnsupportedSocketOperationError = ... # type: QAbstractSocket.SocketError
        UnfinishedSocketOperationError = ... # type: QAbstractSocket.SocketError
        ProxyAuthenticationRequiredError = ... # type: QAbstractSocket.SocketError
        SslHandshakeFailedError = ... # type: QAbstractSocket.SocketError
        ProxyConnectionRefusedError = ... # type: QAbstractSocket.SocketError
        ProxyConnectionClosedError = ... # type: QAbstractSocket.SocketError
        ProxyConnectionTimeoutError = ... # type: QAbstractSocket.SocketError
        ProxyNotFoundError = ... # type: QAbstractSocket.SocketError
        ProxyProtocolError = ... # type: QAbstractSocket.SocketError
        OperationError = ... # type: QAbstractSocket.SocketError
        SslInternalError = ... # type: QAbstractSocket.SocketError
        SslInvalidUserDataError = ... # type: QAbstractSocket.SocketError
        TemporaryError = ... # type: QAbstractSocket.SocketError
        UnknownSocketError = ... # type: QAbstractSocket.SocketError

    class NetworkLayerProtocol(enum.Enum):
        IPv4Protocol = ... # type: QAbstractSocket.NetworkLayerProtocol
        IPv6Protocol = ... # type: QAbstractSocket.NetworkLayerProtocol
        AnyIPProtocol = ... # type: QAbstractSocket.NetworkLayerProtocol
        UnknownNetworkLayerProtocol = ... # type: QAbstractSocket.NetworkLayerProtocol

    class SocketType(enum.Enum):
        TcpSocket = ... # type: QAbstractSocket.SocketType
        UdpSocket = ... # type: QAbstractSocket.SocketType
        SctpSocket = ... # type: QAbstractSocket.SocketType
        UnknownSocketType = ... # type: QAbstractSocket.SocketType

    def __init__(self, socketType: 'QAbstractSocket.SocketType', parent: typing.Optional[QtCore.QObject]) -> None: ...

    def setProtocolTag(self, tag: typing.Optional[str]) -> None: ...
    def protocolTag(self) -> str: ...
    @typing.overload
    def bind(self, address: typing.Union['QHostAddress', 'QHostAddress.SpecialAddress'], port: int = ..., mode: 'QAbstractSocket.BindFlag' = ...) -> bool: ...
    @typing.overload
    def bind(self, port: int = ..., mode: 'QAbstractSocket.BindFlag' = ...) -> bool: ...
    def setPauseMode(self, pauseMode: 'QAbstractSocket.PauseMode') -> None: ...
    def pauseMode(self) -> 'QAbstractSocket.PauseMode': ...
    def resume(self) -> None: ...
    def socketOption(self, option: 'QAbstractSocket.SocketOption') -> typing.Any: ...
    def setSocketOption(self, option: 'QAbstractSocket.SocketOption', value: typing.Any) -> None: ...
    def setPeerName(self, name: typing.Optional[str]) -> None: ...
    def setPeerAddress(self, address: typing.Union['QHostAddress', 'QHostAddress.SpecialAddress']) -> None: ...
    def setPeerPort(self, port: int) -> None: ...
    def setLocalAddress(self, address: typing.Union['QHostAddress', 'QHostAddress.SpecialAddress']) -> None: ...
    def setLocalPort(self, port: int) -> None: ...
    def setSocketError(self, socketError: 'QAbstractSocket.SocketError') -> None: ...
    def setSocketState(self, state: 'QAbstractSocket.SocketState') -> None: ...
    def skipData(self, maxSize: int) -> int: ...
    def writeData(self, a0: PyQt6.sip.Buffer) -> int: ...
    def readLineData(self, maxlen: int) -> bytes: ...
    def readData(self, maxlen: int) -> bytes: ...
    proxyAuthenticationRequired: typing.ClassVar[QtCore.pyqtSignal]
    errorOccurred: typing.ClassVar[QtCore.pyqtSignal]
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    disconnected: typing.ClassVar[QtCore.pyqtSignal]
    connected: typing.ClassVar[QtCore.pyqtSignal]
    hostFound: typing.ClassVar[QtCore.pyqtSignal]
    def proxy(self) -> 'QNetworkProxy': ...
    def setProxy(self, networkProxy: 'QNetworkProxy') -> None: ...
    def waitForDisconnected(self, msecs: int = ...) -> bool: ...
    def waitForBytesWritten(self, msecs: int = ...) -> bool: ...
    def waitForReadyRead(self, msecs: int = ...) -> bool: ...
    def waitForConnected(self, msecs: int = ...) -> bool: ...
    def flush(self) -> bool: ...
    def isSequential(self) -> bool: ...
    def close(self) -> None: ...
    def error(self) -> 'QAbstractSocket.SocketError': ...
    def state(self) -> 'QAbstractSocket.SocketState': ...
    def socketType(self) -> 'QAbstractSocket.SocketType': ...
    def socketDescriptor(self) -> PyQt6.sip.voidptr: ...
    def setSocketDescriptor(self, socketDescriptor: PyQt6.sip.voidptr, state: 'QAbstractSocket.SocketState' = ..., mode: QtCore.QIODeviceBase.OpenModeFlag = ...) -> bool: ...
    def abort(self) -> None: ...
    def setReadBufferSize(self, size: int) -> None: ...
    def readBufferSize(self) -> int: ...
    def peerName(self) -> str: ...
    def peerAddress(self) -> 'QHostAddress': ...
    def peerPort(self) -> int: ...
    def localAddress(self) -> 'QHostAddress': ...
    def localPort(self) -> int: ...
    def bytesToWrite(self) -> int: ...
    def bytesAvailable(self) -> int: ...
    def isValid(self) -> bool: ...
    def disconnectFromHost(self) -> None: ...
    @typing.overload
    def connectToHost(self, hostName: typing.Optional[str], port: int, mode: QtCore.QIODeviceBase.OpenModeFlag = ..., protocol: 'QAbstractSocket.NetworkLayerProtocol' = ...) -> None: ...
    @typing.overload
    def connectToHost(self, address: typing.Union['QHostAddress', 'QHostAddress.SpecialAddress'], port: int, mode: QtCore.QIODeviceBase.OpenModeFlag = ...) -> None: ...


class QAuthenticator(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QAuthenticator') -> None: ...

    def setOption(self, opt: typing.Optional[str], value: typing.Any) -> None: ...
    def options(self) -> typing.Dict[str, typing.Any]: ...
    def option(self, opt: typing.Optional[str]) -> typing.Any: ...
    def isNull(self) -> bool: ...
    def realm(self) -> str: ...
    def setPassword(self, password: typing.Optional[str]) -> None: ...
    def password(self) -> str: ...
    def setUser(self, user: typing.Optional[str]) -> None: ...
    def user(self) -> str: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QDnsDomainNameRecord(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QDnsDomainNameRecord') -> None: ...

    def value(self) -> str: ...
    def timeToLive(self) -> int: ...
    def name(self) -> str: ...
    def swap(self, other: 'QDnsDomainNameRecord') -> None: ...


class QDnsHostAddressRecord(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QDnsHostAddressRecord') -> None: ...

    def value(self) -> 'QHostAddress': ...
    def timeToLive(self) -> int: ...
    def name(self) -> str: ...
    def swap(self, other: 'QDnsHostAddressRecord') -> None: ...


class QDnsMailExchangeRecord(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QDnsMailExchangeRecord') -> None: ...

    def timeToLive(self) -> int: ...
    def preference(self) -> int: ...
    def name(self) -> str: ...
    def exchange(self) -> str: ...
    def swap(self, other: 'QDnsMailExchangeRecord') -> None: ...


class QDnsServiceRecord(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QDnsServiceRecord') -> None: ...

    def weight(self) -> int: ...
    def timeToLive(self) -> int: ...
    def target(self) -> str: ...
    def priority(self) -> int: ...
    def port(self) -> int: ...
    def name(self) -> str: ...
    def swap(self, other: 'QDnsServiceRecord') -> None: ...


class QDnsTextRecord(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QDnsTextRecord') -> None: ...

    def values(self) -> typing.List[QtCore.QByteArray]: ...
    def timeToLive(self) -> int: ...
    def name(self) -> str: ...
    def swap(self, other: 'QDnsTextRecord') -> None: ...


class QDnsLookup(QtCore.QObject):

    class Type(enum.Enum):
        A = ... # type: QDnsLookup.Type
        AAAA = ... # type: QDnsLookup.Type
        ANY = ... # type: QDnsLookup.Type
        CNAME = ... # type: QDnsLookup.Type
        MX = ... # type: QDnsLookup.Type
        NS = ... # type: QDnsLookup.Type
        PTR = ... # type: QDnsLookup.Type
        SRV = ... # type: QDnsLookup.Type
        TXT = ... # type: QDnsLookup.Type

    class Error(enum.Enum):
        NoError = ... # type: QDnsLookup.Error
        ResolverError = ... # type: QDnsLookup.Error
        OperationCancelledError = ... # type: QDnsLookup.Error
        InvalidRequestError = ... # type: QDnsLookup.Error
        InvalidReplyError = ... # type: QDnsLookup.Error
        ServerFailureError = ... # type: QDnsLookup.Error
        ServerRefusedError = ... # type: QDnsLookup.Error
        NotFoundError = ... # type: QDnsLookup.Error
        TimeoutError = ... # type: QDnsLookup.Error

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, type: 'QDnsLookup.Type', name: typing.Optional[str], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, type: 'QDnsLookup.Type', name: typing.Optional[str], nameserver: typing.Union['QHostAddress', 'QHostAddress.SpecialAddress'], parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, type: 'QDnsLookup.Type', name: typing.Optional[str], nameserver: typing.Union['QHostAddress', 'QHostAddress.SpecialAddress'], port: int, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    nameserverPortChanged: typing.ClassVar[QtCore.pyqtSignal]
    def setNameserverPort(self, port: int) -> None: ...
    def nameserverPort(self) -> int: ...
    nameserverChanged: typing.ClassVar[QtCore.pyqtSignal]
    @typing.overload
    def setNameserver(self, nameserver: typing.Union['QHostAddress', 'QHostAddress.SpecialAddress']) -> None: ...
    @typing.overload
    def setNameserver(self, nameserver: typing.Union['QHostAddress', 'QHostAddress.SpecialAddress'], port: int) -> None: ...
    def nameserver(self) -> 'QHostAddress': ...
    typeChanged: typing.ClassVar[QtCore.pyqtSignal]
    nameChanged: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    def lookup(self) -> None: ...
    def abort(self) -> None: ...
    def textRecords(self) -> typing.List[QDnsTextRecord]: ...
    def serviceRecords(self) -> typing.List[QDnsServiceRecord]: ...
    def pointerRecords(self) -> typing.List[QDnsDomainNameRecord]: ...
    def nameServerRecords(self) -> typing.List[QDnsDomainNameRecord]: ...
    def mailExchangeRecords(self) -> typing.List[QDnsMailExchangeRecord]: ...
    def hostAddressRecords(self) -> typing.List[QDnsHostAddressRecord]: ...
    def canonicalNameRecords(self) -> typing.List[QDnsDomainNameRecord]: ...
    def setType(self, a0: 'QDnsLookup.Type') -> None: ...
    def type(self) -> 'QDnsLookup.Type': ...
    def setName(self, name: typing.Optional[str]) -> None: ...
    def name(self) -> str: ...
    def isFinished(self) -> bool: ...
    def errorString(self) -> str: ...
    def error(self) -> 'QDnsLookup.Error': ...


class QHostAddress(PyQt6.sip.simplewrapper):

    class ConversionModeFlag(enum.Flag):
        ConvertV4MappedToIPv4 = ... # type: QHostAddress.ConversionModeFlag
        ConvertV4CompatToIPv4 = ... # type: QHostAddress.ConversionModeFlag
        ConvertUnspecifiedAddress = ... # type: QHostAddress.ConversionModeFlag
        ConvertLocalHost = ... # type: QHostAddress.ConversionModeFlag
        TolerantConversion = ... # type: QHostAddress.ConversionModeFlag
        StrictConversion = ... # type: QHostAddress.ConversionModeFlag

    class SpecialAddress(enum.Enum):
        Null = ... # type: QHostAddress.SpecialAddress
        Broadcast = ... # type: QHostAddress.SpecialAddress
        LocalHost = ... # type: QHostAddress.SpecialAddress
        LocalHostIPv6 = ... # type: QHostAddress.SpecialAddress
        AnyIPv4 = ... # type: QHostAddress.SpecialAddress
        AnyIPv6 = ... # type: QHostAddress.SpecialAddress
        Any = ... # type: QHostAddress.SpecialAddress

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, address: 'QHostAddress.SpecialAddress') -> None: ...
    @typing.overload
    def __init__(self, ip4Addr: int) -> None: ...
    @typing.overload
    def __init__(self, address: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, ip6Addr: typing.Tuple[int, int, int, int, int, int, int, int, int, int, int, int, int, int, int, int]) -> None: ...
    @typing.overload
    def __init__(self, copy: typing.Union['QHostAddress', 'QHostAddress.SpecialAddress']) -> None: ...

    def isPrivateUse(self) -> bool: ...
    def isBroadcast(self) -> bool: ...
    def isUniqueLocalUnicast(self) -> bool: ...
    def isSiteLocal(self) -> bool: ...
    def isLinkLocal(self) -> bool: ...
    def isGlobal(self) -> bool: ...
    def isEqual(self, address: typing.Union['QHostAddress', 'QHostAddress.SpecialAddress'], mode: 'QHostAddress.ConversionModeFlag' = ...) -> bool: ...
    def isMulticast(self) -> bool: ...
    def swap(self, other: 'QHostAddress') -> None: ...
    @staticmethod
    def parseSubnet(subnet: typing.Optional[str]) -> typing.Tuple['QHostAddress', int]: ...
    def isLoopback(self) -> bool: ...
    @typing.overload
    def isInSubnet(self, subnet: typing.Union['QHostAddress', 'QHostAddress.SpecialAddress'], netmask: int) -> bool: ...
    @typing.overload
    def isInSubnet(self, subnet: typing.Tuple[typing.Union['QHostAddress', 'QHostAddress.SpecialAddress'], int]) -> bool: ...
    def __hash__(self) -> int: ...
    def clear(self) -> None: ...
    def isNull(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def setScopeId(self, id: typing.Optional[str]) -> None: ...
    def scopeId(self) -> str: ...
    def toString(self) -> str: ...
    def toIPv6Address(self) -> typing.Tuple[int, int, int, int, int, int, int, int, int, int, int, int, int, int, int, int]: ...
    def toIPv4Address(self) -> typing.Tuple[int, typing.Optional[bool]]: ...
    def protocol(self) -> QAbstractSocket.NetworkLayerProtocol: ...
    @typing.overload
    def setAddress(self, address: 'QHostAddress.SpecialAddress') -> None: ...
    @typing.overload
    def setAddress(self, ip4Addr: int) -> None: ...
    @typing.overload
    def setAddress(self, address: typing.Optional[str]) -> bool: ...
    @typing.overload
    def setAddress(self, ip6Addr: typing.Tuple[int, int, int, int, int, int, int, int, int, int, int, int, int, int, int, int]) -> None: ...


class QHostInfo(PyQt6.sip.simplewrapper):

    class HostInfoError(enum.Enum):
        NoError = ... # type: QHostInfo.HostInfoError
        HostNotFound = ... # type: QHostInfo.HostInfoError
        UnknownError = ... # type: QHostInfo.HostInfoError

    @typing.overload
    def __init__(self, id: int = ...) -> None: ...
    @typing.overload
    def __init__(self, d: 'QHostInfo') -> None: ...

    def swap(self, other: 'QHostInfo') -> None: ...
    @staticmethod
    def localDomainName() -> str: ...
    @staticmethod
    def localHostName() -> str: ...
    @staticmethod
    def fromName(name: typing.Optional[str]) -> 'QHostInfo': ...
    @staticmethod
    def abortHostLookup(lookupId: int) -> None: ...
    @staticmethod
    def lookupHost(name: typing.Optional[str], slot: PYQT_SLOT) -> int: ...
    def lookupId(self) -> int: ...
    def setLookupId(self, id: int) -> None: ...
    def setErrorString(self, errorString: typing.Optional[str]) -> None: ...
    def errorString(self) -> str: ...
    def setError(self, error: 'QHostInfo.HostInfoError') -> None: ...
    def error(self) -> 'QHostInfo.HostInfoError': ...
    def setAddresses(self, addresses: typing.Iterable[typing.Union[QHostAddress, QHostAddress.SpecialAddress]]) -> None: ...
    def addresses(self) -> typing.List[QHostAddress]: ...
    def setHostName(self, name: typing.Optional[str]) -> None: ...
    def hostName(self) -> str: ...


class QHstsPolicy(PyQt6.sip.simplewrapper):

    class PolicyFlag(enum.Flag):
        IncludeSubDomains = ... # type: QHstsPolicy.PolicyFlag

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, expiry: typing.Union[QtCore.QDateTime, datetime.datetime], flags: 'QHstsPolicy.PolicyFlag', host: typing.Optional[str], mode: QtCore.QUrl.ParsingMode = ...) -> None: ...
    @typing.overload
    def __init__(self, rhs: 'QHstsPolicy') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def isExpired(self) -> bool: ...
    def includesSubDomains(self) -> bool: ...
    def setIncludesSubDomains(self, include: bool) -> None: ...
    def expiry(self) -> QtCore.QDateTime: ...
    def setExpiry(self, expiry: typing.Union[QtCore.QDateTime, datetime.datetime]) -> None: ...
    def host(self, options: QtCore.QUrl.ComponentFormattingOption = ...) -> str: ...
    def setHost(self, host: typing.Optional[str], mode: QtCore.QUrl.ParsingMode = ...) -> None: ...
    def swap(self, other: 'QHstsPolicy') -> None: ...


class QHttp1Configuration(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QHttp1Configuration') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __hash__(self) -> int: ...
    def swap(self, other: 'QHttp1Configuration') -> None: ...
    def numberOfConnectionsPerHost(self) -> int: ...
    def setNumberOfConnectionsPerHost(self, amount: int) -> None: ...


class QHttp2Configuration(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QHttp2Configuration') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def swap(self, other: 'QHttp2Configuration') -> None: ...
    def maxFrameSize(self) -> int: ...
    def setMaxFrameSize(self, size: int) -> bool: ...
    def streamReceiveWindowSize(self) -> int: ...
    def setStreamReceiveWindowSize(self, size: int) -> bool: ...
    def sessionReceiveWindowSize(self) -> int: ...
    def setSessionReceiveWindowSize(self, size: int) -> bool: ...
    def huffmanCompressionEnabled(self) -> bool: ...
    def setHuffmanCompressionEnabled(self, enable: bool) -> None: ...
    def serverPushEnabled(self) -> bool: ...
    def setServerPushEnabled(self, enable: bool) -> None: ...


class QHttpHeaders(PyQt6.sip.simplewrapper):

    class WellKnownHeader(enum.Enum):
        AIM = ... # type: QHttpHeaders.WellKnownHeader
        Accept = ... # type: QHttpHeaders.WellKnownHeader
        AcceptAdditions = ... # type: QHttpHeaders.WellKnownHeader
        AcceptCH = ... # type: QHttpHeaders.WellKnownHeader
        AcceptDatetime = ... # type: QHttpHeaders.WellKnownHeader
        AcceptEncoding = ... # type: QHttpHeaders.WellKnownHeader
        AcceptFeatures = ... # type: QHttpHeaders.WellKnownHeader
        AcceptLanguage = ... # type: QHttpHeaders.WellKnownHeader
        AcceptPatch = ... # type: QHttpHeaders.WellKnownHeader
        AcceptPost = ... # type: QHttpHeaders.WellKnownHeader
        AcceptRanges = ... # type: QHttpHeaders.WellKnownHeader
        AcceptSignature = ... # type: QHttpHeaders.WellKnownHeader
        AccessControlAllowCredentials = ... # type: QHttpHeaders.WellKnownHeader
        AccessControlAllowHeaders = ... # type: QHttpHeaders.WellKnownHeader
        AccessControlAllowMethods = ... # type: QHttpHeaders.WellKnownHeader
        AccessControlAllowOrigin = ... # type: QHttpHeaders.WellKnownHeader
        AccessControlExposeHeaders = ... # type: QHttpHeaders.WellKnownHeader
        AccessControlMaxAge = ... # type: QHttpHeaders.WellKnownHeader
        AccessControlRequestHeaders = ... # type: QHttpHeaders.WellKnownHeader
        AccessControlRequestMethod = ... # type: QHttpHeaders.WellKnownHeader
        Age = ... # type: QHttpHeaders.WellKnownHeader
        Allow = ... # type: QHttpHeaders.WellKnownHeader
        ALPN = ... # type: QHttpHeaders.WellKnownHeader
        AltSvc = ... # type: QHttpHeaders.WellKnownHeader
        AltUsed = ... # type: QHttpHeaders.WellKnownHeader
        Alternates = ... # type: QHttpHeaders.WellKnownHeader
        ApplyToRedirectRef = ... # type: QHttpHeaders.WellKnownHeader
        AuthenticationControl = ... # type: QHttpHeaders.WellKnownHeader
        AuthenticationInfo = ... # type: QHttpHeaders.WellKnownHeader
        Authorization = ... # type: QHttpHeaders.WellKnownHeader
        CacheControl = ... # type: QHttpHeaders.WellKnownHeader
        CacheStatus = ... # type: QHttpHeaders.WellKnownHeader
        CalManagedID = ... # type: QHttpHeaders.WellKnownHeader
        CalDAVTimezones = ... # type: QHttpHeaders.WellKnownHeader
        CapsuleProtocol = ... # type: QHttpHeaders.WellKnownHeader
        CDNCacheControl = ... # type: QHttpHeaders.WellKnownHeader
        CDNLoop = ... # type: QHttpHeaders.WellKnownHeader
        CertNotAfter = ... # type: QHttpHeaders.WellKnownHeader
        CertNotBefore = ... # type: QHttpHeaders.WellKnownHeader
        ClearSiteData = ... # type: QHttpHeaders.WellKnownHeader
        ClientCert = ... # type: QHttpHeaders.WellKnownHeader
        ClientCertChain = ... # type: QHttpHeaders.WellKnownHeader
        Close = ... # type: QHttpHeaders.WellKnownHeader
        Connection = ... # type: QHttpHeaders.WellKnownHeader
        ContentDigest = ... # type: QHttpHeaders.WellKnownHeader
        ContentDisposition = ... # type: QHttpHeaders.WellKnownHeader
        ContentEncoding = ... # type: QHttpHeaders.WellKnownHeader
        ContentID = ... # type: QHttpHeaders.WellKnownHeader
        ContentLanguage = ... # type: QHttpHeaders.WellKnownHeader
        ContentLength = ... # type: QHttpHeaders.WellKnownHeader
        ContentLocation = ... # type: QHttpHeaders.WellKnownHeader
        ContentRange = ... # type: QHttpHeaders.WellKnownHeader
        ContentSecurityPolicy = ... # type: QHttpHeaders.WellKnownHeader
        ContentSecurityPolicyReportOnly = ... # type: QHttpHeaders.WellKnownHeader
        ContentType = ... # type: QHttpHeaders.WellKnownHeader
        Cookie = ... # type: QHttpHeaders.WellKnownHeader
        CrossOriginEmbedderPolicy = ... # type: QHttpHeaders.WellKnownHeader
        CrossOriginEmbedderPolicyReportOnly = ... # type: QHttpHeaders.WellKnownHeader
        CrossOriginOpenerPolicy = ... # type: QHttpHeaders.WellKnownHeader
        CrossOriginOpenerPolicyReportOnly = ... # type: QHttpHeaders.WellKnownHeader
        CrossOriginResourcePolicy = ... # type: QHttpHeaders.WellKnownHeader
        DASL = ... # type: QHttpHeaders.WellKnownHeader
        Date = ... # type: QHttpHeaders.WellKnownHeader
        DAV = ... # type: QHttpHeaders.WellKnownHeader
        DeltaBase = ... # type: QHttpHeaders.WellKnownHeader
        Depth = ... # type: QHttpHeaders.WellKnownHeader
        Destination = ... # type: QHttpHeaders.WellKnownHeader
        DifferentialID = ... # type: QHttpHeaders.WellKnownHeader
        DPoP = ... # type: QHttpHeaders.WellKnownHeader
        DPoPNonce = ... # type: QHttpHeaders.WellKnownHeader
        EarlyData = ... # type: QHttpHeaders.WellKnownHeader
        ETag = ... # type: QHttpHeaders.WellKnownHeader
        Expect = ... # type: QHttpHeaders.WellKnownHeader
        ExpectCT = ... # type: QHttpHeaders.WellKnownHeader
        Expires = ... # type: QHttpHeaders.WellKnownHeader
        Forwarded = ... # type: QHttpHeaders.WellKnownHeader
        From = ... # type: QHttpHeaders.WellKnownHeader
        Hobareg = ... # type: QHttpHeaders.WellKnownHeader
        Host = ... # type: QHttpHeaders.WellKnownHeader
        If = ... # type: QHttpHeaders.WellKnownHeader
        IfMatch = ... # type: QHttpHeaders.WellKnownHeader
        IfModifiedSince = ... # type: QHttpHeaders.WellKnownHeader
        IfNoneMatch = ... # type: QHttpHeaders.WellKnownHeader
        IfRange = ... # type: QHttpHeaders.WellKnownHeader
        IfScheduleTagMatch = ... # type: QHttpHeaders.WellKnownHeader
        IfUnmodifiedSince = ... # type: QHttpHeaders.WellKnownHeader
        IM = ... # type: QHttpHeaders.WellKnownHeader
        IncludeReferredTokenBindingID = ... # type: QHttpHeaders.WellKnownHeader
        KeepAlive = ... # type: QHttpHeaders.WellKnownHeader
        Label = ... # type: QHttpHeaders.WellKnownHeader
        LastEventID = ... # type: QHttpHeaders.WellKnownHeader
        LastModified = ... # type: QHttpHeaders.WellKnownHeader
        Link = ... # type: QHttpHeaders.WellKnownHeader
        Location = ... # type: QHttpHeaders.WellKnownHeader
        LockToken = ... # type: QHttpHeaders.WellKnownHeader
        MaxForwards = ... # type: QHttpHeaders.WellKnownHeader
        MementoDatetime = ... # type: QHttpHeaders.WellKnownHeader
        Meter = ... # type: QHttpHeaders.WellKnownHeader
        MIMEVersion = ... # type: QHttpHeaders.WellKnownHeader
        Negotiate = ... # type: QHttpHeaders.WellKnownHeader
        NEL = ... # type: QHttpHeaders.WellKnownHeader
        ODataEntityId = ... # type: QHttpHeaders.WellKnownHeader
        ODataIsolation = ... # type: QHttpHeaders.WellKnownHeader
        ODataMaxVersion = ... # type: QHttpHeaders.WellKnownHeader
        ODataVersion = ... # type: QHttpHeaders.WellKnownHeader
        OptionalWWWAuthenticate = ... # type: QHttpHeaders.WellKnownHeader
        OrderingType = ... # type: QHttpHeaders.WellKnownHeader
        Origin = ... # type: QHttpHeaders.WellKnownHeader
        OriginAgentCluster = ... # type: QHttpHeaders.WellKnownHeader
        OSCORE = ... # type: QHttpHeaders.WellKnownHeader
        OSLCCoreVersion = ... # type: QHttpHeaders.WellKnownHeader
        Overwrite = ... # type: QHttpHeaders.WellKnownHeader
        PingFrom = ... # type: QHttpHeaders.WellKnownHeader
        PingTo = ... # type: QHttpHeaders.WellKnownHeader
        Position = ... # type: QHttpHeaders.WellKnownHeader
        Prefer = ... # type: QHttpHeaders.WellKnownHeader
        PreferenceApplied = ... # type: QHttpHeaders.WellKnownHeader
        Priority = ... # type: QHttpHeaders.WellKnownHeader
        ProxyAuthenticate = ... # type: QHttpHeaders.WellKnownHeader
        ProxyAuthenticationInfo = ... # type: QHttpHeaders.WellKnownHeader
        ProxyAuthorization = ... # type: QHttpHeaders.WellKnownHeader
        ProxyStatus = ... # type: QHttpHeaders.WellKnownHeader
        PublicKeyPins = ... # type: QHttpHeaders.WellKnownHeader
        PublicKeyPinsReportOnly = ... # type: QHttpHeaders.WellKnownHeader
        Range = ... # type: QHttpHeaders.WellKnownHeader
        RedirectRef = ... # type: QHttpHeaders.WellKnownHeader
        Referer = ... # type: QHttpHeaders.WellKnownHeader
        Refresh = ... # type: QHttpHeaders.WellKnownHeader
        ReplayNonce = ... # type: QHttpHeaders.WellKnownHeader
        ReprDigest = ... # type: QHttpHeaders.WellKnownHeader
        RetryAfter = ... # type: QHttpHeaders.WellKnownHeader
        ScheduleReply = ... # type: QHttpHeaders.WellKnownHeader
        ScheduleTag = ... # type: QHttpHeaders.WellKnownHeader
        SecPurpose = ... # type: QHttpHeaders.WellKnownHeader
        SecTokenBinding = ... # type: QHttpHeaders.WellKnownHeader
        SecWebSocketAccept = ... # type: QHttpHeaders.WellKnownHeader
        SecWebSocketExtensions = ... # type: QHttpHeaders.WellKnownHeader
        SecWebSocketKey = ... # type: QHttpHeaders.WellKnownHeader
        SecWebSocketProtocol = ... # type: QHttpHeaders.WellKnownHeader
        SecWebSocketVersion = ... # type: QHttpHeaders.WellKnownHeader
        Server = ... # type: QHttpHeaders.WellKnownHeader
        ServerTiming = ... # type: QHttpHeaders.WellKnownHeader
        SetCookie = ... # type: QHttpHeaders.WellKnownHeader
        Signature = ... # type: QHttpHeaders.WellKnownHeader
        SignatureInput = ... # type: QHttpHeaders.WellKnownHeader
        SLUG = ... # type: QHttpHeaders.WellKnownHeader
        SoapAction = ... # type: QHttpHeaders.WellKnownHeader
        StatusURI = ... # type: QHttpHeaders.WellKnownHeader
        StrictTransportSecurity = ... # type: QHttpHeaders.WellKnownHeader
        Sunset = ... # type: QHttpHeaders.WellKnownHeader
        SurrogateCapability = ... # type: QHttpHeaders.WellKnownHeader
        SurrogateControl = ... # type: QHttpHeaders.WellKnownHeader
        TCN = ... # type: QHttpHeaders.WellKnownHeader
        TE = ... # type: QHttpHeaders.WellKnownHeader
        Timeout = ... # type: QHttpHeaders.WellKnownHeader
        Topic = ... # type: QHttpHeaders.WellKnownHeader
        Traceparent = ... # type: QHttpHeaders.WellKnownHeader
        Tracestate = ... # type: QHttpHeaders.WellKnownHeader
        Trailer = ... # type: QHttpHeaders.WellKnownHeader
        TransferEncoding = ... # type: QHttpHeaders.WellKnownHeader
        TTL = ... # type: QHttpHeaders.WellKnownHeader
        Upgrade = ... # type: QHttpHeaders.WellKnownHeader
        Urgency = ... # type: QHttpHeaders.WellKnownHeader
        UserAgent = ... # type: QHttpHeaders.WellKnownHeader
        VariantVary = ... # type: QHttpHeaders.WellKnownHeader
        Vary = ... # type: QHttpHeaders.WellKnownHeader
        Via = ... # type: QHttpHeaders.WellKnownHeader
        WantContentDigest = ... # type: QHttpHeaders.WellKnownHeader
        WantReprDigest = ... # type: QHttpHeaders.WellKnownHeader
        WWWAuthenticate = ... # type: QHttpHeaders.WellKnownHeader
        XContentTypeOptions = ... # type: QHttpHeaders.WellKnownHeader
        XFrameOptions = ... # type: QHttpHeaders.WellKnownHeader
        AcceptCharset = ... # type: QHttpHeaders.WellKnownHeader
        CPEPInfo = ... # type: QHttpHeaders.WellKnownHeader
        Pragma = ... # type: QHttpHeaders.WellKnownHeader
        ProtocolInfo = ... # type: QHttpHeaders.WellKnownHeader
        ProtocolQuery = ... # type: QHttpHeaders.WellKnownHeader

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QHttpHeaders') -> None: ...

    def toListOfPairs(self) -> typing.List[typing.Tuple[QtCore.QByteArray, QtCore.QByteArray]]: ...
    @staticmethod
    def fromListOfPairs(headers: typing.Iterable[typing.Tuple[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]]]) -> 'QHttpHeaders': ...
    @staticmethod
    def wellKnownHeaderName(name: 'QHttpHeaders.WellKnownHeader') -> QtCore.QByteArray: ...
    def isEmpty(self) -> bool: ...
    def reserve(self, size: int) -> None: ...
    def size(self) -> int: ...
    @typing.overload
    def combinedValue(self, name: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> QtCore.QByteArray: ...
    @typing.overload
    def combinedValue(self, name: 'QHttpHeaders.WellKnownHeader') -> QtCore.QByteArray: ...
    def nameAt(self, i: int) -> str: ...
    def valueAt(self, i: int) -> QtCore.QByteArray: ...
    @typing.overload
    def values(self, name: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> typing.List[QtCore.QByteArray]: ...
    @typing.overload
    def values(self, name: 'QHttpHeaders.WellKnownHeader') -> typing.List[QtCore.QByteArray]: ...
    @typing.overload
    def value(self, name: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]], defaultValue: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview] = ...) -> QtCore.QByteArray: ...
    @typing.overload
    def value(self, name: 'QHttpHeaders.WellKnownHeader', defaultValue: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview] = ...) -> QtCore.QByteArray: ...
    def removeAt(self, i: int) -> None: ...
    @typing.overload
    def removeAll(self, name: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> None: ...
    @typing.overload
    def removeAll(self, name: 'QHttpHeaders.WellKnownHeader') -> None: ...
    def clear(self) -> None: ...
    @typing.overload
    def contains(self, name: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> bool: ...
    @typing.overload
    def contains(self, name: 'QHttpHeaders.WellKnownHeader') -> bool: ...
    @typing.overload
    def replace(self, i: int, name: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]], newValue: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> bool: ...
    @typing.overload
    def replace(self, i: int, name: 'QHttpHeaders.WellKnownHeader', newValue: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> bool: ...
    @typing.overload
    def insert(self, i: int, name: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]], value: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> bool: ...
    @typing.overload
    def insert(self, i: int, name: 'QHttpHeaders.WellKnownHeader', value: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> bool: ...
    @typing.overload
    def append(self, name: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]], value: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> bool: ...
    @typing.overload
    def append(self, name: 'QHttpHeaders.WellKnownHeader', value: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> bool: ...
    def swap(self, other: 'QHttpHeaders') -> None: ...


class QHttpPart(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QHttpPart') -> None: ...

    def swap(self, other: 'QHttpPart') -> None: ...
    def setBodyDevice(self, device: typing.Optional[QtCore.QIODevice]) -> None: ...
    def setBody(self, body: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    def setRawHeader(self, headerName: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], headerValue: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    def setHeader(self, header: 'QNetworkRequest.KnownHeaders', value: typing.Any) -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QHttpMultiPart(QtCore.QObject):

    class ContentType(enum.Enum):
        MixedType = ... # type: QHttpMultiPart.ContentType
        RelatedType = ... # type: QHttpMultiPart.ContentType
        FormDataType = ... # type: QHttpMultiPart.ContentType
        AlternativeType = ... # type: QHttpMultiPart.ContentType

    @typing.overload
    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...
    @typing.overload
    def __init__(self, contentType: 'QHttpMultiPart.ContentType', parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setBoundary(self, boundary: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    def boundary(self) -> QtCore.QByteArray: ...
    def setContentType(self, contentType: 'QHttpMultiPart.ContentType') -> None: ...
    def append(self, httpPart: QHttpPart) -> None: ...


class QLocalServer(QtCore.QObject):

    class SocketOption(enum.Flag):
        UserAccessOption = ... # type: QLocalServer.SocketOption
        GroupAccessOption = ... # type: QLocalServer.SocketOption
        OtherAccessOption = ... # type: QLocalServer.SocketOption
        WorldAccessOption = ... # type: QLocalServer.SocketOption
        AbstractNamespaceOption = ... # type: QLocalServer.SocketOption

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def listenBacklogSize(self) -> int: ...
    def setListenBacklogSize(self, size: int) -> None: ...
    def socketDescriptor(self) -> PyQt6.sip.voidptr: ...
    def socketOptions(self) -> 'QLocalServer.SocketOption': ...
    def setSocketOptions(self, options: 'QLocalServer.SocketOption') -> None: ...
    def incomingConnection(self, socketDescriptor: PyQt6.sip.voidptr) -> None: ...
    newConnection: typing.ClassVar[QtCore.pyqtSignal]
    @staticmethod
    def removeServer(name: typing.Optional[str]) -> bool: ...
    def waitForNewConnection(self, msecs: int = ...) -> typing.Tuple[bool, typing.Optional[bool]]: ...
    def setMaxPendingConnections(self, numConnections: int) -> None: ...
    def serverError(self) -> QAbstractSocket.SocketError: ...
    def fullServerName(self) -> str: ...
    def serverName(self) -> str: ...
    def nextPendingConnection(self) -> typing.Optional['QLocalSocket']: ...
    def maxPendingConnections(self) -> int: ...
    @typing.overload
    def listen(self, name: typing.Optional[str]) -> bool: ...
    @typing.overload
    def listen(self, socketDescriptor: PyQt6.sip.voidptr) -> bool: ...
    def isListening(self) -> bool: ...
    def hasPendingConnections(self) -> bool: ...
    def errorString(self) -> str: ...
    def close(self) -> None: ...


class QLocalSocket(QtCore.QIODevice):

    class SocketOption(enum.Flag):
        NoOptions = ... # type: QLocalSocket.SocketOption
        AbstractNamespaceOption = ... # type: QLocalSocket.SocketOption

    class LocalSocketState(enum.Enum):
        UnconnectedState = ... # type: QLocalSocket.LocalSocketState
        ConnectingState = ... # type: QLocalSocket.LocalSocketState
        ConnectedState = ... # type: QLocalSocket.LocalSocketState
        ClosingState = ... # type: QLocalSocket.LocalSocketState

    class LocalSocketError(enum.Enum):
        ConnectionRefusedError = ... # type: QLocalSocket.LocalSocketError
        PeerClosedError = ... # type: QLocalSocket.LocalSocketError
        ServerNotFoundError = ... # type: QLocalSocket.LocalSocketError
        SocketAccessError = ... # type: QLocalSocket.LocalSocketError
        SocketResourceError = ... # type: QLocalSocket.LocalSocketError
        SocketTimeoutError = ... # type: QLocalSocket.LocalSocketError
        DatagramTooLargeError = ... # type: QLocalSocket.LocalSocketError
        ConnectionError = ... # type: QLocalSocket.LocalSocketError
        UnsupportedSocketOperationError = ... # type: QLocalSocket.LocalSocketError
        OperationError = ... # type: QLocalSocket.LocalSocketError
        UnknownSocketError = ... # type: QLocalSocket.LocalSocketError

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def readLineData(self, maxlen: int) -> bytes: ...
    def socketOptions(self) -> 'QLocalSocket.SocketOption': ...
    def setSocketOptions(self, option: 'QLocalSocket.SocketOption') -> None: ...
    def skipData(self, maxSize: int) -> int: ...
    def writeData(self, a0: PyQt6.sip.Buffer) -> int: ...
    def readData(self, maxlen: int) -> bytes: ...
    stateChanged: typing.ClassVar[QtCore.pyqtSignal]
    errorOccurred: typing.ClassVar[QtCore.pyqtSignal]
    disconnected: typing.ClassVar[QtCore.pyqtSignal]
    connected: typing.ClassVar[QtCore.pyqtSignal]
    def waitForReadyRead(self, msecs: int = ...) -> bool: ...
    def waitForDisconnected(self, msecs: int = ...) -> bool: ...
    def waitForConnected(self, msecs: int = ...) -> bool: ...
    def waitForBytesWritten(self, msecs: int = ...) -> bool: ...
    def state(self) -> 'QLocalSocket.LocalSocketState': ...
    def socketDescriptor(self) -> PyQt6.sip.voidptr: ...
    def setSocketDescriptor(self, socketDescriptor: PyQt6.sip.voidptr, state: 'QLocalSocket.LocalSocketState' = ..., mode: QtCore.QIODeviceBase.OpenModeFlag = ...) -> bool: ...
    def setReadBufferSize(self, size: int) -> None: ...
    def readBufferSize(self) -> int: ...
    def isValid(self) -> bool: ...
    def flush(self) -> bool: ...
    def error(self) -> 'QLocalSocket.LocalSocketError': ...
    def close(self) -> None: ...
    def canReadLine(self) -> bool: ...
    def bytesToWrite(self) -> int: ...
    def bytesAvailable(self) -> int: ...
    def isSequential(self) -> bool: ...
    def abort(self) -> None: ...
    def fullServerName(self) -> str: ...
    def setServerName(self, name: typing.Optional[str]) -> None: ...
    def serverName(self) -> str: ...
    def open(self, mode: QtCore.QIODeviceBase.OpenModeFlag = ...) -> bool: ...
    def disconnectFromServer(self) -> None: ...
    @typing.overload
    def connectToServer(self, name: typing.Optional[str], mode: QtCore.QIODeviceBase.OpenModeFlag = ...) -> None: ...
    @typing.overload
    def connectToServer(self, mode: QtCore.QIODeviceBase.OpenModeFlag = ...) -> None: ...


class QNetworkAccessManager(QtCore.QObject):

    class Operation(enum.Enum):
        HeadOperation = ... # type: QNetworkAccessManager.Operation
        GetOperation = ... # type: QNetworkAccessManager.Operation
        PutOperation = ... # type: QNetworkAccessManager.Operation
        PostOperation = ... # type: QNetworkAccessManager.Operation
        DeleteOperation = ... # type: QNetworkAccessManager.Operation
        CustomOperation = ... # type: QNetworkAccessManager.Operation

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setTransferTimeout(self, timeout: int = ...) -> None: ...
    def transferTimeout(self) -> int: ...
    def setAutoDeleteReplies(self, autoDelete: bool) -> None: ...
    def autoDeleteReplies(self) -> bool: ...
    def isStrictTransportSecurityStoreEnabled(self) -> bool: ...
    def enableStrictTransportSecurityStore(self, enabled: bool, storeDir: typing.Optional[str] = ...) -> None: ...
    def redirectPolicy(self) -> 'QNetworkRequest.RedirectPolicy': ...
    def setRedirectPolicy(self, policy: 'QNetworkRequest.RedirectPolicy') -> None: ...
    def strictTransportSecurityHosts(self) -> typing.List[QHstsPolicy]: ...
    def addStrictTransportSecurityHosts(self, knownHosts: typing.Iterable[QHstsPolicy]) -> None: ...
    def isStrictTransportSecurityEnabled(self) -> bool: ...
    def setStrictTransportSecurityEnabled(self, enabled: bool) -> None: ...
    def clearConnectionCache(self) -> None: ...
    def supportedSchemesImplementation(self) -> typing.List[str]: ...
    def connectToHost(self, hostName: typing.Optional[str], port: int = ...) -> None: ...
    @typing.overload
    def connectToHostEncrypted(self, hostName: typing.Optional[str], port: int = ..., sslConfiguration: 'QSslConfiguration' = ...) -> None: ...
    @typing.overload
    def connectToHostEncrypted(self, hostName: typing.Optional[str], port: int, sslConfiguration: 'QSslConfiguration', peerName: typing.Optional[str]) -> None: ...
    def supportedSchemes(self) -> typing.List[str]: ...
    def clearAccessCache(self) -> None: ...
    @typing.overload
    def sendCustomRequest(self, request: 'QNetworkRequest', verb: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], data: typing.Optional[QtCore.QIODevice] = ...) -> typing.Optional['QNetworkReply']: ...
    @typing.overload
    def sendCustomRequest(self, request: 'QNetworkRequest', verb: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], data: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> typing.Optional['QNetworkReply']: ...
    @typing.overload
    def sendCustomRequest(self, request: 'QNetworkRequest', verb: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], multiPart: typing.Optional[QHttpMultiPart]) -> typing.Optional['QNetworkReply']: ...
    def deleteResource(self, request: 'QNetworkRequest') -> typing.Optional['QNetworkReply']: ...
    def setCache(self, cache: typing.Optional[QAbstractNetworkCache]) -> None: ...
    def cache(self) -> typing.Optional[QAbstractNetworkCache]: ...
    def setProxyFactory(self, factory: typing.Optional['QNetworkProxyFactory']) -> None: ...
    def proxyFactory(self) -> typing.Optional['QNetworkProxyFactory']: ...
    def createRequest(self, op: 'QNetworkAccessManager.Operation', request: 'QNetworkRequest', device: typing.Optional[QtCore.QIODevice] = ...) -> 'QNetworkReply': ...
    preSharedKeyAuthenticationRequired: typing.ClassVar[QtCore.pyqtSignal]
    sslErrors: typing.ClassVar[QtCore.pyqtSignal]
    encrypted: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    authenticationRequired: typing.ClassVar[QtCore.pyqtSignal]
    proxyAuthenticationRequired: typing.ClassVar[QtCore.pyqtSignal]
    @typing.overload
    def put(self, request: 'QNetworkRequest', data: typing.Optional[QtCore.QIODevice]) -> typing.Optional['QNetworkReply']: ...
    @typing.overload
    def put(self, request: 'QNetworkRequest', data: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> typing.Optional['QNetworkReply']: ...
    @typing.overload
    def put(self, request: 'QNetworkRequest', multiPart: typing.Optional[QHttpMultiPart]) -> typing.Optional['QNetworkReply']: ...
    @typing.overload
    def post(self, request: 'QNetworkRequest', data: typing.Optional[QtCore.QIODevice]) -> typing.Optional['QNetworkReply']: ...
    @typing.overload
    def post(self, request: 'QNetworkRequest', data: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> typing.Optional['QNetworkReply']: ...
    @typing.overload
    def post(self, request: 'QNetworkRequest', multiPart: typing.Optional[QHttpMultiPart]) -> typing.Optional['QNetworkReply']: ...
    @typing.overload
    def get(self, request: 'QNetworkRequest') -> typing.Optional['QNetworkReply']: ...
    @typing.overload
    def get(self, request: 'QNetworkRequest', data: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> typing.Optional['QNetworkReply']: ...
    @typing.overload
    def get(self, request: 'QNetworkRequest', data: typing.Optional[QtCore.QIODevice]) -> typing.Optional['QNetworkReply']: ...
    def head(self, request: 'QNetworkRequest') -> typing.Optional['QNetworkReply']: ...
    def setCookieJar(self, cookieJar: typing.Optional['QNetworkCookieJar']) -> None: ...
    def cookieJar(self) -> typing.Optional['QNetworkCookieJar']: ...
    def setProxy(self, proxy: 'QNetworkProxy') -> None: ...
    def proxy(self) -> 'QNetworkProxy': ...


class QNetworkCookie(PyQt6.sip.simplewrapper):

    class SameSite(enum.Enum):
        Default = ... # type: QNetworkCookie.SameSite
        None_ = ... # type: QNetworkCookie.SameSite
        Lax = ... # type: QNetworkCookie.SameSite
        Strict = ... # type: QNetworkCookie.SameSite

    class RawForm(enum.Enum):
        NameAndValueOnly = ... # type: QNetworkCookie.RawForm
        Full = ... # type: QNetworkCookie.RawForm

    @typing.overload
    def __init__(self, name: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview] = ..., value: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview] = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QNetworkCookie') -> None: ...

    def setSameSitePolicy(self, sameSite: 'QNetworkCookie.SameSite') -> None: ...
    def sameSitePolicy(self) -> 'QNetworkCookie.SameSite': ...
    def normalize(self, url: QtCore.QUrl) -> None: ...
    def hasSameIdentifier(self, other: 'QNetworkCookie') -> bool: ...
    def swap(self, other: 'QNetworkCookie') -> None: ...
    def setHttpOnly(self, enable: bool) -> None: ...
    def isHttpOnly(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    @staticmethod
    def parseCookies(cookieString: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> typing.List['QNetworkCookie']: ...
    def toRawForm(self, form: 'QNetworkCookie.RawForm' = ...) -> QtCore.QByteArray: ...
    def setValue(self, value: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    def value(self) -> QtCore.QByteArray: ...
    def setName(self, cookieName: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    def name(self) -> QtCore.QByteArray: ...
    def setPath(self, path: typing.Optional[str]) -> None: ...
    def path(self) -> str: ...
    def setDomain(self, domain: typing.Optional[str]) -> None: ...
    def domain(self) -> str: ...
    def setExpirationDate(self, date: typing.Union[QtCore.QDateTime, datetime.datetime]) -> None: ...
    def expirationDate(self) -> QtCore.QDateTime: ...
    def isSessionCookie(self) -> bool: ...
    def setSecure(self, enable: bool) -> None: ...
    def isSecure(self) -> bool: ...


class QNetworkCookieJar(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def validateCookie(self, cookie: QNetworkCookie, url: QtCore.QUrl) -> bool: ...
    def allCookies(self) -> typing.List[QNetworkCookie]: ...
    def setAllCookies(self, cookieList: typing.Iterable[QNetworkCookie]) -> None: ...
    def deleteCookie(self, cookie: QNetworkCookie) -> bool: ...
    def updateCookie(self, cookie: QNetworkCookie) -> bool: ...
    def insertCookie(self, cookie: QNetworkCookie) -> bool: ...
    def setCookiesFromUrl(self, cookieList: typing.Iterable[QNetworkCookie], url: QtCore.QUrl) -> bool: ...
    def cookiesForUrl(self, url: QtCore.QUrl) -> typing.List[QNetworkCookie]: ...


class QNetworkDatagram(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, data: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], destinationAddress: typing.Union[QHostAddress, QHostAddress.SpecialAddress] = ..., port: int = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QNetworkDatagram') -> None: ...

    def makeReply(self, payload: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> 'QNetworkDatagram': ...
    def setData(self, data: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    def data(self) -> QtCore.QByteArray: ...
    def setHopLimit(self, count: int) -> None: ...
    def hopLimit(self) -> int: ...
    def setDestination(self, address: typing.Union[QHostAddress, QHostAddress.SpecialAddress], port: int) -> None: ...
    def setSender(self, address: typing.Union[QHostAddress, QHostAddress.SpecialAddress], port: int = ...) -> None: ...
    def destinationPort(self) -> int: ...
    def senderPort(self) -> int: ...
    def destinationAddress(self) -> QHostAddress: ...
    def senderAddress(self) -> QHostAddress: ...
    def setInterfaceIndex(self, index: int) -> None: ...
    def interfaceIndex(self) -> int: ...
    def isNull(self) -> bool: ...
    def isValid(self) -> bool: ...
    def clear(self) -> None: ...
    def swap(self, other: 'QNetworkDatagram') -> None: ...


class QNetworkDiskCache(QAbstractNetworkCache):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def expire(self) -> int: ...
    def clear(self) -> None: ...
    def fileMetaData(self, fileName: typing.Optional[str]) -> QNetworkCacheMetaData: ...
    def insert(self, device: typing.Optional[QtCore.QIODevice]) -> None: ...
    def prepare(self, metaData: QNetworkCacheMetaData) -> typing.Optional[QtCore.QIODevice]: ...
    def remove(self, url: QtCore.QUrl) -> bool: ...
    def data(self, url: QtCore.QUrl) -> typing.Optional[QtCore.QIODevice]: ...
    def updateMetaData(self, metaData: QNetworkCacheMetaData) -> None: ...
    def metaData(self, url: QtCore.QUrl) -> QNetworkCacheMetaData: ...
    def cacheSize(self) -> int: ...
    def setMaximumCacheSize(self, size: int) -> None: ...
    def maximumCacheSize(self) -> int: ...
    def setCacheDirectory(self, cacheDir: typing.Optional[str]) -> None: ...
    def cacheDirectory(self) -> str: ...


class QNetworkInformation(QtCore.QObject):

    class TransportMedium(enum.Enum):
        Unknown = ... # type: QNetworkInformation.TransportMedium
        Ethernet = ... # type: QNetworkInformation.TransportMedium
        Cellular = ... # type: QNetworkInformation.TransportMedium
        WiFi = ... # type: QNetworkInformation.TransportMedium
        Bluetooth = ... # type: QNetworkInformation.TransportMedium

    class Feature(enum.Enum):
        Reachability = ... # type: QNetworkInformation.Feature
        CaptivePortal = ... # type: QNetworkInformation.Feature
        TransportMedium = ... # type: QNetworkInformation.Feature
        Metered = ... # type: QNetworkInformation.Feature

    class Reachability(enum.Enum):
        Unknown = ... # type: QNetworkInformation.Reachability
        Disconnected = ... # type: QNetworkInformation.Reachability
        Local = ... # type: QNetworkInformation.Reachability
        Site = ... # type: QNetworkInformation.Reachability
        Online = ... # type: QNetworkInformation.Reachability

    @staticmethod
    def loadBackendByFeatures(features: 'QNetworkInformation.Feature') -> bool: ...
    @staticmethod
    def loadBackendByName(backend: str) -> bool: ...
    isMeteredChanged: typing.ClassVar[QtCore.pyqtSignal]
    transportMediumChanged: typing.ClassVar[QtCore.pyqtSignal]
    @staticmethod
    def loadDefaultBackend() -> bool: ...
    def supportedFeatures(self) -> 'QNetworkInformation.Feature': ...
    def isMetered(self) -> bool: ...
    def transportMedium(self) -> 'QNetworkInformation.TransportMedium': ...
    isBehindCaptivePortalChanged: typing.ClassVar[QtCore.pyqtSignal]
    def isBehindCaptivePortal(self) -> bool: ...
    reachabilityChanged: typing.ClassVar[QtCore.pyqtSignal]
    @staticmethod
    def instance() -> typing.Optional['QNetworkInformation']: ...
    @staticmethod
    def availableBackends() -> typing.List[str]: ...
    @typing.overload
    @staticmethod
    def load(backend: str) -> bool: ...
    @typing.overload
    @staticmethod
    def load(features: 'QNetworkInformation.Feature') -> bool: ...
    def supports(self, features: 'QNetworkInformation.Feature') -> bool: ...
    def backendName(self) -> str: ...
    def reachability(self) -> 'QNetworkInformation.Reachability': ...


class QNetworkAddressEntry(PyQt6.sip.simplewrapper):

    class DnsEligibilityStatus(enum.Enum):
        DnsEligibilityUnknown = ... # type: QNetworkAddressEntry.DnsEligibilityStatus
        DnsIneligible = ... # type: QNetworkAddressEntry.DnsEligibilityStatus
        DnsEligible = ... # type: QNetworkAddressEntry.DnsEligibilityStatus

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QNetworkAddressEntry') -> None: ...

    def isTemporary(self) -> bool: ...
    def isPermanent(self) -> bool: ...
    def clearAddressLifetime(self) -> None: ...
    def setAddressLifetime(self, preferred: QtCore.QDeadlineTimer, validity: QtCore.QDeadlineTimer) -> None: ...
    def validityLifetime(self) -> QtCore.QDeadlineTimer: ...
    def preferredLifetime(self) -> QtCore.QDeadlineTimer: ...
    def isLifetimeKnown(self) -> bool: ...
    def setDnsEligibility(self, status: 'QNetworkAddressEntry.DnsEligibilityStatus') -> None: ...
    def dnsEligibility(self) -> 'QNetworkAddressEntry.DnsEligibilityStatus': ...
    def swap(self, other: 'QNetworkAddressEntry') -> None: ...
    def setPrefixLength(self, length: int) -> None: ...
    def prefixLength(self) -> int: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def setBroadcast(self, newBroadcast: typing.Union[QHostAddress, QHostAddress.SpecialAddress]) -> None: ...
    def broadcast(self) -> QHostAddress: ...
    def setNetmask(self, newNetmask: typing.Union[QHostAddress, QHostAddress.SpecialAddress]) -> None: ...
    def netmask(self) -> QHostAddress: ...
    def setIp(self, newIp: typing.Union[QHostAddress, QHostAddress.SpecialAddress]) -> None: ...
    def ip(self) -> QHostAddress: ...


class QNetworkInterface(PyQt6.sip.simplewrapper):

    class InterfaceType(enum.Enum):
        Unknown = ... # type: QNetworkInterface.InterfaceType
        Loopback = ... # type: QNetworkInterface.InterfaceType
        Virtual = ... # type: QNetworkInterface.InterfaceType
        Ethernet = ... # type: QNetworkInterface.InterfaceType
        Slip = ... # type: QNetworkInterface.InterfaceType
        CanBus = ... # type: QNetworkInterface.InterfaceType
        Ppp = ... # type: QNetworkInterface.InterfaceType
        Fddi = ... # type: QNetworkInterface.InterfaceType
        Wifi = ... # type: QNetworkInterface.InterfaceType
        Ieee80211 = ... # type: QNetworkInterface.InterfaceType
        Phonet = ... # type: QNetworkInterface.InterfaceType
        Ieee802154 = ... # type: QNetworkInterface.InterfaceType
        SixLoWPAN = ... # type: QNetworkInterface.InterfaceType
        Ieee80216 = ... # type: QNetworkInterface.InterfaceType
        Ieee1394 = ... # type: QNetworkInterface.InterfaceType

    class InterfaceFlag(enum.Flag):
        IsUp = ... # type: QNetworkInterface.InterfaceFlag
        IsRunning = ... # type: QNetworkInterface.InterfaceFlag
        CanBroadcast = ... # type: QNetworkInterface.InterfaceFlag
        IsLoopBack = ... # type: QNetworkInterface.InterfaceFlag
        IsPointToPoint = ... # type: QNetworkInterface.InterfaceFlag
        CanMulticast = ... # type: QNetworkInterface.InterfaceFlag

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QNetworkInterface') -> None: ...

    def maximumTransmissionUnit(self) -> int: ...
    def type(self) -> 'QNetworkInterface.InterfaceType': ...
    @staticmethod
    def interfaceNameFromIndex(index: int) -> str: ...
    @staticmethod
    def interfaceIndexFromName(name: typing.Optional[str]) -> int: ...
    def swap(self, other: 'QNetworkInterface') -> None: ...
    def humanReadableName(self) -> str: ...
    def index(self) -> int: ...
    @staticmethod
    def allAddresses() -> typing.List[QHostAddress]: ...
    @staticmethod
    def allInterfaces() -> typing.List['QNetworkInterface']: ...
    @staticmethod
    def interfaceFromIndex(index: int) -> 'QNetworkInterface': ...
    @staticmethod
    def interfaceFromName(name: typing.Optional[str]) -> 'QNetworkInterface': ...
    def addressEntries(self) -> typing.List[QNetworkAddressEntry]: ...
    def hardwareAddress(self) -> str: ...
    def flags(self) -> 'QNetworkInterface.InterfaceFlag': ...
    def name(self) -> str: ...
    def isValid(self) -> bool: ...


class QNetworkProxy(PyQt6.sip.simplewrapper):

    class Capability(enum.Flag):
        TunnelingCapability = ... # type: QNetworkProxy.Capability
        ListeningCapability = ... # type: QNetworkProxy.Capability
        UdpTunnelingCapability = ... # type: QNetworkProxy.Capability
        CachingCapability = ... # type: QNetworkProxy.Capability
        HostNameLookupCapability = ... # type: QNetworkProxy.Capability
        SctpTunnelingCapability = ... # type: QNetworkProxy.Capability
        SctpListeningCapability = ... # type: QNetworkProxy.Capability

    class ProxyType(enum.Enum):
        DefaultProxy = ... # type: QNetworkProxy.ProxyType
        Socks5Proxy = ... # type: QNetworkProxy.ProxyType
        NoProxy = ... # type: QNetworkProxy.ProxyType
        HttpProxy = ... # type: QNetworkProxy.ProxyType
        HttpCachingProxy = ... # type: QNetworkProxy.ProxyType
        FtpCachingProxy = ... # type: QNetworkProxy.ProxyType

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, type: 'QNetworkProxy.ProxyType', hostName: typing.Optional[str] = ..., port: int = ..., user: typing.Optional[str] = ..., password: typing.Optional[str] = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QNetworkProxy') -> None: ...

    def setRawHeader(self, headerName: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], value: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    def rawHeader(self, headerName: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> QtCore.QByteArray: ...
    def rawHeaderList(self) -> typing.List[QtCore.QByteArray]: ...
    def hasRawHeader(self, headerName: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> bool: ...
    def setHeader(self, header: 'QNetworkRequest.KnownHeaders', value: typing.Any) -> None: ...
    def header(self, header: 'QNetworkRequest.KnownHeaders') -> typing.Any: ...
    def swap(self, other: 'QNetworkProxy') -> None: ...
    def capabilities(self) -> 'QNetworkProxy.Capability': ...
    def setCapabilities(self, capab: 'QNetworkProxy.Capability') -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def isTransparentProxy(self) -> bool: ...
    def isCachingProxy(self) -> bool: ...
    @staticmethod
    def applicationProxy() -> 'QNetworkProxy': ...
    @staticmethod
    def setApplicationProxy(proxy: 'QNetworkProxy') -> None: ...
    def port(self) -> int: ...
    def setPort(self, port: int) -> None: ...
    def hostName(self) -> str: ...
    def setHostName(self, hostName: typing.Optional[str]) -> None: ...
    def password(self) -> str: ...
    def setPassword(self, password: typing.Optional[str]) -> None: ...
    def user(self) -> str: ...
    def setUser(self, userName: typing.Optional[str]) -> None: ...
    def type(self) -> 'QNetworkProxy.ProxyType': ...
    def setType(self, type: 'QNetworkProxy.ProxyType') -> None: ...


class QNetworkProxyQuery(PyQt6.sip.simplewrapper):

    class QueryType(enum.Enum):
        TcpSocket = ... # type: QNetworkProxyQuery.QueryType
        UdpSocket = ... # type: QNetworkProxyQuery.QueryType
        TcpServer = ... # type: QNetworkProxyQuery.QueryType
        UrlRequest = ... # type: QNetworkProxyQuery.QueryType
        SctpSocket = ... # type: QNetworkProxyQuery.QueryType
        SctpServer = ... # type: QNetworkProxyQuery.QueryType

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, requestUrl: QtCore.QUrl, type: 'QNetworkProxyQuery.QueryType' = ...) -> None: ...
    @typing.overload
    def __init__(self, hostname: typing.Optional[str], port: int, protocolTag: typing.Optional[str] = ..., type: 'QNetworkProxyQuery.QueryType' = ...) -> None: ...
    @typing.overload
    def __init__(self, bindPort: int, protocolTag: typing.Optional[str] = ..., type: 'QNetworkProxyQuery.QueryType' = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QNetworkProxyQuery') -> None: ...

    def swap(self, other: 'QNetworkProxyQuery') -> None: ...
    def setUrl(self, url: QtCore.QUrl) -> None: ...
    def url(self) -> QtCore.QUrl: ...
    def setProtocolTag(self, protocolTag: typing.Optional[str]) -> None: ...
    def protocolTag(self) -> str: ...
    def setLocalPort(self, port: int) -> None: ...
    def localPort(self) -> int: ...
    def setPeerHostName(self, hostname: typing.Optional[str]) -> None: ...
    def peerHostName(self) -> str: ...
    def setPeerPort(self, port: int) -> None: ...
    def peerPort(self) -> int: ...
    def setQueryType(self, type: 'QNetworkProxyQuery.QueryType') -> None: ...
    def queryType(self) -> 'QNetworkProxyQuery.QueryType': ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QNetworkProxyFactory(PyQt6.sip.wrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QNetworkProxyFactory') -> None: ...

    @staticmethod
    def usesSystemConfiguration() -> bool: ...
    @staticmethod
    def setUseSystemConfiguration(enable: bool) -> None: ...
    @staticmethod
    def systemProxyForQuery(query: QNetworkProxyQuery = ...) -> typing.List[QNetworkProxy]: ...
    @staticmethod
    def proxyForQuery(query: QNetworkProxyQuery) -> typing.List[QNetworkProxy]: ...
    @staticmethod
    def setApplicationProxyFactory(factory: typing.Optional['QNetworkProxyFactory']) -> None: ...
    def queryProxy(self, query: QNetworkProxyQuery = ...) -> typing.List[QNetworkProxy]: ...


class QNetworkReply(QtCore.QIODevice):

    class NetworkError(enum.Enum):
        NoError = ... # type: QNetworkReply.NetworkError
        ConnectionRefusedError = ... # type: QNetworkReply.NetworkError
        RemoteHostClosedError = ... # type: QNetworkReply.NetworkError
        HostNotFoundError = ... # type: QNetworkReply.NetworkError
        TimeoutError = ... # type: QNetworkReply.NetworkError
        OperationCanceledError = ... # type: QNetworkReply.NetworkError
        SslHandshakeFailedError = ... # type: QNetworkReply.NetworkError
        UnknownNetworkError = ... # type: QNetworkReply.NetworkError
        ProxyConnectionRefusedError = ... # type: QNetworkReply.NetworkError
        ProxyConnectionClosedError = ... # type: QNetworkReply.NetworkError
        ProxyNotFoundError = ... # type: QNetworkReply.NetworkError
        ProxyTimeoutError = ... # type: QNetworkReply.NetworkError
        ProxyAuthenticationRequiredError = ... # type: QNetworkReply.NetworkError
        UnknownProxyError = ... # type: QNetworkReply.NetworkError
        ContentAccessDenied = ... # type: QNetworkReply.NetworkError
        ContentOperationNotPermittedError = ... # type: QNetworkReply.NetworkError
        ContentNotFoundError = ... # type: QNetworkReply.NetworkError
        AuthenticationRequiredError = ... # type: QNetworkReply.NetworkError
        UnknownContentError = ... # type: QNetworkReply.NetworkError
        ProtocolUnknownError = ... # type: QNetworkReply.NetworkError
        ProtocolInvalidOperationError = ... # type: QNetworkReply.NetworkError
        ProtocolFailure = ... # type: QNetworkReply.NetworkError
        ContentReSendError = ... # type: QNetworkReply.NetworkError
        TemporaryNetworkFailureError = ... # type: QNetworkReply.NetworkError
        NetworkSessionFailedError = ... # type: QNetworkReply.NetworkError
        BackgroundRequestNotAllowedError = ... # type: QNetworkReply.NetworkError
        ContentConflictError = ... # type: QNetworkReply.NetworkError
        ContentGoneError = ... # type: QNetworkReply.NetworkError
        InternalServerError = ... # type: QNetworkReply.NetworkError
        OperationNotImplementedError = ... # type: QNetworkReply.NetworkError
        ServiceUnavailableError = ... # type: QNetworkReply.NetworkError
        UnknownServerError = ... # type: QNetworkReply.NetworkError
        TooManyRedirectsError = ... # type: QNetworkReply.NetworkError
        InsecureRedirectError = ... # type: QNetworkReply.NetworkError

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    requestSent: typing.ClassVar[QtCore.pyqtSignal]
    socketStartedConnecting: typing.ClassVar[QtCore.pyqtSignal]
    def ignoreSslErrorsImplementation(self, a0: typing.Iterable['QSslError']) -> None: ...
    def setSslConfigurationImplementation(self, a0: 'QSslConfiguration') -> None: ...
    def sslConfigurationImplementation(self, a0: 'QSslConfiguration') -> None: ...
    def rawHeaderPairs(self) -> typing.List[typing.Tuple[QtCore.QByteArray, QtCore.QByteArray]]: ...
    def isRunning(self) -> bool: ...
    def isFinished(self) -> bool: ...
    def setFinished(self, finished: bool) -> None: ...
    def setAttribute(self, code: 'QNetworkRequest.Attribute', value: typing.Any) -> None: ...
    def setRawHeader(self, headerName: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], value: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    def setHeader(self, header: 'QNetworkRequest.KnownHeaders', value: typing.Any) -> None: ...
    def setUrl(self, url: QtCore.QUrl) -> None: ...
    def setError(self, errorCode: 'QNetworkReply.NetworkError', errorString: typing.Optional[str]) -> None: ...
    def setRequest(self, request: 'QNetworkRequest') -> None: ...
    def setOperation(self, operation: QNetworkAccessManager.Operation) -> None: ...
    def writeData(self, a0: PyQt6.sip.Buffer) -> int: ...
    redirectAllowed: typing.ClassVar[QtCore.pyqtSignal]
    redirected: typing.ClassVar[QtCore.pyqtSignal]
    preSharedKeyAuthenticationRequired: typing.ClassVar[QtCore.pyqtSignal]
    downloadProgress: typing.ClassVar[QtCore.pyqtSignal]
    uploadProgress: typing.ClassVar[QtCore.pyqtSignal]
    sslErrors: typing.ClassVar[QtCore.pyqtSignal]
    errorOccurred: typing.ClassVar[QtCore.pyqtSignal]
    encrypted: typing.ClassVar[QtCore.pyqtSignal]
    finished: typing.ClassVar[QtCore.pyqtSignal]
    metaDataChanged: typing.ClassVar[QtCore.pyqtSignal]
    @typing.overload
    def ignoreSslErrors(self) -> None: ...
    @typing.overload
    def ignoreSslErrors(self, errors: typing.Iterable['QSslError']) -> None: ...
    def setSslConfiguration(self, configuration: 'QSslConfiguration') -> None: ...
    def sslConfiguration(self) -> 'QSslConfiguration': ...
    def attribute(self, code: 'QNetworkRequest.Attribute') -> typing.Any: ...
    def rawHeader(self, headerName: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> QtCore.QByteArray: ...
    def rawHeaderList(self) -> typing.List[QtCore.QByteArray]: ...
    def hasRawHeader(self, headerName: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> bool: ...
    def header(self, header: 'QNetworkRequest.KnownHeaders') -> typing.Any: ...
    def url(self) -> QtCore.QUrl: ...
    def error(self) -> 'QNetworkReply.NetworkError': ...
    def request(self) -> 'QNetworkRequest': ...
    def operation(self) -> QNetworkAccessManager.Operation: ...
    def manager(self) -> typing.Optional[QNetworkAccessManager]: ...
    def setReadBufferSize(self, size: int) -> None: ...
    def readBufferSize(self) -> int: ...
    def isSequential(self) -> bool: ...
    def close(self) -> None: ...
    def abort(self) -> None: ...


class QNetworkRequest(PyQt6.sip.simplewrapper):

    class TransferTimeoutConstant(enum.Enum):
        DefaultTransferTimeoutConstant = ... # type: QNetworkRequest.TransferTimeoutConstant

    class RedirectPolicy(enum.Enum):
        ManualRedirectPolicy = ... # type: QNetworkRequest.RedirectPolicy
        NoLessSafeRedirectPolicy = ... # type: QNetworkRequest.RedirectPolicy
        SameOriginRedirectPolicy = ... # type: QNetworkRequest.RedirectPolicy
        UserVerifiedRedirectPolicy = ... # type: QNetworkRequest.RedirectPolicy

    class Priority(enum.Enum):
        HighPriority = ... # type: QNetworkRequest.Priority
        NormalPriority = ... # type: QNetworkRequest.Priority
        LowPriority = ... # type: QNetworkRequest.Priority

    class LoadControl(enum.Enum):
        Automatic = ... # type: QNetworkRequest.LoadControl
        Manual = ... # type: QNetworkRequest.LoadControl

    class CacheLoadControl(enum.Enum):
        AlwaysNetwork = ... # type: QNetworkRequest.CacheLoadControl
        PreferNetwork = ... # type: QNetworkRequest.CacheLoadControl
        PreferCache = ... # type: QNetworkRequest.CacheLoadControl
        AlwaysCache = ... # type: QNetworkRequest.CacheLoadControl

    class Attribute(enum.Enum):
        HttpStatusCodeAttribute = ... # type: QNetworkRequest.Attribute
        HttpReasonPhraseAttribute = ... # type: QNetworkRequest.Attribute
        RedirectionTargetAttribute = ... # type: QNetworkRequest.Attribute
        ConnectionEncryptedAttribute = ... # type: QNetworkRequest.Attribute
        CacheLoadControlAttribute = ... # type: QNetworkRequest.Attribute
        CacheSaveControlAttribute = ... # type: QNetworkRequest.Attribute
        SourceIsFromCacheAttribute = ... # type: QNetworkRequest.Attribute
        DoNotBufferUploadDataAttribute = ... # type: QNetworkRequest.Attribute
        HttpPipeliningAllowedAttribute = ... # type: QNetworkRequest.Attribute
        HttpPipeliningWasUsedAttribute = ... # type: QNetworkRequest.Attribute
        CustomVerbAttribute = ... # type: QNetworkRequest.Attribute
        CookieLoadControlAttribute = ... # type: QNetworkRequest.Attribute
        AuthenticationReuseAttribute = ... # type: QNetworkRequest.Attribute
        CookieSaveControlAttribute = ... # type: QNetworkRequest.Attribute
        BackgroundRequestAttribute = ... # type: QNetworkRequest.Attribute
        EmitAllUploadProgressSignalsAttribute = ... # type: QNetworkRequest.Attribute
        Http2AllowedAttribute = ... # type: QNetworkRequest.Attribute
        Http2WasUsedAttribute = ... # type: QNetworkRequest.Attribute
        OriginalContentLengthAttribute = ... # type: QNetworkRequest.Attribute
        RedirectPolicyAttribute = ... # type: QNetworkRequest.Attribute
        Http2DirectAttribute = ... # type: QNetworkRequest.Attribute
        AutoDeleteReplyOnFinishAttribute = ... # type: QNetworkRequest.Attribute
        ConnectionCacheExpiryTimeoutSecondsAttribute = ... # type: QNetworkRequest.Attribute
        Http2CleartextAllowedAttribute = ... # type: QNetworkRequest.Attribute
        UseCredentialsAttribute = ... # type: QNetworkRequest.Attribute
        User = ... # type: QNetworkRequest.Attribute
        UserMax = ... # type: QNetworkRequest.Attribute

    class KnownHeaders(enum.Enum):
        ContentTypeHeader = ... # type: QNetworkRequest.KnownHeaders
        ContentLengthHeader = ... # type: QNetworkRequest.KnownHeaders
        LocationHeader = ... # type: QNetworkRequest.KnownHeaders
        LastModifiedHeader = ... # type: QNetworkRequest.KnownHeaders
        CookieHeader = ... # type: QNetworkRequest.KnownHeaders
        SetCookieHeader = ... # type: QNetworkRequest.KnownHeaders
        ContentDispositionHeader = ... # type: QNetworkRequest.KnownHeaders
        UserAgentHeader = ... # type: QNetworkRequest.KnownHeaders
        ServerHeader = ... # type: QNetworkRequest.KnownHeaders
        IfModifiedSinceHeader = ... # type: QNetworkRequest.KnownHeaders
        ETagHeader = ... # type: QNetworkRequest.KnownHeaders
        IfMatchHeader = ... # type: QNetworkRequest.KnownHeaders
        IfNoneMatchHeader = ... # type: QNetworkRequest.KnownHeaders

    @typing.overload
    def __init__(self, url: QtCore.QUrl) -> None: ...
    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QNetworkRequest') -> None: ...

    def setDecompressedSafetyCheckThreshold(self, threshold: int) -> None: ...
    def decompressedSafetyCheckThreshold(self) -> int: ...
    def setTransferTimeout(self, timeout: int = ...) -> None: ...
    def transferTimeout(self) -> int: ...
    def setHttp2Configuration(self, configuration: QHttp2Configuration) -> None: ...
    def setHttp1Configuration(self, configuration: QHttp1Configuration) -> None: ...
    def http2Configuration(self) -> QHttp2Configuration: ...
    def http1Configuration(self) -> QHttp1Configuration: ...
    def setPeerVerifyName(self, peerName: typing.Optional[str]) -> None: ...
    def peerVerifyName(self) -> str: ...
    def setMaximumRedirectsAllowed(self, maximumRedirectsAllowed: int) -> None: ...
    def maximumRedirectsAllowed(self) -> int: ...
    def swap(self, other: 'QNetworkRequest') -> None: ...
    def setPriority(self, priority: 'QNetworkRequest.Priority') -> None: ...
    def priority(self) -> 'QNetworkRequest.Priority': ...
    def originatingObject(self) -> typing.Optional[QtCore.QObject]: ...
    def setOriginatingObject(self, object: typing.Optional[QtCore.QObject]) -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def setSslConfiguration(self, configuration: 'QSslConfiguration') -> None: ...
    def sslConfiguration(self) -> 'QSslConfiguration': ...
    def setAttribute(self, code: 'QNetworkRequest.Attribute', value: typing.Any) -> None: ...
    def attribute(self, code: 'QNetworkRequest.Attribute', defaultValue: typing.Any = ...) -> typing.Any: ...
    def setRawHeader(self, headerName: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], value: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    def rawHeader(self, headerName: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> QtCore.QByteArray: ...
    def rawHeaderList(self) -> typing.List[QtCore.QByteArray]: ...
    def hasRawHeader(self, headerName: typing.Union[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Optional[str]]) -> bool: ...
    def setHeader(self, header: 'QNetworkRequest.KnownHeaders', value: typing.Any) -> None: ...
    def header(self, header: 'QNetworkRequest.KnownHeaders') -> typing.Any: ...
    def setUrl(self, url: QtCore.QUrl) -> None: ...
    def url(self) -> QtCore.QUrl: ...


class QOcspResponse(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QOcspResponse') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __hash__(self) -> int: ...
    def swap(self, other: 'QOcspResponse') -> None: ...
    def subject(self) -> 'QSslCertificate': ...
    def responder(self) -> 'QSslCertificate': ...
    def revocationReason(self) -> QOcspRevocationReason: ...
    def certificateStatus(self) -> QOcspCertificateStatus: ...


class QPasswordDigestor(PyQt6.sip.simplewrapper):

    def deriveKeyPbkdf2(self, algorithm: QtCore.QCryptographicHash.Algorithm, password: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], salt: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], iterations: int, dkLen: int) -> QtCore.QByteArray: ...
    def deriveKeyPbkdf1(self, algorithm: QtCore.QCryptographicHash.Algorithm, password: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], salt: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], iterations: int, dkLen: int) -> QtCore.QByteArray: ...


class QSsl(PyQt6.sip.simplewrapper):

    class SupportedFeature(enum.Enum):
        CertificateVerification = ... # type: QSsl.SupportedFeature
        ClientSideAlpn = ... # type: QSsl.SupportedFeature
        ServerSideAlpn = ... # type: QSsl.SupportedFeature
        Ocsp = ... # type: QSsl.SupportedFeature
        Psk = ... # type: QSsl.SupportedFeature
        SessionTicket = ... # type: QSsl.SupportedFeature
        Alerts = ... # type: QSsl.SupportedFeature

    class ImplementedClass(enum.Enum):
        Key = ... # type: QSsl.ImplementedClass
        Certificate = ... # type: QSsl.ImplementedClass
        Socket = ... # type: QSsl.ImplementedClass
        DiffieHellman = ... # type: QSsl.ImplementedClass
        EllipticCurve = ... # type: QSsl.ImplementedClass
        Dtls = ... # type: QSsl.ImplementedClass
        DtlsCookie = ... # type: QSsl.ImplementedClass

    class AlertType(enum.Enum):
        CloseNotify = ... # type: QSsl.AlertType
        UnexpectedMessage = ... # type: QSsl.AlertType
        BadRecordMac = ... # type: QSsl.AlertType
        RecordOverflow = ... # type: QSsl.AlertType
        DecompressionFailure = ... # type: QSsl.AlertType
        HandshakeFailure = ... # type: QSsl.AlertType
        NoCertificate = ... # type: QSsl.AlertType
        BadCertificate = ... # type: QSsl.AlertType
        UnsupportedCertificate = ... # type: QSsl.AlertType
        CertificateRevoked = ... # type: QSsl.AlertType
        CertificateExpired = ... # type: QSsl.AlertType
        CertificateUnknown = ... # type: QSsl.AlertType
        IllegalParameter = ... # type: QSsl.AlertType
        UnknownCa = ... # type: QSsl.AlertType
        AccessDenied = ... # type: QSsl.AlertType
        DecodeError = ... # type: QSsl.AlertType
        DecryptError = ... # type: QSsl.AlertType
        ExportRestriction = ... # type: QSsl.AlertType
        ProtocolVersion = ... # type: QSsl.AlertType
        InsufficientSecurity = ... # type: QSsl.AlertType
        InternalError = ... # type: QSsl.AlertType
        InappropriateFallback = ... # type: QSsl.AlertType
        UserCancelled = ... # type: QSsl.AlertType
        NoRenegotiation = ... # type: QSsl.AlertType
        MissingExtension = ... # type: QSsl.AlertType
        UnsupportedExtension = ... # type: QSsl.AlertType
        CertificateUnobtainable = ... # type: QSsl.AlertType
        UnrecognizedName = ... # type: QSsl.AlertType
        BadCertificateStatusResponse = ... # type: QSsl.AlertType
        BadCertificateHashValue = ... # type: QSsl.AlertType
        UnknownPskIdentity = ... # type: QSsl.AlertType
        CertificateRequired = ... # type: QSsl.AlertType
        NoApplicationProtocol = ... # type: QSsl.AlertType
        UnknownAlertMessage = ... # type: QSsl.AlertType

    class AlertLevel(enum.Enum):
        Warning = ... # type: QSsl.AlertLevel
        Fatal = ... # type: QSsl.AlertLevel
        Unknown = ... # type: QSsl.AlertLevel

    class SslOption(enum.Flag):
        SslOptionDisableEmptyFragments = ... # type: QSsl.SslOption
        SslOptionDisableSessionTickets = ... # type: QSsl.SslOption
        SslOptionDisableCompression = ... # type: QSsl.SslOption
        SslOptionDisableServerNameIndication = ... # type: QSsl.SslOption
        SslOptionDisableLegacyRenegotiation = ... # type: QSsl.SslOption
        SslOptionDisableSessionSharing = ... # type: QSsl.SslOption
        SslOptionDisableSessionPersistence = ... # type: QSsl.SslOption
        SslOptionDisableServerCipherPreference = ... # type: QSsl.SslOption

    class SslProtocol(enum.Enum):
        UnknownProtocol = ... # type: QSsl.SslProtocol
        TlsV1_0 = ... # type: QSsl.SslProtocol
        TlsV1_0OrLater = ... # type: QSsl.SslProtocol
        TlsV1_1 = ... # type: QSsl.SslProtocol
        TlsV1_1OrLater = ... # type: QSsl.SslProtocol
        TlsV1_2 = ... # type: QSsl.SslProtocol
        TlsV1_2OrLater = ... # type: QSsl.SslProtocol
        AnyProtocol = ... # type: QSsl.SslProtocol
        SecureProtocols = ... # type: QSsl.SslProtocol
        DtlsV1_0 = ... # type: QSsl.SslProtocol
        DtlsV1_0OrLater = ... # type: QSsl.SslProtocol
        DtlsV1_2 = ... # type: QSsl.SslProtocol
        DtlsV1_2OrLater = ... # type: QSsl.SslProtocol
        TlsV1_3 = ... # type: QSsl.SslProtocol
        TlsV1_3OrLater = ... # type: QSsl.SslProtocol

    class AlternativeNameEntryType(enum.Enum):
        EmailEntry = ... # type: QSsl.AlternativeNameEntryType
        DnsEntry = ... # type: QSsl.AlternativeNameEntryType
        IpAddressEntry = ... # type: QSsl.AlternativeNameEntryType

    class KeyAlgorithm(enum.Enum):
        Opaque = ... # type: QSsl.KeyAlgorithm
        Rsa = ... # type: QSsl.KeyAlgorithm
        Dsa = ... # type: QSsl.KeyAlgorithm
        Ec = ... # type: QSsl.KeyAlgorithm
        Dh = ... # type: QSsl.KeyAlgorithm

    class EncodingFormat(enum.Enum):
        Pem = ... # type: QSsl.EncodingFormat
        Der = ... # type: QSsl.EncodingFormat

    class KeyType(enum.Enum):
        PrivateKey = ... # type: QSsl.KeyType
        PublicKey = ... # type: QSsl.KeyType


class QSslCertificate(PyQt6.sip.simplewrapper):

    class PatternSyntax(enum.Enum):
        RegularExpression = ... # type: QSslCertificate.PatternSyntax
        Wildcard = ... # type: QSslCertificate.PatternSyntax
        FixedString = ... # type: QSslCertificate.PatternSyntax

    class SubjectInfo(enum.Enum):
        Organization = ... # type: QSslCertificate.SubjectInfo
        CommonName = ... # type: QSslCertificate.SubjectInfo
        LocalityName = ... # type: QSslCertificate.SubjectInfo
        OrganizationalUnitName = ... # type: QSslCertificate.SubjectInfo
        CountryName = ... # type: QSslCertificate.SubjectInfo
        StateOrProvinceName = ... # type: QSslCertificate.SubjectInfo
        DistinguishedNameQualifier = ... # type: QSslCertificate.SubjectInfo
        SerialNumber = ... # type: QSslCertificate.SubjectInfo
        EmailAddress = ... # type: QSslCertificate.SubjectInfo

    @typing.overload
    def __init__(self, device: typing.Optional[QtCore.QIODevice], format: QSsl.EncodingFormat = ...) -> None: ...
    @typing.overload
    def __init__(self, data: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview] = ..., format: QSsl.EncodingFormat = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QSslCertificate') -> None: ...

    def subjectDisplayName(self) -> str: ...
    def issuerDisplayName(self) -> str: ...
    @staticmethod
    def importPkcs12(device: typing.Optional[QtCore.QIODevice], key: typing.Optional['QSslKey'], certificate: typing.Optional['QSslCertificate'], caCertificates: typing.Optional[typing.Iterable['QSslCertificate']] = ..., passPhrase: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview] = ...) -> bool: ...
    def __hash__(self) -> int: ...
    def isSelfSigned(self) -> bool: ...
    @staticmethod
    def verify(certificateChain: typing.Iterable['QSslCertificate'], hostName: typing.Optional[str] = ...) -> typing.List['QSslError']: ...
    def toText(self) -> str: ...
    def extensions(self) -> typing.List['QSslCertificateExtension']: ...
    def issuerInfoAttributes(self) -> typing.List[QtCore.QByteArray]: ...
    def subjectInfoAttributes(self) -> typing.List[QtCore.QByteArray]: ...
    def isBlacklisted(self) -> bool: ...
    def swap(self, other: 'QSslCertificate') -> None: ...
    def handle(self) -> typing.Optional[PyQt6.sip.voidptr]: ...
    @staticmethod
    def fromData(data: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], format: QSsl.EncodingFormat = ...) -> typing.List['QSslCertificate']: ...
    @staticmethod
    def fromDevice(device: typing.Optional[QtCore.QIODevice], format: QSsl.EncodingFormat = ...) -> typing.List['QSslCertificate']: ...
    @staticmethod
    def fromPath(path: typing.Optional[str], format: QSsl.EncodingFormat = ..., syntax: 'QSslCertificate.PatternSyntax' = ...) -> typing.List['QSslCertificate']: ...
    def toDer(self) -> QtCore.QByteArray: ...
    def toPem(self) -> QtCore.QByteArray: ...
    def publicKey(self) -> 'QSslKey': ...
    def expiryDate(self) -> QtCore.QDateTime: ...
    def effectiveDate(self) -> QtCore.QDateTime: ...
    def subjectAlternativeNames(self) -> typing.Dict[QSsl.AlternativeNameEntryType, typing.List[str]]: ...
    @typing.overload
    def subjectInfo(self, info: 'QSslCertificate.SubjectInfo') -> typing.List[str]: ...
    @typing.overload
    def subjectInfo(self, attribute: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> typing.List[str]: ...
    @typing.overload
    def issuerInfo(self, info: 'QSslCertificate.SubjectInfo') -> typing.List[str]: ...
    @typing.overload
    def issuerInfo(self, attribute: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> typing.List[str]: ...
    def digest(self, algorithm: QtCore.QCryptographicHash.Algorithm = ...) -> QtCore.QByteArray: ...
    def serialNumber(self) -> QtCore.QByteArray: ...
    def version(self) -> QtCore.QByteArray: ...
    def clear(self) -> None: ...
    def isNull(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QSslCertificateExtension(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QSslCertificateExtension') -> None: ...

    def isSupported(self) -> bool: ...
    def isCritical(self) -> bool: ...
    def value(self) -> typing.Any: ...
    def name(self) -> str: ...
    def oid(self) -> str: ...
    def swap(self, other: 'QSslCertificateExtension') -> None: ...


class QSslCipher(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, name: typing.Optional[str]) -> None: ...
    @typing.overload
    def __init__(self, name: typing.Optional[str], protocol: QSsl.SslProtocol) -> None: ...
    @typing.overload
    def __init__(self, other: 'QSslCipher') -> None: ...

    def swap(self, other: 'QSslCipher') -> None: ...
    def protocol(self) -> QSsl.SslProtocol: ...
    def protocolString(self) -> str: ...
    def encryptionMethod(self) -> str: ...
    def authenticationMethod(self) -> str: ...
    def keyExchangeMethod(self) -> str: ...
    def usedBits(self) -> int: ...
    def supportedBits(self) -> int: ...
    def name(self) -> str: ...
    def isNull(self) -> bool: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...


class QSslConfiguration(PyQt6.sip.simplewrapper):

    class NextProtocolNegotiationStatus(enum.Enum):
        NextProtocolNegotiationNone = ... # type: QSslConfiguration.NextProtocolNegotiationStatus
        NextProtocolNegotiationNegotiated = ... # type: QSslConfiguration.NextProtocolNegotiationStatus
        NextProtocolNegotiationUnsupported = ... # type: QSslConfiguration.NextProtocolNegotiationStatus

    NextProtocolHttp1_1 = ... # type: bytes

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QSslConfiguration') -> None: ...

    @staticmethod
    def setDefaultDtlsConfiguration(configuration: 'QSslConfiguration') -> None: ...
    @staticmethod
    def defaultDtlsConfiguration() -> 'QSslConfiguration': ...
    def setDtlsCookieVerificationEnabled(self, enable: bool) -> None: ...
    def dtlsCookieVerificationEnabled(self) -> bool: ...
    def setMissingCertificateIsFatal(self, cannotRecover: bool) -> None: ...
    def missingCertificateIsFatal(self) -> bool: ...
    def setHandshakeMustInterruptOnError(self, interrupt: bool) -> None: ...
    def handshakeMustInterruptOnError(self) -> bool: ...
    @typing.overload
    def addCaCertificates(self, path: typing.Optional[str], format: QSsl.EncodingFormat = ..., syntax: QSslCertificate.PatternSyntax = ...) -> bool: ...
    @typing.overload
    def addCaCertificates(self, certificates: typing.Iterable[QSslCertificate]) -> None: ...
    def addCaCertificate(self, certificate: QSslCertificate) -> None: ...
    def ocspStaplingEnabled(self) -> bool: ...
    def setOcspStaplingEnabled(self, enable: bool) -> None: ...
    def setBackendConfiguration(self, backendConfiguration: typing.Dict[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], typing.Any] = ...) -> None: ...
    def setBackendConfigurationOption(self, name: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], value: typing.Any) -> None: ...
    def backendConfiguration(self) -> typing.Dict[QtCore.QByteArray, typing.Any]: ...
    def setDiffieHellmanParameters(self, dhparams: 'QSslDiffieHellmanParameters') -> None: ...
    def diffieHellmanParameters(self) -> 'QSslDiffieHellmanParameters': ...
    def setPreSharedKeyIdentityHint(self, hint: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    def preSharedKeyIdentityHint(self) -> QtCore.QByteArray: ...
    def ephemeralServerKey(self) -> 'QSslKey': ...
    @staticmethod
    def supportedEllipticCurves() -> typing.List['QSslEllipticCurve']: ...
    def setEllipticCurves(self, curves: typing.Iterable['QSslEllipticCurve']) -> None: ...
    def ellipticCurves(self) -> typing.List['QSslEllipticCurve']: ...
    @staticmethod
    def systemCaCertificates() -> typing.List[QSslCertificate]: ...
    @staticmethod
    def supportedCiphers() -> typing.List[QSslCipher]: ...
    def sessionProtocol(self) -> QSsl.SslProtocol: ...
    def nextProtocolNegotiationStatus(self) -> 'QSslConfiguration.NextProtocolNegotiationStatus': ...
    def nextNegotiatedProtocol(self) -> QtCore.QByteArray: ...
    def allowedNextProtocols(self) -> typing.List[QtCore.QByteArray]: ...
    def setAllowedNextProtocols(self, protocols: typing.Iterable[typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]]) -> None: ...
    def sessionTicketLifeTimeHint(self) -> int: ...
    def setSessionTicket(self, sessionTicket: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    def sessionTicket(self) -> QtCore.QByteArray: ...
    def setLocalCertificateChain(self, localChain: typing.Iterable[QSslCertificate]) -> None: ...
    def localCertificateChain(self) -> typing.List[QSslCertificate]: ...
    def swap(self, other: 'QSslConfiguration') -> None: ...
    def testSslOption(self, option: QSsl.SslOption) -> bool: ...
    def setSslOption(self, option: QSsl.SslOption, on: bool) -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    @staticmethod
    def setDefaultConfiguration(configuration: 'QSslConfiguration') -> None: ...
    @staticmethod
    def defaultConfiguration() -> 'QSslConfiguration': ...
    def setCaCertificates(self, certificates: typing.Iterable[QSslCertificate]) -> None: ...
    def caCertificates(self) -> typing.List[QSslCertificate]: ...
    @typing.overload
    def setCiphers(self, ciphers: typing.Optional[str]) -> None: ...
    @typing.overload
    def setCiphers(self, ciphers: typing.Iterable[QSslCipher]) -> None: ...
    def ciphers(self) -> typing.List[QSslCipher]: ...
    def setPrivateKey(self, key: 'QSslKey') -> None: ...
    def privateKey(self) -> 'QSslKey': ...
    def sessionCipher(self) -> QSslCipher: ...
    def peerCertificateChain(self) -> typing.List[QSslCertificate]: ...
    def peerCertificate(self) -> QSslCertificate: ...
    def setLocalCertificate(self, certificate: QSslCertificate) -> None: ...
    def localCertificate(self) -> QSslCertificate: ...
    def setPeerVerifyDepth(self, depth: int) -> None: ...
    def peerVerifyDepth(self) -> int: ...
    def setPeerVerifyMode(self, mode: 'QSslSocket.PeerVerifyMode') -> None: ...
    def peerVerifyMode(self) -> 'QSslSocket.PeerVerifyMode': ...
    def setProtocol(self, protocol: QSsl.SslProtocol) -> None: ...
    def protocol(self) -> QSsl.SslProtocol: ...
    def isNull(self) -> bool: ...


class QSslDiffieHellmanParameters(PyQt6.sip.simplewrapper):

    class Error(enum.Enum):
        NoError = ... # type: QSslDiffieHellmanParameters.Error
        InvalidInputDataError = ... # type: QSslDiffieHellmanParameters.Error
        UnsafeParametersError = ... # type: QSslDiffieHellmanParameters.Error

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, other: 'QSslDiffieHellmanParameters') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __hash__(self) -> int: ...
    def errorString(self) -> str: ...
    def error(self) -> 'QSslDiffieHellmanParameters.Error': ...
    def isValid(self) -> bool: ...
    def isEmpty(self) -> bool: ...
    @typing.overload
    @staticmethod
    def fromEncoded(encoded: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], encoding: QSsl.EncodingFormat = ...) -> 'QSslDiffieHellmanParameters': ...
    @typing.overload
    @staticmethod
    def fromEncoded(device: typing.Optional[QtCore.QIODevice], encoding: QSsl.EncodingFormat = ...) -> 'QSslDiffieHellmanParameters': ...
    @staticmethod
    def defaultParameters() -> 'QSslDiffieHellmanParameters': ...
    def swap(self, other: 'QSslDiffieHellmanParameters') -> None: ...


class QSslEllipticCurve(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, a0: 'QSslEllipticCurve') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def __hash__(self) -> int: ...
    def isTlsNamedCurve(self) -> bool: ...
    def isValid(self) -> bool: ...
    def longName(self) -> str: ...
    def shortName(self) -> str: ...
    @staticmethod
    def fromLongName(name: typing.Optional[str]) -> 'QSslEllipticCurve': ...
    @staticmethod
    def fromShortName(name: typing.Optional[str]) -> 'QSslEllipticCurve': ...


class QSslError(PyQt6.sip.simplewrapper):

    class SslError(enum.Enum):
        UnspecifiedError = ... # type: QSslError.SslError
        NoError = ... # type: QSslError.SslError
        UnableToGetIssuerCertificate = ... # type: QSslError.SslError
        UnableToDecryptCertificateSignature = ... # type: QSslError.SslError
        UnableToDecodeIssuerPublicKey = ... # type: QSslError.SslError
        CertificateSignatureFailed = ... # type: QSslError.SslError
        CertificateNotYetValid = ... # type: QSslError.SslError
        CertificateExpired = ... # type: QSslError.SslError
        InvalidNotBeforeField = ... # type: QSslError.SslError
        InvalidNotAfterField = ... # type: QSslError.SslError
        SelfSignedCertificate = ... # type: QSslError.SslError
        SelfSignedCertificateInChain = ... # type: QSslError.SslError
        UnableToGetLocalIssuerCertificate = ... # type: QSslError.SslError
        UnableToVerifyFirstCertificate = ... # type: QSslError.SslError
        CertificateRevoked = ... # type: QSslError.SslError
        InvalidCaCertificate = ... # type: QSslError.SslError
        PathLengthExceeded = ... # type: QSslError.SslError
        InvalidPurpose = ... # type: QSslError.SslError
        CertificateUntrusted = ... # type: QSslError.SslError
        CertificateRejected = ... # type: QSslError.SslError
        SubjectIssuerMismatch = ... # type: QSslError.SslError
        AuthorityIssuerSerialNumberMismatch = ... # type: QSslError.SslError
        NoPeerCertificate = ... # type: QSslError.SslError
        HostNameMismatch = ... # type: QSslError.SslError
        NoSslSupport = ... # type: QSslError.SslError
        CertificateBlacklisted = ... # type: QSslError.SslError
        CertificateStatusUnknown = ... # type: QSslError.SslError
        OcspNoResponseFound = ... # type: QSslError.SslError
        OcspMalformedRequest = ... # type: QSslError.SslError
        OcspMalformedResponse = ... # type: QSslError.SslError
        OcspInternalError = ... # type: QSslError.SslError
        OcspTryLater = ... # type: QSslError.SslError
        OcspSigRequred = ... # type: QSslError.SslError
        OcspUnauthorized = ... # type: QSslError.SslError
        OcspResponseCannotBeTrusted = ... # type: QSslError.SslError
        OcspResponseCertIdUnknown = ... # type: QSslError.SslError
        OcspResponseExpired = ... # type: QSslError.SslError
        OcspStatusUnknown = ... # type: QSslError.SslError

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, error: 'QSslError.SslError') -> None: ...
    @typing.overload
    def __init__(self, error: 'QSslError.SslError', certificate: QSslCertificate) -> None: ...
    @typing.overload
    def __init__(self, other: 'QSslError') -> None: ...

    def __hash__(self) -> int: ...
    def swap(self, other: 'QSslError') -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def certificate(self) -> QSslCertificate: ...
    def errorString(self) -> str: ...
    def error(self) -> 'QSslError.SslError': ...


class QSslKey(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, encoded: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview], algorithm: QSsl.KeyAlgorithm, encoding: QSsl.EncodingFormat = ..., type: QSsl.KeyType = ..., passPhrase: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview] = ...) -> None: ...
    @typing.overload
    def __init__(self, device: typing.Optional[QtCore.QIODevice], algorithm: QSsl.KeyAlgorithm, encoding: QSsl.EncodingFormat = ..., type: QSsl.KeyType = ..., passPhrase: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview] = ...) -> None: ...
    @typing.overload
    def __init__(self, handle: typing.Optional[PyQt6.sip.voidptr], type: QSsl.KeyType = ...) -> None: ...
    @typing.overload
    def __init__(self, other: 'QSslKey') -> None: ...

    def swap(self, other: 'QSslKey') -> None: ...
    def __ne__(self, other: object): ...
    def __eq__(self, other: object): ...
    def handle(self) -> typing.Optional[PyQt6.sip.voidptr]: ...
    def toDer(self, passPhrase: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview] = ...) -> QtCore.QByteArray: ...
    def toPem(self, passPhrase: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview] = ...) -> QtCore.QByteArray: ...
    def algorithm(self) -> QSsl.KeyAlgorithm: ...
    def type(self) -> QSsl.KeyType: ...
    def length(self) -> int: ...
    def clear(self) -> None: ...
    def isNull(self) -> bool: ...


class QSslPreSharedKeyAuthenticator(PyQt6.sip.simplewrapper):

    @typing.overload
    def __init__(self) -> None: ...
    @typing.overload
    def __init__(self, authenticator: 'QSslPreSharedKeyAuthenticator') -> None: ...

    def __eq__(self, other: object): ...
    def __ne__(self, other: object): ...
    def maximumPreSharedKeyLength(self) -> int: ...
    def preSharedKey(self) -> QtCore.QByteArray: ...
    def setPreSharedKey(self, preSharedKey: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    def maximumIdentityLength(self) -> int: ...
    def identity(self) -> QtCore.QByteArray: ...
    def setIdentity(self, identity: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview]) -> None: ...
    def identityHint(self) -> QtCore.QByteArray: ...
    def swap(self, authenticator: 'QSslPreSharedKeyAuthenticator') -> None: ...


class QTcpServer(QtCore.QObject):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    pendingConnectionAvailable: typing.ClassVar[QtCore.pyqtSignal]
    def listenBacklogSize(self) -> int: ...
    def setListenBacklogSize(self, size: int) -> None: ...
    acceptError: typing.ClassVar[QtCore.pyqtSignal]
    newConnection: typing.ClassVar[QtCore.pyqtSignal]
    def addPendingConnection(self, socket: typing.Optional['QTcpSocket']) -> None: ...
    def incomingConnection(self, handle: PyQt6.sip.voidptr) -> None: ...
    def resumeAccepting(self) -> None: ...
    def pauseAccepting(self) -> None: ...
    def proxy(self) -> QNetworkProxy: ...
    def setProxy(self, networkProxy: QNetworkProxy) -> None: ...
    def errorString(self) -> str: ...
    def serverError(self) -> QAbstractSocket.SocketError: ...
    def nextPendingConnection(self) -> typing.Optional['QTcpSocket']: ...
    def hasPendingConnections(self) -> bool: ...
    def waitForNewConnection(self, msecs: int = ...) -> typing.Tuple[bool, typing.Optional[bool]]: ...
    def setSocketDescriptor(self, socketDescriptor: PyQt6.sip.voidptr) -> bool: ...
    def socketDescriptor(self) -> PyQt6.sip.voidptr: ...
    def serverAddress(self) -> QHostAddress: ...
    def serverPort(self) -> int: ...
    def maxPendingConnections(self) -> int: ...
    def setMaxPendingConnections(self, numConnections: int) -> None: ...
    def isListening(self) -> bool: ...
    def close(self) -> None: ...
    def listen(self, address: typing.Union[QHostAddress, QHostAddress.SpecialAddress] = ..., port: int = ...) -> bool: ...


class QSslServer(QTcpServer):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def incomingConnection(self, socket: PyQt6.sip.voidptr) -> None: ...
    startedEncryptionHandshake: typing.ClassVar[QtCore.pyqtSignal]
    handshakeInterruptedOnError: typing.ClassVar[QtCore.pyqtSignal]
    alertReceived: typing.ClassVar[QtCore.pyqtSignal]
    alertSent: typing.ClassVar[QtCore.pyqtSignal]
    preSharedKeyAuthenticationRequired: typing.ClassVar[QtCore.pyqtSignal]
    errorOccurred: typing.ClassVar[QtCore.pyqtSignal]
    peerVerifyError: typing.ClassVar[QtCore.pyqtSignal]
    sslErrors: typing.ClassVar[QtCore.pyqtSignal]
    def handshakeTimeout(self) -> int: ...
    def setHandshakeTimeout(self, timeout: int) -> None: ...
    def sslConfiguration(self) -> QSslConfiguration: ...
    def setSslConfiguration(self, sslConfiguration: QSslConfiguration) -> None: ...


class QTcpSocket(QAbstractSocket):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...


class QSslSocket(QTcpSocket):

    class PeerVerifyMode(enum.Enum):
        VerifyNone = ... # type: QSslSocket.PeerVerifyMode
        QueryPeer = ... # type: QSslSocket.PeerVerifyMode
        VerifyPeer = ... # type: QSslSocket.PeerVerifyMode
        AutoVerifyPeer = ... # type: QSslSocket.PeerVerifyMode

    class SslMode(enum.Enum):
        UnencryptedMode = ... # type: QSslSocket.SslMode
        SslClientMode = ... # type: QSslSocket.SslMode
        SslServerMode = ... # type: QSslSocket.SslMode

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    @staticmethod
    def isFeatureSupported(feat: QSsl.SupportedFeature, backendName: typing.Optional[str] = ...) -> bool: ...
    @staticmethod
    def supportedFeatures(backendName: typing.Optional[str] = ...) -> typing.List[QSsl.SupportedFeature]: ...
    @staticmethod
    def isClassImplemented(cl: QSsl.ImplementedClass, backendName: typing.Optional[str] = ...) -> bool: ...
    @staticmethod
    def implementedClasses(backendName: typing.Optional[str] = ...) -> typing.List[QSsl.ImplementedClass]: ...
    @staticmethod
    def isProtocolSupported(protocol: QSsl.SslProtocol, backendName: typing.Optional[str] = ...) -> bool: ...
    @staticmethod
    def supportedProtocols(backendName: typing.Optional[str] = ...) -> typing.List[QSsl.SslProtocol]: ...
    @staticmethod
    def setActiveBackend(backendName: typing.Optional[str]) -> bool: ...
    @staticmethod
    def activeBackend() -> str: ...
    @staticmethod
    def availableBackends() -> typing.List[str]: ...
    handshakeInterruptedOnError: typing.ClassVar[QtCore.pyqtSignal]
    alertReceived: typing.ClassVar[QtCore.pyqtSignal]
    alertSent: typing.ClassVar[QtCore.pyqtSignal]
    def continueInterruptedHandshake(self) -> None: ...
    def sslHandshakeErrors(self) -> typing.List[QSslError]: ...
    def ocspResponses(self) -> typing.List[QOcspResponse]: ...
    @staticmethod
    def sslLibraryBuildVersionString() -> str: ...
    @staticmethod
    def sslLibraryBuildVersionNumber() -> int: ...
    def sessionProtocol(self) -> QSsl.SslProtocol: ...
    def localCertificateChain(self) -> typing.List[QSslCertificate]: ...
    def setLocalCertificateChain(self, localChain: typing.Iterable[QSslCertificate]) -> None: ...
    @staticmethod
    def sslLibraryVersionString() -> str: ...
    @staticmethod
    def sslLibraryVersionNumber() -> int: ...
    def disconnectFromHost(self) -> None: ...
    def connectToHost(self, hostName: typing.Optional[str], port: int, mode: QtCore.QIODeviceBase.OpenModeFlag = ..., protocol: QAbstractSocket.NetworkLayerProtocol = ...) -> None: ...
    def resume(self) -> None: ...
    def setPeerVerifyName(self, hostName: typing.Optional[str]) -> None: ...
    def peerVerifyName(self) -> str: ...
    def socketOption(self, option: QAbstractSocket.SocketOption) -> typing.Any: ...
    def setSocketOption(self, option: QAbstractSocket.SocketOption, value: typing.Any) -> None: ...
    newSessionTicketReceived: typing.ClassVar[QtCore.pyqtSignal]
    encryptedBytesWritten: typing.ClassVar[QtCore.pyqtSignal]
    peerVerifyError: typing.ClassVar[QtCore.pyqtSignal]
    def setSslConfiguration(self, config: QSslConfiguration) -> None: ...
    def sslConfiguration(self) -> QSslConfiguration: ...
    def encryptedBytesToWrite(self) -> int: ...
    def encryptedBytesAvailable(self) -> int: ...
    def setReadBufferSize(self, size: int) -> None: ...
    def setPeerVerifyDepth(self, depth: int) -> None: ...
    def peerVerifyDepth(self) -> int: ...
    def setPeerVerifyMode(self, mode: 'QSslSocket.PeerVerifyMode') -> None: ...
    def peerVerifyMode(self) -> 'QSslSocket.PeerVerifyMode': ...
    def skipData(self, maxSize: int) -> int: ...
    def writeData(self, a0: PyQt6.sip.Buffer) -> int: ...
    def readData(self, maxlen: int) -> bytes: ...
    preSharedKeyAuthenticationRequired: typing.ClassVar[QtCore.pyqtSignal]
    modeChanged: typing.ClassVar[QtCore.pyqtSignal]
    sslErrors: typing.ClassVar[QtCore.pyqtSignal]
    encrypted: typing.ClassVar[QtCore.pyqtSignal]
    @typing.overload
    def ignoreSslErrors(self) -> None: ...
    @typing.overload
    def ignoreSslErrors(self, errors: typing.Iterable[QSslError]) -> None: ...
    def startServerEncryption(self) -> None: ...
    def startClientEncryption(self) -> None: ...
    @staticmethod
    def supportsSsl() -> bool: ...
    def waitForDisconnected(self, msecs: int = ...) -> bool: ...
    def waitForBytesWritten(self, msecs: int = ...) -> bool: ...
    def waitForReadyRead(self, msecs: int = ...) -> bool: ...
    def waitForEncrypted(self, msecs: int = ...) -> bool: ...
    def waitForConnected(self, msecs: int = ...) -> bool: ...
    def privateKey(self) -> QSslKey: ...
    @typing.overload
    def setPrivateKey(self, key: QSslKey) -> None: ...
    @typing.overload
    def setPrivateKey(self, fileName: typing.Optional[str], algorithm: QSsl.KeyAlgorithm = ..., format: QSsl.EncodingFormat = ..., passPhrase: typing.Union[QtCore.QByteArray, bytes, bytearray, memoryview] = ...) -> None: ...
    def sessionCipher(self) -> QSslCipher: ...
    def peerCertificateChain(self) -> typing.List[QSslCertificate]: ...
    def peerCertificate(self) -> QSslCertificate: ...
    def localCertificate(self) -> QSslCertificate: ...
    @typing.overload
    def setLocalCertificate(self, certificate: QSslCertificate) -> None: ...
    @typing.overload
    def setLocalCertificate(self, path: typing.Optional[str], format: QSsl.EncodingFormat = ...) -> None: ...
    def atEnd(self) -> bool: ...
    def close(self) -> None: ...
    def canReadLine(self) -> bool: ...
    def bytesToWrite(self) -> int: ...
    def bytesAvailable(self) -> int: ...
    def setProtocol(self, protocol: QSsl.SslProtocol) -> None: ...
    def protocol(self) -> QSsl.SslProtocol: ...
    def isEncrypted(self) -> bool: ...
    def mode(self) -> 'QSslSocket.SslMode': ...
    def setSocketDescriptor(self, socketDescriptor: PyQt6.sip.voidptr, state: QAbstractSocket.SocketState = ..., mode: QtCore.QIODeviceBase.OpenModeFlag = ...) -> bool: ...
    @typing.overload
    def connectToHostEncrypted(self, hostName: typing.Optional[str], port: int, mode: QtCore.QIODeviceBase.OpenModeFlag = ..., protocol: QAbstractSocket.NetworkLayerProtocol = ...) -> None: ...
    @typing.overload
    def connectToHostEncrypted(self, hostName: typing.Optional[str], port: int, sslPeerName: typing.Optional[str], mode: QtCore.QIODeviceBase.OpenModeFlag = ..., protocol: QAbstractSocket.NetworkLayerProtocol = ...) -> None: ...


class QUdpSocket(QAbstractSocket):

    def __init__(self, parent: typing.Optional[QtCore.QObject] = ...) -> None: ...

    def setMulticastInterface(self, iface: QNetworkInterface) -> None: ...
    def multicastInterface(self) -> QNetworkInterface: ...
    @typing.overload
    def leaveMulticastGroup(self, groupAddress: typing.Union[QHostAddress, QHostAddress.SpecialAddress]) -> bool: ...
    @typing.overload
    def leaveMulticastGroup(self, groupAddress: typing.Union[QHostAddress, QHostAddress.SpecialAddress], iface: QNetworkInterface) -> bool: ...
    @typing.overload
    def joinMulticastGroup(self, groupAddress: typing.Union[QHostAddress, QHostAddress.SpecialAddress]) -> bool: ...
    @typing.overload
    def joinMulticastGroup(self, groupAddress: typing.Union[QHostAddress, QHostAddress.SpecialAddress], iface: QNetworkInterface) -> bool: ...
    @typing.overload
    def writeDatagram(self, a0: PyQt6.sip.Buffer, a1: typing.Union[QHostAddress, QHostAddress.SpecialAddress], a2: int) -> int: ...
    @typing.overload
    def writeDatagram(self, datagram: QNetworkDatagram) -> int: ...
    def receiveDatagram(self, maxSize: int = ...) -> QNetworkDatagram: ...
    def readDatagram(self, maxlen: int) -> typing.Tuple[bytes, typing.Optional[QHostAddress], typing.Optional[int]]: ...
    def pendingDatagramSize(self) -> int: ...
    def hasPendingDatagrams(self) -> bool: ...
