// qinputmethod.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QInputMethod : public QObject
{
%TypeHeaderCode
#include <qinputmethod.h>
%End

public:
    QTransform inputItemTransform() const;
    void setInputItemTransform(const QTransform &transform);
    QRectF cursorRectangle() const;
    QRectF keyboardRectangle() const;

    enum Action
    {
        Click,
        ContextMenu,
    };

    bool isVisible() const;
    void setVisible(bool visible);
    bool isAnimating() const;
    QLocale locale() const;
    Qt::LayoutDirection inputDirection() const;
    QRectF inputItemRectangle() const;
    void setInputItemRectangle(const QRectF &rect);
    static QVariant queryFocusObject(Qt::InputMethodQuery query, const QVariant &argument);

public slots:
    void show();
    void hide();
    void update(Qt::InputMethodQueries queries);
    void reset();
    void commit();
    void invokeAction(QInputMethod::Action a, int cursorPosition);

signals:
    void cursorRectangleChanged();
    void keyboardRectangleChanged();
    void visibleChanged();
    void animatingChanged();
    void localeChanged();
    void inputDirectionChanged(Qt::LayoutDirection newDirection);

public:
    QRectF anchorRectangle() const;
    QRectF inputItemClipRectangle() const;

signals:
    void anchorRectangleChanged();
    void inputItemClipRectangleChanged();

private:
    QInputMethod();
    virtual ~QInputMethod();
};
