# -*- coding: utf-8 -*-

import os
import sys
import json
import importlib.util

class CythonModuleLoader:
    def __init__(self):
        self.loaded_modules = {}
        self.mapping = {}
        self.load_mapping()
    def get_resource_path(self, relative_path):
        if hasattr(sys, '_MEIPASS'):
            base_path = sys._MEIPASS
        else:
            base_path = os.path.abspath(".")
        return os.path.join(base_path, relative_path)
    
    def load_mapping(self):

        mapping_file = self.get_resource_path("cython_mapping.json")
        if os.path.exists(mapping_file):
            try:
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.mapping = data.get("modules", {})
                    print(f": {len(self.mapping)} ")
            except Exception as e:
                print(f": {e}")
    
    def load_cython_module(self, module_name):
        if module_name in self.loaded_modules:
            return self.loaded_modules[module_name]
        
        compiled_name = self.mapping.get(module_name)
        if not compiled_name:
            return None
        
        compiled_path = self.get_resource_path(compiled_name)
        if not os.path.exists(compiled_path):
            return None
        
        try:
            spec = importlib.util.spec_from_file_location(
                module_name.replace('.py', ''), 
                compiled_path
            )
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                sys.modules[module_name.replace('.py', '')] = module
                spec.loader.exec_module(module)
                self.loaded_modules[module_name] = module
                print(f": {module_name}")
                return module
        except Exception as e:
            print(f" {module_name}: {e}")
        
        return None
    
    def load_all_modules(self):
        success_count = 0
        for module_name in self.mapping.keys():
            if self.load_cython_module(module_name):
                success_count += 1
        
        print(f" {success_count}/{len(self.mapping)} ")
        return success_count == len(self.mapping)

# 全局加载器实例
_cython_loader = CythonModuleLoader()

def get_cython_module(module_name):

    return _cython_loader.load_cython_module(module_name)

def load_all_cython_modules():

    return _cython_loader.load_all_modules()


if __name__ != "__main__":
    load_all_cython_modules()
