#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ADBTools文件夹是否被正确打包到exe中
"""

import os
import sys

def test_adbtools_files():
    """测试ADBTools文件夹中的文件是否存在"""
    print("🔍 测试ADBTools文件夹打包情况...")
    
    # 获取程序运行目录
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe
        base_dir = sys._MEIPASS
        print(f"✓ 运行在打包环境中，临时目录: {base_dir}")
    else:
        # 如果是源码运行
        base_dir = os.path.dirname(os.path.abspath(__file__))
        print(f"✓ 运行在开发环境中，当前目录: {base_dir}")
    
    # 检查ADBTools文件夹
    adbtools_dir = os.path.join(base_dir, "ADBTools")
    print(f"🔍 检查ADBTools路径: {adbtools_dir}")
    
    if os.path.exists(adbtools_dir):
        print("✅ ADBTools文件夹存在！")
        files = os.listdir(adbtools_dir)
        print(f"📁 包含 {len(files)} 个文件:")
        
        for file in files:
            file_path = os.path.join(adbtools_dir, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                print(f"  ✅ {file} ({size:,} 字节)")
            else:
                print(f"  📁 {file} (目录)")
        
        # 特别检查关键工具
        key_tools = ["adb.exe", "fastboot.exe", "pay.exe"]
        print("\n🔧 检查关键工具:")
        for tool in key_tools:
            tool_path = os.path.join(adbtools_dir, tool)
            if os.path.exists(tool_path):
                size = os.path.getsize(tool_path)
                print(f"  ✅ {tool} 存在 ({size:,} 字节)")
            else:
                print(f"  ❌ {tool} 不存在")
        
        return True
    else:
        print("❌ ADBTools文件夹不存在！")
        print(f"📁 当前目录内容: {os.listdir(base_dir) if os.path.exists(base_dir) else '目录不存在'}")
        return False

def test_other_resources():
    """测试其他资源文件"""
    print("\n🔍 测试其他资源文件...")
    
    if getattr(sys, 'frozen', False):
        base_dir = sys._MEIPASS
    else:
        base_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 检查ico文件夹
    ico_dir = os.path.join(base_dir, "ico")
    if os.path.exists(ico_dir):
        print("✅ ico文件夹存在")
        files = os.listdir(ico_dir)
        for file in files:
            print(f"  ✅ ico/{file}")
    else:
        print("❌ ico文件夹不存在")
    
    # 检查tup文件夹
    tup_dir = os.path.join(base_dir, "tup")
    if os.path.exists(tup_dir):
        print("✅ tup文件夹存在")
        files = os.listdir(tup_dir)
        for file in files:
            print(f"  ✅ tup/{file}")
    else:
        print("❌ tup文件夹不存在")

if __name__ == "__main__":
    print("🧪 ADBTools打包测试工具")
    print("=" * 50)
    
    adb_ok = test_adbtools_files()
    test_other_resources()
    
    print("\n" + "=" * 50)
    if adb_ok:
        print("🎉 测试通过！ADBTools已正确打包")
    else:
        print("❌ 测试失败！ADBTools未正确打包")
