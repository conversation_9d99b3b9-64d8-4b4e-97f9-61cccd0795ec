@echo off
chcp 65001 >nul
title 三合一加密打包工具

echo.
echo ================================================================
echo                   三合一加密打包工具 v2.0.0
echo ================================================================
echo.
echo 分层保护方案：
echo   外层：Nuitka编译 + VMProtect加壳（自签名证书）
echo   中层：PyArmor混淆非核心代码（免费版）
echo   内层：Cython编译核心算法
echo.
echo 包含所有模块和依赖，特别是ADBTools整个文件夹
echo.
echo ================================================================
echo.

:MENU
echo 请选择操作：
echo.
echo [1] 完整构建（推荐）
echo [2] 仅Cython编译
echo [3] 仅PyArmor混淆
echo [4] 仅Nuitka编译
echo [5] 清理构建目录
echo [6] 查看构建日志
echo [7] 打开输出目录
echo [0] 退出
echo.
set /p choice=请输入选择 (0-7): 

if "%choice%"=="1" goto FULL_BUILD
if "%choice%"=="2" goto CYTHON_ONLY
if "%choice%"=="3" goto PYARMOR_ONLY
if "%choice%"=="4" goto NUITKA_ONLY
if "%choice%"=="5" goto CLEAN
if "%choice%"=="6" goto VIEW_LOG
if "%choice%"=="7" goto OPEN_OUTPUT
if "%choice%"=="0" goto EXIT

echo 无效选择，请重新输入。
echo.
goto MENU

:FULL_BUILD
echo.
echo 开始完整构建...
echo ================================================================
python advanced_build.py
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 构建成功完成！
    echo 📁 输出文件位于: advanced_build\final_output\
    echo.
    set /p open_output=是否打开输出目录？(y/n): 
    if /i "!open_output!"=="y" explorer "advanced_build\final_output"
) else (
    echo.
    echo ❌ 构建失败！请检查错误信息。
)
echo.
pause
goto MENU

:CYTHON_ONLY
echo.
echo 开始Cython编译...
echo ================================================================
python advanced_build.py --cython-only
if %ERRORLEVEL% EQU 0 (
    echo ✅ Cython编译完成！
) else (
    echo ❌ Cython编译失败！
)
echo.
pause
goto MENU

:PYARMOR_ONLY
echo.
echo 开始PyArmor混淆...
echo ================================================================
python advanced_build.py --pyarmor-only
if %ERRORLEVEL% EQU 0 (
    echo ✅ PyArmor混淆完成！
) else (
    echo ❌ PyArmor混淆失败！
)
echo.
pause
goto MENU

:NUITKA_ONLY
echo.
echo 开始Nuitka编译...
echo ================================================================
python advanced_build.py --nuitka-only
if %ERRORLEVEL% EQU 0 (
    echo ✅ Nuitka编译完成！
) else (
    echo ❌ Nuitka编译失败！
)
echo.
pause
goto MENU

:CLEAN
echo.
echo 清理构建目录...
echo ================================================================
python advanced_build.py --clean
echo ✅ 清理完成！
echo.
pause
goto MENU

:VIEW_LOG
echo.
if exist "advanced_build\build.log" (
    echo 打开构建日志...
    notepad "advanced_build\build.log"
) else (
    echo ❌ 构建日志不存在，请先执行构建。
    echo.
    pause
)
goto MENU

:OPEN_OUTPUT
echo.
if exist "advanced_build\final_output" (
    echo 打开输出目录...
    explorer "advanced_build\final_output"
) else (
    echo ❌ 输出目录不存在，请先执行构建。
    echo.
    pause
)
goto MENU

:EXIT
echo.
echo 感谢使用三合一加密打包工具！
echo.
pause
exit /b 0
