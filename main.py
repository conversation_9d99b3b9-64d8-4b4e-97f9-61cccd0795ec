# -*- coding: utf-8 -*-
"""
益民固件刷写工具 - 主程序入口
支持ColorOS设备的固件刷写和解锁功能
"""
import sys
import os
import subprocess
import atexit
import shutil
import time
import psutil
import tempfile
import ctypes
import win32security
import ntsecuritycon as con
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtGui import QPixmap, QIcon
from flash_tool import FlashToolUI
import glob


def is_admin():
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    try:
        if sys.argv[-1] != 'asadmin':
            script = os.path.abspath(sys.argv[0])
            params = ' '.join([script] + sys.argv[1:] + ['asadmin'])
            ctypes.windll.shell32.ShellExecuteW(None, "runas", sys.executable, params, None, 1)
            sys.exit()
    except Exception as e:
        print(f"失败: {str(e)}")
        sys.exit(1)

def set_directory_permissions(path):
    try:
        # 获取当前用户的SID
        username = os.environ.get('USERNAME')
        if not username:
            raise Exception("无法获取当前用户名")
            
        # 获取目录的安全描述符
        sd = win32security.GetFileSecurity(path, win32security.DACL_SECURITY_INFORMATION)
        dacl = sd.GetSecurityDescriptorDacl()
        
        # 获取当前用户的SID
        user_sid = win32security.LookupAccountName(None, username)[0]
        
        # 添加完全控制权限
        dacl.AddAccessAllowedAce(
            win32security.ACL_REVISION,
            con.FILE_ALL_ACCESS | con.FILE_GENERIC_READ | con.FILE_GENERIC_WRITE | con.FILE_GENERIC_EXECUTE,
            user_sid
        )
        
       
        sd.SetSecurityDescriptorDacl(1, dacl, 0)
        win32security.SetFileSecurity(path, win32security.DACL_SECURITY_INFORMATION, sd)
        return True
    except Exception as e:
        print(f"设置出错: {str(e)}")
        return False

def setup_temp_dir():
    """设置临时目录为 C:\\syiming"""
    try:
        TEMP_DIR = "C:\\syiming"

        # 确保目录存在
        if not os.path.exists(TEMP_DIR):
            try:
                os.makedirs(TEMP_DIR)
                print(f"创建临时目录: {TEMP_DIR}")
            except PermissionError:
                QMessageBox.critical(None, "错误", f"无法创建目录 {TEMP_DIR}，请确保有足够的权限", QMessageBox.StandardButton.Ok)
                sys.exit(1)

        # 设置目录权限
        if not set_directory_permissions(TEMP_DIR):
            QMessageBox.warning(None, "警告", f"无法设置目录 {TEMP_DIR} 的权限", QMessageBox.StandardButton.Ok)

        # 测试写入权限
        test_file = os.path.join(TEMP_DIR, "test_write.tmp")
        try:
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            print("目录权限检查通过")
        except Exception as e:
            QMessageBox.critical(None, "错误", f"目录没有写入权限 - {str(e)}", QMessageBox.StandardButton.Ok)
            sys.exit(1)

        # 设置临时目录
        tempfile.tempdir = TEMP_DIR
        os.environ['TMPDIR'] = TEMP_DIR
        os.environ['TEMP'] = TEMP_DIR
        os.environ['TMP'] = TEMP_DIR
        print(f"临时目录已设置为: {TEMP_DIR}")

    except Exception as e:
        QMessageBox.critical(None, "错误", f"初始化临时目录时出错: {str(e)}", QMessageBox.StandardButton.Ok)
        sys.exit(1)

def get_resource_path(relative_path):
    if hasattr(sys, '_MEIPASS'):
        # 打包后的环境
        base_path = sys._MEIPASS
    else:
        # 开发环境
        base_path = os.path.abspath(".")
    
    path = os.path.join(base_path, relative_path)
    
    # 调试用：检查路径是否存在
    if not os.path.exists(path):
        print(f"警告：资源文件不存在 - {path}")
    
    return path

def load_pixmap(image_path):
    """加载图片资源"""
    return QPixmap(get_resource_path(image_path))

def load_icon(icon_path):
    """加载图标资源"""
    return QIcon(get_resource_path(icon_path))

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # 设置应用程序图标
    app.setWindowIcon(load_icon("ico/icon.ico"))
    
    # 强制管理员权限
    if not is_admin():
        try:
            script = os.path.abspath(sys.argv[0])
            params = ' '.join([script] + sys.argv[1:] + ['asadmin'])
            ctypes.windll.shell32.ShellExecuteW(None, "runas", sys.executable, params, None, 1)
        except Exception as e:
            QMessageBox.critical(None, "错误", "程序需要以管理员身份运行！", QMessageBox.StandardButton.Ok)
        sys.exit(0)

    # 在QApplication初始化后设置临时目录
    setup_temp_dir()
    
    window = FlashToolUI()
    window.show()
    sys.exit(app.exec())

def kill_adb_processes():
    for proc in psutil.process_iter(['name']):
        if proc.info['name'] in ['adb.exe']:  # 根据您的实际情况调整
            try:
                proc.kill()
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass

def cleanup_folders():
    """清理临时文件夹"""
    folders_to_clean = ['temp']
    for folder in folders_to_clean:
        if os.path.exists(folder):
            try:
                shutil.rmtree(folder, ignore_errors=True)
            except Exception as e:
                print(f"清理文件夹 {folder}: {e}")

def cleanup_nuitka_tempdirs():
    kill_adb_processes()
    temp_root = os.path.join(tempfile.gettempdir(), "syiming")
    if os.path.exists(temp_root):
        for d in glob.glob(os.path.join(temp_root, "onefile_*")):
            try:
                shutil.rmtree(d, ignore_errors=True)
            except Exception as e:
                print(f": {d} - {e}")

atexit.register(cleanup_nuitka_tempdirs)

if __name__ == "__main__":
    main() 
    