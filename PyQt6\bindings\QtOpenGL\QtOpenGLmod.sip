// QtOpenGLmod.sip generated by MetaSIP
//
// This file is part of the QtOpenGL Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%Module(name=PyQt6.QtOpenGL, keyword_arguments="Optional", use_limited_api=True)

%Import QtCore/QtCoremod.sip
%Import QtGui/QtGuimod.sip

%Copying
Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>

This file is part of PyQt6.

This file may be used under the terms of the GNU General Public License
version 3.0 as published by the Free Software Foundation and appearing in
the file LICENSE included in the packaging of this file.  Please review the
following information to ensure the GNU General Public License version 3.0
requirements will be met: http://www.gnu.org/copyleft/gpl.html.

If you do not wish to use this file under the terms of the GPL version 3.0
then you may purchase a commercial license.  For more information contact
<EMAIL>.

This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
%End

%DefaultSupertype PyQt6.sip.simplewrapper

%Include qopenglbuffer.sip
%Include qopengldebug.sip
%Include qopenglframebufferobject.sip
%Include qopenglpaintdevice.sip
%Include qopenglpixeltransferoptions.sip
%Include qopenglshaderprogram.sip
%Include qopengltexture.sip
%Include qopengltextureblitter.sip
%Include qopengltimerquery.sip
%Include qopenglversionfunctions.sip
%Include qopenglversionfunctionsfactory.sip
%Include qopenglvertexarrayobject.sip
%Include qopenglwindow.sip
%Include qopenglfunctions_2_0.sip
%Include qopenglfunctions_2_1.sip
%Include qopenglfunctions_4_1_core.sip
%Include qopenglfunctions_es2.sip
%Include qopenglversionprofile.sip
%Include qpyopengl_qlist.sip
%Include qpyopengl_std_pair.sip
