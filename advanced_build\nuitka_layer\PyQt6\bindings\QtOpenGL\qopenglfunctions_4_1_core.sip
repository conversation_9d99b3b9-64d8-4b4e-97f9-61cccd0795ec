// qopenglfunctions_4_1_core.sip generated by MetaSIP
//
// This file is part of the QtOpenGL Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (!PyQt_OpenGL_ES2)

class QOpenGLFunctions_4_1_Core : public QAbstractOpenGLFunctions
{
%TypeHeaderCode
#include <qopenglfunctions_4_1_core.h>
%End

public:
    QOpenGLFunctions_4_1_Core();
    virtual ~QOpenGLFunctions_4_1_Core();
    virtual bool initializeOpenGLFunctions();
    void glViewport(GLint x, GLint y, GLsizei width, GLsizei height);
    void glDepthRange(GLdouble nearVal, GLdouble farVal);
    GLboolean glIsEnabled(GLenum cap);
    void glGetTexLevelParameteriv(GLenum target, GLint level, GLenum pname, SIP_PYOBJECT *params /TypeHint="int"/);
%MethodCode
        GLint params[1];
            
        sipCpp->glGetTexLevelParameteriv(a0, a1, a2, params);
        
        a3 = qpyopengl_from_GLint(&sipIsErr, params, 1);
%End

    void glGetTexLevelParameterfv(GLenum target, GLint level, GLenum pname, SIP_PYOBJECT *params /TypeHint="float"/);
%MethodCode
        GLfloat params[1];
            
        sipCpp->glGetTexLevelParameterfv(a0, a1, a2, params);
        
        a3 = qpyopengl_from_GLfloat(&sipIsErr, params, 1);
%End

    void glGetTexParameteriv(GLenum target, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[int, Tuple[int, int, int, int]]"/);
%MethodCode
        GLint params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        #if defined(GL_TEXTURE_SWIZZLE_RGBA) || defined(GL_TEXTURE_BORDER_COLOR)
        #if defined(GL_TEXTURE_SWIZZLE_RGBA)
        case GL_TEXTURE_SWIZZLE_RGBA:
        #endif
        #if defined(GL_TEXTURE_BORDER_COLOR)
        case GL_TEXTURE_BORDER_COLOR:
        #endif
            nr_params = 4;
            break;
        #endif
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetTexParameteriv(a0, a1, params);
        
        a2 = qpyopengl_from_GLint(&sipIsErr, params, nr_params);
%End

    void glGetTexParameterfv(GLenum target, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[float, Tuple[float, float, float, float]]"/);
%MethodCode
        GLfloat params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        #if defined(GL_TEXTURE_SWIZZLE_RGBA) || defined(GL_TEXTURE_BORDER_COLOR)
        #if defined(GL_TEXTURE_SWIZZLE_RGBA)
        case GL_TEXTURE_SWIZZLE_RGBA:
        #endif
        #if defined(GL_TEXTURE_BORDER_COLOR)
        case GL_TEXTURE_BORDER_COLOR:
        #endif
            nr_params = 4;
            break;
        #endif
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetTexParameterfv(a0, a1, params);
        
        a2 = qpyopengl_from_GLfloat(&sipIsErr, params, nr_params);
%End

    const char *glGetString(GLenum name);
%MethodCode
        sipRes = reinterpret_cast<const char *>(sipCpp->glGetString(a0));
%End

    void glGetIntegerv(GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[int, Tuple[int, ...]]"/);
%MethodCode
        GLint fixed_params[16], *params;
        GLint nr_params;
        GLenum query;
        
        nr_params = qpyopengl_get(a0, &query);
        
        if (nr_params == 0)
        {
            sipCpp->glGetIntegerv(query, &nr_params);
            params = new GLint[nr_params];
        }
        else
        {
            params = fixed_params;
        }
        
        sipCpp->glGetIntegerv(a0, params);
        a1 = qpyopengl_from_GLint(&sipIsErr, params, nr_params);
            
        if (params != fixed_params)
            delete[] params;
%End

    void glGetFloatv(GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[float, Tuple[float, ...]]"/);
%MethodCode
        GLfloat fixed_params[16], *params;
        GLint nr_params;
        GLenum query;
        
        nr_params = qpyopengl_get(a0, &query);
        
        if (nr_params == 0)
        {
            sipCpp->glGetIntegerv(query, &nr_params);
            params = new GLfloat[nr_params];
        }
        else
        {
            params = fixed_params;
        }
        
        sipCpp->glGetFloatv(a0, params);
        a1 = qpyopengl_from_GLfloat(&sipIsErr, params, nr_params);
            
        if (params != fixed_params)
            delete[] params;
%End

    GLenum glGetError();
    void glGetDoublev(GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[float, Tuple[float, ...]]"/);
%MethodCode
        GLdouble fixed_params[16], *params;
        GLint nr_params;
        GLenum query;
        
        nr_params = qpyopengl_get(a0, &query);
        
        if (nr_params == 0)
        {
            sipCpp->glGetIntegerv(query, &nr_params);
            params = new GLdouble[nr_params];
        }
        else
        {
            params = fixed_params;
        }
        
        sipCpp->glGetDoublev(a0, params);
        a1 = qpyopengl_from_GLdouble(&sipIsErr, params, nr_params);
            
        if (params != fixed_params)
            delete[] params;
%End

    void glGetBooleanv(GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[bool, Tuple[bool, ...]]"/);
%MethodCode
        GLboolean fixed_params[16], *params;
        GLint nr_params;
        GLenum query;
        
        nr_params = qpyopengl_get(a0, &query);
        
        if (nr_params == 0)
        {
            sipCpp->glGetIntegerv(query, &nr_params);
            params = new GLboolean[nr_params];
        }
        else
        {
            params = fixed_params;
        }
        
        sipCpp->glGetBooleanv(a0, params);
        a1 = qpyopengl_from_GLboolean(&sipIsErr, params, nr_params);
            
        if (params != fixed_params)
            delete[] params;
%End

    SIP_PYOBJECT glReadPixels(GLint x, GLint y, GLsizei width, GLsizei height, GLenum format, GLenum type) /TypeHint="Union[Tuple[float, ...], Tuple[int, ...]]"/;
%MethodCode
        int components;
        
        switch (a4)
        {
            case GL_BGR:
            case GL_RGB:
            {
                components = 3;
                break;
            }
        
            case GL_BGRA:
            case GL_RGBA:
            {
                components = 4;
                break;
            }
        
            case GL_RED:
            case GL_GREEN:
            case GL_BLUE:
            case GL_ALPHA:
            case GL_DEPTH_COMPONENT:
            case GL_STENCIL_INDEX:
            case GL_DEPTH_STENCIL:
            {
                components = 1;
                break;
            }
        
            default:
                components = 0;
        }
        
        Py_ssize_t length = components * a2 * a3;
        
        switch (a5)
        {
            case GL_FLOAT:
            {
                GLfloat *data = new GLfloat[length];
        
                sipCpp->glReadPixels(a0, a1, a2, a3, a4, a5, data);
                sipRes = qpyopengl_from_GLfloat(&sipIsErr, data, length);
                delete [] data;
        
                break;
            }
        
            case GL_INT:
            {
                GLint *data = new GLint[length];
        
                sipCpp->glReadPixels(a0, a1, a2, a3, a4, a5, data);
                sipRes = qpyopengl_from_GLint(&sipIsErr, data, length);
                delete [] data;
        
                break;
            }
        
            case GL_UNSIGNED_INT:
            case GL_UNSIGNED_INT_8_8_8_8:
            case GL_UNSIGNED_INT_8_8_8_8_REV:
            case GL_UNSIGNED_INT_10_10_10_2:
            case GL_UNSIGNED_INT_2_10_10_10_REV:
            case GL_UNSIGNED_INT_24_8:
            case GL_UNSIGNED_INT_10F_11F_11F_REV:
            case GL_UNSIGNED_INT_5_9_9_9_REV:
            {
                GLuint *data = new GLuint[length];
        
                sipCpp->glReadPixels(a0, a1, a2, a3, a4, a5, data);
                sipRes = qpyopengl_from_GLuint(&sipIsErr, data, length);
                delete [] data;
        
                break;
            }
        
            case GL_SHORT:
            case GL_UNSIGNED_SHORT:
            case GL_UNSIGNED_SHORT_5_6_5:
            case GL_UNSIGNED_SHORT_5_6_5_REV:
            case GL_UNSIGNED_SHORT_4_4_4_4:
            case GL_UNSIGNED_SHORT_4_4_4_4_REV:
            case GL_UNSIGNED_SHORT_5_5_5_1:
            case GL_UNSIGNED_SHORT_1_5_5_5_REV:
            case GL_BYTE:
            case GL_UNSIGNED_BYTE:
            case GL_UNSIGNED_BYTE_3_3_2:
            case GL_UNSIGNED_BYTE_2_3_3_REV:
            case GL_HALF_FLOAT:
            case GL_FLOAT_32_UNSIGNED_INT_24_8_REV:
            default:
                sipIsErr = 1;
                PyErr_SetString(PyExc_ValueError, "pixel data format not supported");
        }
%End

    void glReadBuffer(GLenum mode);
    void glPixelStorei(GLenum pname, GLint param);
    void glPixelStoref(GLenum pname, GLfloat param);
    void glDepthFunc(GLenum func);
    void glStencilOp(GLenum fail, GLenum zfail, GLenum zpass);
    void glStencilFunc(GLenum func, GLint ref, GLuint mask);
    void glLogicOp(GLenum opcode);
    void glBlendFunc(GLenum sfactor, GLenum dfactor);
    void glFlush();
    void glFinish();
    void glEnable(GLenum cap);
    void glDisable(GLenum cap);
    void glDepthMask(GLboolean flag);
    void glColorMask(GLboolean red, GLboolean green, GLboolean blue, GLboolean alpha);
    void glStencilMask(GLuint mask);
    void glClearDepth(GLdouble depth);
    void glClearStencil(GLint s);
    void glClearColor(GLfloat red, GLfloat green, GLfloat blue, GLfloat alpha);
    void glClear(GLbitfield mask);
    void glDrawBuffer(GLenum mode);
    void glTexImage2D(GLenum target, GLint level, GLint internalformat, GLsizei width, GLsizei height, GLint border, GLenum format, GLenum type, SIP_PYOBJECT pixels /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a8, a7, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexImage2D(a0, a1, a2, a3, a4, a5, a6, a7, array);
%End

    void glTexImage1D(GLenum target, GLint level, GLint internalformat, GLsizei width, GLint border, GLenum format, GLenum type, SIP_PYOBJECT pixels /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a7, a6, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexImage1D(a0, a1, a2, a3, a4, a5, a6, array);
%End

    void glTexParameteriv(GLenum target, GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexParameteriv(a0, a1, reinterpret_cast<const GLint *>(array));
%End

    void glTexParameteri(GLenum target, GLenum pname, GLint param);
    void glTexParameterfv(GLenum target, GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexParameterfv(a0, a1, reinterpret_cast<const GLfloat *>(array));
%End

    void glTexParameterf(GLenum target, GLenum pname, GLfloat param);
    void glScissor(GLint x, GLint y, GLsizei width, GLsizei height);
    void glPolygonMode(GLenum face, GLenum mode);
    void glPointSize(GLfloat size);
    void glLineWidth(GLfloat width);
    void glHint(GLenum target, GLenum mode);
    void glFrontFace(GLenum mode);
    void glCullFace(GLenum mode);
    GLboolean glIsTexture(GLuint texture);
    void glGenTextures(GLsizei n, SIP_PYOBJECT *textures /TypeHint="Union[int, Tuple[int, ...]]"/);
%MethodCode
        GLuint *params = new GLuint[a0];
            
        sipCpp->glGenTextures(a0, params);
        
        a1 = qpyopengl_from_GLuint(&sipIsErr, params, a0);
        
        delete[] params;
%End

    void glDeleteTextures(GLsizei n, SIP_PYOBJECT textures /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_UNSIGNED_INT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glDeleteTextures(a0, reinterpret_cast<const GLuint *>(array));
%End

    void glBindTexture(GLenum target, GLuint texture);
    void glTexSubImage2D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLenum type, SIP_PYOBJECT pixels /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a8, a7, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexSubImage2D(a0, a1, a2, a3, a4, a5, a6, a7, array);
%End

    void glTexSubImage1D(GLenum target, GLint level, GLint xoffset, GLsizei width, GLenum format, GLenum type, SIP_PYOBJECT pixels /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a6, a5, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexSubImage1D(a0, a1, a2, a3, a4, a5, array);
%End

    void glCopyTexSubImage2D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint x, GLint y, GLsizei width, GLsizei height);
    void glCopyTexSubImage1D(GLenum target, GLint level, GLint xoffset, GLint x, GLint y, GLsizei width);
    void glCopyTexImage2D(GLenum target, GLint level, GLenum internalformat, GLint x, GLint y, GLsizei width, GLsizei height, GLint border);
    void glCopyTexImage1D(GLenum target, GLint level, GLenum internalformat, GLint x, GLint y, GLsizei width, GLint border);
    void glPolygonOffset(GLfloat factor, GLfloat units);
    void glDrawElements(GLenum mode, GLsizei count, GLenum type, SIP_PYOBJECT indices /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a3, a2, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glDrawElements(a0, a1, a2, array);
%End

    void glDrawArrays(GLenum mode, GLint first, GLsizei count);
    void glCopyTexSubImage3D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLint x, GLint y, GLsizei width, GLsizei height);
    void glTexSubImage3D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLenum type, SIP_PYOBJECT pixels /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a10, a9, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexSubImage3D(a0, a1, a2, a3, a4, a5, a6, a7, a8, a9, array);
%End

    void glTexImage3D(GLenum target, GLint level, GLint internalformat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLenum format, GLenum type, SIP_PYOBJECT pixels /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a9, a8, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glTexImage3D(a0, a1, a2, a3, a4, a5, a6, a7, a8, array);
%End

    void glDrawRangeElements(GLenum mode, GLuint start, GLuint end, GLsizei count, GLenum type, SIP_PYOBJECT indices /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a5, a4, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glDrawRangeElements(a0, a1, a2, a3, a4, array);
%End

    void glBlendEquation(GLenum mode);
    void glBlendColor(GLfloat red, GLfloat green, GLfloat blue, GLfloat alpha);
    void glCompressedTexSubImage1D(GLenum target, GLint level, GLint xoffset, GLsizei width, GLenum format, GLsizei imageSize, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a6, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glCompressedTexSubImage1D(a0, a1, a2, a3, a4, a5, array);
%End

    void glCompressedTexSubImage2D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLsizei width, GLsizei height, GLenum format, GLsizei imageSize, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a8, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glCompressedTexSubImage2D(a0, a1, a2, a3, a4, a5, a6, a7, array);
%End

    void glCompressedTexSubImage3D(GLenum target, GLint level, GLint xoffset, GLint yoffset, GLint zoffset, GLsizei width, GLsizei height, GLsizei depth, GLenum format, GLsizei imageSize, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a10, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glCompressedTexSubImage3D(a0, a1, a2, a3, a4, a5, a6, a7, a8, a9,
                array);
%End

    void glCompressedTexImage1D(GLenum target, GLint level, GLenum internalformat, GLsizei width, GLint border, GLsizei imageSize, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a6, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glCompressedTexImage1D(a0, a1, a2, a3, a4, a5, array);
%End

    void glCompressedTexImage2D(GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLint border, GLsizei imageSize, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a7, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glCompressedTexImage2D(a0, a1, a2, a3, a4, a5, a6, array);
%End

    void glCompressedTexImage3D(GLenum target, GLint level, GLenum internalformat, GLsizei width, GLsizei height, GLsizei depth, GLint border, GLsizei imageSize, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a8, GL_UNSIGNED_BYTE,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glCompressedTexImage3D(a0, a1, a2, a3, a4, a5, a6, a7, array);
%End

    void glSampleCoverage(GLfloat value, GLboolean invert);
    void glActiveTexture(GLenum texture);
    void glPointParameteriv(GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glPointParameteriv(a0, reinterpret_cast<const GLint *>(array));
%End

    void glPointParameteri(GLenum pname, GLint param);
    void glPointParameterfv(GLenum pname, SIP_PYOBJECT params /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glPointParameterfv(a0, reinterpret_cast<const GLfloat *>(array));
%End

    void glPointParameterf(GLenum pname, GLfloat param);
    void glBlendFuncSeparate(GLenum sfactorRGB, GLenum dfactorRGB, GLenum sfactorAlpha, GLenum dfactorAlpha);
    void glGetBufferParameteriv(GLenum target, GLenum pname, GLint *params);
    GLboolean glUnmapBuffer(GLenum target);
    void glBufferSubData(GLenum target, GLintptr offset, GLsizeiptr size, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array;
        
        if (a3 == Py_None)
            array = 0;
        else
            array = qpyopengl_value_array(&sipError, a3, GL_UNSIGNED_BYTE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glBufferSubData(a0, a1, a2, array);
%End

    void glBufferData(GLenum target, GLsizeiptr size, SIP_PYOBJECT data /TypeHint="PYQT_OPENGL_ARRAY"/, GLenum usage);
%MethodCode
        const GLvoid *array;
        
        if (a2 == Py_None)
            array = 0;
        else
            array = qpyopengl_value_array(&sipError, a2, GL_UNSIGNED_BYTE, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glBufferData(a0, a1, array, a3);
%End

    GLboolean glIsBuffer(GLuint buffer);
    void glGenBuffers(GLsizei n, SIP_PYOBJECT *buffers /TypeHint="Union[int, Tuple[int, ...]]"/);
%MethodCode
        GLuint *params = new GLuint[a0];
            
        sipCpp->glGenBuffers(a0, params);
        
        a1 = qpyopengl_from_GLuint(&sipIsErr, params, a0);
        
        delete[] params;
%End

    void glDeleteBuffers(GLsizei n, SIP_PYOBJECT buffers /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_UNSIGNED_INT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glDeleteBuffers(a0, reinterpret_cast<const GLuint *>(array));
%End

    void glBindBuffer(GLenum target, GLuint buffer);
    void glGetQueryiv(GLenum target, GLenum pname, GLint *params);
    void glEndQuery(GLenum target);
    void glBeginQuery(GLenum target, GLuint id);
    GLboolean glIsQuery(GLuint id);
    void glDeleteQueries(GLsizei n, SIP_PYOBJECT ids /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_UNSIGNED_INT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glDeleteQueries(a0, reinterpret_cast<const GLuint *>(array));
%End

    void glGenQueries(GLsizei n, SIP_PYOBJECT *ids /TypeHint="Union[int, Tuple[int, ...]]"/);
%MethodCode
        GLuint *params = new GLuint[a0];
            
        sipCpp->glGenQueries(a0, params);
        
        a1 = qpyopengl_from_GLuint(&sipIsErr, params, a0);
        
        delete[] params;
%End

    void glVertexAttribPointer(GLuint index, GLint size, GLenum type, GLboolean normalized, GLsizei stride, SIP_PYOBJECT pointer /TypeHint="PYQT_OPENGL_BOUND_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array_cached(&sipError, a5, a2, sipSelf,
                "VertexAttribPointer", a0);
        
        if (sipError == sipErrorNone)
            sipCpp->glVertexAttribPointer(a0, a1, a2, a3, a4, array);
%End

    void glValidateProgram(GLuint program);
    void glUniformMatrix4fv(GLint location, GLsizei count, GLboolean transpose, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a3, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniformMatrix4fv(a0, a1, a2,
                    reinterpret_cast<const GLfloat *>(array));
%End

    void glUniformMatrix3fv(GLint location, GLsizei count, GLboolean transpose, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a3, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniformMatrix3fv(a0, a1, a2,
                    reinterpret_cast<const GLfloat *>(array));
%End

    void glUniformMatrix2fv(GLint location, GLsizei count, GLboolean transpose, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a3, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniformMatrix2fv(a0, a1, a2,
                    reinterpret_cast<const GLfloat *>(array));
%End

    void glUniform4iv(GLint location, GLsizei count, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform4iv(a0, a1, reinterpret_cast<const GLint *>(array));
%End

    void glUniform3iv(GLint location, GLsizei count, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform3iv(a0, a1, reinterpret_cast<const GLint *>(array));
%End

    void glUniform2iv(GLint location, GLsizei count, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform2iv(a0, a1, reinterpret_cast<const GLint *>(array));
%End

    void glUniform1iv(GLint location, GLsizei count, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_INT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform1iv(a0, a1, reinterpret_cast<const GLint *>(array));
%End

    void glUniform4fv(GLint location, GLsizei count, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform4fv(a0, a1, reinterpret_cast<const GLfloat *>(array));
%End

    void glUniform3fv(GLint location, GLsizei count, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform3fv(a0, a1, reinterpret_cast<const GLfloat *>(array));
%End

    void glUniform2fv(GLint location, GLsizei count, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform2fv(a0, a1, reinterpret_cast<const GLfloat *>(array));
%End

    void glUniform1fv(GLint location, GLsizei count, SIP_PYOBJECT value /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a2, GL_FLOAT, sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glUniform1fv(a0, a1, reinterpret_cast<const GLfloat *>(array));
%End

    void glUniform4i(GLint location, GLint v0, GLint v1, GLint v2, GLint v3);
    void glUniform3i(GLint location, GLint v0, GLint v1, GLint v2);
    void glUniform2i(GLint location, GLint v0, GLint v1);
    void glUniform1i(GLint location, GLint v0);
    void glUniform4f(GLint location, GLfloat v0, GLfloat v1, GLfloat v2, GLfloat v3);
    void glUniform3f(GLint location, GLfloat v0, GLfloat v1, GLfloat v2);
    void glUniform2f(GLint location, GLfloat v0, GLfloat v1);
    void glUniform1f(GLint location, GLfloat v0);
    void glUseProgram(GLuint program);
    void glLinkProgram(GLuint program);
    GLboolean glIsShader(GLuint shader);
    GLboolean glIsProgram(GLuint program);
    void glGetVertexAttribiv(GLuint index, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[int, Tuple[int, int, int, int]]"/);
%MethodCode
        GLint params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_CURRENT_VERTEX_ATTRIB:
            nr_params = 4;
            break;
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetVertexAttribiv(a0, a1, params);
        
        a2 = qpyopengl_from_GLint(&sipIsErr, params, nr_params);
%End

    void glGetVertexAttribfv(GLuint index, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[float, Tuple[float, float, float, float]]"/);
%MethodCode
        GLfloat params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_CURRENT_VERTEX_ATTRIB:
            nr_params = 4;
            break;
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetVertexAttribfv(a0, a1, params);
        
        a2 = qpyopengl_from_GLfloat(&sipIsErr, params, nr_params);
%End

    void glGetVertexAttribdv(GLuint index, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[float, Tuple[float, float, float, float]]"/);
%MethodCode
        GLdouble params[4];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        case GL_CURRENT_VERTEX_ATTRIB:
            nr_params = 4;
            break;
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetVertexAttribdv(a0, a1, params);
        
        a2 = qpyopengl_from_GLdouble(&sipIsErr, params, nr_params);
%End

    GLint glGetUniformLocation(GLuint program, const GLchar *name);
    SIP_PYOBJECT glGetShaderSource(GLuint shader) /TypeHint="bytes"/;
%MethodCode
        GLint bufsize;
        
        sipCpp->glGetShaderiv(a0, GL_SHADER_SOURCE_LENGTH, &bufsize);
        
        if (bufsize > 0)
        {
            GLchar *source = new GLchar[bufsize];
        
            sipCpp->glGetShaderSource(a0, bufsize, 0, source);
            sipRes = PyBytes_FromString(source);
        
            delete[] source;
        }
        else
        {
            sipRes = PyBytes_FromString("");
        }
%End

    SIP_PYOBJECT glGetShaderInfoLog(GLuint shader) /TypeHint="bytes"/;
%MethodCode
        GLint bufsize;
        
        sipCpp->glGetShaderiv(a0, GL_INFO_LOG_LENGTH, &bufsize);
        
        if (bufsize > 0)
        {
            GLchar *log = new GLchar[bufsize];
        
            sipCpp->glGetShaderInfoLog(a0, bufsize, 0, log);
            sipRes = PyBytes_FromString(log);
        
            delete[] log;
        }
        else
        {
            sipRes = PyBytes_FromString("");
        }
%End

    void glGetShaderiv(GLuint shader, GLenum pname, GLint *params);
    SIP_PYOBJECT glGetProgramInfoLog(GLuint program) /TypeHint="bytes"/;
%MethodCode
        GLint bufsize;
        
        sipCpp->glGetProgramiv(a0, GL_INFO_LOG_LENGTH, &bufsize);
        
        if (bufsize > 0)
        {
            GLchar *log = new GLchar[bufsize];
        
            sipCpp->glGetProgramInfoLog(a0, bufsize, 0, log);
            sipRes = PyBytes_FromString(log);
        
            delete[] log;
        }
        else
        {
            sipRes = PyBytes_FromString("");
        }
%End

    void glGetProgramiv(GLuint program, GLenum pname, SIP_PYOBJECT *params /TypeHint="Union[int, Tuple[int, int, int]]"/);
%MethodCode
        GLint params[3];
        Py_ssize_t nr_params;
        
        switch (a1)
        {
        #if defined(GL_COMPUTE_LOCAL_WORK_SIZE)
        case GL_COMPUTE_LOCAL_WORK_SIZE:
            nr_params = 3;
            break;
        #endif
        
        default:
            nr_params = 1;
        }
        
        sipCpp->glGetProgramiv(a0, a1, params);
        
        a2 = qpyopengl_from_GLint(&sipIsErr, params, nr_params);
%End

    GLint glGetAttribLocation(GLuint program, const GLchar *name);
    SIP_PYOBJECT glGetAttachedShaders(GLuint program) /TypeHint="Tuple[int, ...]"/;
%MethodCode
        GLint nr_shaders;
        
        sipCpp->glGetProgramiv(a0, GL_ATTACHED_SHADERS, &nr_shaders);
        
        if (nr_shaders < 1)
        {
            sipRes = PyTuple_New(0);
        }
        else
        {
            GLuint *shaders = new GLuint[nr_shaders];
        
            sipCpp->glGetAttachedShaders(a0, nr_shaders, 0, shaders);
        
            sipRes = PyTuple_New(nr_shaders);
        
            if (sipRes)
            {
                for (GLint i = 0; i < nr_shaders; ++i)
                {
                    PyObject *itm = PyLong_FromLong(shaders[i]);
                    
                    if (!itm)
                    {
                        Py_DECREF(sipRes);
                        sipRes = 0;
                        break;
                    }
                    
                    PyTuple_SetItem(sipRes, i, itm);
                }
            }
        
            delete[] shaders;
        }
        
        if (!sipRes)
            sipIsErr = 1;
%End

    SIP_PYOBJECT glGetActiveUniform(GLuint program, GLuint index) /TypeHint="Tuple[str, int, int]"/;
%MethodCode
        GLint bufsize;
        
        sipCpp->glGetProgramiv(a0, GL_ACTIVE_UNIFORM_MAX_LENGTH, &bufsize);
        
        GLchar *name = new GLchar[bufsize];
        GLint size;
        GLenum type;
        
        sipCpp->glGetActiveUniform(a0, a1, bufsize, 0, &size, &type, name);
        
        sipRes = Py_BuildValue("siI", name, size, type);
        
        if (!sipRes)
            sipIsErr = 1;
        
        delete[] name;
%End

    SIP_PYOBJECT glGetActiveAttrib(GLuint program, GLuint index) /TypeHint="Tuple[str, int, int]"/;
%MethodCode
        GLint bufsize;
        
        sipCpp->glGetProgramiv(a0, GL_ACTIVE_ATTRIBUTE_MAX_LENGTH, &bufsize);
        
        GLchar *name = new GLchar[bufsize];
        GLint size;
        GLenum type;
        
        sipCpp->glGetActiveAttrib(a0, a1, bufsize, 0, &size, &type, name);
        
        sipRes = Py_BuildValue("siI", name, size, type);
        
        if (!sipRes)
            sipIsErr = 1;
        
        delete[] name;
%End

    void glEnableVertexAttribArray(GLuint index);
    void glDisableVertexAttribArray(GLuint index);
    void glDetachShader(GLuint program, GLuint shader);
    void glDeleteShader(GLuint shader);
    void glDeleteProgram(GLuint program);
    GLuint glCreateShader(GLenum type);
    GLuint glCreateProgram();
    void glCompileShader(GLuint shader);
    void glBindAttribLocation(GLuint program, GLuint index, const GLchar *name);
    void glAttachShader(GLuint program, GLuint shader);
    void glStencilMaskSeparate(GLenum face, GLuint mask);
    void glStencilFuncSeparate(GLenum face, GLenum func, GLint ref, GLuint mask);
    void glStencilOpSeparate(GLenum face, GLenum sfail, GLenum dpfail, GLenum dppass);
    void glDrawBuffers(GLsizei n, SIP_PYOBJECT bufs /TypeHint="PYQT_OPENGL_ARRAY"/);
%MethodCode
        const GLvoid *array = qpyopengl_value_array(&sipError, a1, GL_UNSIGNED_INT,
                sipSelf);
        
        if (sipError == sipErrorNone)
            sipCpp->glDrawBuffers(a0, reinterpret_cast<const GLenum *>(array));
%End

    void glBlendEquationSeparate(GLenum modeRGB, GLenum modeAlpha);
    GLboolean glIsVertexArray(GLuint array);
    void glBindVertexArray(GLuint array);
    void glFramebufferTextureLayer(GLenum target, GLenum attachment, GLuint texture, GLint level, GLint layer);
    void glRenderbufferStorageMultisample(GLenum target, GLsizei samples, GLenum internalformat, GLsizei width, GLsizei height);
    void glBlitFramebuffer(GLint srcX0, GLint srcY0, GLint srcX1, GLint srcY1, GLint dstX0, GLint dstY0, GLint dstX1, GLint dstY1, GLbitfield mask, GLenum filter);
    void glGenerateMipmap(GLenum target);
    void glFramebufferRenderbuffer(GLenum target, GLenum attachment, GLenum renderbuffertarget, GLuint renderbuffer);
    void glFramebufferTexture3D(GLenum target, GLenum attachment, GLenum textarget, GLuint texture, GLint level, GLint zoffset);
    void glFramebufferTexture2D(GLenum target, GLenum attachment, GLenum textarget, GLuint texture, GLint level);
    void glFramebufferTexture1D(GLenum target, GLenum attachment, GLenum textarget, GLuint texture, GLint level);
    GLenum glCheckFramebufferStatus(GLenum target);
    void glBindFramebuffer(GLenum target, GLuint framebuffer);
    GLboolean glIsFramebuffer(GLuint framebuffer);
    void glRenderbufferStorage(GLenum target, GLenum internalformat, GLsizei width, GLsizei height);
    void glBindRenderbuffer(GLenum target, GLuint renderbuffer);
    GLboolean glIsRenderbuffer(GLuint renderbuffer);
    void glClearBufferfi(GLenum buffer, GLint drawbuffer, GLfloat depth, GLint stencil);
    void glUniform4ui(GLint location, GLuint v0, GLuint v1, GLuint v2, GLuint v3);
    void glUniform3ui(GLint location, GLuint v0, GLuint v1, GLuint v2);
    void glUniform2ui(GLint location, GLuint v0, GLuint v1);
    void glUniform1ui(GLint location, GLuint v0);
    void glEndConditionalRender();
    void glBeginConditionalRender(GLuint id, GLenum mode);
    void glClampColor(GLenum target, GLenum clamp);
    void glBindBufferBase(GLenum target, GLuint index, GLuint buffer);
    void glEndTransformFeedback();
    void glBeginTransformFeedback(GLenum primitiveMode);
    GLboolean glIsEnabledi(GLenum target, GLuint index);
    void glDisablei(GLenum target, GLuint index);
    void glEnablei(GLenum target, GLuint index);
    void glColorMaski(GLuint index, GLboolean r, GLboolean g, GLboolean b, GLboolean a);
    void glUniformBlockBinding(GLuint program, GLuint uniformBlockIndex, GLuint uniformBlockBinding);
    void glPrimitiveRestartIndex(GLuint index);
    void glTexBuffer(GLenum target, GLenum internalformat, GLuint buffer);
    void glDrawArraysInstanced(GLenum mode, GLint first, GLsizei count, GLsizei instancecount);
    void glSampleMaski(GLuint index, GLbitfield mask);
    void glTexImage3DMultisample(GLenum target, GLsizei samples, GLint internalformat, GLsizei width, GLsizei height, GLsizei depth, GLboolean fixedsamplelocations);
    void glTexImage2DMultisample(GLenum target, GLsizei samples, GLint internalformat, GLsizei width, GLsizei height, GLboolean fixedsamplelocations);
    void glProvokingVertex(GLenum mode);
    void glFramebufferTexture(GLenum target, GLenum attachment, GLuint texture, GLint level);
    void glVertexAttribP4ui(GLuint index, GLenum type, GLboolean normalized, GLuint value);
    void glVertexAttribP3ui(GLuint index, GLenum type, GLboolean normalized, GLuint value);
    void glVertexAttribP2ui(GLuint index, GLenum type, GLboolean normalized, GLuint value);
    void glVertexAttribP1ui(GLuint index, GLenum type, GLboolean normalized, GLuint value);
    void glQueryCounter(GLuint id, GLenum target);
    void glSamplerParameterf(GLuint sampler, GLenum pname, GLfloat param);
    void glSamplerParameteri(GLuint sampler, GLenum pname, GLint param);
    void glBindSampler(GLuint unit, GLuint sampler);
    GLboolean glIsSampler(GLuint sampler);
    void glVertexAttribDivisor(GLuint index, GLuint divisor);
    void glEndQueryIndexed(GLenum target, GLuint index);
    void glBeginQueryIndexed(GLenum target, GLuint index, GLuint id);
    void glDrawTransformFeedbackStream(GLenum mode, GLuint id, GLuint stream);
    void glDrawTransformFeedback(GLenum mode, GLuint id);
    void glResumeTransformFeedback();
    void glPauseTransformFeedback();
    GLboolean glIsTransformFeedback(GLuint id);
    void glBindTransformFeedback(GLenum target, GLuint id);
    void glPatchParameteri(GLenum pname, GLint value);
    void glUniform4d(GLint location, GLdouble x, GLdouble y, GLdouble z, GLdouble w);
    void glUniform3d(GLint location, GLdouble x, GLdouble y, GLdouble z);
    void glUniform2d(GLint location, GLdouble x, GLdouble y);
    void glUniform1d(GLint location, GLdouble x);
    void glBlendFuncSeparatei(GLuint buf, GLenum srcRGB, GLenum dstRGB, GLenum srcAlpha, GLenum dstAlpha);
    void glBlendFunci(GLuint buf, GLenum src, GLenum dst);
    void glBlendEquationSeparatei(GLuint buf, GLenum modeRGB, GLenum modeAlpha);
    void glBlendEquationi(GLuint buf, GLenum mode);
    void glMinSampleShading(GLfloat value);
    void glDepthRangeIndexed(GLuint index, GLdouble n, GLdouble f);
    void glScissorIndexed(GLuint index, GLint left, GLint bottom, GLsizei width, GLsizei height);
    void glViewportIndexedf(GLuint index, GLfloat x, GLfloat y, GLfloat w, GLfloat h);
    void glVertexAttribL4d(GLuint index, GLdouble x, GLdouble y, GLdouble z, GLdouble w);
    void glVertexAttribL3d(GLuint index, GLdouble x, GLdouble y, GLdouble z);
    void glVertexAttribL2d(GLuint index, GLdouble x, GLdouble y);
    void glVertexAttribL1d(GLuint index, GLdouble x);
    void glValidateProgramPipeline(GLuint pipeline);
    void glProgramUniform4ui(GLuint program, GLint location, GLuint v0, GLuint v1, GLuint v2, GLuint v3);
    void glProgramUniform4d(GLuint program, GLint location, GLdouble v0, GLdouble v1, GLdouble v2, GLdouble v3);
    void glProgramUniform4f(GLuint program, GLint location, GLfloat v0, GLfloat v1, GLfloat v2, GLfloat v3);
    void glProgramUniform4i(GLuint program, GLint location, GLint v0, GLint v1, GLint v2, GLint v3);
    void glProgramUniform3ui(GLuint program, GLint location, GLuint v0, GLuint v1, GLuint v2);
    void glProgramUniform3d(GLuint program, GLint location, GLdouble v0, GLdouble v1, GLdouble v2);
    void glProgramUniform3f(GLuint program, GLint location, GLfloat v0, GLfloat v1, GLfloat v2);
    void glProgramUniform3i(GLuint program, GLint location, GLint v0, GLint v1, GLint v2);
    void glProgramUniform2ui(GLuint program, GLint location, GLuint v0, GLuint v1);
    void glProgramUniform2d(GLuint program, GLint location, GLdouble v0, GLdouble v1);
    void glProgramUniform2f(GLuint program, GLint location, GLfloat v0, GLfloat v1);
    void glProgramUniform2i(GLuint program, GLint location, GLint v0, GLint v1);
    void glProgramUniform1ui(GLuint program, GLint location, GLuint v0);
    void glProgramUniform1d(GLuint program, GLint location, GLdouble v0);
    void glProgramUniform1f(GLuint program, GLint location, GLfloat v0);
    void glProgramUniform1i(GLuint program, GLint location, GLint v0);
    GLboolean glIsProgramPipeline(GLuint pipeline);
    void glBindProgramPipeline(GLuint pipeline);
    void glActiveShaderProgram(GLuint pipeline, GLuint program);
    void glUseProgramStages(GLuint pipeline, GLbitfield stages, GLuint program);
    void glProgramParameteri(GLuint program, GLenum pname, GLint value);
    void glClearDepthf(GLfloat dd);
    void glDepthRangef(GLfloat n, GLfloat f);
    void glReleaseShaderCompiler();
};

%End
