// qquickrendertarget.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQuickRenderTarget
{
%TypeHeaderCode
#include <qquickrendertarget.h>
%End

public:
    QQuickRenderTarget();
    ~QQuickRenderTarget();
    QQuickRenderTarget(const QQuickRenderTarget &other);
    bool isNull() const;
%If (PyQt_OpenGL)
    static QQuickRenderTarget fromOpenGLTexture(uint textureId, const QSize &pixelSize, int sampleCount = 1);
%End
%If (Qt_6_4_0 -)
    static QQuickRenderTarget fromOpenGLTexture(uint textureId, uint format, const QSize &pixelSize, int sampleCount = 1);
%End
%If (Qt_6_2_0 -)
    static QQuickRenderTarget fromOpenGLRenderBuffer(uint renderbufferId, const QSize &pixelSize, int sampleCount = 1);
%End
%If (Qt_6_4_0 -)
    static QQuickRenderTarget fromPaintDevice(QPaintDevice *device);
%End
%If (Qt_6_3_0 -)
    qreal devicePixelRatio() const;
%End
%If (Qt_6_3_0 -)
    void setDevicePixelRatio(qreal ratio);
%End
%If (Qt_6_4_0 -)
    bool mirrorVertically() const;
%End
%If (Qt_6_4_0 -)
    void setMirrorVertically(bool enable);
%End
};

bool operator==(const QQuickRenderTarget &lhs, const QQuickRenderTarget &rhs);
bool operator!=(const QQuickRenderTarget &lhs, const QQuickRenderTarget &rhs);
