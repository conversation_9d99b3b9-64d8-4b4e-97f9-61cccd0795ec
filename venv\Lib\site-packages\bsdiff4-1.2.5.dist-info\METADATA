Metadata-Version: 2.1
Name: bsdiff4
Version: 1.2.5
Summary: binary diff and patch using the BSDIFF4-format
Home-page: https://github.com/ilanschnell/bsdiff4
Author: <PERSON><PERSON>
Author-email: il<PERSON><PERSON><PERSON>@gmail.com
License: BSD
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: C
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Utilities
License-File: LICENSE

=======================================================
bsdiff4: binary diff and patch using the BSDIFF4-format
=======================================================

The code is mostly derived from cx_bsdiff (written by Anthony Tuininga,
http://cx-bsdiff.sourceforge.net/).  The cx_bsdiff code in turn was derived
from bsdiff, the standalone utility produced for BSD which can be found
at http://www.daemonology.net/bsdiff.
In addition to the two functions (diff and patch) cx_bsdiff provides, this
package includes:

* an interface to the BSDIFF4-format
* command line interfaces: bsdiff4 and bspatch4
* tests


The bsdiff4 package defines the following high level functions:

``diff(src_bytes, dst_bytes)`` -> bytes
   Return a BSDIFF4-format patch (from ``src_bytes`` to ``dst_bytes``) as
   bytes.

``patch(src_bytes, patch_bytes)`` -> bytes
   Apply the BSDIFF4-format ``patch_bytes`` to ``src_bytes`` and return
   the bytes.

``file_diff(src_path, dst_path, patch_path)``
   Write a BSDIFF4-format patch (from the file ``src_path`` to the
   file ``dst_path``) to the file ``patch_path``.

``file_patch(src_path, dst_path, patch_path)``
   Apply the BSDIFF4-format file ``patch_path`` to the file ``src_path``
   and write the result to the file ``dst_path``.

``file_patch_inplace(path, patch_path)``
   Apply the BSDIFF4-format file ``patch_path`` to the file ``path``
   in place.


Example:

.. code-block:: python

   >>> import bsdiff4
   >>> a = 100000 * b'a'
   >>> b = bytearray(a)
   >>> b[100:106] = b' diff '
   >>> p = bsdiff4.diff(a, bytes(b))
   >>> len(p)
   154
   >>> bsdiff4.patch(a, p) == b
   True
