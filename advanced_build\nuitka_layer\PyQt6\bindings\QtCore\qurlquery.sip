// qurlquery.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QUrlQuery
{
%TypeHeaderCode
#include <qurlquery.h>
%End

public:
    QUrlQuery();
    explicit QUrlQuery(const QUrl &url);
    explicit QUrlQuery(const QString &queryString);
    QUrlQuery(const QUrlQuery &other);
    ~QUrlQuery();
    bool operator==(const QUrlQuery &other) const;
    bool operator!=(const QUrlQuery &other) const;
    void swap(QUrlQuery &other /Constrained/);
    bool isEmpty() const;
    bool isDetached() const;
    void clear();
    QString query(QUrl::ComponentFormattingOptions options = QUrl::PrettyDecoded) const;
    void setQuery(const QString &queryString);
    QString toString(QUrl::ComponentFormattingOptions options = QUrl::PrettyDecoded) const;
    void setQueryDelimiters(QChar valueDelimiter, QChar pairDelimiter);
    QChar queryValueDelimiter() const;
    QChar queryPairDelimiter() const;
    void setQueryItems(const QList<std::pair<QString, QString>> &query);
    QList<std::pair<QString, QString>> queryItems(QUrl::ComponentFormattingOptions options = QUrl::PrettyDecoded) const;
    bool hasQueryItem(const QString &key) const;
    void addQueryItem(const QString &key, const QString &value);
    void removeQueryItem(const QString &key);
    QString queryItemValue(const QString &key, QUrl::ComponentFormattingOptions options = QUrl::PrettyDecoded) const;
    QStringList allQueryItemValues(const QString &key, QUrl::ComponentFormattingOptions options = QUrl::PrettyDecoded) const;
    void removeAllQueryItems(const QString &key);
    static QChar defaultQueryValueDelimiter();
%MethodCode
        sipRes = new QChar(QUrlQuery::defaultQueryValueDelimiter());
%End

    static QChar defaultQueryPairDelimiter();
%MethodCode
        sipRes = new QChar(QUrlQuery::defaultQueryPairDelimiter());
%End

    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};
