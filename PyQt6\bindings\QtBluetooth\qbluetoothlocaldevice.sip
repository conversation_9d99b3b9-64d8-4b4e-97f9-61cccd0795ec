// qbluetoothlocaldevice.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QBluetoothLocalDevice : public QObject
{
%TypeHeaderCode
#include <qbluetoothlocaldevice.h>
%End

public:
    enum Pairing
    {
        Unpaired,
        Paired,
        AuthorizedPaired,
    };

    enum HostMode
    {
        HostPoweredOff,
        HostConnectable,
        HostDiscoverable,
        HostDiscoverableLimitedInquiry,
    };

    enum Error
    {
        NoError,
        PairingError,
%If (Qt_6_4_0 -)
        MissingPermissionsError,
%End
        UnknownError,
    };

    explicit QBluetoothLocalDevice(QObject *parent /TransferThis/ = 0);
    QBluetoothLocalDevice(const QBluetoothAddress &address, QObject *parent /TransferThis/ = 0);
    virtual ~QBluetoothLocalDevice();
    bool isValid() const;
    void requestPairing(const QBluetoothAddress &address, QBluetoothLocalDevice::Pairing pairing);
    QBluetoothLocalDevice::Pairing pairingStatus(const QBluetoothAddress &address) const;
    void setHostMode(QBluetoothLocalDevice::HostMode mode);
    QBluetoothLocalDevice::HostMode hostMode() const;
    void powerOn();
    QString name() const;
    QBluetoothAddress address() const;
    static QList<QBluetoothHostInfo> allDevices();
    QList<QBluetoothAddress> connectedDevices() const;

signals:
    void hostModeStateChanged(QBluetoothLocalDevice::HostMode state);
    void pairingFinished(const QBluetoothAddress &address, QBluetoothLocalDevice::Pairing pairing);
    void errorOccurred(QBluetoothLocalDevice::Error error);
    void deviceConnected(const QBluetoothAddress &address);
    void deviceDisconnected(const QBluetoothAddress &address);
};

%End
