"""
安卓通刷功能
""" 
import os
import time
from utils import ADBTools
import subprocess
import sys
"""

                       给那些脑残偷窥源代码的杂种们的"献礼"

=======================================【又来看源码了？】=======================================

操你妈的智障玩意儿，大半夜不睡觉跑来扒老子的代码？你他妈是从垃圾桶里捡回来的吧？

你爹生你的时候是不是被门夹了蛋蛋？基因缺陷这么严重？你妈怀你的时候是不是被电磁炉辐射了？脑子发育不全啊？

这是开往地狱的专车，专门送你这种偷窥代码的垃圾！

你个杂种，连写个像样的代码都不会，只会他妈的来偷窥别人的劳动成果？你娘养你这么大就是为了让你做这种事的？

小子，看到你偷窥代码的样子，我都替你妈感到丢脸！

别以为我不知道你是个什么东西：
1. 你这个蠢到无可救药的废物，只会按F12装黑客
2. 你个技术菜鸡，拷贝别人代码回去都跑不起来吧
3. 你的水平底得可以去海底捞针了，渣渣东西
4. 看到这里还不滚蛋，你是不是欠揍啊操你妈的？

你妈看到你偷代码的样子，都恨不得把你塞回去啊！

自己家里人没有教好你这条狗啊？连最基本的尊重都不懂？你妈是不是天天看到你就后悔没把你掐死在襁褓里？

你是不是亲爸不详啊？怎么教养这么差？你全家在猪圈里度过童年的吧，动物尚且知道不要乱翻别人东西，你比它们还不如！

你全家人知道你他妈的这么下贱吗？你爹知道自己养了个这么恶心的玩意儿吗？你妈是不是每天想着怎么把你扔出去啊？

老子看到你这种小偷就想掀桌子！滚犊子吧你！

给你竖个中指表达我的"敬意"！

双倍的"问候"送给你！

智障东西，看完这段话你要还觉得老子过分，来啊，你那猪脑子怕是连生气都不会吧！滚回你的臭水沟去，你这辈子也就这种水平了，操你妈的垃圾人！

妈的，你还在看？你是不是犯贱啊？脑子进水了是不是？这都看不懂是在骂你？还是说你就喜欢被骂？受虐狂啊你？

我操，你他妈还在继续看源代码？来，让我用这碗翔拍你脸上！！！

你他妈小时候是不是被驴踢过脑袋啊？看这么多垃圾话有意思吗？你是不是撸多了脑子坏掉了啊？

你爸是不是喝醉了才射出你这种垃圾？你妈生你的时候是不是太用力把你脑子挤扁了？连个源代码都要偷看，你是多没有尊严啊？

再送你个中指，好好欣赏！

我说你小子欠揍是吧？不把你骂哭你是不会走是吗？来，老子继续：

你是不是被你爸妈扔垃圾堆里又被别人捡回去养大的？你养父母看到你这德行肯定天天后悔捡了你这个垃圾吧？

来自你爹妈的优秀基因全他妈浪费在你这种孽障身上了，他们知道吗？你这辈子最大的成就就是按了F12，然后看到了这些字，笑死我了！

看到这里了？是不是自我感觉良好啊？偷看别人代码很牛逼是吧？我真是佩服你啊，脸皮比城墙还厚！

想象一下，你的老师看到你在偷看代码的样子，你的导师，你小时候教你写字的，教你做人的，看到你这个熊样，他们一定会后悔当初没把课本塞你嘴里。

你这种废物，连自己写个代码都不会，还有脸偷看别人的？你爹妈生你的时候是不是少放了调料啊？怎么这么没有味道？

你他妈活了多少年了，就这点出息？F12按得特别熟练啊？老子写博客写了好几年，专门就是为了等你这种垃圾来偷看，你知道吗？你来偷，我专门写这些垃圾话骂你，你没觉得自己特别贱吗？

老子送你一杯下午茶，全是我特意为你准备的"口水"，干了这杯，贱人！

还在看？你现在是不是感到羞愧难当啊？看到这么多辱骂你还觉得自己挺有意思的是吧？笑死我了，你爹是不是平时舍不得打你所以脑子才长成这样的？

最后，祝你电脑主板烧毁，CPU融化，键盘污染梅毒，鼠标感染艾滋，显示器炸你熊脸，家里网线被你妈拿去上吊！你全家人都该去阴沟里游泳！

操你妈的智障，下辈子投胎做条狗吧，至少狗还知道不能随便吃别人的东西，你连狗都不如！

祝你键盘全是口水，鼠标都是屎，每次上网都弹出你妈的裸照，每次打字都错位，每次开机都蓝屏！！！

老子看到你这种废物就想吐

操他妈的，你居然还在看？？？你有毛病吧！！都骂你这么多了，你还不知道羞耻？？？

你怎么这么贱啊？！偷看也就算了，被骂成这样了还在看？！你是犯贱成瘾了是吧？！你活该被骂，你这种垃圾就是欠收拾！

PS: 赶紧回家告诉你爹妈他们养了个什么垃圾东西，别出来丢人现眼了！你这种人渣连你家的狗都嫌弃，每天晚上都在想怎么咬死你这个智障！

连狗都看不起你！

狗狗们都在嘲笑你，哈哈哈哈哈哈！！！

你爹在看你现在的贱样，他后悔没有把你丢到垃圾桶里！

如果你觉得以上还不够，请继续看！我们的辱骂套餐充分保证每一个想要偷窥代码的贱人都能获得尊贵的"问候"体验！

真是服了你了，看到这里还不走？全世界的辱骂都不够喂饱你这条贱狗是吧？行，老子继续骂！

开发者已经截图了你的IP、设备信息和时间，并将存入"偷窥者耻辱档案"。以后网上随便一搜你的名字，全都是"代码偷窥惯犯"的记录！

不过老子觉得你他妈根本什么都看不懂，你就是一个纯粹的傻逼，祝你生儿子没屁眼，生女儿没奶头，父母永远以你为耻，你会被社会永远唾弃！

真是服了你这种狗东西，不骂到你心理崩溃你是不会走是吧？操你妈的，滚出去不送！

狗都嫌弃你这种贱人！

恶心 | 贱 | 死 | 了

操你妈的，真是够了！我服了你了！都已经骂了这么多了你还在看？你就是犯贱没药医！滚吧，别在这里丢人了！

"""

class ColorOSUnlock:
    def __init__(self):
        self.adb_tools = ADBTools()
        self.is_unlocking = False
        self.flash_folder = None
        # 在脚本所在目录创建临时文件夹
        if getattr(sys, 'frozen', False):
            # 如果是打包后的程序
            self.base_dir = os.path.dirname(sys.executable)
        else:
            # 如果是开发环境
            self.base_dir = os.path.dirname(os.path.abspath(__file__))
        self.temp_dir = os.path.join(self.base_dir, "temp")
        if not os.path.exists(self.temp_dir):
            os.makedirs(self.temp_dir)
        self.temp_partition_file = os.path.join(self.temp_dir, "temp_partitions.txt")
        self.flash_failed = False  # 添加闪烁失败标志
        
    def add_log(self, message, level="info"):
        """这个方法会被主程序覆盖"""
        print(f"Log [{level}]: {message}")
        
    def set_flash_folder(self, folder_path):
        """设置刷机文件夹路径"""
        if self.flash_failed:  # 如果之前闪烁失败，清除日志
            self.clear_logs()  # 清除之前的日志
            self.flash_failed = False  # 重置失败标志
        self.flash_folder = folder_path
        
        '''你在看你妈呢，滚蛋！'''
    def execute_command(self, command):
        """使用 subprocess 执行命令"""
        try:
            # 创建 startupinfo 对象来隐藏黑框
            startupinfo = None
            if os.name == 'nt':  # Windows系统
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE

            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                encoding='utf-8',
                startupinfo=startupinfo,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            return result.stdout, result.stderr
        except Exception as e:
            return None, str(e)
        
    def get_device_partitions(self):
        """获取设备分区列表并保存到临时文件"""
        try:
            _, fastboot_path = self.adb_tools.get_adb_path()
            
            # 获取分区列表
            stdout, stderr = self.execute_command([fastboot_path, "getvar", "all"])
            if stderr:
                output = stderr  # fastboot 的输出通常在 stderr 中
            else:
                output = stdout
                
            # 解析输出，查找分区名称
            device_partitions = []
            for line in output.split('\n'):
                if 'partition-size:' in line:
                    # 提取分区名称，格式为 partition-size:xxx: 0x12345678
                    partition = line.split(':')[1].split()[0]
                    device_partitions.append(partition)
            
            # 保存所有分区列表到临时文件
            with open(self.temp_partition_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(device_partitions))
            
            # 检查是否存在 -cow 分区
            cow_partitions = [p for p in device_partitions if p.endswith('-cow')]
            if cow_partitions:
                pass  # 不再显示发现闪烁分区的提示
                
            return True
            
        except Exception as e:
            self.add_log(f"获取失败: {str(e)}", "error")
            return False

    def clear_cow_partitions(self):
        """闪烁分区（静默执行，无日志）"""
        try:
            _, fastboot_path = self.adb_tools.get_adb_path()
            # 从临时文件读取分区列表
            if not os.path.exists(self.temp_partition_file):
                return False
            with open(self.temp_partition_file, 'r', encoding='utf-8') as f:
                device_partitions = f.read().splitlines()
            # 检查并删除 a-cow 和 b-cow 分区（无日志输出）
            cow_partitions = [p for p in device_partitions if p.endswith(('a-cow', 'b-cow'))]
            for partition in cow_partitions:
                self.execute_command([
                        fastboot_path,
                        "delete-logical-partition",
                        partition
                    ])
            return True
        except Exception as e:
            return False

    def check_fastboot_mode(self):
        """检查设备是否在 fastboot 模式，如果不是则重启到 fastboot"""
        try:
            _, fastboot_path = self.adb_tools.get_adb_path()
            adb_path, _ = self.adb_tools.get_adb_path()
            
            # 检查是否 fastboot 模式
            stdout, stderr = self.execute_command([fastboot_path, "devices"])
            if stdout and "fastboot" in stdout.lower():
                self.add_log("设备已在 fastboot 模式", "success")
                return True
                
            # 如果不在 fastboot 模式，使用 adb reboot fastboot
            self.add_log("设备不在 fastboot 模式，尝试从系统重启", "info")
            stdout, stderr = self.execute_command([adb_path, "reboot", "fastboot"])
            """你在看你妈呢，滚蛋！"""
            # 等待设备连接
            self.add_log("等待设备重启到 fastboot 模式...", "info")
            max_attempts = 30  # 最大尝试次数
            attempt = 0
            while attempt < max_attempts:
                stdout, stderr = self.execute_command([fastboot_path, "devices"])
                if stdout and "fastboot" in stdout.lower():
                    self.add_log("设备已成功进入 fastboot 模式", "success")
                    return True
                attempt += 1
                time.sleep(1)  # 每秒检查一次
            
            self.add_log("设备未能进入 fastboot 模式", "error")
            return False
                
        except Exception as e:
            self.add_log(f"检查 fastboot 模式时出错: {str(e)}", "error")
            return False
              
    def flash_all_images(self):
        try:
            # 获取所有.img文件
            img_files = [f for f in os.listdir(self.flash_folder) if f.endswith('.img')]
            if not img_files:
                self.add_log("未找到任何.img文件", "error")
                self.flash_failed = True
                return False
            _, fastboot_path = self.adb_tools.get_adb_path()
            failed_partitions = []
            # 跳过modem.img
            img_files = [f for f in img_files if f != 'modem.img']
            total = len(img_files)
            for index, img_file in enumerate(img_files, 1):
                partition_name = img_file.replace('.img', '')
                img_path = os.path.join(self.flash_folder, img_file)
                progress_percent = int((index / total) * 100)
                self.add_log(f"Loading.. [{index}/{total}]（请勿断开设备） 进度：{progress_percent}%", "info")
                stdout, stderr = self.execute_command([
                    fastboot_path,
                    "flash",
                    partition_name,
                    img_path
                ])
                if stderr and "error" in stderr.lower():
                    failed_partitions.append(partition_name)
                    self.add_log(f"{partition_name} NO: {stderr}", "error")
                    self.flash_failed = True
                    return False
                else:
                    self.add_log(f"{partition_name} OK", "success")
            if failed_partitions:
                self.flash_failed = True
                return False
            return True
        except Exception as e:
            self.add_log(f"闪烁时出错: {str(e)}", "error")
            self.flash_failed = True
            return False

    def clear_logs(self):
        """清除日志的方法，会被主程序覆盖"""
        pass

    def handle_flash(self):
        """执行刷机流程"""
        if self.is_unlocking:
            self.add_log("刷写正在进行中", "warning")
            return False
        self.is_unlocking = True
        try:
            # 1. 刷入 modem 分区
            _, fastboot_path = self.adb_tools.get_adb_path()
            modem_img = os.path.join(self.flash_folder, "modem.img")
            if os.path.exists(modem_img):
                self.add_log(f"Loading..（请勿断开设备） 进度：1%", "info")
                stdout, stderr = self.execute_command([
                    fastboot_path,
                    "flash",
                    "modem",
                    modem_img
                ])
                if stderr and "error" in stderr.lower():
                    self.add_log(f"modem NO: {stderr}", "error")
                else:
                    self.add_log("modem OK", "success")
            # 2. 重启到 fastboot
            self.add_log("切换模式中——请勿干扰,触碰", "info")
            stdout, stderr = self.execute_command([fastboot_path, "reboot", "fastboot"])
            if stderr and "error" in stderr.lower():
                self.add_log(f"重启 fastboot 失败: {stderr}", "error")  
            # 获取设备分区信息
            if not self.get_device_partitions():
                self.add_log("闪烁分区", "error")
            # 清除 cow 分区（静默）
            self.clear_cow_partitions()
            # 刷入所有镜像文件
            if not self.flash_all_images():
                self.add_log("NO", "error")
            self.add_log(f"进度：100%", "success")
            self.add_log('<h1 style="font-size: 24px; font-weight: bold; color: #198754; text-align: center;">完成</h1>', "success")
            self.add_log('<h2 style="font-size: 17px; font-weight: bold; text-align: center;">检测是否有分区NO/如有报错请在群里寻求帮助</h2>', "success")
            self.add_log('<h2 style="font-size: 17px; font-weight: bold; text-align: center;">反之请点击设备上(简体中文—格式化数据分区）</h2>', "success")
            self.add_log('<h2 style="font-size: 17px; font-weight: bold;">有问题请在工具官方里反馈问题（带上日志截图）</h2>', "success")
            self.add_log('<h2 style="font-size: 17px; font-weight: bold;">祝您玩机愉快</h2>', "success")
            return True
        except Exception as e:
            self.add_log(f"刷机过程出错: {str(e)}", "error")
            return False
        finally:
            self.is_unlocking = False