import sys
import os
import subprocess
import time
from datetime import datetime
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QPushButton, QTextEdit, QLabel, QMessageBox, QFrame,
                           QHBoxLayout, QFileDialog, QMenu, QDialog, QComboBox, QTextBrowser, QScrollArea)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QPoint, QUrl
from PyQt6.QtGui import QFont, QPixmap, QIcon, QDesktopServices
from mx9p import ADBTools
from wr3j import ColorOS15Function
from bv8k import ColorOSUnlock
from dt5c import PayloadExtractor
from gp4r import CustomFlashDialog
from fn2w import CustomMessageBox
import threading
import traceback
from ly6h import FastbooDTFunction
"""
益民固件刷写工具 - UI界面模块
提供图形化界面用于ColorOS设备的固件刷写和解锁功能
"""
class FlashThread(QThread):
    log_signal = pyqtSignal(str, str)  # 信号：(message, level)
    finished_signal = pyqtSignal(bool)  # 信号：成功/失败

    def __init__(self, flash_function, folder_path):
        super().__init__()
        self.flash_function = flash_function
        self.folder_path = folder_path
        
    def run(self):
        try:
            # 设置刷机文件夹
            self.flash_function.set_flash_folder(self.folder_path)
            
            # 重定向日志输出到信号
            def log_handler(message, level="info"):
                self.log_signal.emit(message, level)
            self.flash_function.add_log = log_handler
            
            # 执行刷机流程
            success = self.flash_function.handle_flash()
            self.finished_signal.emit(success)
            
        except Exception as e:
            self.log_signal.emit(f"刷机过程出错: {str(e)}", "error")
            self.finished_signal.emit(False)

class PayloadThread(QThread):
    log_signal = pyqtSignal(str, str)  # 信号：(message, level)
    finished_signal = pyqtSignal(bool)  # 信号：成功/失败

    def __init__(self, payload_function):
        super().__init__()
        self.payload_function = payload_function
        self.is_running = False
        
    def run(self):
        self.is_running = True
        try:
            # 重定向日志输出到信号
            def log_handler(message, level="info"):
                self.log_signal.emit(message, level)
            self.payload_function.add_log = log_handler
            
            # 执行解包流程
            success = self.payload_function.handle_extract()
            self.finished_signal.emit(success)
            
        except Exception as e:
            self.log_signal.emit(f"解析出错: {str(e)}", "error")
            self.finished_signal.emit(False)
        finally:
            self.is_running = False
            
    def stop(self):
        """安全停止线程"""
        if self.is_running:
            self.is_running = False
            if hasattr(self.payload_function, 'is_extracting'):
                self.payload_function.is_extracting = False
            self.wait()

class DeviceCheckThread(QThread):
    status_signal = pyqtSignal(str, str, str)  # 信号：(状态文本, 标签样式, 框架样式)
    
    def __init__(self, adb_tools):
        super().__init__()
        self.adb_tools = adb_tools
        self.is_running = True
        
    def run(self):
        while self.is_running:
            try:
                # 检查 ADB 工具是否存在
                adb_path, fastboot_path = self.adb_tools.get_adb_path()
                if not os.path.exists(adb_path) or not os.path.exists(fastboot_path):
                    self.status_signal.emit(
                        "ADB工具未找到",
                        "color: #dc3545; font-size: 12px;",
                        "background-color: #f8d7da; border-radius: 6px;"
                    )
                    time.sleep(2)
                    continue

                # 获取设备列表
                devices = self.adb_tools.get_devices()
                if not devices:
                    self.status_signal.emit(
                        "未检测到设备",
                        "color: #87CEEB; font-size: 12px;",
                        "background-color:rgb(150, 137, 145); border-radius: 14px;"
                    )
                else:
                    device_info = []
                    for mode, device in devices:
                        if mode == 'device':
                            device_info.append(f"开机模式: {device}")
                        elif mode == 'recovery':
                            device_info.append(f"恢复模式: {device}")
                        elif mode == 'fastboot':
                            device_info.append(f"Fastboot模式: {device}")
                        elif mode == 'sideload':
                            device_info.append(f"Sideload模式: {device}")
                    
                    status_text = "连接设备:\n" + "\n".join(device_info)
                    self.status_signal.emit(
                        status_text,
                        "color: #660000; font-size: 12px;",
                        "background-color: #99ff99; border-radius: 6px;"
                    )
            except Exception as e:
                self.status_signal.emit(
                    f"检查设备状态出错: {str(e)}",
                    "color: #dc3545; font-size: 12px;",
                    "background-color: #f8d7da; border-radius: 6px;"
                )
            
            time.sleep(2)  # 每2秒检查一次
            
    def stop(self):
            self.is_running = False
            self.wait()

class FlashToolUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.adb_tools = ADBTools()
        self.device_check_thread = None  # 添加设备检查线程引用
        self.should_check_device = True
        self.flash_thread = None
        self.temp_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp")
        self.thread_count = 4
        self.support_info_shown = False
        if not os.path.exists(self.temp_dir):
            os.makedirs(self.temp_dir)
        self.temp_partition_file = os.path.join(self.temp_dir, "temp_partitions.txt")
        self.logic_partition_file = "luojifenqu.txt"
        self.flash_folder = "flash_files"
        
        # 设置无边框窗口
        self.setWindowFlags(self.windowFlags() | Qt.WindowType.FramelessWindowHint)
        # 设置窗口背景透明
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # 添加拖动相关变量
        self.draggable = True
        self.dragging = False
        self.offset = QPoint()
        
        # 显示说明弹窗
        self.show_disclaimer()
        
        self.initUI()
        self.start_device_check()

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton and self.draggable:
            self.dragging = True
            self.offset = event.position().toPoint()

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.dragging and self.draggable:
            self.move(self.mapToGlobal(event.position().toPoint() - self.offset))

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.dragging = False

    def show_disclaimer(self):
        """显示免责声明弹窗"""
        disclaimer_dialog = QDialog(self)
        disclaimer_dialog.setWindowTitle("免责声明")
        disclaimer_dialog.setFixedSize(600, 380)
        # 设置窗口标志，防止大小改变
        disclaimer_dialog.setWindowFlags(
            Qt.WindowType.Dialog | 
            Qt.WindowType.CustomizeWindowHint | 
            Qt.WindowType.WindowTitleHint |
            Qt.WindowType.MSWindowsFixedSizeDialogHint
        )
        disclaimer_dialog.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                border-radius: 12px;
            }
            QLabel {
                color: #212529;
                font-size: 14px;
                margin: 10px;
            }
            QPushButton {
                background-color: #198754;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 12px;
                font-size: 16px;
                min-width: 30px;
                opacity: 1;
            }
            QPushButton:hover {
                background-color: #157347;
                opacity: 1;
            }
            QPushButton:pressed {
                background-color: #146c43;
                opacity: 1;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                opacity: 1;
            }
            QScrollArea {
                background-color: #f8f9fa;
                border: none;
            }
            QScrollBar:vertical {
                border: none;
                background: #f8f9fa;
                width: 8px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #6c757d;
                min-height: 20px;
                border-radius: 12px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)
        
        # 使用 QVBoxLayout 并设置固定间距
        layout = QVBoxLayout(disclaimer_dialog)
        layout.setSpacing(5)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 创建内容容器
        content_widget = QWidget()
        content_widget.setStyleSheet("background-color: #f8f9fa;")
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(5)
        content_layout.setContentsMargins(0, 0, 0, 0)
        
        # 免责声明内容
        disclaimer_text = QLabel("""
        <h2 style="color: #dc3545; text-align: center;">玩机交流群:群①:612495233</h2>  
        <h2 style="color: #dc3545; text-align: center;">玩机交流群:群②:1011704574</h2>
        <h2 style="color: #dc3545; text-align: center;">玩机交流群:群③:908695576</h2>
        <h2 style="color: #dc3545; text-align: center;">玩机交流群:群④:605797071</h2>
        <h2 style="color: #dc3545; text-align: center;">免责声明</h2>                                           
        <p style="font-weight: bold; text-align: center; font-size: 16px;">1. 此工具只限在益民里群使用，反之请勿使用！！</p>
        <p style="font-weight: bold; text-align: center; font-size: 16px;">2. 只支持官方系统降级,升级,救砖</p>
        <p style="font-weight: bold; text-align: center; font-size: 16px;">3. 本工具仅供学习交流使用，请勿用于商业用途。</p>
        <p style="font-weight: bold; text-align: center; font-size: 16px;">5. 使用本工具前请确保已备份重要数据。</p>
        <p style="font-weight: bold; text-align: center; font-size: 16px;">6. 使用本工具造成的任何损失，开发者不承担任何责任。</p>
        <p style="font-weight: bold; text-align: center; font-size: 16px;">7. 使用本工具即表示您已阅读并同意以上条款。</p>
        """)
        disclaimer_text.setWordWrap(True)
        disclaimer_text.setMinimumWidth(400)
        disclaimer_text.setAlignment(Qt.AlignmentFlag.AlignCenter)
        content_layout.addWidget(disclaimer_text)
        
        # 倒计时标签
        countdown_label = QLabel("10秒后自动同意")
        countdown_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        content_layout.addWidget(countdown_label)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.setSpacing(20)
        button_layout.setContentsMargins(0, 10, 0, 0)
        
        # 同意按钮
        agree_button = QPushButton("同意")
        agree_button.setEnabled(False)
        agree_button.setStyleSheet("""
            QPushButton {
                background-color: #198754;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 12px;
                font-size: 16px;
                min-width: 30px;
            }
            QPushButton:hover {
                background-color: #157347;
            }
            QPushButton:pressed {
                background-color: #146c43;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        agree_button.clicked.connect(disclaimer_dialog.accept)
        
        # 不同意按钮
        disagree_button = QPushButton("不同意")
        disagree_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 12px;
                font-size: 16px;
                min-width: 30px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
        """)
        disagree_button.clicked.connect(disclaimer_dialog.reject)
        
        button_layout.addWidget(agree_button)
        button_layout.addWidget(disagree_button)
        content_layout.addLayout(button_layout)
        
        # 设置滚动区域的内容
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
        
        # 设置倒计时
        countdown = 0
        def update_countdown():
            nonlocal countdown
            countdown -= 1
            countdown_label.setText(f"{countdown}秒后自动同意")
            if countdown <= 0:
                timer.stop()
                agree_button.setEnabled(True)
                countdown_label.setText("请选择")
        
        timer = QTimer(disclaimer_dialog)
        timer.timeout.connect(update_countdown)
        timer.start(1000)
        
        # 显示弹窗并等待用户响应
        result = disclaimer_dialog.exec()
        
        # 如果用户点击不同意或关闭窗口，退出程序
        if result != QDialog.DialogCode.Accepted:
            sys.exit(0)

    def initUI(self):
        self.setWindowTitle("益民工具箱")
        self.setFixedSize(1100, 700)
        # 不再给QMainWindow设置背景色和圆角
        # self.setStyleSheet( ... )  # 删除或注释掉原QMainWindow样式
        
        # 设置窗口图标
        try:
            # 检查是否是打包后的环境
            if getattr(sys, 'frozen', False):
                # 打包后的环境
                application_path = sys._MEIPASS
            else:
                # 开发环境
                application_path = os.path.dirname(os.path.abspath(__file__))
            
            icon_path = os.path.join(application_path, "ico", "icon.ico")
            if os.path.exists(icon_path):
                icon = QIcon(icon_path)
                self.setWindowIcon(icon)
        except Exception as e:
            print(f"设置图标时出错: {str(e)}")
        
        central_widget = QWidget()
        # 只给centralWidget设置圆角和背景色
        central_widget.setStyleSheet("""
            background-color: rgb(185, 11, 69);
            border-radius: 18px;
        """)
        self.setCentralWidget(central_widget)
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(25, 25, 25, 25)
        
        # 左侧功能面板
        left_panel = QFrame()
        left_panel.setStyleSheet("""
            QFrame {
                background-color: #dc3545;
                border-radius: 12px;
                border: 2px solid #c82333;
            }
        """)
        left_panel.setFixedWidth(245)
        left_layout = QVBoxLayout(left_panel)
        left_layout.setSpacing(3)
        left_layout.setContentsMargins(6, 8, 6, 8)
        
        # 设备状态显示
        self.device_status_frame = QFrame()
        self.device_status_frame.setFixedHeight(80)  # 固定高度
        self.device_status_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-radius: 12px;
            }
        """)
        device_status_layout = QVBoxLayout(self.device_status_frame)
        device_status_layout.setSpacing(2)  # 减小状态标签之间的间距
        device_status_layout.setContentsMargins(0, 0, 0, 0)  # 移除
        
        self.device_status_title = QLabel("设备状态")
        self.device_status_title.setStyleSheet("""
            color: ff7300;
            font-size: 13px;
            font-weight: bold;
        """)
        self.device_status_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.device_status_label = QLabel("检测设备...")
        self.device_status_label.setFixedHeight(40)  # 固定状态文本高度
        self.device_status_label.setStyleSheet("""
            color: ff7300;
            font-size: 12px;
        """)
        self.device_status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.device_status_label.setWordWrap(True)
        
        device_status_layout.addWidget(self.device_status_title)
        device_status_layout.addWidget(self.device_status_label)
        left_layout.addWidget(self.device_status_frame)
        
        # 添加分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet("background-color: #c82333; margin: 3px 0;")
        left_layout.addWidget(separator)

        # 真我功能组
        realme_title = QLabel("欧加真.仅支持骁龙8+及上的机型")
        realme_title.setStyleSheet("""
            color: white;
            font-size: 14px;
            font-weight: bold;
            padding: 2px;
            background-color: white;
            border-radius: 12px;
            color: #dc3545;
        """)
        left_layout.addWidget(realme_title)

        # 真按钮样式
        button_style = """
            QPushButton {
                background-color: #c82333;
                border: 1px solid #bd2130;
                color: white;
                padding: 5px;
                font-size: 12px;
                font-weight: bold;
                border-radius: 12px;
                text-align: center;
                margin: 1px 0;
            }
            QPushButton:hover {
                background-color: #bd2130;
                border-color: #b21f2d;
            }
            QPushButton:pressed {
                background-color: #b21f2d;
                border-color: #a71f2d;
            }
            QPushButton:disabled {
                background-color: #e17381;
                border-color: #e17381;
                color: #eeeeee;
            }
        """

        # 支持作者按钮（蓝色）
        support_button_style = """
            QPushButton {
                background-color: #2468F2;
                border: 1px solid #2055CC;
                color: white;
                padding: 5px;
                font-size: 12px;
                font-weight: bold;
                border-radius: 12px;
                text-align: center;
                margin: 1px 0;
            }
            QPushButton:hover {
                background-color: #2055CC;
                border-color: #1B47B3;
            }
            QPushButton:pressed {
                background-color: #1B47B3;
                border-color: #163C99;
            }
            QPushButton:disabled {
                background-color: #92B3F9;
                border-color: #92B3F9;
                color: #eeeeee;
            }
        """

        # ColorOS升降按钮（粉红色）
        coloros_button_style = """
            QPushButton {
                background-color: #F24983;
                border: 1px solid #CC3E6E;
                color: white;
                padding: 5px;
                font-size: 12px;
                font-weight: bold;
                border-radius: 12px;
                text-align: center;
                margin: 1px 0;
            }
            QPushButton:hover {
                background-color: #CC3E6E;
                border-color: #B33660;
            }
            QPushButton:pressed {
                background-color: #B33660;
                border-color: #992E52;
            }
            QPushButton:disabled {
                background-color: #F9A4C1;
                border-color: #F9A4C1;
                color: #eeeeee;
            }
        """

        # td线刷按钮（紫色）
        force_unlock_button_style = """
            QPushButton {
                background-color: #8250DF;
                border: 1px solid #6E44BC;
                color: white;
                padding: 5px;
                font-size: 12px;
                font-weight: bold;
                border-radius: 12px;
                text-align: center;
                margin: 1px 0;
            }
            QPushButton:hover {
                background-color: #6E44BC;
                border-color: #5F3BA3;
            }
            QPushButton:pressed {
                background-color: #5F3BA3;
                border-color: #50328A;
            }
            QPushButton:disabled {
                background-color: #C1A7EF;
                border-color: #C1A7EF;
                color: #eeeeee;
            }
        """

        # Payload解包按钮（橙色）
        payload_button_style = """
            QPushButton {
                background-color: #F28C48;
                border: 1px solid #CC763C;
                color: white;
                padding: 5px;
                font-size: 12px;
                font-weight: bold;
                border-radius: 12px;
                text-align: center;
                margin: 1px 0;
            }
            QPushButton:hover {
                background-color: #CC763C;
                border-color: #B36734;
            }
            QPushButton:pressed {
                background-color: #B36734;
                border-color: #995A2D;
            }
            QPushButton:disabled {
                background-color: #F9C5A4;
                border-color: #F9C5A4;
                color: #eeeeee;
            }
        """

        # 清除数据按钮（紫色）
        clear_data_button_style = """
            QPushButton {
                background-color: #8250DF;
                border: 1px solid #6E44BC;
                color: white;
                padding: 5px;
                font-size: 12px;
                font-weight: bold;
                border-radius: 12px;
                text-align: center;
                margin: 1px 0;
            }
            QPushButton:hover {
                background-color: #6E44BC;
                border-color: #5F3BA3;
            }
            QPushButton:pressed {
                background-color: #5F3BA3;
                border-color: #50328A;
            }
            QPushButton:disabled {
                background-color: #C1A7EF;
                border-color: #C1A7EF;
                color: #eeeeee;
            }
        """

        # 高级重启按钮（绿色）
        reboot_button_style = """
            QPushButton {
                background-color: #2EA043;
                border: 1px solid #278838;
                color: white;
                padding: 5px;
                font-size: 12px;
                font-weight: bold;
                border-radius: 12px;
                text-align: center;
                margin: 1px 0;
            }
            QPushButton:hover {
                background-color: #278838;
                border-color: #22762F;
            }
            QPushButton:pressed {
                background-color: #22762F;
                border-color: #1D6428;
            }
            QPushButton:disabled {
                background-color: #97D0A1;
                border-color: #97D0A1;
                color: #eeeeee;
            }
        """

        # 支持作者按钮
        self.support_btn = QPushButton("说明")
        self.support_btn.setStyleSheet(support_button_style)
        self.support_btn.setFixedHeight(35)
        left_layout.addWidget(self.support_btn)
        # --- 新增：刷机包下载按钮 ---
        self.download_btn = QPushButton("不限速刷机包下载")
        self.download_btn.setStyleSheet(force_unlock_button_style)
        self.download_btn.setFixedHeight(35)
        self.download_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.download_btn.clicked.connect(lambda: QDesktopServices.openUrl(QUrl("https://alist.yinbl.cn")))
        left_layout.addWidget(self.download_btn)

        self.payload_unpack_btn = QPushButton("全量包分解")
        self.payload_unpack_btn.setStyleSheet(payload_button_style)
        self.payload_unpack_btn.setFixedHeight(35)  # 设置固定高度
        left_layout.addWidget(self.payload_unpack_btn)

        # ColorOS升降按钮（粉红色）
        self.coloros15_btn = QPushButton("ColorOS升降 ")
        self.coloros15_btn.setStyleSheet(coloros_button_style)
        self.coloros15_btn.setFixedHeight(35)  # 设置固定高度
        left_layout.addWidget(self.coloros15_btn)

        # 新增 fastbooDT 按钮
        self.fastboodt_btn = QPushButton("FastbooDT修复")
        self.fastboodt_btn.setStyleSheet(coloros_button_style)
        self.fastboodt_btn.setFixedHeight(35)
        left_layout.addWidget(self.fastboodt_btn)

        self.force_unlock_btn = QPushButton("安卓通用线刷")
        self.force_unlock_btn.setStyleSheet(force_unlock_button_style)
        self.force_unlock_btn.setFixedHeight(35)  # 设置固定高度
        left_layout.addWidget(self.force_unlock_btn)

        

        # 添加分隔线
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.Shape.HLine)
        separator2.setStyleSheet("background-color: #c82333; margin: 3px 0;")
        left_layout.addWidget(separator2)

        # 通用功能组
        common_title = QLabel("通用功能")
        common_title.setStyleSheet("""
            color: white;
            font-size: 16px;
            font-weight: bold;
            padding: 2px;
            background-color: white;
            border-radius: 12px;
            color: #dc3545;
        """)
        left_layout.addWidget(common_title)

        # 通用功能按钮
        self.clear_data_btn = QPushButton("清除数据")
        self.clear_data_btn.setStyleSheet(clear_data_button_style)
        self.clear_data_btn.setFixedHeight(35)  # 设置固定高度
        left_layout.addWidget(self.clear_data_btn)

        self.reboot_btn = QPushButton("高级重启")
        self.reboot_btn.setStyleSheet(reboot_button_style)
        self.reboot_btn.setFixedHeight(35)  # 设置固定高度
        left_layout.addWidget(self.reboot_btn)

        # 自定义刷写按钮（红色）
        custom_flash_button_style = """
            QPushButton {
                background-color: #ff3333;
                border: 1px solid #cc2929;
                color: white;
                padding: 5px;
                font-size: 12px;
                font-weight: bold;
                border-radius: 12px;
                text-align: center;
                margin: 1px 0;
            }
            QPushButton:hover {
                background-color: #cc2929;
                border-color: #b32424;
            }
            QPushButton:pressed {
                background-color: #b32424;
                border-color: #991f1f;
            }
            QPushButton:disabled {
                background-color: #ff9999;
                border-color: #ff9999;
                color: #eeeeee;
            }
        """

        self.custom_flash_btn = QPushButton("自定义刷写")
        self.custom_flash_btn.setStyleSheet(custom_flash_button_style)
        self.custom_flash_btn.setFixedHeight(35)  # 设置固定高度
        left_layout.addWidget(self.custom_flash_btn)

        # 优质流量卡按钮（黄色）
        traffic_card_button_style = """
            QPushButton {
                background-color: #FFD700;
                border: 1px solid #DAA520;
                color: #000000;
                padding: 5px;
                font-size: 12px;
                font-weight: bold;
                border-radius: 12px;
                text-align: center;
                margin: 1px 0;
            }
            QPushButton:hover {
                background-color: #DAA520;
                border-color: #B8860B;
            }
            QPushButton:pressed {
                background-color: #B8860B;
                border-color: #996515;
            }
            QPushButton:disabled {
                background-color: #FFE4B5;
                border-color: #FFE4B5;
                color: #666666;
            }
        """

        self.traffic_card_btn = QPushButton("正规/优质流量/电话卡")
        self.traffic_card_btn.setStyleSheet(traffic_card_button_style)
        self.traffic_card_btn.setFixedHeight(35)  # 设置固定高度
        left_layout.addWidget(self.traffic_card_btn)

        # 更多功能按钮（紫色）
        more_features_button_style = """
            QPushButton {
                background-color: #9B59B6;
                border: 1px solid #8E44AD;
                color: white;
                padding: 5px;
                font-size: 12px;
                font-weight: bold;
                border-radius: 12px;
                text-align: center;
                margin: 1px 0;
            }
            QPushButton:hover {
                background-color: #8E44AD;
                border-color: #7D3C98;
            }
            QPushButton:pressed {
                background-color: #7D3C98;
                border-color: #6C3483;
            }
            QPushButton:disabled {
                background-color: #D2B4DE;
                border-color: #D2B4DE;
                color: #666666;
            }
        """

        self.more_features_btn = QPushButton("更多功能")
        self.more_features_btn.setStyleSheet(more_features_button_style)
        self.more_features_btn.setFixedHeight(35)  # 设置固定高度
        left_layout.addWidget(self.more_features_btn)

        # 添加分隔线
        separator4 = QFrame()
        separator4.setFrameShape(QFrame.Shape.HLine)
        separator4.setStyleSheet("background-color: #c82333; margin: 3px 0;")
        left_layout.addWidget(separator4)


        # 连接按钮信号
        self.coloros15_btn.clicked.connect(self.handle_coloros15)
        self.fastboodt_btn.clicked.connect(self.handle_fastboodt)
        self.force_unlock_btn.clicked.connect(self.handle_force_unlock)
        self.payload_unpack_btn.clicked.connect(self.handle_payload_unpack)
        self.clear_data_btn.clicked.connect(self.handle_clear_data)
        self.reboot_btn.clicked.connect(self.handle_reboot)
        self.support_btn.clicked.connect(self.handle_support)
        self.custom_flash_btn.clicked.connect(self.handle_custom_flash)
        self.traffic_card_btn.clicked.connect(self.handle_traffic_card)
        self.more_features_btn.clicked.connect(self.handle_more_features)

        left_layout.addStretch()
        main_layout.addWidget(left_panel)
        
        # 右侧要内容区域
        right_panel = QFrame()
        right_panel.setStyleSheet("""
            QFrame {
                background-color: #dc3545;
                border-radius: 12px;
            }
        """)
        right_layout = QVBoxLayout(right_panel)
        right_layout.setSpacing(5)  # 进一步减小间距
        right_layout.setContentsMargins(5, 5, 5, 5)  # 减小边距
        
        # 标题和副标题放在一个水平布局中
        header_frame = QFrame()
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(0, 0, 0, 0)
        
        # 标题部分
        title_container = QVBoxLayout()
        title_container.setSpacing(0)
        
        title_label = QLabel("益民ColorOS 升降助手(禁止使用此工具收费代刷)")
        title_label.setFont(QFont("Microsoft YaHei UI", 20, QFont.Weight.Bold))
        title_label.setStyleSheet("color: white;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        
        subtitle_label = QLabel(" 此工具是公益，让每个人都能体会到刷机的乐趣，自此永远相信美好的事情即将发生！")
        subtitle_label.setFont(QFont("Microsoft YaHei UI", 10))
        subtitle_label.setStyleSheet("color: white;")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        
        title_container.addWidget(title_label)
        title_container.addWidget(subtitle_label)
        header_layout.addLayout(title_container)
        
        # 添加窗口控制按钮
        button_container = QHBoxLayout()
        button_container.setSpacing(5)
        
        # 最小化按钮
        self.minimize_btn = QPushButton("—")
        self.minimize_btn.setFixedSize(30, 30)
        self.minimize_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: white;
                border: none;
                font-size: 20px;
                font-weight: bold;
                padding: 0;
                margin: 0;
            }
            QPushButton:hover {
                background-color: rgba(189, 190, 212, 0.1);
                border-radius: 4px;
            }
            QPushButton:pressed {
                background-color: rgba(7, 42, 240, 0.2);
                border-radius: 4px;
            }
        """)
        self.minimize_btn.clicked.connect(self.showMinimized)
        
        # 关闭按钮
        self.close_btn = QPushButton("×")
        self.close_btn.setFixedSize(40, 40)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: white;
                border: none;
                font-size: 20px;
                font-weight: bold;
                padding: 0;
                margin: 0;
            }
            QPushButton:hover {
                background-color:rgb(41, 12, 202);
                border-radius: 4px;
            }
            QPushButton:pressed {
                background-color:rgb(243, 239, 5);
                border-radius: 4px;
            }
        """)
        self.close_btn.clicked.connect(self.close)
        
        button_container.addWidget(self.minimize_btn)
        button_container.addWidget(self.close_btn)
        header_layout.addLayout(button_container)
        
        right_layout.addWidget(header_frame)
        
        # 主要内容区域（日志区）
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background-color:rgba(226, 35, 76, 0.97);
                border-radius: 12px;
                border: 2px solidrgb(255, 146, 141);
                padding: 10px;
            }
        """)
        content_layout = QVBoxLayout(content_frame)
        content_layout.setSpacing(2)  # 最小化内部间距
        content_layout.setContentsMargins(5, 5, 5, 5)  # 减小内边距
        
        # 日志标题和清除按钮放在一行
        log_header = QHBoxLayout()
        
        
        
        # 日志域
        self.log_text = QTextBrowser()
        self.log_text.setReadOnly(True)
        self.log_text.setMinimumHeight(450)
        self.log_text.setOpenExternalLinks(True)
        self.log_text.setStyleSheet("""
            QTextBrowser {
                background-color: #ffeaea;
                border: 1px solid #FF928D;
                border-radius: 12px;
                padding: 8px;
                font-family: "Microsoft YaHei UI";
                font-size: 12px;
                color: #212529;
            }
            QTextBrowser a {
                color: #0d6efd;
                text-decoration: none;
            }
            QTextBrowser a:hover {
                color: #0a58ca;
                text-decoration: underline;
            }
            QScrollBar:vertical {
                border: none;
                background: #ffeaea;
                width: 8px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #FF928D;
                min-height: 20px;
                border-radius: 4px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)
        content_layout.addWidget(self.log_text)
        
        right_layout.addWidget(content_frame, 1)
        main_layout.addWidget(right_panel, 1)

    def start_device_check(self):
        """启动设备状态检查线程"""
        if not self.device_check_thread:
            self.device_check_thread = DeviceCheckThread(self.adb_tools)
            self.device_check_thread.status_signal.connect(self.update_device_status)
            self.device_check_thread.start()

    def stop_device_check(self):
        """停止设备状态检查"""
        self.should_check_device = False
        if self.device_check_thread:
            self.device_check_thread.stop()
            self.device_check_thread = None
        self.device_status_label.setText("载入刷机")
        self.device_status_label.setStyleSheet("""
            color: #6c757d;
            font-size: 12px;
        """)
        self.device_status_frame.setStyleSheet("""
            QFrame {
                background-color: #33cc00;
                border-radius: 12px;
            }
        """)

    def start_device_check(self):
        """启动设备状态检查"""
        self.should_check_device = True
        if not self.device_check_thread:
            self.device_check_thread = DeviceCheckThread(self.adb_tools)
            self.device_check_thread.status_signal.connect(self.update_device_status)
            self.device_check_thread.start()

    def update_device_status(self, status_text, label_style, frame_style):
        """更新设备状态显示"""
        if self.should_check_device:
                self.device_status_label.setText(status_text)
                self.device_status_label.setStyleSheet(label_style)
                self.device_status_frame.setStyleSheet(f"""
                QFrame {{
                    {frame_style}
                }}
                """)

    def on_flash_finished(self, success):
        """刷机完成的回调函数"""

        # 启用所有按钮
        self.enable_all_buttons()

        # 恢复设备状态检查
        self.start_device_check()

        # 显示刷机结果
        if success:
            self.add_log("刷机操作完成", "success")
        else:
            self.add_log("刷机操作结束", "warning")

    def disable_all_buttons(self):
        """禁用所有按钮"""
        for button in self.findChildren(QPushButton):
            button.setEnabled(False)
            
    def enable_all_buttons(self):
        """启用所有按钮"""
        for button in self.findChildren(QPushButton):
            button.setEnabled(True)

    def clear_support_info(self):
        """清除支持作者信息"""
        if self.support_info_shown:
            self.log_text.clear()
            self.support_info_shown = False

    def handle_coloros15(self):
        """处理 ColorOS 升降 按钮点击"""
        self.clear_support_info()  # 清除支持作者信息
        # 弹出刷机注意事项对话框
        warning_dialog = FlashWarningDialog(self)
        if warning_dialog.exec() != QDialog.DialogCode.Accepted:
            self.add_log("已取消刷机操作", "warning")
            return
        try:
            self.add_log("开始 ColorOS 升降 刷机", "info")
            
            # 1. 获取 fastboot 工具路径
            _, fastboot_path = self.adb_tools.get_adb_path()
            if not os.path.exists(fastboot_path):
                self.add_log("Fastboot未找到", "error")
                return
            
            # 2. 创建 startupinfo 对象来隐藏黑框（Windows特定）
            startupinfo = None
            if os.name == 'nt':  # Windows系统
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
            
            # 3. 检查设备状态
            try:
                result = subprocess.run(
                    [fastboot_path, "devices"],
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    timeout=5,
                    startupinfo=startupinfo,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )
                if not result.stdout or 'fastboot' not in result.stdout.lower():
                    self.add_log("设备不在 bootloader 模式，请先进入 bootloader 模式", "error")
                    return
            except Exception as e:
                self.add_log(f"检查设备失败: {str(e)}", "error")
                return
            
            # 创建文件选择对话框
            folder_path = QFileDialog.getExistingDirectory(
                self,
                "选择刷机文件所在文件夹",
                "",
                QFileDialog.Option.ShowDirsOnly
            )
            
            if not folder_path:
                self.add_log("未选择刷机文件夹，操作取消", "warning")
                return
            
            # 检查选择的文件夹中是否有.img文件
            img_files = [f for f in os.listdir(folder_path) if f.endswith('.img')]
            if not img_files:
                self.add_log(f"在选择的文件夹中未找到.img文件: {folder_path}", "error")
                self.add_log("请确保选择了正确的刷机文件夹", "warning")
                all_files = os.listdir(folder_path)
                self.add_log(f"文件夹中的文件: {', '.join(all_files)}", "info")
                return
            else:
                self.add_log(f"找到 {len(img_files)} 个.img文件", "success")
                
            
            # 停止设备状态检查
            self.stop_device_check()
            
            # 创建 ColorOS 15 刷机功能实例
            flash_function = ColorOS15Function()
            flash_function.add_log = self.add_log  # 设置日志函数
            flash_function.flash_folder = folder_path  # 直接设置文件夹路径，不调用 set_flash_folder
            
            # 创建并启动刷机线程
            self.flash_thread = FlashThread(flash_function, folder_path)
            self.flash_thread.log_signal.connect(self.add_log)
            self.flash_thread.finished_signal.connect(self.on_flash_finished)
            self.flash_thread.start()
            
        except Exception as e:
            self.add_log(f"刷机过程出错: {str(e)}", "error")
            # 恢复设备检查
            self.start_device_check()

    def handle_force_unlock(self):
        """处理强解按钮点击事件"""
        self.clear_support_info()  # 清除支持作者信息
        try:
            self.add_log("开始线刷", "info")
            
            # 1. 获取 fastboot 工具路径
            _, fastboot_path = self.adb_tools.get_adb_path()
            if not os.path.exists(fastboot_path):
                self.add_log("Fastboot 工具未找到", "error")
                return
            
            # 2. 创建 startupinfo 对象来隐藏黑框（Windows特定）
            startupinfo = None
            if os.name == 'nt':  # Windows系统
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
            
            # 3. 检查设备状态
            try:
                result = subprocess.run(
                    [fastboot_path, "devices"],
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    timeout=5,
                    startupinfo=startupinfo,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )
                if not result.stdout or 'fastboot' not in result.stdout.lower():
                    self.add_log("设备不在bootloader模式，请先进入bootloader模式", "error")
                    return
            except Exception as e:
                self.add_log(f"检查设备失败: {str(e)}", "error")
                return
            
            # 创建文件选择对话框
            folder_path = QFileDialog.getExistingDirectory(
                self,
                "选择刷机文件所在文件夹",
                "",
                QFileDialog.Option.ShowDirsOnly
            )
            
            if not folder_path:
                self.add_log("未选择刷机文件夹，操作取消", "warning")
                return
            
            # 检查选择的文件夹中是否有.img文件
            img_files = [f for f in os.listdir(folder_path) if f.endswith('.img')]
            if not img_files:
                self.add_log(f"在选择的文件夹中未找到.img文件: {folder_path}", "error")
                self.add_log("请确保选择了正确的刷机文件夹", "warning")
                all_files = os.listdir(folder_path)
                self.add_log(f"文件夹中的文件: {', '.join(all_files)}", "info")
                return
            else:
                self.add_log(f"找到 {len(img_files)} 个.img文件", "success")

            
            # 停止设备状态检查
            self.stop_device_check()
            
            # 创建强解功能实例
            unlock_function = ColorOSUnlock()
            unlock_function.add_log = self.add_log  # 使用 UI 的日志函数
            
            # 创建并启动强解线程
            self.flash_thread = FlashThread(unlock_function, folder_path)
            self.flash_thread.log_signal.connect(self.add_log)
            self.flash_thread.finished_signal.connect(self.on_flash_finished)
            self.flash_thread.start()
            
        except Exception as e:
            self.add_log(f"强解过程出错: {str(e)}", "error")
            # 恢复设备检查
            self.start_device_check()

    def handle_clear_data(self):
        """处理清除数据按钮点击"""
        self.clear_support_info()  # 清除支持作者信息
        try:
            self.add_log("开始清除数据", "info")
            
            # 1. 获取 fastboot 工具路径
            _, fastboot_path = self.adb_tools.get_adb_path()
            if not os.path.exists(fastboot_path):
                self.add_log("Fastboot 工具未找到", "error")
                return False
            
            # 2. 创建 startupinfo 对象来隐藏黑框（Windows特定）
            startupinfo = None
            if os.name == 'nt':  # Windows系统
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
            
            # 3. 检查设备状态
            try:
                result = subprocess.run(
                    [fastboot_path, "devices"],
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    timeout=5,
                    startupinfo=startupinfo,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )
                if not result.stdout or 'fastboot' not in result.stdout.lower():
                    self.add_log("设备不在 fastboot 模式，请先进入 fastboot 模式", "error")
                    return False
            except Exception as e:
                self.add_log(f"检查设备状态失败: {str(e)}", "error")
                return False
            
            # 4. 执行清除数据命令
            self.add_log("正在恢复出厂设置...", "info")
                
            # 执行 fastboot -w 命令
            try:
                subprocess.Popen(
                    [fastboot_path, "-w"],
                    startupinfo=startupinfo,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )
                self.add_log("okay", "info")
            except Exception as e:
                self.add_log(f"失败: {str(e)}", "error")
                return False
            
            # 执行 fastboot erase userdata
            try:
                subprocess.Popen(
                    [fastboot_path, "erase", "userdata"],
                    startupinfo=startupinfo,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )
                self.add_log("okay", "info")
            except Exception as e:
                self.add_log(f"失败: {str(e)}", "error")
                return False
                
            # 执行 fastboot erase metadata
            try:
                subprocess.Popen(
                    [fastboot_path, "erase", "metadata"],
                    startupinfo=startupinfo,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )
                self.add_log("okay", "info")
            except Exception as e:
                self.add_log(f"失败: {str(e)}", "error")
                return False
            
            self.add_log("清除完成", "info")
            return True
            
        except Exception as e:
            self.add_log(f"清除数据时出错: {str(e)}", "error")
            return False

    def handle_reboot(self):
        """处理高级重启按钮点击"""
        self.clear_support_info()  # 清除支持作者信息
        try:
            # 先检查设备状态
            status_text, _ = self.adb_tools.check_device_status()
            if status_text == "未连接":
                self.add_log("设备未连接，请检查设备连接后重试", "error")
                return

            # 创建菜单
            menu = QMenu(self)
            menu.setStyleSheet("""
                QMenu {
                    background-color: #f8f9fa;
                    border: 1px solid #e9ecef;
                    border-radius: 12px;
                    padding: 5px;
                }
                QMenu::item {
                    padding: 8px 20px;
                    color: white;
                    background-color: #2EA043;
                    border: 1px solid #278838;
                    border-radius: 12px;
                    margin: 3px 5px;
                    font-size: 12px;
                    font-weight: bold;
                }
                QMenu::item:selected {
                    background-color: #278838;
                    border-color: #22762F;
                }
                QMenu::item:pressed {
                    background-color: #22762F;
                    border-color: #1D6428;
                }
            """)
            
            # 根据设备状态添加合适的菜单项
            if 'fastboot' in status_text.lower():
                # fastboot 模式的选项
                reboot_system = menu.addAction("重启系统")
                reboot_bootloader = menu.addAction("重启Bootloader")
                reboot_fastboot = menu.addAction("重启Fastboot")
            else:
                # 正常模式下的选项
                reboot_system = menu.addAction("重启系统")
                reboot_bootloader = menu.addAction("重启Bootloader")
                reboot_fastboot = menu.addAction("重启Fastboot")

            # 获取按钮位置
            button_pos = self.reboot_btn.mapToGlobal(self.reboot_btn.rect().bottomLeft())
            
            # 显示菜单
            action = menu.exec(button_pos)
            
            if action == reboot_system:
                self.add_log("重启系统...", "info")
                if 'fastboot' in status_text.lower():
                    self.execute_reboot_command(["fastboot", "reboot"])
                else:
                    self.execute_reboot_command(["adb", "reboot"])
            elif action == reboot_bootloader:
                self.add_log("重启 Bootloader...（这才是刷机模式）", "info")
                if 'fastboot' in status_text.lower():
                    self.execute_reboot_command(["fastboot", "reboot-bootloader"])
                else:
                    self.execute_reboot_command(["adb", "reboot", "bootloader"])
            elif action == reboot_fastboot:  # 在任何模式下都可用
                self.add_log("重启 Fastboot...（这不是刷机模式）", "info")
                if 'fastboot' in status_text.lower():
                    self.execute_reboot_command(["fastboot", "reboot", "fastboot"])
                else:
                    self.execute_reboot_command(["adb", "reboot", "fastboot"])

        except Exception as e:
            self.add_log(f"重启出错: {str(e)}", "error")

    def handle_support(self):
        if self.support_info_shown:
            return
        try:
            # 获取图片路径
            if getattr(sys, 'frozen', False):
                # 如果是打包后的程序
                base_path = os.path.join(sys._MEIPASS, "tup")
            else:
                # 如果是开发环境
                base_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "tup")
            
            img1_path = os.path.join(base_path, "1.png")
            img2_path = os.path.join(base_path, "2.png")

            # 清空现有日志
            self.log_text.clear()

            # 在日志区显示作者信息
            self.add_log('<h2 style="color: #198754; font-size: 24px; margin: 10px 0; text-align: center;">—————— 特别感谢 ——————</h2>', "info")
            self.add_log('<p style="font-size: 17px; font-weight: bold; text-align: center;">酷安:小帅当然 _ 酷安:猫丶羽雫</p>', "info")    
            self.add_log('<h2 style="color: #198754; font-size: 24px; margin: 10px 0; text-align: center;">以下两位是公益贡献者，可支持赞助一下，提供的不限速下载</h2>', "info")
            self.add_log('<h2 style="color: #198754; font-size: 24px; margin: 10px 0; text-align: center;">感谢酷安:小苏小白——提供的rom网站-群主</h2>', "info")
            self.add_log('<p style="font-size: 18px; font-weight: bold; text-align: center"><a href="https://alist.yinbl.cn">刷机包,不限速下载</a></p>', "info")
            self.add_log('<h2 style="color: #198754; font-size: 24px; margin: 10px 0; text-align: center;">感谢酷安:空白没有输——提供的rom网站-管理</h2>', "info")
            self.add_log('<p style="font-size: 18px; font-weight: bold; text-align: center"><a href="https://alist.265011.xyz">这里有丰富的玩机资源</a></p>', "info")
            self.add_log('<p style="font-size: 18px; font-weight: bold; text-align: center">维护不易如果本软件对你有帮助，可支持作者</p>', "info")
            # 显示支付二维码（并排）
            if os.path.exists(img1_path) and os.path.exists(img2_path):
                self.add_log('<p style="font-size: 18px; font-weight: bold; color: #dc3545; text-align: center">打赏二维码：</p>', "info")
            self.add_log("", "info", [img1_path, img2_path])
            self.add_log('<p style="font-size: 18px; font-weight: bold; text-align: center">酷安：益民工具箱</p>', "info")
            self.add_log('<p style="font-size: 18px; font-weight: bold; text-align: center">QQ群:605797071(主体),1011704574,908695576,605797071</p>', "info")
            self.add_log('<h2 style="color: #198754; font-size: 24px; margin: 10px 0; text-align: center;">—— 优质流量卡_电话卡 ——</h2>', "info")
            self.add_log('<p style="font-size: 18px; font-weight: bold; text-align: center">(重点)!首月白嫖(无费用),用最少的钱,体验最好的卡</p>', "info")
            self.add_log('<p style="font-size: 18px; font-weight: bold; text-align: center">各大运营商都有任你挑选</p>', "info")
            self.add_log('<p style="font-size: 18px; font-weight: bold; text-align: center"><a href="https://h5.lot-ml.com/ProductEn/Index/b9397fb387c14d79">点击获取</a></p>', "info")
            self.add_log('<h2 style="color: #198754; font-size: 24px; margin: 10px 0; text-align: center;">——————功能说明——————</h2>', "info")
            self.add_log('<p style="font-size: 18px; font-weight: bold; color: #dc3545;text-align: center">1. ColorOS 升级仅支持真我,OPPO,一加(骁龙8+)及以上;(天机8100)及以上(一加）</p>', "info")
            self.add_log('<p style="font-size: 18px; font-weight: bold; text-align: center">2.注意;不支持第三方系统，只针对官方系统降级.</p>', "info")
            self.add_log('<p style="font-size: 18px; font-weight: bold; text-align: center">3. 先使用全量包解包(选中全量包zip)会自行解析</p>', "info")
            self.add_log('<p style="font-size: 18px; font-weight: bold; text-align: center">4. 解析完成后，选择升降机功能,选中img文件夹(原zip路径)</p>', "info")
            self.add_log('<p style="font-size: 18px; font-weight: bold; text-align: center">5. ColorOS升降(bootloader模式使用)</p>', "info")
            self.add_log('<p style="font-size: 18px; font-weight: bold; text-align: center">6. 安卓通用线刷(bootloader模式使用)(请进群询问管理支持型号)</p>', "info")
            self.add_log('<h2 style="color: #dc3545; font-size: 24px; margin: 10px 0; text-align: center;">—————— 注意事项 ——————</h2>', "info")
            self.add_log('<p style="font-size: 18px; font-weight: bold; color: #dc3545; text-align: center">1. 使用此工具务必解锁BL锁;没解锁无法使用此工具</p>', "info")
            self.add_log('<p style="font-size: 18px; font-weight: bold; color: #dc3545; text-align: center">2. 刷机有风险，请务必做好数据备份</p>', "info")
            self.add_log('<p style="font-size: 18px; font-weight: bold; color: #dc3545; text-align: center">3. 请确保使用正确的官方（全量包）</p>', "info")
            self.add_log('<h2 style="color: #dc3545; font-size: 24px; margin: 10px 0; text-align: center;">—————— 免责声明 ——————</h2>', "info")
            self.add_log('<p style="font-size: 18px; font-weight: bold; color: #dc3545; text-align: center">请勿用于商业用途。用本软件造成的任何损失，不承担任何责任。</p>', "info")
            self.support_info_shown = True  # 设置显示标志
            # 将滚动条设置到顶部
            scrollbar = self.log_text.verticalScrollBar()
            scrollbar.setValue(0)
            
        except Exception as e:
            self.add_log(f"显示时出错: {str(e)}", "error")

    def add_log(self, message, level="info", image_paths=None):
        """添加日志
        level: info, success, warning, error
        image_paths: 可选的图片路径，可以是单个路径或路径列表
        """
        color_map = {
            "info": "#495057",      # 深灰色
            "success": "#198754",    # 绿色
            "warning": "#ffc107",    # 黄色
            "error": "#dc3545"       # 红色
        }
        
        color = color_map.get(level, "#495057")
        
        # 居中显示日志内容
        log_text = f'<div style="text-align:center"><span style="color: {color}; font-weight: bold;">{message}</span></div>'
        self.log_text.append(log_text)
        
        # 如果有图片，添加图片
        if image_paths:
            # 确保image_paths是列表
            if isinstance(image_paths, str):
                image_paths = [image_paths]
            
            # 创建包含所有图片的HTML
            image_html = '<div style="display: flex; justify-content: center; align-items: center; gap: 20px; margin: 10px 0;">'
            
            for path in image_paths:
                if os.path.exists(path):
                    # 创建QImage并加载图片
                    image = QPixmap(path)
                    # 缩放图片到合适的大小（例如最大宽度200像素）
                    scaled_image = image.scaled(200, 200, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                    # 添加图片HTML
                    image_html += f'<img src="{path}" width="{scaled_image.width()}" height="{scaled_image.height()}" style="margin: 0 10px;"/>'
            
            image_html += '</div>'
            
            # 将图片插入到HTML中
            cursor = self.log_text.textCursor()
            cursor.movePosition(cursor.MoveOperation.End)
            self.log_text.setTextCursor(cursor)
            self.log_text.insertHtml(image_html)
        
        # 滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def execute_reboot_command(self, command):
        """执行重启命令"""
        try:
            # 检查ADB工具
            adb_path, fastboot_path = self.adb_tools.get_adb_path()
            if not os.path.exists(adb_path) or not os.path.exists(fastboot_path):
                self.add_log("ADB", "error")
                return False
            
            # 设置命令执行环境
            startupinfo = None
            if os.name == 'nt':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
            
            # 准备命令
            exe_path = adb_path if command[0] == "adb" else fastboot_path
            full_command = [exe_path] + command[1:]
            
            try:
                result = subprocess.run(
                    full_command,
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    timeout=3,
                    startupinfo=startupinfo,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )
                
                # 处理重启命令的特殊情况
                if "reboot" in command:
                    self.add_log("okay", "success")
                    return True
                
                # 处理一般命令的执行结果
                if result.returncode != 0:
                    self.add_log(f"命令执行失败: {result.stderr}", "error")
                    return False
                
                self.add_log("okay", "success")
                return True
                
            except subprocess.TimeoutExpired:
                # 重启命令超时是正常的
                if "reboot" in command:
                    self.add_log("okay", "success")
                    return True
                self.add_log("命令执行超时", "error")
                return False
            
            except Exception as e:
                self.add_log(f"执行命令时出错: {str(e)}", "error")
                return False
        
        except Exception as e:
            self.add_log(f"执行命令时出错: {str(e)}", "error")
            return False

    def get_device_partitions(self):
        """取设备分区信息"""
        try:
            adb_path, _ = self.adb_tools.get_adb_path()
            stdout, stderr = self.execute_command([adb_path, "shell", "ls -l /dev/block/by-name/"])
            if stdout:
                with open(self.temp_partition_file, "w", encoding='utf-8') as f:
                    f.write(stdout)
                self.add_log("成功读取设备分区信息", "success")
                return True
            else:
                self.add_log(f"读取分区失败: {stderr}", "error")
                return False
        except Exception as e:
            self.add_log(f"获取分区信息出错: {str(e)}", "error")
            return False

    def handle_payload_unpack(self):
        """处理全量包分解"""
        self.clear_support_info()  # 清除支持作者信息
        try:
            # 如果已经有正在运行的解包线程，先停止它
            if hasattr(self, 'payload_thread') and self.payload_thread and self.payload_thread.isRunning():
                self.add_log("正在停止当前任务...", "warning")
                self.payload_thread.stop()
                self.payload_thread.wait()
                self.payload_thread.deleteLater()
            
            # 创建PayloadExtractor实例
            payload_function = PayloadExtractor()
            
            # 选择文件
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择全量包文件",
                "",
                "所有文件 (*.*);;ZIP文件 (*.zip);;BIN文件 (*.bin)"  # 修改过滤器顺序，让所有文件显示在前面
            )
            
            if not file_path:
                self.add_log("未选择文件", "warning")
                return
                
            # 获取文件所在文件夹作为输出目录
            output_folder = os.path.dirname(file_path)
                
            # 设置文件和输出目录
            try:
                payload_function.set_payload_file(file_path)
                payload_function.set_extract_folder(output_folder)
            except Exception as e:
                self.add_log(f"设置文件失败: {str(e)}", "error")
                return
            
            # 禁用所有按钮
            self.disable_all_buttons()
            
            # 创建并配置解包线程
            self.payload_thread = PayloadThread(payload_function)
            self.payload_thread.setParent(self)  # 设置父对象以确保正确的生命周期管理
            
            # 断开之前的信号连接（如果有的话）
            try:
                self.payload_thread.log_signal.disconnect()
                self.payload_thread.finished_signal.disconnect()
            except:
                pass  # 如果没有连接，忽略错误
            
            # 重新连接信号
            self.payload_thread.log_signal.connect(self.add_log)
            self.payload_thread.finished_signal.connect(self.on_payload_finished)
            self.payload_thread.finished.connect(self.payload_thread.deleteLater)
            
            # 启动解包线程
            self.payload_thread.start()
            
        except Exception as e:
            error_detail = traceback.format_exc()
            self.add_log(f"解析出错: {str(e)}", "error")
            self.add_log(f"错误详情: {error_detail}", "error")
            # 确保按钮状态恢复
            self.enable_all_buttons()

    def on_payload_finished(self, success):
        """解包完成的回调函数"""
        if success:
            self.add_log("解析完成", "success")
        else:
            self.add_log("解析失败", "error")
        
        # 启用所有按钮
        self.enable_all_buttons()
        
        # 清理线程对象
        if hasattr(self, 'payload_thread'):
            self.payload_thread.deleteLater()
            self.payload_thread = None

    def handle_custom_flash(self):
        """处理自定义刷写按钮点击"""
        try:
            # 检查设备是否在 fastboot 模式
            status_text, _ = self.adb_tools.check_device_status()
            if 'fastboot' not in status_text.lower():
                CustomMessageBox("警告", "设备不在fastboot模式，请先进入fastboot模式", self).exec()
                return
                
            # 显示自定义刷写对话框
            dialog = CustomFlashDialog(self)
            dialog.exec()
            
        except Exception as e:
            self.add_log(f"出错: {str(e)}", "error")

    def handle_traffic_card(self):
        """处理优质流量卡按钮点击"""
        try:
            import webbrowser
            webbrowser.open("https://h5.lot-ml.com/ProductEn/Index/b9397fb387c14d79")
        except Exception as e:
            self.add_log(f"失败: {str(e)}", "error")

    def handle_more_features(self):
        """处理更多功能按钮点击"""
        try:
            from hs7v import MoreFeaturesDialog
            dialog = MoreFeaturesDialog(self)
            dialog.exec()
        except Exception as e:
            self.add_log(f"打开更多功能失败: {str(e)}", "error")

    def closeEvent(self, event):
        """窗口关闭事件处理"""
        try:
            # 停止设备检查线程
            if self.device_check_thread:
                self.device_check_thread.stop()
                self.device_check_thread = None
            
            # 停止刷机线程
            if hasattr(self, 'flash_thread') and self.flash_thread and self.flash_thread.isRunning():
                if hasattr(self.flash_thread, 'flash_function') and hasattr(self.flash_thread.flash_function, 'stop'):
                    self.flash_thread.flash_function.stop()
                self.flash_thread.wait(1000)
            
            # 停止payload线程
            if hasattr(self, 'payload_thread') and self.payload_thread and self.payload_thread.isRunning():
                self.payload_thread.stop()
                self.payload_thread.wait(1000)
            
            event.accept()
        except Exception as e:
            self.add_log(f"关闭窗口时出错: {str(e)}", "error")
            event.accept()

    def handle_fastboodt(self):
        """处理 fastbooDT 按钮点击"""
        self.clear_support_info()
        # 先检查fastboot设备状态
        try:
            _, fastboot_path = self.adb_tools.get_adb_path()
            result = subprocess.run(
                [fastboot_path, "devices"],
                capture_output=True,
                text=True,
                encoding='utf-8',
                timeout=5,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            if not result.stdout or 'fastboot' not in result.stdout.lower():
                self.add_log("设备不在 bootloader 模式，请先进入 bootloader 模式", "error")
                return
        except Exception as e:
            self.add_log(f"检查设备失败: {str(e)}", "error")
            return
        # 弹出fastbooDT专用警告框
        warning_dialog = FastboodtWarningDialog(self)
        if warning_dialog.exec() != QDialog.DialogCode.Accepted:
            self.add_log("已取消fastbooDT修复", "warning")
            return
        # 选择刷机文件夹
        folder_path = QFileDialog.getExistingDirectory(
            self,
            "选择刷机文件所在文件夹",
            "",
            QFileDialog.Option.ShowDirsOnly
        )
        if not folder_path:
            self.add_log("未选择刷机文件夹，操作取消", "warning")
            return
        img_files = [f for f in os.listdir(folder_path) if f.endswith('.img')]
        if not img_files:
            self.add_log(f"在选择的文件夹中未找到.img文件: {folder_path}", "error")
            self.add_log("请确保选择了正确的刷机文件夹", "warning")
            all_files = os.listdir(folder_path)
            self.add_log(f"文件夹中的文件: {', '.join(all_files)}", "info")
            return
        else:
            self.add_log(f"找到 {len(img_files)} 个.img文件", "success")
        # 停止设备状态检查
        self.stop_device_check()
        # 创建 FastbooDTFunction 实例
        fastboodt_function = FastbooDTFunction()
        fastboodt_function.add_log = self.add_log
        fastboodt_function.set_flash_folder(folder_path)
        # 创建并启动刷机线程
        self.flash_thread = FlashThread(fastboodt_function, folder_path)
        self.flash_thread.log_signal.connect(self.add_log)
        self.flash_thread.finished_signal.connect(self.on_flash_finished)
        self.flash_thread.start()

class FlashWarningDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("刷机注意事项")
        self.setFixedSize(420, 320)
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.Dialog
        )
        self.setStyleSheet("""
            QDialog { background:rgb(236, 222, 222); border-radius: 12px; border: 2px solid #dc3545; }
            QLabel { font-size: 18px; color:rgb(12, 10, 10); font-weight: bold; qproperty-alignment: 'AlignCenter'; }
            QPushButton {
                background-color: #dc3545;
                color: white;
                border-radius: 10px;
                font-size: 17px;
                font-weight: bold;
                min-width: 110px;
                min-height: 36px;
                margin: 0 10px;
            }
            QPushButton:hover { background-color: #b21f2d; }
        """)
        layout = QVBoxLayout(self)
        label = QLabel("""
<div style='text-align:center; font-size:22px; font-weight:bold; color:#dc3545;'>刷机前请务必注意：</div><br>
<div style='text-align:center; font-size:18px; font-weight:bold; color:#dc3545;'>
警告：功能只限于真我-OPPO-一加/官方系统降级/升级/救砖/氧OS。<br>
1. 请关闭电脑的所有后台程序，只留此刷机工具运行。<br>
2. 请确保已备份所有重要数据。<br>
3. 设备必须是解锁bootloader。<br>
4. 刷机请使用优质数据线。<br>
5. 刷机有风险，操作需谨慎，后果自负。<br>
</div>
""")
        label.setWordWrap(True)
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label)
        btn_layout = QHBoxLayout()
        self.confirm_btn = QPushButton("确认启动刷机")
        self.cancel_btn = QPushButton("取消")
        btn_layout.addStretch()
        btn_layout.addWidget(self.confirm_btn)
        btn_layout.addWidget(self.cancel_btn)
        btn_layout.addStretch()
        layout.addLayout(btn_layout)
        self.confirm_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)

class FastboodtWarningDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("fastbooTD修复警告")
        self.setFixedSize(420, 220)
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.Dialog
        )
        self.setStyleSheet("""
            QDialog { background:rgb(255, 240, 240); border-radius: 12px; border: 2px solid #dc3545; }
            QLabel { font-size: 18px; color:#dc3545; font-weight: bold; qproperty-alignment: 'AlignCenter'; }
            QPushButton {
                background-color: #dc3545;
                color: white;
                border-radius: 10px;
                font-size: 17px;
                font-weight: bold;
                min-width: 110px;
                min-height: 36px;
                margin: 0 10px;
            }
            QPushButton:hover { background-color: #b21f2d; }
        """)
        layout = QVBoxLayout(self)
        label = QLabel("""
<div style='text-align:center; font-size:20px; font-weight:bold; color:#dc3545;'>fastbooTD修复警告</div><br>
<div style='text-align:center; font-size:16px; font-weight:bold; color:#dc3545;'>
此功能是修fastbooTD模式的。<br>如果你的fastbooTD是正常的，请勿使用！<br>
<br>请勿使用！<br>
<br>请勿使用！<br>


</div>
""")
        label.setWordWrap(True)
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label)
        btn_layout = QHBoxLayout()
        self.confirm_btn = QPushButton("确认继续")
        self.cancel_btn = QPushButton("取消")
        btn_layout.addStretch()
        btn_layout.addWidget(self.confirm_btn)
        btn_layout.addWidget(self.cancel_btn)
        btn_layout.addStretch()
        layout.addLayout(btn_layout)
        self.confirm_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    window = FlashToolUI()
    window.show()
    sys.exit(app.exec())