// qmessageauthenticationcode.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMessageAuthenticationCode
{
%TypeHeaderCode
#include <qmessageauthenticationcode.h>
%End

public:
%If (Qt_6_6_0 -)
    QMessageAuthenticationCode(QCryptographicHash::Algorithm method, QByteArrayView key = {});
%End
%If (- Qt_6_6_0)
    QMessageAuthenticationCode(QCryptographicHash::Algorithm method, const QByteArray &key = QByteArray());
%End
    ~QMessageAuthenticationCode();
    void reset();
%If (Qt_6_6_0 -)
    void setKey(QByteArrayView key);
%End
%If (- Qt_6_6_0)
    void setKey(const QByteArray &key);
%End
%If (Qt_6_6_0 -)
    void addData(QByteArrayView data);
%End
%If (- Qt_6_6_0)
    void addData(SIP_PYBUFFER);
%MethodCode
        sipBufferInfoDef bi;
        
        if (sipGetBufferInfo(a0, &bi) > 0)
        {
            sipCpp->addData(reinterpret_cast<char *>(bi.bi_buf), bi.bi_len);
            sipReleaseBufferInfo(&bi);
        }
        else
        {
            sipIsErr = 1;
        }
%End

%End
    bool addData(QIODevice *device);
    QByteArray result() const;
%If (Qt_6_6_0 -)
    static QByteArray hash(QByteArrayView message, QByteArrayView key, QCryptographicHash::Algorithm method);
%End
%If (- Qt_6_6_0)
    static QByteArray hash(const QByteArray &message, const QByteArray &key, QCryptographicHash::Algorithm method);
%End
%If (Qt_6_6_0 -)
    void swap(QMessageAuthenticationCode &other /Constrained/);
%End

private:
    QMessageAuthenticationCode(const QMessageAuthenticationCode &);
};
