"""
安卓通刷功能
""" 
import os
import time
import sys
from mx9p import ADBTools
"""
ColorOS设备解锁工具
支持ColorOS设备的固件刷写和解锁功能
"""

class ColorOSUnlock:
    def __init__(self):
        self.adb_tools = ADBTools()
        self.is_unlocking = False
        self.flash_folder = None
        # 在脚本所在目录创建临时文件夹
        if getattr(sys, 'frozen', False):
            # 如果是打包后的程序
            self.base_dir = os.path.dirname(sys.executable)
        else:
            # 如果是开发环境
            self.base_dir = os.path.dirname(os.path.abspath(__file__))
        self.temp_dir = os.path.join(self.base_dir, "temp")
        if not os.path.exists(self.temp_dir):
            os.makedirs(self.temp_dir)
        self.temp_partition_file = os.path.join(self.temp_dir, "temp_partitions.txt")
        self.flash_failed = False  # 添加闪烁失败标志
        
    def add_log(self, message, level="info"):
        """这个方法会被主程序覆盖"""
        print(f"Log [{level}]: {message}")
        
    def set_flash_folder(self, folder_path):
        """设置刷机文件夹路径"""
        if self.flash_failed:  # 如果之前闪烁失败，清除日志
            self.clear_logs()  # 清除之前的日志
            self.flash_failed = False  # 重置失败标志
        self.flash_folder = folder_path
        

    def execute_command(self, command):
        """执行命令，使用ADBTools的通用方法"""
        return self.adb_tools.execute_command(command)
        
    def get_device_partitions(self):
        """获取设备分区列表并保存到临时文件"""
        try:
            _, fastboot_path = self.adb_tools.get_adb_path()
            
            # 获取分区列表
            stdout, stderr = self.execute_command([fastboot_path, "getvar", "all"])
            if stderr:
                output = stderr  # fastboot 的输出通常在 stderr 中
            else:
                output = stdout
                
            # 解析输出，查找分区名称
            device_partitions = []
            for line in output.split('\n'):
                if 'partition-size:' in line:
                    # 提取分区名称，格式为 partition-size:xxx: 0x12345678
                    partition = line.split(':')[1].split()[0]
                    device_partitions.append(partition)
            
            # 保存所有分区列表到临时文件
            with open(self.temp_partition_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(device_partitions))
            
            # 检查是否存在 -cow 分区
            cow_partitions = [p for p in device_partitions if p.endswith('-cow')]
            if cow_partitions:
                pass  # 不再显示发现闪烁分区的提示
                
            return True
            
        except Exception as e:
            try:
                self.add_log(f"获取失败: {str(e)}", "error")
            except RuntimeError:
                print(f"获取失败: {str(e)}")
            return False

    def clear_cow_partitions(self):
        """闪烁分区（静默执行，无日志）"""
        try:
            _, fastboot_path = self.adb_tools.get_adb_path()
            # 从临时文件读取分区列表
            if not os.path.exists(self.temp_partition_file):
                return False
            with open(self.temp_partition_file, 'r', encoding='utf-8') as f:
                device_partitions = f.read().splitlines()
            # 检查并删除 a-cow 和 b-cow 分区（无日志输出）
            cow_partitions = [p for p in device_partitions if p.endswith(('a-cow', 'b-cow'))]
            for partition in cow_partitions:
                self.execute_command([
                        fastboot_path,
                        "delete-logical-partition",
                        partition
                    ])
            return True
        except Exception as e:
            return False

    def check_fastboot_mode(self):
        """检查设备是否在 fastboot 模式，如果不是则重启到 fastboot"""
        try:
            _, fastboot_path = self.adb_tools.get_adb_path()
            adb_path, _ = self.adb_tools.get_adb_path()
            
            # 检查是否 fastboot 模式
            stdout, stderr = self.execute_command([fastboot_path, "devices"])
            if stdout and "fastboot" in stdout.lower():
                self.add_log("设备已在 fastboot 模式", "success")
                return True
                
            # 如果不在 fastboot 模式，使用 adb reboot fastboot
            self.add_log("设备不在 fastboot 模式，尝试从系统重启", "info")
            stdout, stderr = self.execute_command([adb_path, "reboot", "fastboot"])

            # 等待设备连接
            self.add_log("等待设备重启到 fastboot 模式...", "info")
            max_attempts = 30  # 最大尝试次数
            attempt = 0
            while attempt < max_attempts:
                stdout, stderr = self.execute_command([fastboot_path, "devices"])
                if stdout and "fastboot" in stdout.lower():
                    self.add_log("设备已成功进入 fastboot 模式", "success")
                    return True
                attempt += 1
                time.sleep(1)  # 每秒检查一次
            
            self.add_log("设备未能进入 fastboot 模式", "error")
            return False
                
        except Exception as e:
            try:
                self.add_log(f"检查 fastboot 模式时出错: {str(e)}", "error")
            except RuntimeError:
                print(f"检查 fastboot 模式时出错: {str(e)}")
            return False
              
    def flash_all_images(self):
        try:
            # 获取所有.img文件
            img_files = [f for f in os.listdir(self.flash_folder) if f.endswith('.img')]
            if not img_files:
                self.add_log("未找到任何.img文件", "error")
                self.flash_failed = True
                return False
            _, fastboot_path = self.adb_tools.get_adb_path()
            failed_partitions = []
            # 跳过modem.img
            img_files = [f for f in img_files if f != 'modem.img']
            total = len(img_files)
            for index, img_file in enumerate(img_files, 1):
                partition_name = img_file.replace('.img', '')
                img_path = os.path.join(self.flash_folder, img_file)
                progress_percent = int((index / total) * 100)
                self.add_log(f"Loading.. [{index}/{total}]（请勿断开设备） 进度：{progress_percent}%", "info")
                stdout, stderr = self.execute_command([
                    fastboot_path,
                    "flash",
                    partition_name,
                    img_path
                ])
                if stderr and "error" in stderr.lower():
                    failed_partitions.append(partition_name)
                    self.add_log(f"{partition_name} NO: {stderr}", "error")
                    self.flash_failed = True
                    return False
                else:
                    self.add_log(f"{partition_name} OK", "success")
            if failed_partitions:
                self.flash_failed = True
                return False
            return True
        except Exception as e:
            try:
                self.add_log(f"闪烁时出错: {str(e)}", "error")
            except RuntimeError:
                print(f"闪烁时出错: {str(e)}")
            self.flash_failed = True
            return False

    def clear_logs(self):
        """清除日志的方法，会被主程序覆盖"""
        pass

    def handle_flash(self):
        """执行刷机流程"""
        if self.is_unlocking:
            self.add_log("刷写正在进行中", "warning")
            return False
        self.is_unlocking = True
        try:
            # 1. 刷入 modem 分区
            _, fastboot_path = self.adb_tools.get_adb_path()
            modem_img = os.path.join(self.flash_folder, "modem.img")
            if os.path.exists(modem_img):
                self.add_log(f"Loading..（请勿断开设备） 进度：1%", "info")
                stdout, stderr = self.execute_command([
                    fastboot_path,
                    "flash",
                    "modem",
                    modem_img
                ])
                if stderr and "error" in stderr.lower():
                    self.add_log(f"modem NO: {stderr}", "error")
                else:
                    self.add_log("modem OK", "success")
            # 2. 重启到 fastboot
            self.add_log("切换模式中——请勿干扰,触碰", "info")
            stdout, stderr = self.execute_command([fastboot_path, "reboot", "fastboot"])
            if stderr and "error" in stderr.lower():
                self.add_log(f"重启 fastboot 失败: {stderr}", "error")  
            # 获取设备分区信息
            if not self.get_device_partitions():
                self.add_log("闪烁分区", "error")
            # 清除 cow 分区（静默）
            self.clear_cow_partitions()
            # 刷入所有镜像文件
            if not self.flash_all_images():
                self.add_log("NO", "error")
            self.add_log(f"进度：100%", "success")
            self.add_log('<h1 style="font-size: 24px; font-weight: bold; color: #198754; text-align: center;">完成</h1>', "success")
            self.add_log('<h2 style="font-size: 17px; font-weight: bold; text-align: center;">检测是否有分区NO/如有报错请在群里寻求帮助</h2>', "success")
            self.add_log('<h2 style="font-size: 17px; font-weight: bold; text-align: center;">反之请点击设备上(简体中文—格式化数据分区）</h2>', "success")
            self.add_log('<h2 style="font-size: 17px; font-weight: bold;">有问题请在工具官方里反馈问题（带上日志截图）</h2>', "success")
            self.add_log('<h2 style="font-size: 17px; font-weight: bold;">祝您玩机愉快</h2>', "success")
            return True
        except Exception as e:
            try:
                self.add_log(f"刷机过程出错: {str(e)}", "error")
            except RuntimeError:
                print(f"刷机过程出错: {str(e)}")
            return False
        finally:
            self.is_unlocking = False