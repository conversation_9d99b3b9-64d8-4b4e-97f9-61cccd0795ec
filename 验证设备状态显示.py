# -*- coding: utf-8 -*-
"""
验证设备状态显示修改
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_device_status_changes():
    """验证设备状态显示修改"""
    print("🔍 验证设备状态显示修改...")
    
    try:
        # 读取源代码文件
        with open('qz4n.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修改
        checks = [
            # 检查是否移除了不必要的日志
            ('移除刷机开始日志', '🔥 刷机开始，设备状态已切换到载入模式' not in content),
            ('移除刷机完成日志', '🎉 刷机操作完成，设备状态已恢复检查' not in content),
            ('移除调试print', 'print("🚀 开始刷机' not in content),
            
            # 检查设备状态设置是否正确
            ('载入刷机状态设置', 'self.device_status_label.setText("载入刷机")' in content),
            ('检测设备状态设置', 'self.device_status_label.setText("检测设备...")' in content),
            
            # 检查方法是否存在
            ('start_flashing方法', 'def start_flashing(self):' in content),
            ('finish_flashing方法', 'def finish_flashing(self, success=True):' in content),
            
            # 检查线程安全改进
            ('线程安全日志', 'except (RuntimeError, AttributeError):' in content),
            ('延迟清理线程', 'QTimer.singleShot(1000' in content),
        ]
        
        print("\n📋 验证结果:")
        all_good = True
        for name, condition in checks:
            if condition:
                print(f"✅ {name} - 正确")
            else:
                print(f"❌ {name} - 有问题")
                all_good = False
        
        # 检查流程是否正确
        print("\n🔄 预期流程:")
        print("1. 点击刷机按钮 → 调用start_flashing() → 设备状态框显示'载入刷机'")
        print("2. 刷机进行中 → 设备状态框保持'载入刷机'（不管重启）")
        print("3. 刷机完成 → 调用finish_flashing() → 设备状态框显示'检测设备...' → 恢复正常设备检查")
        print("4. 不在日志中显示状态变化信息")
        
        if all_good:
            print("\n🎉 所有修改验证通过!")
            print("现在设备状态应该只在设备状态框中显示，不会在日志中显示")
        else:
            print("\n❌ 发现问题，请检查修改")
        
        return all_good
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    verify_device_status_changes()
