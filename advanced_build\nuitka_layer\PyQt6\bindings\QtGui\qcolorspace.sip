// qcolorspace.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QColorSpace
{
%TypeHeaderCode
#include <qcolorspace.h>
%End

public:
    enum NamedColorSpace
    {
        SRgb,
        SRgbLinear,
        AdobeRgb,
        DisplayP3,
        ProPhotoRgb,
    };

    enum class Primaries
    {
        Custom,
        SRgb,
        AdobeRgb,
        DciP3D65,
        ProPhotoRgb,
    };

    enum class TransferFunction
    {
        Custom,
        Linear,
        Gamma,
        SRgb,
        ProPhotoRgb,
    };

    QColorSpace();
    QColorSpace(QColorSpace::NamedColorSpace namedColorSpace);
    QColorSpace(QColorSpace::Primaries primaries, QColorSpace::TransferFunction fun, float gamma = 0.F);
    QColorSpace(QColorSpace::Primaries primaries, float gamma);
%If (Qt_6_1_0 -)
    QColorSpace(QColorSpace::Primaries primaries, const QList<unsigned short> &transferFunctionTable);
%End
    QColorSpace(const QPointF &whitePoint, const QPointF &redPoint, const QPointF &greenPoint, const QPointF &bluePoint, QColorSpace::TransferFunction fun, float gamma = 0.F);
%If (Qt_6_1_0 -)
    QColorSpace(const QPointF &whitePoint, const QPointF &redPoint, const QPointF &greenPoint, const QPointF &bluePoint, const QList<unsigned short> &redTransferFunctionTable, const QList<unsigned short> &greenTransferFunctionTable, const QList<unsigned short> &blueTransferFunctionTable);
%End
%If (Qt_6_1_0 -)
    QColorSpace(const QPointF &whitePoint, const QPointF &redPoint, const QPointF &greenPoint, const QPointF &bluePoint, const QList<unsigned short> &transferFunctionTable);
%End
    QColorSpace(const QColorSpace &colorSpace);
    ~QColorSpace();
    void swap(QColorSpace &colorSpace /Constrained/);
    QColorSpace::Primaries primaries() const;
    QColorSpace::TransferFunction transferFunction() const;
    float gamma() const;
    void setTransferFunction(QColorSpace::TransferFunction transferFunction, float gamma = 0.F);
%If (Qt_6_1_0 -)
    void setTransferFunction(const QList<unsigned short> &transferFunctionTable);
%End
%If (Qt_6_1_0 -)
    void setTransferFunctions(const QList<unsigned short> &redTransferFunctionTable, const QList<unsigned short> &greenTransferFunctionTable, const QList<unsigned short> &blueTransferFunctionTable);
%End
%If (Qt_6_1_0 -)
    QColorSpace withTransferFunction(const QList<unsigned short> &transferFunctionTable) const;
%End
    QColorSpace withTransferFunction(QColorSpace::TransferFunction transferFunction, float gamma = 0.F) const;
%If (Qt_6_1_0 -)
    QColorSpace withTransferFunctions(const QList<unsigned short> &redTransferFunctionTable, const QList<unsigned short> &greenTransferFunctionTable, const QList<unsigned short> &blueTransferFunctionTable) const;
%End
    void setPrimaries(QColorSpace::Primaries primariesId);
    void setPrimaries(const QPointF &whitePoint, const QPointF &redPoint, const QPointF &greenPoint, const QPointF &bluePoint);
    bool isValid() const;
    static QColorSpace fromIccProfile(const QByteArray &iccProfile);
    QByteArray iccProfile() const;
    QColorTransform transformationToColorSpace(const QColorSpace &colorspace) const;
%If (Qt_6_2_0 -)
    QString description() const;
%End
%If (Qt_6_2_0 -)
    void setDescription(const QString &description);
%End
};

bool operator==(const QColorSpace &colorSpace1, const QColorSpace &colorSpace2);
bool operator!=(const QColorSpace &colorSpace1, const QColorSpace &colorSpace2);
QDataStream &operator<<(QDataStream &, const QColorSpace & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QColorSpace & /Constrained/) /ReleaseGIL/;
