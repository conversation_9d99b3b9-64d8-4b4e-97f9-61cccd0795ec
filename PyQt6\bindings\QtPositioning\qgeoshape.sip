// qgeoshape.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QGeoShape
{
%TypeHeaderCode
#include <qgeoshape.h>
%End

%ConvertToSubClassCode
    switch (sipCpp->type())
    {
    case QGeoShape::CircleType:
        sipType = sipType_QGeoCircle;
        break;
    
    case QGeoShape::RectangleType:
        sipType = sipType_QGeoRectangle;
        break;
    
    case QGeoShape::PathType:
        sipType = sipType_QGeoPath;
        break;
    
    case QGeoShape::PolygonType:
        sipType = sipType_QGeoPolygon;
        break;
    
    default:
        sipType = 0;
    }
%End

public:
    QGeoShape();
    QGeoShape(const QGeoShape &other);
    ~QGeoShape();

    enum ShapeType
    {
        UnknownType,
        RectangleType,
        CircleType,
        PathType,
        PolygonType,
    };

    QGeoShape::ShapeType type() const;
    bool isValid() const;
    bool isEmpty() const;
    bool contains(const QGeoCoordinate &coordinate) const;
    QGeoCoordinate center() const;
    QString toString() const;
    QGeoRectangle boundingGeoRectangle() const;
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};

%End
%If (Qt_6_2_0 -)
QDataStream &operator<<(QDataStream &stream, const QGeoShape &shape);
%End
%If (Qt_6_2_0 -)
QDataStream &operator>>(QDataStream &stream, QGeoShape &shape /Constrained/);
%End
%If (Qt_6_2_0 -)
bool operator==(const QGeoShape &lhs, const QGeoShape &rhs);
%End
%If (Qt_6_2_0 -)
bool operator!=(const QGeoShape &lhs, const QGeoShape &rhs);
%End
