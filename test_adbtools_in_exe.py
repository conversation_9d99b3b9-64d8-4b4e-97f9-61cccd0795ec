# -*- coding: utf-8 -*-
"""
ADBTools测试脚本
用于验证打包后的ADBTools是否可用
"""

import os
import sys
import subprocess

def get_resource_path(relative_path):
    """获取资源文件路径"""
    if hasattr(sys, '_MEIPASS'):
        # 打包后的环境
        base_path = sys._MEIPASS
    else:
        # 开发环境
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

def test_adbtools():
    """测试ADBTools"""
    print("🔍 测试ADBTools...")
    
    # 检查ADBTools目录
    adbtools_path = get_resource_path("ADBTools")
    print(f"ADBTools路径: {adbtools_path}")
    
    if os.path.exists(adbtools_path):
        print("✅ ADBTools目录存在")
        files = os.listdir(adbtools_path)
        print(f"包含 {len(files)} 个文件:")
        for file in files:
            file_path = os.path.join(adbtools_path, file)
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                print(f"  ✅ {file} ({size:,} 字节)")
            else:
                print(f"  ❌ {file} (不存在)")
    else:
        print("❌ ADBTools目录不存在")
        return False
    
    # 测试adb.exe
    adb_path = get_resource_path("ADBTools/adb.exe")
    if os.path.exists(adb_path):
        try:
            result = subprocess.run([adb_path, "version"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✅ adb.exe 可以正常运行")
                print(f"版本信息: {result.stdout.strip()}")
            else:
                print("⚠️ adb.exe 运行失败")
        except Exception as e:
            print(f"⚠️ adb.exe 测试异常: {e}")
    else:
        print("❌ adb.exe 不存在")
    
    return True

if __name__ == "__main__":
    test_adbtools()
    input("按回车键退出...")
