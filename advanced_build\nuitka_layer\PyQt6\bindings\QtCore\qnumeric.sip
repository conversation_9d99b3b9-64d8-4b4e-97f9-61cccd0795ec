// qnumeric.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%ModuleCode
#include <qnumeric.h>
%End

bool qIsInf(double d);
bool qIsFinite(double d);
bool qIsNaN(double d);
double qInf();
double qSNaN();
double qQNaN();
quint64 qFloatDistance(double a, double b);
%If (Qt_6_5_0 -)
double qAbs(const double &t);
%End
%If (Qt_6_5_0 -)
int qRound(double d);
%End
%If (Qt_6_5_0 -)
qint64 qRound64(double d);
%End
%If (Qt_6_5_0 -)
bool qFuzzyCompare(double p1, double p2);
%End
%If (Qt_6_5_0 -)
bool qFuzzyIsNull(double d);
%End
