# PyArmor 混淆配置
PLUGINS = ['check_docker', 'check_debug', 'check_vmware']  # 添加反调试和虚拟机检测
CROSS_PROTECTION = 1                    # 启用交叉保护
RESTRICT_MODE = 3                       # 最高级别的限制模式
ENABLE_SUFFIX = '.py'                   # 只处理 .py 文件
WRAP_MODE = 1                           # 包装模式，增加保护
OBFUSCATE_CODE = 1                      # 启用代码混淆
PLATFORM = None                         # 适用于所有平台
PROTECTION_LEVEL = 2                    # 高级别保护
