"""
ColorOS  （9以上）
"""

import os
import sys
import time
import threading
import subprocess
from mx9p import ADBTools
"""
ColorOS 15设备刷写功能模块
支持ColorOS 15设备的固件刷写和解锁功能
"""


class ColorOS15Function:
    def __init__(self):
        self.adb_tools = ADBTools()
        if getattr(sys, 'frozen', False):
            self.base_dir = os.path.dirname(sys.executable)
        else:
            self.base_dir = os.path.dirname(os.path.abspath(__file__))
        self.flash_folder = None
        self.is_flashing = False
        self.flash_thread = None
        self.temp_dir = os.path.join(self.base_dir, "temp")
        if not os.path.exists(self.temp_dir):
            os.makedirs(self.temp_dir)
        self.temp_partition_file = os.path.join(self.temp_dir, "temp_partitions.txt")
        
        # 定义逻辑分区列表
        self.logical_partitions = [
            "my_bigball", "my_carrier", "my_company", "my_engineering",
            "my_heytap", "my_manifest", "my_preload", "my_product",
            "my_region", "my_stock", "odm", "odm_dlkm", "product",
            "system", "system_dlkm", "system_ext", "vendor", "vendor_dlkm"
        ]

    def set_flash_folder(self, folder_path):
        """设置刷机文件夹路径"""
        self.flash_folder = folder_path
        
    def add_log(self, message, level="info"):
        """这个方法会被主程序覆盖"""
        print(f"Log [{level}]: {message}")

    def get_device_partitions(self):
        
        try:
            _, fastboot_path = self.adb_tools.get_adb_path()
            
            # 获取分区列表
            stdout, stderr = self.execute_command([fastboot_path, "getvar", "all"])
            if stderr:
                output = stderr  # fastboot 的输出通常在 stderr 中
            else:
                output = stdout
                
            # 解析输出，查找分区名称
            device_partitions = []
            for line in output.split('\n'):
                if 'partition-size:' in line:
                    # 提取分区名称，格式为 partition-size:xxx: 0x12345678
                    partition = line.split(':')[1].split()[0]
                    device_partitions.append(partition)
            
            # 保存所有分区列表到临时文件
            with open(self.temp_partition_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(device_partitions))
            
            # 检查逻辑分区的 cow 分区
            found_cow_partitions = []
            for partition in self.logical_partitions:
                for suffix in ['a', 'b']:
                    cow_partition = f"{partition}_{suffix}-cow"
                    if cow_partition in device_partitions:
                        found_cow_partitions.append(cow_partition)
            

            return True
            
        except Exception as e:
            self.add_log(f"获取出错: {str(e)}", "error")
            return False

    def clear_cow_partitions(self):
        
        try:
            _, fastboot_path = self.adb_tools.get_adb_path()
            # 从临时文件读取分区列表
            if not os.path.exists(self.temp_partition_file):
                return False
            with open(self.temp_partition_file, 'r', encoding='utf-8') as f:
                device_partitions = f.read().splitlines()
            # 检查并删除 cow 分区（无日志输出）
            for partition in device_partitions:
                if partition.endswith(('-cow')):
                    self.execute_command([
                        fastboot_path,
                        "delete-logical-partition",
                        partition
                    ])
            return True
        except Exception as e:
            return False

    def execute_command(self, command):
        """使用 subprocess 执行命令"""
        try:
            # 创建 startupinfo 对象来隐藏黑框
            startupinfo = None
            if os.name == 'nt':  # Windows系统
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE

            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                encoding='utf-8',
                startupinfo=startupinfo,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            return result.stdout, result.stderr
        except Exception as e:
            return None, str(e)

    def flash_modem(self):
        
        try:
            modem_file = os.path.join(self.flash_folder, "modem.img")
            if not os.path.exists(modem_file):
                return True

            _, fastboot_path = self.adb_tools.get_adb_path()
            self.add_log(f"Loading..（请勿断开设备）", "info")
            stdout, stderr = self.execute_command([
                fastboot_path, 
                "flash", 
                "--slot=all", 
                "modem",
                modem_file
            ])
            if stderr and "error" in stderr.lower():
                self.add_log(f"modem NO: {stderr}", "error")
            else:
                self.add_log(f"modem OK", "success")
            return True
        except Exception as e:
            self.add_log(f"刷入分区时出错: {str(e)}", "error")
            return True

    def is_logical_partition(self, partition_name):
        
        return partition_name in self.logical_partitions

    def flash_non_logical_partitions(self):
        
        try:
            flash_files = [f.replace('.img', '') for f in os.listdir(self.flash_folder) 
                          if f.endswith('.img') and f != 'modem.img']
            failed_partitions = []
            _, fastboot_path = self.adb_tools.get_adb_path()
            non_logical_partitions = [p for p in flash_files if not self.is_logical_partition(p)]
            total_tasks = len(non_logical_partitions)
            current_task = 0
            for index, partition in enumerate(non_logical_partitions, 1):
                img_file = os.path.join(self.flash_folder, f"{partition}.img")
                current_task += 1
                self.add_log(f"Loading.. [{current_task}/{total_tasks}](请勿断开设备)", "info")
                stdout, stderr = self.execute_command([
                        fastboot_path, 
                        "flash", 
                    "--slot=all", 
                    partition,
                        img_file
                    ])
                if stderr and "error" in stderr.lower():
                    failed_partitions.append(partition)
                    self.add_log(f"{partition} NO: {stderr}", "error")
                else:
                    self.add_log(f"{partition} OK", "success")
            if failed_partitions:
                pass
            return True
        except Exception as e:
            self.add_log(f"分区时出错: {str(e)}", "error")
            return True

    def flash_logical_partitions(self):
        
        try:
            # 获取所有分区文件
            flash_files = [f.replace('.img', '') for f in os.listdir(self.flash_folder) 
                          if f.endswith('.img') and f != 'modem.img']
            
            # 过滤出逻辑分区
            logical_partitions = [p for p in flash_files if self.is_logical_partition(p)]
            
            if not logical_partitions:
                return True

            failed_partitions = []
            _, fastboot_path = self.adb_tools.get_adb_path()
            
            # 直接刷入逻辑分区
            total = len(logical_partitions)
            for index, partition in enumerate(logical_partitions, 1):
                img_file = os.path.join(self.flash_folder, f"{partition}.img")
                
                self.add_log(f"Loading.. [{index}/{total}](请勿断开设备)", "info")
                stdout, stderr = self.execute_command([
                    fastboot_path,
                    "flash",
                    partition,
                    img_file
                ])
                
                if stderr and "error" in stderr.lower():
                    failed_partitions.append(partition)
                    self.add_log(f"{partition} NO: {stderr}", "error")
                else:
                    self.add_log(f"{partition} OK", "success")

            if failed_partitions:
                pass
            
            return True
            
        except Exception as e:
            self.add_log(f"处理分区出错: {str(e)}", "error")
            return True

    def _do_flash(self):
        """实际的刷写过程（所有分区直接刷入，不再AB分区）"""
        try:
            self.add_log("开始刷机...", "info")
            self.add_log("载入刷机", "info")
            main_steps = 4
            all_img_files = [f for f in os.listdir(self.flash_folder) if f.endswith('.img') and f != 'modem.img']
            all_partitions = [f.replace('.img', '') for f in all_img_files]
            logical = [p for p in all_partitions if self.is_logical_partition(p)]
            non_logical = [p for p in all_partitions if not self.is_logical_partition(p)]
            non_logical_count = len(non_logical)
            logical_count = len(logical)
            modem_file = os.path.join(self.flash_folder, "modem.img")
            modem_count = 1 if os.path.exists(modem_file) else 0
            total_steps = main_steps + non_logical_count + logical_count + modem_count - 1
            current_step = 0
            # 1. 刷modem
            if os.path.exists(modem_file):
                _, fastboot_path = self.adb_tools.get_adb_path()
                current_step += 1
                percent = int(current_step / total_steps * 100)
                self.add_log(f"Loading..（请勿断开设备） 进度：{percent}%", "info")
                stdout, stderr = self.execute_command([
                    fastboot_path,
                    "flash",
                    "modem",
                    modem_file
                ])
                if stderr and "error" in stderr.lower():
                    self.add_log(f"modem NO: {stderr}", "error")
                else:
                    self.add_log(f"modem OK", "success")
            else:
                current_step += 1  # 跳过modem
            # 2. 重启
            current_step += 1
            _, fastboot_path = self.adb_tools.get_adb_path()
            self.add_log("正在切换模式...请勿触碰设备", "info")
            self.execute_command([fastboot_path, "reboot", "fastboot"])
            time.sleep(8)
            # 3. 获取分区
            current_step += 1
            if not self.get_device_partitions():
                self.add_log("分区失败", "error")
                return False
            # 4. 删除cow
            current_step += 1
            self.clear_cow_partitions()
            # 5. 刷非逻辑分区
            failed_partitions = []
            for partition in non_logical:
                current_step += 1
                percent = int(current_step / total_steps * 100)
                img_file = os.path.join(self.flash_folder, f"{partition}.img")
                self.add_log(f"Loading.. [{current_step-main_steps}/{non_logical_count}](请勿断开设备) 进度：{percent}%", "info")
                stdout, stderr = self.execute_command([
                    fastboot_path,
                    "flash",
                    partition,
                    img_file
                ])
                if stderr and "error" in stderr.lower():
                    failed_partitions.append(partition)
                    self.add_log(f"{partition} NO: {stderr}", "error")
                else:
                    self.add_log(f"{partition} OK", "success")
            # 6. 刷逻辑分区
            for partition in logical:
                current_step += 1
                percent = int(current_step / total_steps * 100)
                img_file = os.path.join(self.flash_folder, f"{partition}.img")
                self.add_log(f"Loading.. [{current_step-main_steps-non_logical_count}/{logical_count}](请勿断开设备) 进度：{percent}%", "info")
                stdout, stderr = self.execute_command([
                    fastboot_path,
                    "flash",
                    partition,
                    img_file
                ])
                if stderr and "error" in stderr.lower():
                    failed_partitions.append(partition)
                    self.add_log(f"{partition} NO: {stderr}", "error")
                else:
                    self.add_log(f"{partition} OK", "success")
            self.add_log(f"进度：100%", "success")
            self.add_log('<h1 style="font-size: 24px; font-weight: bold; color: #198754;">完成</h1>', "success")
            self.add_log('<h2 style="font-size: 17px; font-weight: bold;">检查是否有分区NO—如有报错请在工具官方群里寻求帮助</h2>', "success")
            self.add_log('<h2 style="font-size: 17px; font-weight: bold;">反之请点击设备上(简体中文—格式化数据分区）</h2>', "success")
            self.add_log('<h2 style="font-size: 17px; font-weight: bold;">有问题请在工具官方里反馈问题（带上日志截图）</h2>', "success")
            self.add_log('<h2 style="font-size: 17px; font-weight: bold;">祝您玩机愉快</h2>', "success")
            return True
        except Exception as e:
            try:
                self.add_log(f"刷机过程出错: {str(e)}", "error")
            except RuntimeError:
                # 线程已被删除，使用print输出错误
                print(f"刷机过程出错: {str(e)}")
            return False
        finally:
            self.is_flashing = False

    def stop_flash(self):
        """停止刷写过程"""
        if self.is_flashing:
            self.is_flashing = False
            self.add_log("正在停止刷写...", "warning")
            if self.flash_thread:
                self.flash_thread.join(timeout=1)
            return True
        return False

    def is_busy(self):
        """检查是否正在刷写"""
        return self.is_flashing

    def handle_flash(self):
        """执行刷机流程"""
        if self.is_flashing:
            self.add_log("刷写正在进行中", "warning")
            return False
            
        self.is_flashing = True
        self.flash_thread = threading.Thread(target=self._do_flash)
        self.flash_thread.daemon = True
        self.flash_thread.start()
        return True

# 确保类可以被导入
__all__ = ['ColorOS15Function']