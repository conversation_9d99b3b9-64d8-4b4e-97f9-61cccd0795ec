// QtWidgetsmod.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%Module(name=PyQt6.QtWidgets, keyword_arguments="Optional", use_limited_api=True)

%Import QtCore/QtCoremod.sip
%Import QtGui/QtGuimod.sip

%Copying
Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>

This file is part of PyQt6.

This file may be used under the terms of the GNU General Public License
version 3.0 as published by the Free Software Foundation and appearing in
the file LICENSE included in the packaging of this file.  Please review the
following information to ensure the GNU General Public License version 3.0
requirements will be met: http://www.gnu.org/copyleft/gpl.html.

If you do not wish to use this file under the terms of the GPL version 3.0
then you may purchase a commercial license.  For more information contact
<EMAIL>.

This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
%End

%DefaultSupertype PyQt6.sip.simplewrapper

%Include qabstractbutton.sip
%Include qabstractitemdelegate.sip
%Include qabstractitemview.sip
%Include qabstractscrollarea.sip
%Include qabstractslider.sip
%Include qabstractspinbox.sip
%Include qapplication.sip
%Include qboxlayout.sip
%Include qbuttongroup.sip
%Include qcalendarwidget.sip
%Include qcheckbox.sip
%Include qcolordialog.sip
%Include qcolumnview.sip
%Include qcombobox.sip
%Include qcommandlinkbutton.sip
%Include qcommonstyle.sip
%Include qcompleter.sip
%Include qdatawidgetmapper.sip
%Include qdatetimeedit.sip
%Include qdial.sip
%Include qdialog.sip
%Include qdialogbuttonbox.sip
%Include qdockwidget.sip
%Include qdrawutil.sip
%Include qerrormessage.sip
%Include qfiledialog.sip
%Include qfileiconprovider.sip
%Include qfocusframe.sip
%Include qfontcombobox.sip
%Include qfontdialog.sip
%Include qformlayout.sip
%Include qframe.sip
%Include qgesture.sip
%Include qgesturerecognizer.sip
%Include qgraphicsanchorlayout.sip
%Include qgraphicseffect.sip
%Include qgraphicsgridlayout.sip
%Include qgraphicsitem.sip
%Include qgraphicslayout.sip
%Include qgraphicslayoutitem.sip
%Include qgraphicslinearlayout.sip
%Include qgraphicsproxywidget.sip
%Include qgraphicsscene.sip
%Include qgraphicssceneevent.sip
%Include qgraphicstransform.sip
%Include qgraphicsview.sip
%Include qgraphicswidget.sip
%Include qgridlayout.sip
%Include qgroupbox.sip
%Include qheaderview.sip
%Include qinputdialog.sip
%Include qitemdelegate.sip
%Include qitemeditorfactory.sip
%Include qkeysequenceedit.sip
%Include qlabel.sip
%Include qlayout.sip
%Include qlayoutitem.sip
%Include qlcdnumber.sip
%Include qlineedit.sip
%Include qlistview.sip
%Include qlistwidget.sip
%Include qmainwindow.sip
%Include qmdiarea.sip
%Include qmdisubwindow.sip
%Include qmenu.sip
%Include qmenubar.sip
%Include qmessagebox.sip
%Include qplaintextedit.sip
%Include qprogressbar.sip
%Include qprogressdialog.sip
%Include qproxystyle.sip
%Include qpushbutton.sip
%Include qradiobutton.sip
%Include qrubberband.sip
%Include qscrollarea.sip
%Include qscrollbar.sip
%Include qscroller.sip
%Include qscrollerproperties.sip
%Include qsizegrip.sip
%Include qsizepolicy.sip
%Include qslider.sip
%Include qspinbox.sip
%Include qsplashscreen.sip
%Include qsplitter.sip
%Include qstackedlayout.sip
%Include qstackedwidget.sip
%Include qstatusbar.sip
%Include qstyle.sip
%Include qstyleditemdelegate.sip
%Include qstylefactory.sip
%Include qstyleoption.sip
%Include qstylepainter.sip
%Include qsystemtrayicon.sip
%Include qtabbar.sip
%Include qtableview.sip
%Include qtablewidget.sip
%Include qtabwidget.sip
%Include qtextbrowser.sip
%Include qtextedit.sip
%Include qtoolbar.sip
%Include qtoolbox.sip
%Include qtoolbutton.sip
%Include qtooltip.sip
%Include qtreeview.sip
%Include qtreewidget.sip
%Include qtreewidgetitemiterator.sip
%Include qundoview.sip
%Include qwhatsthis.sip
%Include qwidget.sip
%Include qwidgetaction.sip
%Include qwizard.sip
%Include qfilesystemmodel.sip
%Include qpywidgets_qlist.sip
