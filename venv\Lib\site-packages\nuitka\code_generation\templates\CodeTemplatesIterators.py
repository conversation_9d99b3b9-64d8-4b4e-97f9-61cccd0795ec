#     Copyright 2025, <PERSON>, mailto:<EMAIL> find license text at end of file


""" Templates for the iterator handling.

"""

template_loop_break_next = """\
if (%(to_name)s == NULL) {
    if (CHECK_AND_CLEAR_STOP_ITERATION_OCCURRED(tstate)) {
%(break_indicator_code)s
        goto %(break_target)s;
    } else {
%(release_temps)s
        FETCH_ERROR_OCCURRED_STATE(tstate, &%(exception_state_name)s);
%(var_description_code)s
%(line_number_code)s
        goto %(exception_target)s;
    }
}
"""

from . import TemplateDebugWrapper  # isort:skip

TemplateDebugWrapper.checkDebug(globals())

#     Part of "Nuitka", an optimizing Python compiler that is compatible and
#     integrates with CPython, but also works on its own.
#
#     Licensed under the Apache License, Version 2.0 (the "License");
#     you may not use this file except in compliance with the License.
#     You may obtain a copy of the License at
#
#        http://www.apache.org/licenses/LICENSE-2.0
#
#     Unless required by applicable law or agreed to in writing, software
#     distributed under the License is distributed on an "AS IS" BASIS,
#     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#     See the License for the specific language governing permissions and
#     limitations under the License.
