[BUG]: out of license

## Command Line
D:\py\Scripts\pyarmor gen --output dist flash_tool.py main.py config.py

## Environments
Python 3.12.7
Pyarmor 9.0.6 (trial), 000000, non-profits
Platform windows.x86_64
Native windows.amd64
Home C:\Users\<USER>\.pyarmor

## Traceback
Traceback (most recent call last):
  File "D:\py\Lib\site-packages\pyarmor\cli\__main__.py", line 784, in main
    main_entry(sys.argv[1:])
  File "D:\py\Lib\site-packages\pyarmor\cli\__main__.py", line 777, in main_entry
    return args.func(ctx, args)
           ^^^^^^^^^^^^^^^^^^^^
  File "D:\py\Lib\site-packages\pyarmor\cli\__main__.py", line 245, in cmd_gen
    builder.process(options)
  File "D:\py\Lib\site-packages\pyarmor\cli\generate.py", line 188, in process
    self._obfuscate_scripts()
  File "D:\py\Lib\site-packages\pyarmor\cli\generate.py", line 144, in _obfuscate_scripts
    code = Pytransform3.generate_obfuscated_script(self.ctx, r)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\py\Lib\site-packages\pyarmor\cli\core\__init__.py", line 95, in generate_obfuscated_script
    return m.generate_obfuscated_script(ctx, res)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<maker>", line 716, in generate_obfuscated_script
  File "D:\py\Lib\site-packages\pyarmor\cli\__init__.py", line 16, in process
    return meth(self, res, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<maker>", line 546, in process
  File "<maker>", line 552, in coserialize
  File "<maker>", line 597, in _build_ast_body
RuntimeError: out of license


