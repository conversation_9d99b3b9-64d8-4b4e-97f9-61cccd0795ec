[pyarmor]

;; Pyarmor version
major = 9
minor = 1
patch = 5

;; Compatible core version
cli.core = 7.6.6
mini.core = 1.0
ecc.core = 1.0
vmc.core = 1.0

;; Deprecated since Pyarmor 8.2.5
; cli.runtime = 3.2.5

;; Default timeout when send request to remote server for
;;     check Pyarmor license
;;     register Pyarmor license
timeout = 6

regurl = https://api.dashingsoft.com/product/key/enter/%s/?
buyurl = https://jondy.github.io/paypal/index.html
docurl = https://pyarmor.readthedocs.io/{lang}/latest

[finder]
recursive = 0
;; includes =
excludes = */__pycache__
pyexts = .py .pyw

;; Data files need to copy to output path
;;   *.txt only copy .txt file
;;   * means all data files
;;   0 means nothing to copy
data_files = 0

;;
;; How to find dependent packages
;;
findall = 0

[builder]
;;
;; Part 1: only global/local settings, not in module level
;;

;; File encoding to read scripts
encoding = utf-8

;; Trace obfuscation
enable_trace = 0

;; Use Themida to protect runtime package in windows
enable_themida = 0

;; Import prefix to import runtime package
import_prefix = 0

;; Sometimes __file__ is not defined, replace it with __name__ to fix this issue
bootstrap_file = __file__

;; Exclude co objects by co_name
exclude_co_names = <lambda> <listcomp> <setcomp> <dictcomp> <genexpr>

;; Common modules which are no restrict
exclude_restrict_modules = __init__

;; Outer key name
outer_keyname = pyarmor.rkey

;; The final marker is "# %s: " % VALUE
;; If VALUE == "false", then disable inline plugin
inline_plugin_marker = pyarmor

;; Default plugins
plugins = CodesignPlugin DarwinUniversalPlugin

;; Using shared runtime package
; use_runtime = /path/to/runtime

;; How many loops for jit iv
jit_iv_threshold = 100

;; Now "argument" is not available
rft_enables = builtin import function class method global local

;; Exclude unknown attrs automically
;    0     disable auto exclude, use auto include
;    1     auto exclude and load .pyarmor/rft/exclude_table
;    2     auto exclude but not load exclude_table
rft_auto_exclude = 1

;; Export all the names in module attribute __all__
;; It means leave all the names in the __all__ as they're
rft_auto_export = 1

;; Enable dev mode for rft
rft_dev_mode = 0

;; Export module and classes
; rft_export_names = pkg.mod pkg.mod.cls pkg.mod.attr

;; How to reform import statement
;;   0 Convert import statement to security form
;;   1 Simple rename the imported name
rft_simple_import = 0

;; Extra paths to find dependent package
; pypaths =

;; List module names couldn't be found automically
; hidden_imports =

;; If it's enabled, disable some features to make scripts work with nuitka
;; convenient settings for nuitka, but now it's TBD
; support_nuitka = 0

;; Group device flag for machine id
group_device_flag = 22

;; Whether propagate package private configuration to module
;;
;; If enable, "pkg.mod" will inherit "pkg" configuration
;;
;; Otherwise, "pkg.mod" doesn't merge "pkg" configuration
propagate_package_options = 0

;;
;; Part 2: global/local/module level options
;;

;; The argument optimize specifies the optimization level of the
;; compiler; the default value of -1 selects the optimization level of
;; the interpreter as given by -O options. Explicit levels are 0 (no
;; optimization; __debug__ is true), 1 (asserts are removed, __debug__
;; is false) or 2 (docstrings are removed too).
optimize = -1

;; It's not used now
type_comments = false

;; Write refactor result scripts
trace_rft = 0

enable_jit = 0
enable_bcc = 0
enable_rft = 0

;; assert: call import
assert_call = 0
assert_import = 0

;; mix string constant
mix_str = 0

;; hide function name in traceback
;;   1: hide function.__name__
;;   2: also hide function.__qualname__ (not implemented)
mix_coname = 0

;; mix local variables
mix_localnames = 1

;; mix argument names, it also clears annotations
mix_argnames = 0

obf_module = 1
obf_code = 1

;; 0: no wrap mode
;; 1: simple wrap mode
;; 2: full wrap mode
;; Since Python 3.12, "2" is same as "1", both of them use full wrap mode
wrap_mode = 1

restrict_module = 1

;;
;; Advanced features
;;

;; check license when importing each module
import_check_license = 0

;; clear module co after importing
clear_module_co = 1

;; clear frame.f_locals for wrap mode, it's meanless for non-wrap mode
clear_frame_locals = 0

;; Model level to rename attributes ruler
;;
;;    x.y.z:?.%.z
;;
;;    ?  auto map
;;    %  force rename
;;
; rft_rulers =

;; Model level to exclude names
; rft_excludes =

;; Whether encrypt name in statement import
rft_mix_import_name = 0

;; Unused, now this option isn't used
;; Strip leading name package
strip_package_name = 1

;;
;; Compatiable options
;;

;; Since pyarmor 8.5.3, pyarmor.core 6.5.1
;;
;; If private_module_same_as_restrict is set to 1, --private is same as --restrict, it's default behaviours in previous version. Both --private and --restrict don't allow plain scripts to import the obfsucated modules
;;
;; But the right way should be
;;
;;    --private allow
;;    --restrict not allow
;;
;; When need this behaviours, set private_module_same_as_restrict is to 0
;;
private_module_same_as_restrict = 0

[runtime]

;; Generate extension for all Python3.7+
universal = 0

;; Default runtime package name
package_name_format = pyarmor_runtime_{suffix}

;; The file ext only keep .so/.pyd, for example
;;     pyarmor_runtime.cpython-37m-darwin.so
;;     if simple_extension_name == 1 then
;;     pyarmor_runtime.so
simple_extension_name = 1

;; Whether patching extension to embedded runtime key
;;
;;   0: do not patch extension, but store runtime key to one file
;;   1: runtime key is embedded into extension
;;
;; The purpose is to solve code sign issue for Darwin platform
;; Do not patch extension will keep code sign, so need not re-sign
;; But it need an extra file.
;;
;; Note: it's different from outer runtime key.
;;
patch_extension = 1

;; Obfuscate runtime key
;;   0: no obfuscation
;;   1: simple obfuscation
;;
obf_key_mode = 0

;; Enable outer runtime key
outer = 0

;; Pyarmor raises PyExc_RuntimeError by default
;;     0    raise PyExc_RuntimeError
;;     1    raise PyExc_SystemExit
;;     2    call libc exit to quit directly
on_error = 0

;; Check runtime key periodically, support formats:
;;   3600s
;;   60m
;;   1h
;;   1 = 1h
; period = 1

;; Expired runtime key, support 2 forms:
;;
;;     30: expired in 30 days
;;     2025-12-31
;;
; expired =

;; Check network time by those servers
;;
;; Support formats:
;;
;;     [and,]host1,host2,...
;;
;; If host is start with "http://", then send http request, otherwise use ntp protocol
;;
;; If host is special name "local", return local time
;;
;; If there are many hosts, first request host1, if not get response, then request host2, ...
;;
;; If there is leading "and,", then check all the hosts and return the largest timestamp
;;
;; For example,
;;
;;     pool.ntp.org
;;     http://www.ntp.org
;;     http://**************
;;     pool.ntp.org,http://worldtimeapi.org/api
;;     pool.ntp.org,http://worldtimeapi.org/api,local
;;     and,pool.ntp.org,http://worldtimeapi.org/api,local
;;
nts = local
nts_timeout = 3

;; Bind runtime key to multiple devices, one line one machine
; devices =

;; Bind runtime key to Python interperter. Each line defines a rule,
;; match all the rules. The rule formats
;;
;;      D
;;      S: symbol start end xxxxxx(md5)
;;
; interps =

;; Insert runtime hooks
; hooks = hooks.py

;; Enable timer
timer = 0

;; Target platforms
; platforms =

;; If there are customized runtime messages
messages = messages.cfg:utf-8

[assert.call]
;; and: function is in obfuscated script and match ruler
;;  or: function is in obfuscated script or match ruler
auto_mode = and

; includes =
; excludes =

[assert.import]
;; and: module is obfuscated and match ruler
;;  or: module is obfuscated or match ruler
auto_mode = and

; includes =
; excludes =

[mix.str]
;; do not mix short string len(s) < this value
threshold = 8

; includes =
; excludes =

[pack]
;; For Darwin to code sign binary file
; codesign_identify =

;; Strip output path to match archive info
strip = 0

;; How to do when the obfuscated module has no matched .pyc in bundle
;;    error, issue a error and exit
;;    warning, issue a warning and continue
;;    ignore, do nothing
;;    append, append it to archive
no_matched_pyc = error

;; Extra options to call PyInstaller when packing the obfuscated scripts
;; One option one line, name and value separted by one whitespace
;; For example
;;
;;   pyi_options = -w
;;                 --add-data default.cfg:.
;;                 --icon myapp.ico
;;
;; The following options could not be used here:
;;
;;   --distpath, --workpath, --specpath
;;
; pyi_options =

[bcc]
unsupported_functions = exec eval super locals __assert_armored__
unsupported_nodes = AsyncFunctionDef AsyncFor AsyncWith Await Yield YieldFrom GeneratorExp NamedExpr MatchValue MatchSingleton MatchSequence MatchMapping MatchClass MatchStar MatchAs MatchOr

;; Include and exclude function/method names
; includes =
; excludes =

;; Use opcode CALL_FUNCTION_EX to patch call
;; Global option, all scripts must be same
call_function_ex = 0

;; Generate bcc function to show right lineno in traceback
;; If disable, lineno is always function definition lineno
trace_lineno = 0

keep_nest_name = 0

;; Do not convert lambda to bcc
ignore_lambda = 0

;; Use op_mkfunc2 to build unsupported functions
enable_pure_function = 1

;; Convert comprehensions to bcc code
enable_comprehension = 1

[windows.x86_64.bcc]
cc = clang.exe
cflags = -O3 -Wno-unsequenced -fno-asynchronous-unwind-tables -fno-unwind-tables --target=x86_64-elf-windows -c -DENABLE_BCC_MEMSET

[windows.x86.bcc]
cc = clang.exe
cflags = --target=i686-elf-linux -O3 -Wno-unsequenced -fno-asynchronous-unwind-tables -fno-unwind-tables -fno-stack-protector -fPIC -mno-sse -std=c99 -c

[linux.x86_64.bcc]
cflags = -O3 -Wno-unsequenced -fno-asynchronous-unwind-tables -fno-unwind-tables -fPIC -fno-stack-protector -c

[linux.aarch64.bcc]
cflags = -O3 -Wno-unsequenced -fno-asynchronous-unwind-tables -fno-unwind-tables -fPIC -fno-stack-protector -shared -nostdlib -DENABLE_BCC_MEMSET -Tlinux.aarch64.ldscript

[linux.x86.bcc]
cflags = -O3 -Wno-unsequenced -fno-asynchronous-unwind-tables -fno-unwind-tables -fno-stack-protector -fPIC -c

[linux.armv7.bcc]
cflags = -O3 -Wno-unsequenced -fno-asynchronous-unwind-tables -fno-unwind-tables -fno-stack-protector -shared -nostdlib -Tlinux.armv7.ldscript

[darwin.x86_64.bcc]
cflags = -O3 -Wno-unsequenced -fno-asynchronous-unwind-tables -fno-unwind-tables --target=x86_64-elf-gnu_linux -fPIC -c -DENABLE_BCC_MEMSET

[darwin.aarch64.bcc]
cflags = -O3 -Wno-unsequenced -fno-asynchronous-unwind-tables -fno-unwind-tables --target=arm64-macho-darwin -fPIC -fno-addrsig -fno-stack-protector -shared -nostdlib -lsystem

[android.x86_64.bcc]
cflags = -O3 -Wno-unsequenced -fno-asynchronous-unwind-tables -fno-unwind-tables -fPIC -fno-stack-protector -c

[android.aarch64.bcc]
cflags = -O3 -Wno-unsequenced -fno-asynchronous-unwind-tables -fno-unwind-tables -fPIC -fno-stack-protector -shared -nostdlib -DENABLE_BCC_MEMSET -Tlinux.aarch64.ldscript

[android.x86.bcc]
cflags = -O3 -Wno-unsequenced -fno-asynchronous-unwind-tables -fno-unwind-tables -fno-stack-protector -fPIC -c

[android.armv7.bcc]
cflags = -O3 -Wno-unsequenced -fno-asynchronous-unwind-tables -fno-unwind-tables -fno-stack-protector -shared -nostdlib -Tlinux.armv7.ldscript

[alpine.x86_64.bcc]
cflags = -O3 -Wno-unsequenced -fno-asynchronous-unwind-tables -fno-unwind-tables -fPIC -fno-stack-protector -c

[alpine.aarch64.bcc]
cflags = -O3 -Wno-unsequenced -fno-asynchronous-unwind-tables -fno-unwind-tables -fPIC -fno-stack-protector -shared -nostdlib -DENABLE_BCC_MEMSET -Tlinux.aarch64.ldscript

[freebsd.x86_64.bcc]
cflags = -O3 -Wno-unsequenced -fno-asynchronous-unwind-tables -fno-unwind-tables -fPIC -fno-stack-protector -c

;;;;;;;;;;;;;;;;;;;;;;
;;
;; Logging settings
;;
;;;;;;;;;;;;;;;;;;;;;;
[loggers]
keys=root,cli,trace,bug

[handlers]
keys=console,trace,debug,bug

[formatters]
keys=console,debug,trace,bug

[logger_root]
handlers=console

[logger_cli]
level=INFO
handlers=console,debug
qualname=cli
propagate=0

[logger_trace]
level=ERROR
handlers=trace
qualname=trace
propagate=0

[logger_bug]
level=INFO
handlers=bug
qualname=cli.bug
propagate=0

[handler_console]
class=StreamHandler
level=INFO
formatter=console
args=(sys.stderr,)

[handler_debug]
class=FileHandler
level=CRITICAL
formatter=debug
args=('pyarmor.debug.log', 'w')
kwargs={'delay': True}

[handler_trace]
class=FileHandler
level=DEBUG
formatter=trace
args=('pyarmor.trace.log', 'w')
kwargs={'delay': True}

[handler_bug]
class=FileHandler
level=INFO
formatter=bug
args=('pyarmor.bug.log', 'w')
kwargs={'delay': True}

[formatter_console]
format=%(levelname)-8s %(message)s

[formatter_debug]
format=%(asctime)s %(message)s

[formatter_trace]
format=%(name)-20s %(message)s

[formatter_bug]
format=%(message)s
