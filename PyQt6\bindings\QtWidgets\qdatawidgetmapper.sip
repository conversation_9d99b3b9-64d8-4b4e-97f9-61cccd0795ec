// qdatawidgetmapper.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDataWidgetMapper : public QObject
{
%TypeHeaderCode
#include <qdatawidgetmapper.h>
%End

public:
    enum SubmitPolicy
    {
        AutoSubmit,
        ManualSubmit,
    };

    explicit QDataWidgetMapper(QObject *parent /TransferThis/ = 0);
    virtual ~QDataWidgetMapper();
    void setModel(QAbstractItemModel *model /KeepReference/);
    QAbstractItemModel *model() const;
    void setItemDelegate(QAbstractItemDelegate *delegate /KeepReference/);
    QAbstractItemDelegate *itemDelegate() const;
    void setRootIndex(const QModelIndex &index);
    QModelIndex rootIndex() const;
    void setOrientation(Qt::Orientation aOrientation);
    Qt::Orientation orientation() const;
    void setSubmitPolicy(QDataWidgetMapper::SubmitPolicy policy);
    QDataWidgetMapper::SubmitPolicy submitPolicy() const;
    void addMapping(QWidget *widget, int section);
    void addMapping(QWidget *widget, int section, const QByteArray &propertyName);
    void removeMapping(QWidget *widget);
    QByteArray mappedPropertyName(QWidget *widget) const;
    int mappedSection(QWidget *widget) const;
    QWidget *mappedWidgetAt(int section) const;
    void clearMapping();
    int currentIndex() const;

public slots:
    void revert();
    virtual void setCurrentIndex(int index);
    void setCurrentModelIndex(const QModelIndex &index);
    bool submit();
    void toFirst();
    void toLast();
    void toNext();
    void toPrevious();

signals:
    void currentIndexChanged(int index);
};
