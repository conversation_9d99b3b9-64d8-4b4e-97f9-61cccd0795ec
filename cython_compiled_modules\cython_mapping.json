{"modules": {"coloros.py": "coloros.cp312-win_amd64.pyd", "coloros15.py": "coloros15.cp312-win_amd64.pyd", "utils.py": "utils.cp312-win_amd64.pyd", "fastboodt.py": "fastboodt.cp312-win_amd64.pyd", "zhidinyishuaxie.py": "zhidinyishuaxie.cp312-win_amd64.pyd", "payload_extractor.py": "payload_extractor.cp312-win_amd64.pyd", "custom_messagebox.py": "custom_messagebox.cp312-win_amd64.pyd", "genduodakhd.py": "genduodakhd.cp312-win_amd64.pyd", "font_extractor.py": "font_extractor.cp312-win_amd64.pyd", "flash_tool.py": "flash_tool.cp312-win_amd64.pyd", "main.py": "main.cp312-win_amd64.pyd"}, "total_modules": 11, "failed_modules": [], "success_rate": "100.0%", "compile_time": "2025-06-23 14:47:50", "build_directory": "cython_compiled_modules"}