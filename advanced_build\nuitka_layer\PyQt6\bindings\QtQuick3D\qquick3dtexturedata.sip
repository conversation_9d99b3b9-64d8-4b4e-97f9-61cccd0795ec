// qquick3dtexturedata.sip generated by MetaSIP
//
// This file is part of the QtQuick3D Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQuick3DTextureData : public QQuick3DObject
{
%TypeHeaderCode
#include <qquick3dtexturedata.h>
%End

public:
    enum Format
    {
        None /PyName=None_/,
        RGBA8,
        RGBA16F,
        RGBA32F,
        RGBE8,
        R8,
        R16,
        R16F,
        R32F,
        BC1,
        BC2,
        BC3,
        BC4,
        BC5,
        BC6H,
        BC7,
        DXT1_RGBA,
        DXT1_RGB,
        DXT3_RGBA,
        DXT5_RGBA,
        ETC2_RGB8,
        ETC2_RGB8A1,
        ETC2_RGBA8,
        ASTC_4x4,
        ASTC_5x4,
        ASTC_5x5,
        ASTC_6x5,
        ASTC_6x6,
        ASTC_8x5,
        ASTC_8x6,
        ASTC_8x8,
        ASTC_10x5,
        ASTC_10x6,
        ASTC_10x8,
        ASTC_10x10,
        ASTC_12x10,
        ASTC_12x12,
    };

    QQuick3DTextureData(QQuick3DObject *parent /TransferThis/ = 0);
    virtual ~QQuick3DTextureData();
    const QByteArray textureData() const;
    void setTextureData(const QByteArray &data);
    QSize size() const;
    void setSize(const QSize &size);
    QQuick3DTextureData::Format format() const;
    void setFormat(QQuick3DTextureData::Format format);
    bool hasTransparency() const;
    void setHasTransparency(bool hasTransparency);
%If (Qt_6_6_0 -)
    int depth() const;
%End
%If (Qt_6_6_0 -)
    void setDepth(int depth);
%End
};
