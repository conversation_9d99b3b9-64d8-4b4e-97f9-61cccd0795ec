import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qtquickdialogs2foreign_p.h"
        name: "QPlatformDialogHelper"
        accessSemantics: "reference"
        prototype: "QObject"
        Enum {
            name: "StandardButtons"
            alias: "StandardButton"
            isFlag: true
            values: [
                "NoButton",
                "Ok",
                "Save",
                "SaveAll",
                "Open",
                "Yes",
                "YesToAll",
                "No",
                "NoToAll",
                "Abort",
                "Retry",
                "Ignore",
                "Close",
                "Cancel",
                "Discard",
                "Help",
                "Apply",
                "Reset",
                "RestoreDefaults",
                "FirstButton",
                "LastButton",
                "LowestBit",
                "HighestBit"
            ]
        }
        Enum {
            name: "ButtonRole"
            values: [
                "InvalidRole",
                "AcceptRole",
                "RejectRole",
                "DestructiveR<PERSON>",
                "<PERSON>R<PERSON>",
                "Help<PERSON><PERSON>",
                "Yes<PERSON><PERSON>",
                "NoRole",
                "ResetR<PERSON>",
                "ApplyR<PERSON>",
                "NRoles",
                "RoleMask",
                "AlternateRole",
                "Stretch",
                "Reverse",
                "EOL"
            ]
        }
        Enum {
            name: "ButtonLayout"
            values: [
                "UnknownLayout",
                "WinLayout",
                "MacLayout",
                "KdeLayout",
                "GnomeLayout",
                "AndroidLayout"
            ]
        }
        Signal { name: "accept" }
        Signal { name: "reject" }
    }
    Component {
        file: "private/qquickabstractdialog_p.h"
        name: "QQuickAbstractDialog"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        Enum {
            name: "StandardCode"
            values: ["Rejected", "Accepted"]
        }
        Property {
            name: "data"
            type: "QObject"
            isList: true
            read: "data"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "parentWindow"
            type: "QWindow"
            isPointer: true
            read: "parentWindow"
            write: "setParentWindow"
            reset: "resetParentWindow"
            notify: "parentWindowChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "title"
            type: "QString"
            read: "title"
            write: "setTitle"
            notify: "titleChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "flags"
            type: "Qt::WindowFlags"
            read: "flags"
            write: "setFlags"
            notify: "flagsChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "modality"
            type: "Qt::WindowModality"
            read: "modality"
            write: "setModality"
            notify: "modalityChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "result"
            type: "int"
            read: "result"
            write: "setResult"
            notify: "resultChanged"
            index: 6
            isFinal: true
        }
        Signal { name: "accepted" }
        Signal { name: "rejected" }
        Signal { name: "parentWindowChanged" }
        Signal { name: "titleChanged" }
        Signal { name: "flagsChanged" }
        Signal { name: "modalityChanged" }
        Signal { name: "visibleChanged" }
        Signal { name: "resultChanged" }
        Method { name: "open" }
        Method { name: "close" }
        Method { name: "accept" }
        Method { name: "reject" }
        Method {
            name: "done"
            Parameter { name: "result"; type: "int" }
        }
    }
    Component {
        file: "private/qquickcolordialog_p.h"
        name: "QQuickColorDialog"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuickAbstractDialog"
        exports: ["QtQuick.Dialogs/ColorDialog 6.4"]
        exportMetaObjectRevisions: [1540]
        Property {
            name: "selectedColor"
            type: "QColor"
            read: "selectedColor"
            write: "setSelectedColor"
            notify: "selectedColorChanged"
            index: 0
        }
        Property {
            name: "options"
            type: "QColorDialogOptions::ColorDialogOptions"
            read: "options"
            write: "setOptions"
            reset: "resetOptions"
            notify: "optionsChanged"
            index: 1
        }
        Signal { name: "selectedColorChanged" }
        Signal { name: "optionsChanged" }
    }
    Component {
        file: "private/qquickfiledialog_p.h"
        name: "QQuickFileDialog"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuickAbstractDialog"
        exports: ["QtQuick.Dialogs/FileDialog 6.2"]
        exportMetaObjectRevisions: [1538]
        Enum {
            name: "FileMode"
            values: ["OpenFile", "OpenFiles", "SaveFile"]
        }
        Property {
            name: "fileMode"
            type: "FileMode"
            read: "fileMode"
            write: "setFileMode"
            notify: "fileModeChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "selectedFile"
            type: "QUrl"
            read: "selectedFile"
            write: "setSelectedFile"
            notify: "selectedFileChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "selectedFiles"
            type: "QUrl"
            isList: true
            read: "selectedFiles"
            notify: "selectedFilesChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "currentFile"
            type: "QUrl"
            read: "currentFile"
            write: "setCurrentFile"
            notify: "currentFileChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "currentFiles"
            type: "QUrl"
            isList: true
            read: "currentFiles"
            write: "setCurrentFiles"
            notify: "currentFilesChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "currentFolder"
            type: "QUrl"
            read: "currentFolder"
            write: "setCurrentFolder"
            notify: "currentFolderChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "options"
            type: "QFileDialogOptions::FileDialogOptions"
            read: "options"
            write: "setOptions"
            reset: "resetOptions"
            notify: "optionsChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "nameFilters"
            type: "QStringList"
            read: "nameFilters"
            write: "setNameFilters"
            reset: "resetNameFilters"
            notify: "nameFiltersChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "selectedNameFilter"
            type: "QQuickFileNameFilter"
            isPointer: true
            read: "selectedNameFilter"
            index: 8
            isReadonly: true
            isConstant: true
        }
        Property {
            name: "defaultSuffix"
            type: "QString"
            read: "defaultSuffix"
            write: "setDefaultSuffix"
            reset: "resetDefaultSuffix"
            notify: "defaultSuffixChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "acceptLabel"
            type: "QString"
            read: "acceptLabel"
            write: "setAcceptLabel"
            reset: "resetAcceptLabel"
            notify: "acceptLabelChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "rejectLabel"
            type: "QString"
            read: "rejectLabel"
            write: "setRejectLabel"
            reset: "resetRejectLabel"
            notify: "rejectLabelChanged"
            index: 11
            isFinal: true
        }
        Signal { name: "fileModeChanged" }
        Signal { name: "selectedFileChanged" }
        Signal { name: "selectedFilesChanged" }
        Signal { name: "currentFileChanged" }
        Signal { name: "currentFilesChanged" }
        Signal { name: "currentFolderChanged" }
        Signal { name: "optionsChanged" }
        Signal { name: "nameFiltersChanged" }
        Signal { name: "defaultSuffixChanged" }
        Signal { name: "acceptLabelChanged" }
        Signal { name: "rejectLabelChanged" }
    }
    Component {
        file: "private/qtquickdialogs2foreign_p.h"
        name: "QQuickFileNameFilter"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "index"
            type: "int"
            read: "index"
            write: "setIndex"
            notify: "indexChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            notify: "nameChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "extensions"
            type: "QStringList"
            read: "extensions"
            notify: "extensionsChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "globs"
            type: "QStringList"
            read: "globs"
            notify: "globsChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Signal {
            name: "indexChanged"
            Parameter { name: "index"; type: "int" }
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "QString" }
        }
        Signal {
            name: "extensionsChanged"
            Parameter { name: "extensions"; type: "QStringList" }
        }
        Signal {
            name: "globsChanged"
            Parameter { name: "globs"; type: "QStringList" }
        }
    }
    Component {
        file: "private/qquickfolderdialog_p.h"
        name: "QQuickFolderDialog"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuickAbstractDialog"
        exports: ["QtQuick.Dialogs/FolderDialog 6.3"]
        exportMetaObjectRevisions: [1539]
        Property {
            name: "currentFolder"
            type: "QUrl"
            read: "currentFolder"
            write: "setCurrentFolder"
            notify: "currentFolderChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "selectedFolder"
            type: "QUrl"
            read: "selectedFolder"
            write: "setSelectedFolder"
            notify: "selectedFolderChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "options"
            type: "QFileDialogOptions::FileDialogOptions"
            read: "options"
            write: "setOptions"
            reset: "resetOptions"
            notify: "optionsChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "acceptLabel"
            type: "QString"
            read: "acceptLabel"
            write: "setAcceptLabel"
            reset: "resetAcceptLabel"
            notify: "acceptLabelChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "rejectLabel"
            type: "QString"
            read: "rejectLabel"
            write: "setRejectLabel"
            reset: "resetRejectLabel"
            notify: "rejectLabelChanged"
            index: 4
            isFinal: true
        }
        Signal { name: "currentFolderChanged" }
        Signal { name: "selectedFolderChanged" }
        Signal { name: "optionsChanged" }
        Signal { name: "acceptLabelChanged" }
        Signal { name: "rejectLabelChanged" }
    }
    Component {
        file: "private/qquickfontdialog_p.h"
        name: "QQuickFontDialog"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuickAbstractDialog"
        exports: ["QtQuick.Dialogs/FontDialog 6.2"]
        exportMetaObjectRevisions: [1538]
        Property {
            name: "selectedFont"
            type: "QFont"
            read: "selectedFont"
            write: "setSelectedFont"
            notify: "selectedFontChanged"
            index: 0
        }
        Property {
            name: "currentFont"
            type: "QFont"
            read: "currentFont"
            write: "setCurrentFont"
            notify: "currentFontChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "options"
            type: "QFontDialogOptions::FontDialogOptions"
            read: "options"
            write: "setOptions"
            reset: "resetOptions"
            notify: "optionsChanged"
            index: 2
        }
        Signal { name: "selectedFontChanged" }
        Signal { name: "currentFontChanged" }
        Signal { name: "optionsChanged" }
    }
    Component {
        file: "private/qquickmessagedialog_p.h"
        name: "QQuickMessageDialog"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuickAbstractDialog"
        extension: "QPlatformDialogHelper"
        extensionIsNamespace: true
        exports: ["QtQuick.Dialogs/MessageDialog 6.3"]
        exportMetaObjectRevisions: [1539]
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            notify: "textChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "informativeText"
            type: "QString"
            read: "informativeText"
            write: "setInformativeText"
            notify: "informativeTextChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "detailedText"
            type: "QString"
            read: "detailedText"
            write: "setDetailedText"
            notify: "detailedTextChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "buttons"
            type: "QPlatformDialogHelper::StandardButtons"
            read: "buttons"
            write: "setButtons"
            notify: "buttonsChanged"
            index: 3
            isFinal: true
        }
        Signal { name: "textChanged" }
        Signal { name: "informativeTextChanged" }
        Signal { name: "detailedTextChanged" }
        Signal { name: "buttonsChanged" }
        Signal {
            name: "buttonClicked"
            Parameter { name: "button"; type: "QPlatformDialogHelper::StandardButton" }
            Parameter { name: "role"; type: "QPlatformDialogHelper::ButtonRole" }
        }
        Method {
            name: "handleClick"
            Parameter { name: "button"; type: "QPlatformDialogHelper::StandardButton" }
            Parameter { name: "role"; type: "QPlatformDialogHelper::ButtonRole" }
        }
    }
}
