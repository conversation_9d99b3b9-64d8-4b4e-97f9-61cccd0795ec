// membersheet.sip generated by MetaSIP
//
// This file is part of the QtDesigner Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDesignerMemberSheetExtension
{
%TypeHeaderCode
#include <membersheet.h>
%End

public:
    QDesignerMemberSheetExtension();
    virtual ~QDesignerMemberSheetExtension();
    virtual int count() const = 0 /__len__/;
    virtual int indexOf(const QString &name) const = 0;
    virtual QString memberName(int index) const = 0;
    virtual QString memberGroup(int index) const = 0;
    virtual void setMemberGroup(int index, const QString &group) = 0;
    virtual bool isVisible(int index) const = 0;
    virtual void setVisible(int index, bool b) = 0;
    virtual bool isSignal(int index) const = 0;
    virtual bool isSlot(int index) const = 0;
    virtual bool inheritedFromWidget(int index) const = 0;
    virtual QString declaredInClass(int index) const = 0;
    virtual QString signature(int index) const = 0;
    virtual QList<QByteArray> parameterTypes(int index) const = 0;
    virtual QList<QByteArray> parameterNames(int index) const = 0;

private:
    QDesignerMemberSheetExtension(const QDesignerMemberSheetExtension &);
};
