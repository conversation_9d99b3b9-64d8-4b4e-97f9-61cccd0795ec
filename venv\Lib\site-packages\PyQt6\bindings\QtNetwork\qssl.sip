// qssl.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_SSL)

namespace QSsl
{
%TypeHeaderCode
#include <qssl.h>
%End

    enum KeyType
    {
        PrivateKey,
        PublicKey,
    };

    enum EncodingFormat
    {
        Pem,
        Der,
    };

    enum KeyAlgorithm
    {
        Opaque,
        Rsa,
        Dsa,
        Ec,
        Dh,
    };

    enum AlternativeNameEntryType
    {
        EmailEntry,
        DnsEntry,
        IpAddressEntry,
    };

    enum SslProtocol
    {
        UnknownProtocol,
        TlsV1_0,
        TlsV1_0OrLater,
        TlsV1_1,
        TlsV1_1OrLater,
        TlsV1_2,
        TlsV1_2OrLater,
        AnyProtocol,
        SecureProtocols,
        DtlsV1_0,
        DtlsV1_0OrLater,
        DtlsV1_2,
        DtlsV1_2OrLater,
        TlsV1_3,
        TlsV1_3OrLater,
    };

    enum SslOption /BaseType=Flag/
    {
        SslOptionDisableEmptyFragments,
        SslOptionDisableSessionTickets,
        SslOptionDisableCompression,
        SslOptionDisableServerNameIndication,
        SslOptionDisableLegacyRenegotiation,
        SslOptionDisableSessionSharing,
        SslOptionDisableSessionPersistence,
        SslOptionDisableServerCipherPreference,
    };

    typedef QFlags<QSsl::SslOption> SslOptions;

    enum class AlertLevel
    {
        Warning,
        Fatal,
        Unknown,
    };

    enum class AlertType
    {
        CloseNotify,
        UnexpectedMessage,
        BadRecordMac,
        RecordOverflow,
        DecompressionFailure,
        HandshakeFailure,
        NoCertificate,
        BadCertificate,
        UnsupportedCertificate,
        CertificateRevoked,
        CertificateExpired,
        CertificateUnknown,
        IllegalParameter,
        UnknownCa,
        AccessDenied,
        DecodeError,
        DecryptError,
        ExportRestriction,
        ProtocolVersion,
        InsufficientSecurity,
        InternalError,
        InappropriateFallback,
        UserCancelled,
        NoRenegotiation,
        MissingExtension,
        UnsupportedExtension,
        CertificateUnobtainable,
        UnrecognizedName,
        BadCertificateStatusResponse,
        BadCertificateHashValue,
        UnknownPskIdentity,
        CertificateRequired,
        NoApplicationProtocol,
        UnknownAlertMessage,
    };

%If (Qt_6_1_0 -)

    enum class ImplementedClass
    {
        Key,
        Certificate,
        Socket,
        DiffieHellman,
        EllipticCurve,
        Dtls,
%If (Qt_6_2_0 -)
        DtlsCookie,
%End
    };

%End
%If (Qt_6_1_0 -)

    enum class SupportedFeature
    {
        CertificateVerification,
        ClientSideAlpn,
        ServerSideAlpn,
        Ocsp,
        Psk,
        SessionTicket,
        Alerts,
    };

%End
};

%End
