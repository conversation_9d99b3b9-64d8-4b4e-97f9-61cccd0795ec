// qboxlayout.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QBoxLayout : public QLayout
{
%TypeHeaderCode
#include <qboxlayout.h>
%End

public:
    enum Direction
    {
        LeftToRight,
        RightToLeft,
        TopToBottom,
        BottomToTop,
        Down,
        Up,
    };

    QBoxLayout(QBoxLayout::Direction direction, QWidget *parent /TransferThis/ = 0);
    virtual ~QBoxLayout();
    QBoxLayout::Direction direction() const;
    void setDirection(QBoxLayout::Direction);
    void addSpacing(int size);
    void addStretch(int stretch = 0);
    void addWidget(QWidget * /GetWrapper/, int stretch = 0, Qt::Alignment alignment = Qt::Alignment());
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipCpp->addWidget(a0, a1, *a2);
        Py_END_ALLOW_THREADS
        
        // The layout's parent widget (if there is one) will now have ownership.
        QWidget *parent = sipCpp->parentWidget();
        
        if (parent)
        {
            PyObject *py_parent = sipGetPyObject(parent, sipType_QWidget);
        
            if (py_parent)
                sipTransferTo(a0Wrapper, py_parent);
        }
        else
        {
            // For now give the Python ownership to the layout.  This maintains
            // compatibility with previous versions and allows addWidget(QWidget()).
            sipTransferTo(a0Wrapper, sipSelf);
        }
%End

    void addLayout(QLayout *layout /Transfer/, int stretch = 0);
    void addStrut(int);
    virtual void addItem(QLayoutItem * /Transfer/);
    void insertSpacing(int index, int size);
    void insertStretch(int index, int stretch = 0);
    void insertWidget(int index, QWidget *widget /GetWrapper/, int stretch = 0, Qt::Alignment alignment = Qt::Alignment());
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipCpp->insertWidget(a0, a1, a2, *a3);
        Py_END_ALLOW_THREADS
        
        // The layout's parent widget (if there is one) will now have ownership.
        QWidget *parent = sipCpp->parentWidget();
        
        if (parent)
        {
            PyObject *py_parent = sipGetPyObject(parent, sipType_QWidget);
        
            if (py_parent)
                sipTransferTo(a1Wrapper, py_parent);
        }
        else
        {
            // For now give the Python ownership to the layout.  This maintains
            // compatibility with previous versions and allows insertWidget(QWidget()).
            sipTransferTo(a1Wrapper, sipSelf);
        }
%End

    void insertLayout(int index, QLayout *layout /Transfer/, int stretch = 0);
    bool setStretchFactor(QWidget *w, int stretch);
    bool setStretchFactor(QLayout *l, int stretch);
    virtual QSize sizeHint() const;
    virtual QSize minimumSize() const;
    virtual QSize maximumSize() const;
    virtual bool hasHeightForWidth() const;
    virtual int heightForWidth(int) const;
    virtual int minimumHeightForWidth(int) const;
    virtual Qt::Orientations expandingDirections() const;
    virtual void invalidate();
    virtual QLayoutItem *itemAt(int) const;
    virtual QLayoutItem *takeAt(int) /TransferBack/;
    virtual int count() const;
    virtual void setGeometry(const QRect &);
    virtual int spacing() const;
    virtual void setSpacing(int spacing);
    void addSpacerItem(QSpacerItem *spacerItem /Transfer/);
    void insertSpacerItem(int index, QSpacerItem *spacerItem /Transfer/);
    void setStretch(int index, int stretch);
    int stretch(int index) const;
    void insertItem(int index, QLayoutItem * /Transfer/);
};

class QHBoxLayout : public QBoxLayout
{
%TypeHeaderCode
#include <qboxlayout.h>
%End

public:
    QHBoxLayout();
    explicit QHBoxLayout(QWidget *parent /TransferThis/);
    virtual ~QHBoxLayout();
};

class QVBoxLayout : public QBoxLayout
{
%TypeHeaderCode
#include <qboxlayout.h>
%End

public:
    QVBoxLayout();
    explicit QVBoxLayout(QWidget *parent /TransferThis/);
    virtual ~QVBoxLayout();
};
