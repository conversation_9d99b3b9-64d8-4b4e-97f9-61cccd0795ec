{#     Copyright 2025, <PERSON>, mailto:<EMAIL> find license text at end of file #}

{% from 'HelperSlotsCommon.c.j2' import goto_exit %}

{% macro bytes_slot(props, operator, nb_slot, target, left, right, result, operand1, operand2, exit_result_ok, exit_result_exception) %}
    // Not every code path will make use of all possible results.
    NUITKA_MAY_BE_UNUSED PyObject *obj_result;

    {# TODO: Could and should in-line and specialize this per slot too. #}
{% if nb_slot == "nb_add" %}
    PyObject *x = {{ left.getSlotCallExpression("sq_concat", "PyBytes_Type.tp_as_sequence->sq_concat", operand1, operand2) }};
{% elif nb_slot == "nb_multiply" %}
    PyObject *x = {{ left.getSlotCallExpression("sq_repeat", "PyBytes_Type.tp_as_sequence->sq_repeat", operand1, operand2) }};
{% else %}
    PyObject *x = {{ left.getSlotCallExpression(nb_slot, "PyBytes_Type.tp_as_number->" + nb_slot, operand1, operand2) }};
{% endif %}
    assert(x != Py_NotImplemented);

    {{ goto_exit(props, "exit_result_object", "x") }}

exit_result_object:
    if (unlikely(obj_result == NULL)) {
        {{ goto_exit(props, exit_result_exception) }}
    }
{% if target %}
    {{ target.getAssignFromObjectExpressionCode(result, "obj_result") }}
{% else %}
    {# TODO: Check the reference we were handed down and do it in-place really. #}
    // We got an object handed, that we have to release.
    Py_DECREF({{ operand1 }});

    {{ operand1 }} = obj_result;
{% endif %}
    {{ goto_exit(props, exit_result_ok) }}

{% endmacro %}

{#     Part of "Nuitka", an optimizing Python compiler that is compatible and   #}
{#     integrates with CPython, but also works on its own.                      #}
{#                                                                              #}
{#     Licensed under the Apache License, Version 2.0 (the "License");          #}
{#     you may not use this file except in compliance with the License.         #}
{#     You may obtain a copy of the License at                                  #}
{#                                                                              #}
{#        http://www.apache.org/licenses/LICENSE-2.0                            #}
{#                                                                              #}
{#     Unless required by applicable law or agreed to in writing, software      #}
{#     distributed under the License is distributed on an "AS IS" BASIS,        #}
{#     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. #}
{#     See the License for the specific language governing permissions and      #}
{#     limitations under the License.                                           #}
