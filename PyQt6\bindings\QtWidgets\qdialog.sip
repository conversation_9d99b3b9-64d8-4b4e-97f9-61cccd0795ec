// qdialog.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDialog : public QWidget
{
%TypeHeaderCode
#include <qdialog.h>
%End

public:
    QDialog(QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    virtual ~QDialog();

    enum DialogCode /BaseType=IntEnum/
    {
        Rejected,
        Accepted,
    };

    int result() const;
    virtual void setVisible(bool visible);
    virtual QSize sizeHint() const;
    virtual QSize minimumSizeHint() const;
    void setSizeGripEnabled(bool);
    bool isSizeGripEnabled() const;
    void setModal(bool modal);
    void setResult(int r);
    virtual int exec() /PostHook=__pyQtPostEventLoopHook__,PreHook=__pyQtPreEventLoopHook__,ReleaseGIL/;
%MethodCode
        // Transfer ownership back to Python (a modal dialog will probably have the
        // main window as it's parent).  This means the Qt dialog will be deleted when
        // the Python wrapper is garbage collected.  Although this is a little
        // inconsistent, it saves having to code it explicitly to avoid the memory
        // leak.
        sipTransferBack(sipSelf);
        
        Py_BEGIN_ALLOW_THREADS
        sipRes = sipSelfWasArg ? sipCpp->QDialog::exec()
                               : sipCpp->exec();
        Py_END_ALLOW_THREADS
%End

public slots:
    virtual void done(int);
    virtual void accept();
    virtual void reject();
    virtual void open();

signals:
    void accepted();
    void finished(int result);
    void rejected();

protected:
    virtual void keyPressEvent(QKeyEvent *);
    virtual void closeEvent(QCloseEvent *);
    virtual void showEvent(QShowEvent *);
    virtual void resizeEvent(QResizeEvent *);
    virtual void contextMenuEvent(QContextMenuEvent *);
    virtual bool eventFilter(QObject *, QEvent *);
};
