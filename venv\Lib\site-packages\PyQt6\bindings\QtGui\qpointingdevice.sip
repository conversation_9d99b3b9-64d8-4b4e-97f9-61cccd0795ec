// qpointingdevice.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPointingDeviceUniqueId
{
%TypeHeaderCode
#include <qpointingdevice.h>
%End

public:
    QPointingDeviceUniqueId();
    static QPointingDeviceUniqueId fromNumericId(qint64 id);
    bool isValid() const;
    qint64 numericId() const;
    Py_hash_t __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};

bool operator==(QPointingDeviceUniqueId lhs, QPointingDeviceUniqueId rhs);
bool operator!=(QPointingDeviceUniqueId lhs, QPointingDeviceUniqueId rhs);

class QPointingDevice : public QInputDevice
{
%TypeHeaderCode
#include <qpointingdevice.h>
%End

public:
    enum class PointerType /BaseType=Flag/
    {
        Unknown,
        Generic,
        Finger,
        Pen,
        Eraser,
        Cursor,
        AllPointerTypes,
    };

    typedef QFlags<QPointingDevice::PointerType> PointerTypes;
    QPointingDevice(const QString &name, qint64 systemId, QInputDevice::DeviceType devType, QPointingDevice::PointerType pType, QInputDevice::Capabilities caps, int maxPoints, int buttonCount, const QString &seatName = QString(), QPointingDeviceUniqueId uniqueId = QPointingDeviceUniqueId(), QObject *parent /TransferThis/ = 0);
    QPointingDevice(QObject *parent /TransferThis/ = 0);
    virtual ~QPointingDevice();
    QPointingDevice::PointerType pointerType() const;
    int maximumPoints() const;
    int buttonCount() const;
    QPointingDeviceUniqueId uniqueId() const;
    static const QPointingDevice *primaryPointingDevice(const QString &seatName = QString());
    bool operator==(const QPointingDevice &other) const;
};
