// abstractformwindowmanager.sip generated by MetaSIP
//
// This file is part of the QtDesigner Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDesignerFormWindowManagerInterface : public QObject /Abstract/
{
%TypeHeaderCode
#include <abstractformwindowmanager.h>
%End

public:
    explicit QDesignerFormWindowManagerInterface(QObject *parent /TransferThis/ = 0);
    virtual ~QDesignerFormWindowManagerInterface();
    QAction *actionFormLayout() const /Transfer/;
    QAction *actionSimplifyLayout() const /Transfer/;
    virtual QDesignerFormWindowInterface *activeFormWindow() const = 0;
    virtual int formWindowCount() const = 0;
    virtual QDesignerFormWindowInterface *formWindow(int index) const = 0 /Transfer/;
    virtual QDesignerFormWindowInterface *createFormWindow(QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags()) = 0;
    virtual QDesignerFormEditorInterface *core() const = 0;

signals:
    void formWindowAdded(QDesignerFormWindowInterface *formWindow);
    void formWindowRemoved(QDesignerFormWindowInterface *formWindow);
    void activeFormWindowChanged(QDesignerFormWindowInterface *formWindow);
    void formWindowSettingsChanged(QDesignerFormWindowInterface *fw);

public slots:
    virtual void addFormWindow(QDesignerFormWindowInterface *formWindow) = 0;
    virtual void removeFormWindow(QDesignerFormWindowInterface *formWindow) = 0;
    virtual void setActiveFormWindow(QDesignerFormWindowInterface *formWindow) = 0;

public:
    enum Action
    {
        CutAction,
        CopyAction,
        PasteAction,
        DeleteAction,
        SelectAllAction,
        LowerAction,
        RaiseAction,
        UndoAction,
        RedoAction,
        HorizontalLayoutAction,
        VerticalLayoutAction,
        SplitHorizontalAction,
        SplitVerticalAction,
        GridLayoutAction,
        FormLayoutAction,
        BreakLayoutAction,
        AdjustSizeAction,
        SimplifyLayoutAction,
        DefaultPreviewAction,
        FormWindowSettingsDialogAction,
    };

    enum ActionGroup
    {
        StyledPreviewActionGroup,
    };

    virtual QAction *action(QDesignerFormWindowManagerInterface::Action action) const = 0 /Transfer/;
    virtual QActionGroup *actionGroup(QDesignerFormWindowManagerInterface::ActionGroup actionGroup) const = 0 /Transfer/;

public slots:
    virtual void showPreview() = 0;
    virtual void closeAllPreviews() = 0;
    virtual void showPluginDialog() = 0;
};
