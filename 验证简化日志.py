# -*- coding: utf-8 -*-
"""
验证简化日志修改
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_simplified_logs():
    """验证简化日志修改"""
    print("🔍 验证简化日志修改...")
    
    try:
        # 读取源代码文件
        with open('qz4n.py', 'r', encoding='utf-8') as f:
            qz4n_content = f.read()
        
        with open('wr3j.py', 'r', encoding='utf-8') as f:
            wr3j_content = f.read()
            
        with open('bv8k.py', 'r', encoding='utf-8') as f:
            bv8k_content = f.read()
        
        # 检查移除的日志
        removed_logs = [
            # 刷机前检查的详细日志
            ('移除"正在检查设备模式..."', '"正在检查设备模式..."' not in qz4n_content),
            ('移除"当前模式:"', '"当前模式:"' not in qz4n_content),
            ('移除"检测到Fastboot模式，正在切换..."', '"检测到Fastboot模式，正在切换到Bootloader模式..."' not in qz4n_content),
            ('移除"等待设备切换模式..."', '"等待设备切换模式..."' not in qz4n_content),
            
            # 刷机后检查的详细日志
            ('移除"正在检查刷机后设备状态..."', '"正在检查刷机后设备状态..."' not in wr3j_content),
            ('移除"正在检查刷机后设备状态..."', '"正在检查刷机后设备状态..."' not in bv8k_content),
        ]
        
        # 检查保留的关键日志
        kept_logs = [
            # 保留的成功消息
            ('保留"成功切换到Bootloader模式"', '"✅ 成功切换到Bootloader模式"' in qz4n_content),
            ('保留"设备已在Bootloader模式"', '"✅ 设备已在Bootloader模式"' in qz4n_content),
            ('保留"设备在Fastboot模式，可以继续操作"', '"✅ 设备在Fastboot模式，可以继续操作"' in wr3j_content),
            ('保留"设备在Fastboot模式，可以继续操作"', '"✅ 设备在Fastboot模式，可以继续操作"' in bv8k_content),
        ]
        
        print("\n📋 移除的日志检查:")
        all_removed = True
        for name, condition in removed_logs:
            if condition:
                print(f"✅ {name} - 已移除")
            else:
                print(f"❌ {name} - 仍存在")
                all_removed = False
        
        print("\n📋 保留的日志检查:")
        all_kept = True
        for name, condition in kept_logs:
            if condition:
                print(f"✅ {name} - 已保留")
            else:
                print(f"❌ {name} - 已丢失")
                all_kept = False
        
        # 检查功能完整性
        print("\n🔧 功能完整性检查:")
        functionality_checks = [
            ('模式检查功能', 'check_and_switch_to_bootloader' in qz4n_content),
            ('刷机后检查功能', 'check_post_flash_mode' in wr3j_content),
            ('强解后检查功能', 'check_post_flash_mode' in bv8k_content),
            ('fastboot类型检测', 'check_fastboot_type' in qz4n_content),
        ]
        
        all_functional = True
        for name, condition in functionality_checks:
            if condition:
                print(f"✅ {name} - 正常")
            else:
                print(f"❌ {name} - 缺失")
                all_functional = False
        
        print("\n🎯 简化后的日志流程:")
        print("刷机前:")
        print("  - ✅ 成功切换到Bootloader模式")
        print("  - ✅ 设备已在Bootloader模式")
        print("")
        print("刷机中:")
        print("  - 开始刷机...")
        print("  - 载入刷机")
        print("  - 正在切换模式...请勿触碰设备")
        print("  - Loading.. [X/Y](请勿断开设备) 进度：X%")
        print("  - partition OK")
        print("  - 进度：100%")
        print("  - 完成")
        print("")
        print("刷机后:")
        print("  - ✅ 设备在Fastboot模式，可以继续操作")
        
        if all_removed and all_kept and all_functional:
            print("\n🎉 所有简化修改都已正确应用!")
            print("日志输出现在更加简洁，只显示关键信息")
        else:
            print("\n❌ 发现问题，请检查修改")
        
        return all_removed and all_kept and all_functional
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    verify_simplified_logs()
