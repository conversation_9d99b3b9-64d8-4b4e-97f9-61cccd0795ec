// qsslserver.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_4_0 -)
%If (PyQt_SSL)

class QSslServer : public QTcpServer
{
%TypeHeaderCode
#include <qsslserver.h>
%End

public:
    explicit QSslServer(QObject *parent /TransferThis/ = 0);
    virtual ~QSslServer();
    void setSslConfiguration(const QSslConfiguration &sslConfiguration);
    QSslConfiguration sslConfiguration() const;
    void setHandshakeTimeout(int timeout);
    int handshakeTimeout() const;

signals:
    void sslErrors(QSslSocket *socket, const QList<QSslError> &errors);
    void peerVerifyError(QSslSocket *socket, const QSslError &error);
    void errorOccurred(QSslSocket *socket, QAbstractSocket::SocketError error);
    void preSharedKeyAuthenticationRequired(QSslSocket *socket, QSslPreSharedKeyAuthenticator *authenticator);
    void alertSent(QSslSocket *socket, QSsl::AlertLevel level, QSsl::AlertType type, const QString &description);
    void alertReceived(QSslSocket *socket, QSsl::AlertLevel level, QSsl::AlertType type, const QString &description);
    void handshakeInterruptedOnError(QSslSocket *socket, const QSslError &error);
    void startedEncryptionHandshake(QSslSocket *socket);

protected:
    virtual void incomingConnection(qintptr socket);
};

%End
%End
