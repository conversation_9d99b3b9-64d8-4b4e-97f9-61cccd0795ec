# -*- python -*-
#     Copyright 2025, <PERSON>, mailto:<EMAIL> find license text at end of file


"""
The Nuitka scons file. If you have Scons or platform knowledge, please be
especially invited and contribute improvements.

This file is used to build an executable or shared library. Nuitka needs no
build process for itself, although it can be compiled using the same method.
"""

# Make nuitka package importable from calling installation

import sys
import os
import types

sys.modules["nuitka"] = types.ModuleType("nuitka")
sys.modules["nuitka"].__path__ = [os.environ["NUITKA_PACKAGE_DIR"]]

# We are in the build.build package really.
import nuitka.build  # pylint: disable=unused-import

__package__ = "nuitka.build"  # pylint: disable=redefined-builtin

# isort:start

from SCons.Script import (  # pylint: disable=I0021,import-error
    ARGUMENTS,
    Environment,
    GetOption,
)

from nuitka.Tracing import (
    my_print,
    scons_details_logger,
    scons_logger,
    setQuiet,
)
from nuitka.utils.FileOperations import (
    changeFilenameExtension,
    getExternalUsePath,
    getFilenameExtension,
)
from nuitka.utils.Json import loadJsonFromFilename
from nuitka.utils.Utils import (
    isAIX,
    isDebianBasedLinux,
    isFedoraBasedLinux,
    isFreeBSD,
    isMacOS,
)

from .DataComposerInterface import getConstantBlobFilename
from .SconsCaching import enableCcache, enableClcache
from .SconsCompilerSettings import (
    addConstantBlobFile,
    checkWindowsCompilerFound,
    decideConstantsBlobResourceMode,
    enableWindowsStackSize,
    importEnvironmentVariableSettings,
    reportCCompiler,
    setupCCompiler,
    switchFromGccToGpp,
)
from .SconsHacks import getEnhancedToolDetect, makeGccUseLinkerFile
from .SconsProgress import enableSconsProgressBar, setSconsProgressBarTotal
from .SconsSpawn import enableSpawnMonitoring
from .SconsUtils import (
    addClangClPathFromMSVC,
    changeKeyboardInterruptToErrorExit,
    createDefinitionsFile,
    createEnvironment,
    getArgumentBool,
    getArgumentDefaulted,
    getArgumentInt,
    getArgumentList,
    getArgumentRequired,
    getExecutablePath,
    getMsvcVersionString,
    initScons,
    isClangName,
    isGccName,
    makeResultPathFileSystemEncodable,
    prepareEnvironment,
    provideStaticSourceFile,
    raiseNoCompilerFoundErrorExit,
    scanSourceDir,
    setArguments,
    setupScons,
    writeSconsReport,
)

# spell-checker: ignore ccversion,cflags,ccflags,werror,cppdefines,cpppath,linkflags,libpath

# Set the arguments.
setArguments(ARGUMENTS)

# Set up the basic stuff.
initScons()

# The directory containing the C files generated by Nuitka to be built using
# scons. They are referred to as sources from here on.
source_dir = getArgumentRequired("source_dir")

# The directory containing Nuitka provided C files to be built and where it
# should be used.
nuitka_src = getArgumentRequired("nuitka_src")
static_src = os.path.join(source_dir, "static")

# The name of executable that we are supposed to produce.
result_exe = getArgumentRequired("result_exe")

# Name of the main program (in executable mode, typically __main__ but can be in a package too.)
main_module_name = getArgumentDefaulted("main_module_name", "__main__")

# Full names shall be used, no remapping for cacheable filenames.
full_names = getArgumentBool("full_names", False)

# Module mode: Create a Python extension module, create an executable otherwise.
module_mode = getArgumentBool("module_mode", False)

# Debug mode: Less optimizations, debug information in the resulting binary.
debug_mode = getArgumentBool("debug_mode", False)

# Debugger mode: Debug information in the resulting binary and intention to run
# in debugger.
debugger_mode = getArgumentBool("debugger_mode", False)

# Profiling mode: Outputs vmprof based information from program run.
profile_mode = getArgumentBool("profile_mode", False)

# Python version to target.
python_version_str = getArgumentRequired("python_version")
python_version = tuple(int(d) for d in python_version_str.split("."))

gil_mode = getArgumentBool("gil_mode")

# The ABI flags to target.
abiflags = getArgumentDefaulted("abiflags", "")

if not gil_mode and "t" not in abiflags:
    abiflags = "t" + abiflags

python_abi_version = python_version_str + abiflags

# Python debug mode: reference count checking, assertions in CPython core.
python_debug = getArgumentBool("python_debug", False)

# Full compatibility, even where it's stupid, i.e. do not provide information,
# even if available, in order to assert maximum compatibility. Intended to
# control level of compatibility to absurd.
full_compat_mode = getArgumentBool("full_compat", False)

# Experimental indications. Do things that are not yet safe to do.
experimental = getArgumentList("experimental", "")

# Deployment mode
deployment_mode = getArgumentBool("deployment", False)

# Experimental indications. Do things that are not yet safe to do.
no_deployment = getArgumentList("no_deployment", "")

# Debug mode indications. Do check things with fine granularity.
debug_modes = getArgumentList("debug_modes", "")

# Tracing mode. Output program progress.
trace_mode = getArgumentBool("trace_mode", False)

# LTO mode: Use link time optimizations of C compiler if available and known
# good with the compiler in question.
lto_mode = getArgumentDefaulted("lto_mode", "auto")

# PGO mode: Use profile guided optimization of C compiler if available.
pgo_mode = getArgumentDefaulted("pgo_mode", "no")

# Console mode specified
console_mode = getArgumentDefaulted("console_mode", "attach")

# Windows might be running a Python whose DLL we have to use.
uninstalled_python = getArgumentBool("uninstalled_python", False)

# Unstripped mode: Do not remove debug symbols.
unstripped_mode = getArgumentBool("unstripped_mode", False)

# Target arch, uses for compiler choice and quick linking of constants binary
# data.
target_arch = getArgumentRequired("target_arch")

# MinGW compiler mode, optional and interesting to Windows only.
mingw_mode = getArgumentBool("mingw_mode", False)

# Clang compiler mode, forced on macOS and FreeBSD (excluding PowerPC), optional on Linux.
clang_mode = getArgumentBool("clang_mode", False)

# Clang on Windows with no requirement to use MinGW64 or using MSYS2 MinGW flavor,
# is changed to ClangCL from Visual Studio.
clangcl_mode = False
if os.name == "nt" and not mingw_mode and clang_mode:
    clang_mode = False
    clangcl_mode = True

# Frozen modules count, determines the need for the bytecode frozen
# modules loader.
frozen_modules = getArgumentInt("frozen_modules", 0)

# Standalone mode
standalone_mode = getArgumentBool("standalone_mode", False)

# Onefile mode
onefile_mode = getArgumentBool("onefile_mode", False)

# MacOS bundle
macos_bundle_mode = getArgumentBool("macos_bundle_mode", False)

# Onefile temp mode
onefile_temp_mode = getArgumentBool("onefile_temp_mode", False)

forced_stdout_path = getArgumentDefaulted("forced_stdout_path", None)
forced_stderr_path = getArgumentDefaulted("forced_stderr_path", None)

# Show scons mode, output information about Scons operation
show_scons_mode = getArgumentBool("show_scons", False)
scons_details_logger.is_quiet = not show_scons_mode

if int(os.getenv("NUITKA_QUIET", "0")):
    setQuiet()

# Home of Python to be compiled against, used to find include files and
# libraries to link against.
python_prefix = getArgumentRequired("python_prefix")

python_prefix_external = getExternalUsePath(python_prefix)

# Forced MSVC version (windows-only)
msvc_version = getArgumentDefaulted("msvc_version", None)

# Disable ccache/clcache usage if that is requested
disable_ccache = getArgumentBool("disable_ccache", False)

no_python_warnings = getArgumentBool("no_python_warnings", False)

# sys.flags values to pass along
# python_sysflag_py3k_warning
python_sysflag_py3k_warning = getArgumentBool("python_sysflag_py3k_warning", False)
# python_sysflag_division_warning
python_sysflag_division_warning = getArgumentBool(
    "python_sysflag_division_warning", False
)
# python_sysflag_division_warning
python_sysflag_bytes_warning = getArgumentBool("python_sysflag_bytes_warning", False)
# python_sysflag_no_site
python_sysflag_no_site = getArgumentBool("python_sysflag_no_site", False)
# python_sysflag_verbose
python_sysflag_verbose = getArgumentBool("python_sysflag_verbose", False)
# python_sysflag_unicode
python_sysflag_unicode = getArgumentBool("python_sysflag_unicode", False)
# python_sysflag_utf8
python_sysflag_utf8 = getArgumentBool("python_sysflag_utf8", False)
# python_sysflag_optimize
python_sysflag_optimize = getArgumentInt("python_sysflag_optimize", 0)
# python_sysflag_dontwritebytecode
python_sysflag_dontwritebytecode = getArgumentBool(
    "python_sysflag_dontwritebytecode", False
)
# python_flag_no_asserts
python_flag_no_asserts = getArgumentBool("python_flag_no_asserts", False)
# python_flag_no_docstrings
python_flag_no_docstrings = getArgumentBool("python_flag_no_docstrings", False)
# python_flag_no_annotations
python_flag_no_annotations = getArgumentBool("python_flag_no_annotations", False)
# python_sysflag_no_randomization
python_sysflag_no_randomization = getArgumentBool(
    "python_sysflag_no_randomization", False
)

# python_sysflag_unbuffered
python_sysflag_unbuffered = getArgumentBool("python_sysflag_unbuffered", False)

# python_sysflag_isolated
python_sysflag_isolated = getArgumentBool("python_sysflag_isolated", False)

# file reference mode
file_reference_mode = getArgumentRequired("file_reference_mode")

# Preprocessor defines from plugins
cpp_defines = getArgumentList("cpp_defines", "")
cpp_include_dirs = getArgumentList("cpp_include_dirs", "")
link_dirs = getArgumentList("link_dirs", "")
link_libraries = getArgumentList("link_libraries", "")

# From statically compiled modules of the Python
link_module_libs = getArgumentList("link_module_libs", "")

# Allow automatic downloads for ccache, etc.
assume_yes_for_downloads = getArgumentBool("assume_yes_for_downloads", False)

# Low memory mode, compile using less memory if possible.
low_memory = getArgumentBool("low_memory", False)

# Minimum version required on macOS.
macos_min_version = getArgumentDefaulted("macos_min_version", "")

# Target arch for macOS.
macos_target_arch = getArgumentDefaulted("macos_target_arch", "")

# gcc compiler cf_protection option
cf_protection = getArgumentDefaulted("cf_protection", "auto")

if getArgumentBool("progress_bar", True) and not show_scons_mode:
    enableSconsProgressBar()

# Amount of jobs to use.
job_count = GetOption("num_jobs")

# Prepare environment for compiler detection.
mingw_mode = prepareEnvironment(mingw_mode=mingw_mode)

# Patch the compiler detection.
Environment.Detect = getEnhancedToolDetect()

# Create Scons environment, the main control tool. Don't include "mingw" on
# Windows immediately, we will default to MSVC if available.
env = createEnvironment(
    mingw_mode=mingw_mode,
    msvc_version=msvc_version,
    target_arch=target_arch,
    experimental=experimental,
    no_deployment=no_deployment,
    debug_modes=debug_modes,
)

scons_details_logger.info("Initial CC: %r" % env.get("CC"))
scons_details_logger.info(
    "Initial CCVERSION: %r" % (env.get("CCVERSION"),),
)

if "CC" in os.environ:
    # If the environment variable CC is set, use that.
    env["CC"] = os.environ["CC"]
    env["CCVERSION"] = None

    scons_details_logger.info("Overridden with environment CC: %r" % env["CC"])
elif clangcl_mode:
    # If possible, add Clang directory from MSVC if available.
    addClangClPathFromMSVC(env=env)
elif clang_mode and not mingw_mode:
    # If requested by the user, use the clang compiler, overriding what was
    # said in environment.

    env["CC"] = "clang"
    env["CCVERSION"] = None

# On Windows, in case MSVC was not found and not previously forced, use the
# winlibs MinGW64 as a download, and use it as a fallback.
env = checkWindowsCompilerFound(
    env=env,
    target_arch=target_arch,
    clang_mode=clang_mode,
    msvc_version=msvc_version,
    assume_yes_for_downloads=assume_yes_for_downloads,
    download_ok=True,
)

env.the_compiler = env["CC"]
env.the_cc_name = os.path.normcase(os.path.basename(env.the_compiler))
env.module_mode = module_mode
env.standalone_mode = standalone_mode
env.debug_mode = debug_mode
env.debugger_mode = debugger_mode
env.unstripped_mode = unstripped_mode
env.console_mode = console_mode
env.source_dir = source_dir
env.nuitka_src = nuitka_src
env.low_memory = low_memory
env.macos_min_version = macos_min_version
env.macos_target_arch = macos_target_arch

# Requested or user provided, detect if it's clang even from environment
if isClangName(env.the_cc_name):
    clang_mode = True
    env["CCVERSION"] = None

# We consider clang to be a form of gcc for the most things, they strive to
# be compatible.
env.gcc_mode = isGccName(env.the_cc_name) or clang_mode
env.clang_mode = clang_mode

# Only use MSVC if not already clear, we are using MinGW.
env.msvc_mode = os.name == "nt" and not env.gcc_mode
env.mingw_mode = os.name == "nt" and env.gcc_mode
env.clangcl_mode = clangcl_mode

# For Python3.13, we need to enforce it for now to use MSVC
if os.name == "nt" and not env.msvc_mode and python_version >= (3, 13):
    scons_logger.sysexit(
        """\
Sorry, non-MSVC is not currently supported with Python 3.13,
due to differences in layout internal structures of Python.

Newer Nuitka will work to solve this. Use Python 3.12 or
option "--msvc=latest" as a workaround for now and wait
for updates of Nuitka to add MinGW64 support back."""
    )

# gcc compiler cf_protection option
env.cf_protection = cf_protection

# Consider switching from gcc to its g++ compiler as a workaround that makes us work without C11.
switchFromGccToGpp(
    env=env,
)

if env.the_compiler is None or getExecutablePath(env.the_compiler, env=env) is None:
    raiseNoCompilerFoundErrorExit()

no_import_lib = False

if show_scons_mode:
    my_print("Scons: Compiler used", end=" ")
    my_print(getExecutablePath(env.the_compiler, env=env), end=" ")

    if os.name == "nt" and env.msvc_mode:
        my_print("(MSVC %s)" % getMsvcVersionString(env))

    my_print()

# Set build directory and scons general settings.
setupScons(env, source_dir)

# Report the C compiler used.
reportCCompiler(env, "Backend", scons_logger.info)

# Set up C compiler settings.
setupCCompiler(
    env=env,
    lto_mode=lto_mode,
    pgo_mode=pgo_mode,
    job_count=job_count,
    onefile_compile=False,
)


# Avoid them as appearing to be different files. TODO: Find out which
# clang version has this, clang-8 does not.
if env.msvc_mode:
    # With Clang on Windows, there is also an linker to use. spell-checker: ignore bigobj
    env.Append(
        CCFLAGS=[
            "/EHsc",  # No C++ exception handling code.
            "/J",  # default char type is unsigned.
            "/Gd",  # Use C calling convention by default.
            "/bigobj",  # Product object files with larger internal limits.
        ]
    )

    # No incremental linking.
    env.Append(LINKFLAGS=["/INCREMENTAL:NO"])

    if env.module_mode:
        # Make sure we handle import library on our own and put it into the
        # build directory, spell-checker: ignore IMPLIB

        no_import_lib = True
        env.Append(
            LINKFLAGS=[
                "/INCREMENTAL:NO",
                "/IMPLIB:%s" % os.path.join(source_dir, "import.lib"),
            ]
        )

if not env.module_mode:
    enableWindowsStackSize(env=env, target_arch=target_arch)

if env.debug_mode:
    if env.gcc_mode:
        # Allow gcc/clang to point out all kinds of inconsistency to us by
        # raising an error.

        if "allow-c-warnings" not in env.experimental_flags and not env.debugger_mode:
            env.Append(
                CCFLAGS=[
                    "-Wall",
                    "-Werror",
                ]
            )
        else:
            env.Append(CCFLAGS=["-Wno-unused-but-set-variable"])

        env.Append(
            CCFLAGS=[
                # Unfortunately Py_INCREF(Py_False) triggers aliasing warnings,
                # which are unfounded, so disable them.
                "-Wno-error=strict-aliasing",
                "-Wno-strict-aliasing",
                # At least for self-compiled Python3.2, and MinGW this happens
                # and has little use anyway.
                "-Wno-error=format",
                "-Wno-format",
            ]
        )

    elif env.msvc_mode:
        if "allow-c-warnings" not in env.experimental_flags and not env.debugger_mode:
            env.Append(CCFLAGS=["/WX"])

        # Disable warnings that system headers already show.
        env.Append(
            CCFLAGS=[
                "/W4",
                "/wd4505",
                "/wd4127",
                "/wd4100",
                "/wd4702",
                "/wd4189",
                "/wd4211",
                "/wd4115",
            ]
        )

        # Disable warnings, that CPython headers already show.
        if python_version >= (3,):
            env.Append(CCFLAGS=["/wd4512", "/wd4510", "/wd4610"])

        if python_version >= (3, 13):
            env.Append(CCFLAGS=["/wd4324"])

        # We use null arrays in our structure Python declarations, which C11 does
        # not really allow, but should work.
        env.Append(CCFLAGS=["/wd4200"])

        # Do not show deprecation warnings, we will use methods for as long
        # as they work.
        env.Append(CCFLAGS=["/wd4996"])

if full_compat_mode:
    env.Append(CPPDEFINES=["_NUITKA_FULL_COMPAT"])

if profile_mode:
    env.Append(CPPDEFINES=["_NUITKA_PROFILE"])

if trace_mode:
    env.Append(CPPDEFINES=["_NUITKA_TRACE"])

if env.standalone_mode:
    env.Append(CPPDEFINES=["_NUITKA_STANDALONE"])

if macos_bundle_mode:
    env.Append(CPPDEFINES=["_NUITKA_MACOS_BUNDLE"])

if onefile_mode:
    env.Append(CPPDEFINES=["_NUITKA_ONEFILE_MODE"])

if onefile_temp_mode:
    env.Append(CPPDEFINES=["_NUITKA_ONEFILE_TEMP_BOOL"])

if deployment_mode:
    env.Append(CPPDEFINES=["_NUITKA_DEPLOYMENT_MODE"])

# We need "dl" in accelerated mode.
if "linux" in sys.platform:
    env.Append(LIBS=["dl"])

if not env.msvc_mode:
    env.Append(LIBS=["m"])

if python_debug:
    env.Append(CPPDEFINES=["Py_DEBUG"])

if env.static_libpython:
    env.Append(CPPDEFINES=["Py_NO_ENABLE_SHARED"])


def _detectPythonHeaderPath():
    if os.name == "nt":
        # On Windows, the CPython installation layout is relatively fixed, but on MSYS2
        # compiled for mingw64, it's more standard.

        candidates = [
            os.path.join(python_prefix_external, "include"),
            # On MSYS2 with MinGW64 Python, it is also the other form.
            os.path.join(
                python_prefix_external, "include", "python" + python_abi_version
            ),
        ]
    else:
        # The python header path is a combination of python version and debug
        # indication, we make sure the headers are found by adding it to the C
        # include path.

        candidates = [
            os.path.join(
                python_prefix_external, "include", "python" + python_abi_version
            ),
            # CPython source code checkout
            os.path.join(python_prefix_external, "Include"),
            # Haiku specific paths:
            os.path.join(
                python_prefix_external, "develop/headers", "python" + python_abi_version
            ),
        ]

        # Not all Python versions, have the ABI version to use for the debug version.
        if python_debug and "d" in python_abi_version:
            candidates.append(
                os.path.join(
                    python_prefix_external,
                    "include",
                    "python" + python_abi_version.replace("d", ""),
                )
            )

    for candidate in candidates:
        if os.path.exists(os.path.join(candidate, "Python.h")):
            yield candidate
            break
    else:
        if os.name == "nt":
            scons_logger.sysexit(
                """\
Error, you seem to be using the unsupported embeddable CPython distribution \
use a full Python instead."""
            )
        else:
            scons_logger.sysexit(
                """\
Error, no 'Python.h' %s headers can be found at '%s', dependency \
not satisfied!"""
                % ("debug" if python_debug else "development", candidates)
            )

    if python_version >= (3, 13):
        yield os.path.join(candidate, "internal", "mimalloc")

    if env.self_compiled_python_uninstalled:
        yield python_prefix_external


env.Append(CPPPATH=list(_detectPythonHeaderPath()))

# To support self-built Python on Windows, need to also add the "PC" directory,
# that a normal install won't have.
if os.name == "nt":
    python_header_path = os.path.join(python_prefix_external, "PC")

    if os.path.exists(python_header_path):
        env.Append(CPPPATH=[python_header_path])

if env.nuitka_python:
    env.Append(CPPDEFINES=["_NUITKA_PYTHON"])

if env.static_libpython:
    env.Append(CPPDEFINES=["_NUITKA_STATIC_LIBPYTHON"])

if not gil_mode:
    env.Append(CPPDEFINES="Py_GIL_DISABLED")

# TODO: This ought to be decided outside of scons and per flavor maybe.
if env.static_libpython and (
    (not os.name == "nt" and not isMacOS()) or env.nuitka_python
):
    env.Append(CPPDEFINES=["_NUITKA_USE_UNEXPOSED_API"])

if python_version >= (3, 12):
    if env.static_libpython:
        env.Append(
            CPPPATH=[
                os.path.join(env.nuitka_src, "inline_copy", "python_hacl", "hacl_312"),
                os.path.join(
                    env.nuitka_src, "inline_copy", "python_hacl", "hacl_312", "include"
                ),
            ]
        )

        env.Append(CPPDEFINES=["_NUITKA_INLINE_COPY_HACL"])

    # Remove it from static link libraries as well, if present, so far they are
    # bugs and do not exist.
    link_module_libs = [
        link_module_lib
        for link_module_lib in link_module_libs
        if "libHacl_Hash_SHA2" not in link_module_lib
    ]

if os.name == "nt":
    if env.nuitka_python:
        link_data = loadJsonFromFilename(
            os.path.join(python_prefix_external, "link.json")
        )
        env.Append(LIBS=link_data["libraries"])
        env.Append(LIBPATH=link_data["library_dirs"])
        for define, value in link_data["macros"]:
            if value:
                env.Append(CPPDEFINES=[define + "=" + value])
            else:
                env.Append(CPPDEFINES=[define])

        if "link_flags" in link_data:
            env.Append(LINKFLAGS=link_data["link_flags"])
        if "compile_flags" in link_data:
            env.Append(CFLAGS=link_data["compile_flags"])
    elif env.gcc_mode and env.static_libpython:
        env.Append(LIBS=[env.File(env.static_libpython)])
    else:
        # Some non CPython flavors on Windows have this.
        def addWinLib():
            # Make sure to locate the Python link library from multiple potential
            # locations (installed vs. self-built).
            if python_debug:
                win_lib_name = "python" + python_abi_version.replace(".", "") + "_d"
            else:
                win_lib_name = "python" + python_abi_version.replace(".", "")

            if python_version >= (3,):
                pc_build_dir = (
                    "PCBuild/amd64" if target_arch == "x86_64" else "PCBuild/win32"
                )
            else:
                pc_build_dir = "PCBuild"

            for candidate in ("libs", pc_build_dir):
                win_lib_path = os.path.join(python_prefix_external, candidate)

                if os.path.exists(os.path.join(win_lib_path, win_lib_name + ".lib")):
                    break
            else:
                scons_logger.sysexit("Error, cannot find '%s.lib' file." % win_lib_name)

            env.Append(LIBPATH=[win_lib_path])
            env.Append(LIBS=[win_lib_name])

        if not env.msys2_mingw_python:
            addWinLib()
elif not env.module_mode:
    # Add the python library path to the library path
    if env.self_compiled_python_uninstalled:
        python_lib_path = python_prefix_external
    else:
        python_lib_path = os.path.join(python_prefix_external, "lib")

    env.Append(LIBPATH=[python_lib_path])

    # Any module libs that are for self-compiled Python to be static.
    env.Append(_LIBFLAGS=["-l" + lib_desc for lib_desc in link_module_libs])

    if env.nuitka_python:
        link_data = loadJsonFromFilename(
            os.path.join(python_prefix_external, "link.json")
        )
        for lib in link_data["libraries"]:
            # Need to prevent Scons from stripping .a from the passed in libs.
            if lib.startswith(":") and lib.endswith(".a"):
                env.Append(_LIBFLAGS=["-l" + lib])
            elif os.path.isfile(lib):
                env.Append(_LIBFLAGS=[lib])
            else:
                env.Append(LIBS=[lib])
        env.Append(LIBPATH=link_data["library_dirs"])
        for define, value in link_data["macros"]:
            if value:
                env.Append(CPPDEFINES=[define + "=" + value])
            else:
                env.Append(CPPDEFINES=[define])

        if "link_flags" in link_data:
            env.Append(LINKFLAGS=link_data["link_flags"])
        if "compile_flags" in link_data:
            env.Append(CFLAGS=link_data["compile_flags"])
    elif env.static_libpython:
        env.Append(LIBS=[env.File(env.static_libpython)])

        # The linker won't succeed in searching for those for system Python of Debian
        # compiled Pythons. Help that. spell-checker: ignore rdynamic,Xlinker,pthread
        if python_prefix_external == "/usr" and isDebianBasedLinux():
            env.Append(LIBS=["z", "m", "util", "pthread"])

            if python_version >= (3,):
                env.Append(LIBS=["expat"])

            env.Append(LINKFLAGS=["-Xlinker", "-export-dynamic", "-rdynamic"])

        if env.arch_python:
            env.Append(LINKFLAGS=["-rdynamic"])
    else:
        # Fedora, Debian and Ubuntu distinguish the system libraries like this.
        if (
            python_debug
            and python_prefix_external == "/usr"
            and python_version < (3,)
            and (isDebianBasedLinux() or isFedoraBasedLinux())
        ):
            env.Append(LIBS=["python" + python_abi_version + "_d"])
        else:
            env.Append(LIBS=["python" + python_abi_version])

    if python_prefix_external != "/usr" and "linux" in sys.platform:
        env.Append(LIBS=["dl", "pthread", "util", "rt", "m"])

        if env.gcc_mode:
            if clang_mode:
                env.Append(LINKFLAGS=["-Wl,--export-dynamic"])
            else:
                env.Append(LINKFLAGS=["-export-dynamic"])

    # For NetBSD the rpath is required, on FreeBSD it's warned as unused, macOS
    # and AIX don't have it, but some Python flavors like Standalone Python
    # won't find it unless provided like this, so lets try to add this by
    # default and remove OSes that give errors one by one.
    if not isFreeBSD() and not isMacOS() and not isAIX():
        env.Append(LINKFLAGS=["-Wl,-rpath=" + python_lib_path])

if isMacOS():
    if env.module_mode:
        # Dynamic lookup needed for extension modules.
        env.Append(LINKFLAGS=["-undefined", "dynamic_lookup"])
    else:
        # For macOS we need to make sure install_name_tool can do its work
        # spell-checker: ignore headerpad
        env.Append(LINKFLAGS=["-headerpad_max_install_names"])

if env.android_termux_python:
    # For Android Termux Python we need modules to link against the
    # shared library of Python, different from all other ones.

    if env.module_mode:
        env.Append(_LIBFLAGS=["-lpython%s" % python_version_str])

# The static include files reside in Nuitka installation, which may be where
# the "nuitka.build" package lives.
nuitka_include = os.path.join(env.nuitka_src, "include")

if not os.path.exists(os.path.join(nuitka_include, "nuitka", "prelude.h")):
    scons_logger.sysexit(
        "Error, cannot locate Nuitka includes at '%s', this is a broken Nuitka installation."
        % nuitka_include
    )

# We have include files in the build directory and the static include directory
# that is located inside Nuitka installation.
env.Append(
    CPPPATH=[
        source_dir,
        nuitka_include,
        os.path.join(env.nuitka_src, "static_src"),
        os.path.join(env.nuitka_src, "inline_copy", "libbacktrace"),
    ]
)

# Set load libpython from binary directory default
if env.gcc_mode and not isMacOS() and not os.name == "nt" and not env.module_mode:
    if env.standalone_mode:
        rpath = "$$ORIGIN"
    else:
        rpath = python_lib_path

    env.Append(LINKFLAGS=["-Wl,-R,'%s'" % rpath])

    # The rpath is no longer used unless we do this on modern Linux. The
    # option name is not very revealing, but basically without this, the
    # rpath in the binary will be ignored by the loader.
    # spell-checker: ignore dtags
    if "linux" in sys.platform:
        env.Append(LINKFLAGS=["-Wl,--disable-new-dtags"])


addConstantBlobFile(
    env=env,
    resource_desc=decideConstantsBlobResourceMode(env=env, module_mode=module_mode),
    blob_filename=getConstantBlobFilename(env.source_dir),
    target_arch=target_arch,
)

env.Append(CPPDEFINES=["_NUITKA_FROZEN=%d" % frozen_modules])

# Tell compiler to create a shared library or program.
if env.module_mode:
    if isGccName(env.the_cc_name):
        env.Append(CCFLAGS=["-shared"])
    elif env.clang_mode:
        pass
    elif env.msvc_mode:
        env.Append(CCFLAGS=["/LD"])  # Create a DLL.
    else:
        assert False, env.the_cc_name
else:
    if env.msvc_mode:
        # For complete outputs, we have to match the C runtime of the Python DLL, if any,
        # for Nuitka-Python there is of course none.
        if not env.nuitka_python and (
            forced_stdout_path not in ("{NONE}", "{NULL}", None)
            or forced_stderr_path not in ("{NONE}", "{NULL}", None)
        ):
            env.Append(CCFLAGS=["/MD"])  # Multithreaded, dynamic version of C run time.
        else:
            env.Append(CCFLAGS=["/MT"])  # Multithreaded, static version of C run time.

if env.module_mode:
    env.Append(CPPDEFINES=["_NUITKA_MODULE"])
else:
    env.Append(CPPDEFINES=["_NUITKA_EXE"])


def discoverSourceFiles():
    result = []

    # Scan for Nuitka created source files, and add them too.
    result.extend(scanSourceDir(env=env, dirname=source_dir, plugins=False))
    result.extend(
        scanSourceDir(
            env=env,
            dirname=os.path.join(source_dir, "plugins"),
            plugins=True,
        )
    )

    static_src_filenames = []

    # Main program, unless of course it's a Python module/package we build.
    if not env.module_mode:
        static_src_filenames.append("MainProgram.c")

    # Compiled types.
    static_src_filenames.append("CompiledFunctionType.c")

    result += [
        provideStaticSourceFile(
            env=env,
            sub_path=filename,
            c11_mode=env.c11_mode,
        )
        for filename in static_src_filenames
    ]

    return result


source_files = discoverSourceFiles()

# Remove the target file to avoid cases where it falsely doesn't get rebuild and
# then lingers from previous builds, and also workaround for MinGW64 not
# supporting unicode result paths for "-o" basename.
result_exe = makeResultPathFileSystemEncodable(env=env, result_exe=result_exe)


if env.module_mode:
    # For Python modules, the standard shared library extension is not what gets
    # used. spell-checker: ignore SHLIBSUFFIX
    module_suffix = getFilenameExtension(result_exe)
    result_base_path = changeFilenameExtension(result_exe, "")

    extra_suffix = getFilenameExtension(result_base_path)
    if extra_suffix != "":
        module_suffix = extra_suffix + module_suffix
        result_base_path = changeFilenameExtension(result_base_path, "")

    assert getFilenameExtension(result_base_path) == "", result_base_path

    env["SHLIBSUFFIX"] = module_suffix

    target = env.SharedLibrary(
        result_base_path, source_files, no_import_lib=no_import_lib
    )
else:
    target = env.Program(result_exe, source_files)

# Use compiler/linker flags provided via environment variables
importEnvironmentVariableSettings(env)


if job_count:
    scons_details_logger.info("Told to run compilation on %d CPUs." % job_count)


def createBuildDefinitionsFile():
    build_definitions = {}

    if uninstalled_python and not env.static_libpython:
        # Use the non-external one, so it's not a short path.
        build_definitions["PYTHON_HOME_PATH"] = python_prefix

    build_definitions["NO_PYTHON_WARNINGS"] = 1 if no_python_warnings else 0

    if python_version < (3,):
        build_definitions["SYSFLAG_PY3K_WARNING"] = (
            1 if python_sysflag_py3k_warning else 0
        )

        build_definitions["SYSFLAG_DIVISION_WARNING"] = (
            1 if python_sysflag_division_warning else 0
        )

        build_definitions["SYSFLAG_UNICODE"] = 1 if python_sysflag_unicode else 0

    build_definitions["SYSFLAG_BYTES_WARNING"] = (
        1 if python_sysflag_bytes_warning else 0
    )

    build_definitions["SYSFLAG_NO_SITE"] = 1 if python_sysflag_no_site else 0

    build_definitions["SYSFLAG_VERBOSE"] = 1 if python_sysflag_verbose else 0

    build_definitions["SYSFLAG_UTF8"] = 1 if python_sysflag_utf8 else 0

    build_definitions["SYSFLAG_OPTIMIZE"] = python_sysflag_optimize

    build_definitions["SYSFLAG_DONTWRITEBYTECODE"] = (
        1 if python_sysflag_dontwritebytecode else 0
    )

    build_definitions["_NUITKA_NO_ASSERTS"] = 1 if python_flag_no_asserts else 0

    build_definitions["_NUITKA_NO_DOCSTRINGS"] = 1 if python_flag_no_docstrings else 0

    build_definitions["_NUITKA_NO_ANNOTATIONS"] = 1 if python_flag_no_annotations else 0

    build_definitions["SYSFLAG_NO_RANDOMIZATION"] = (
        1 if python_sysflag_no_randomization else 0
    )

    build_definitions["SYSFLAG_UNBUFFERED"] = 1 if python_sysflag_unbuffered else 0

    build_definitions["SYSFLAG_ISOLATED"] = 1 if python_sysflag_isolated else 0

    if forced_stdout_path:
        if forced_stdout_path == "{NONE}":
            build_definitions["NUITKA_FORCED_STDOUT_NONE_BOOL"] = 1
        elif forced_stdout_path == "{NULL}":
            build_definitions["NUITKA_FORCED_STDOUT_NULL_BOOL"] = 1
        else:
            build_definitions["NUITKA_FORCED_STDOUT_PATH"] = forced_stdout_path

    if forced_stderr_path:
        if forced_stderr_path == "{NONE}":
            build_definitions["NUITKA_FORCED_STDERR_NONE_BOOL"] = 1
        elif forced_stderr_path == "{NULL}":
            build_definitions["NUITKA_FORCED_STDERR_NULL_BOOL"] = 1
        else:
            build_definitions["NUITKA_FORCED_STDERR_PATH"] = forced_stderr_path

    build_definitions["NUITKA_MAIN_MODULE_NAME"] = main_module_name
    build_definitions["NUITKA_MAIN_IS_PACKAGE_BOOL"] = main_module_name != "__main__"

    createDefinitionsFile(source_dir, "build_definitions.h", build_definitions)


if forced_stderr_path and not forced_stdout_path:
    env.Append(CPPDEFINES=["NUITKA_STDERR_NOT_VISIBLE"])

if file_reference_mode == "original":
    env.Append(CPPDEFINES=["_NUITKA_FILE_REFERENCE_ORIGINAL_MODE"])


createBuildDefinitionsFile()

# The meta path based loader might want to respect that, so it does verbose traces in module
# mode, mostly for debugging purposes only.
if env.module_mode and python_sysflag_verbose:
    env.Append(CPPDEFINES=["_NUITKA_SYSFLAG_VERBOSE=1"])

# Hack to make Scons use tempfile for gcc linking, to avoid line length limits,
# which can make linking fail with many modules otherwise. Most needed on Windows,
# but useful on other platforms too.
if env.gcc_mode:
    makeGccUseLinkerFile(
        env=env,
        source_files=source_files,
    )

# Plugin contributed C defines should be used too.
env.Append(CPPDEFINES=cpp_defines)
# Plugin contributed C include directories should be used too.
env.Append(CPPPATH=cpp_include_dirs)
# Plugin contributed link dirs should be used too.
env.Append(LIBPATH=link_dirs)
# Plugin contributed link libraries should be used too.
env.Append(LIBS=link_libraries)

# Work around windows bugs and use watchdogs to track progress of compilation.
enableSpawnMonitoring(
    env=env,
    source_files=source_files,
)

# Before we go, also lets turn KeyboardInterrupt into a mere error exit as the
# scons traceback is not going to be very interesting to us.
changeKeyboardInterruptToErrorExit()

# Check if ccache is installed, and complain if it is not.
if env.gcc_mode:
    enableCcache(
        env=env,
        source_dir=source_dir,
        python_prefix=python_prefix_external,
        assume_yes_for_downloads=assume_yes_for_downloads,
        disable_ccache=disable_ccache,
    )

if env.msvc_mode and not disable_ccache:
    enableClcache(
        env=env,
        source_dir=source_dir,
    )

writeSconsReport(env=env, target=target)

setSconsProgressBarTotal(name=env.progressbar_name, total=len(source_files))

scons_details_logger.info("Launching Scons target: %s" % target)
env.Default(target)

#     Part of "Nuitka", an optimizing Python compiler that is compatible and
#     integrates with CPython, but also works on its own.
#
#     Licensed under the Apache License, Version 2.0 (the "License");
#     you may not use this file except in compliance with the License.
#     You may obtain a copy of the License at
#
#        http://www.apache.org/licenses/LICENSE-2.0
#
#     Unless required by applicable law or agreed to in writing, software
#     distributed under the License is distributed on an "AS IS" BASIS,
#     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#     See the License for the specific language governing permissions and
#     limitations under the License.
