{#     Copyright 2025, <PERSON>, mailto:<EMAIL> find license text at end of file #}

PyObject *MAKE_LIST{{args_count}}(PyThreadState *tstate, PyObject *list) {
    CHECK_OBJECT(list);
    assert(PyList_CheckExact(list));
    assert(PyList_GET_SIZE(list) == {{args_count}});

    PyObject *result = MAKE_LIST_EMPTY(tstate, {{args_count}});

    if (unlikely(result == NULL)) {
        return NULL;
    }

{% for i in range(args_count) %}
    {
        PyObject *item = PyList_GET_ITEM(list, {{i}});
        Py_INCREF(item);
        PyList_SET_ITEM(result, {{i}}, item);
    }
{% endfor %}

    return result;
}

{#     Part of "Nuitka", an optimizing Python compiler that is compatible and   #}
{#     integrates with CPython, but also works on its own.                      #}
{#                                                                              #}
{#     Licensed under the Apache License, Version 2.0 (the "License");          #}
{#     you may not use this file except in compliance with the License.         #}
{#     You may obtain a copy of the License at                                  #}
{#                                                                              #}
{#        http://www.apache.org/licenses/LICENSE-2.0                            #}
{#                                                                              #}
{#     Unless required by applicable law or agreed to in writing, software      #}
{#     distributed under the License is distributed on an "AS IS" BASIS,        #}
{#     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. #}
{#     See the License for the specific language governing permissions and      #}
{#     limitations under the License.                                           #}
