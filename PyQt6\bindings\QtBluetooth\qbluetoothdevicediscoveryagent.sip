// qbluetoothdevicediscoveryagent.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QBluetoothDeviceDiscoveryAgent : public QObject
{
%TypeHeaderCode
#include <qbluetoothdevicediscoveryagent.h>
%End

public:
    enum Error
    {
        NoError,
        InputOutputError,
        PoweredOffError,
        InvalidBluetoothAdapterError,
        UnsupportedPlatformError,
        UnsupportedDiscoveryMethod,
        LocationServiceTurnedOffError,
%If (Qt_6_4_0 -)
        MissingPermissionsError,
%End
        UnknownError,
    };

    explicit QBluetoothDeviceDiscoveryAgent(QObject *parent /TransferThis/ = 0);
    QBluetoothDeviceDiscoveryAgent(const QBluetoothAddress &deviceAdapter, QObject *parent /TransferThis/ = 0);
    virtual ~QBluetoothDeviceDiscoveryAgent();
    bool isActive() const;
    QBluetoothDeviceDiscoveryAgent::Error error() const;
    QString errorString() const;
    QList<QBluetoothDeviceInfo> discoveredDevices() const;

public slots:
    void start();
    void start(QBluetoothDeviceDiscoveryAgent::DiscoveryMethods method);
    void stop();

signals:
    void deviceDiscovered(const QBluetoothDeviceInfo &info);
    void finished();
    void errorOccurred(QBluetoothDeviceDiscoveryAgent::Error error);
    void canceled();
    void deviceUpdated(const QBluetoothDeviceInfo &info, QBluetoothDeviceInfo::Fields updatedFields);

public:
    enum DiscoveryMethod /BaseType=Flag/
    {
        NoMethod,
        ClassicMethod,
        LowEnergyMethod,
    };

    typedef QFlags<QBluetoothDeviceDiscoveryAgent::DiscoveryMethod> DiscoveryMethods;
    void setLowEnergyDiscoveryTimeout(int msTimeout);
    int lowEnergyDiscoveryTimeout() const;
    static QBluetoothDeviceDiscoveryAgent::DiscoveryMethods supportedDiscoveryMethods();
};

%End
