// qtreewidgetitemiterator.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTreeWidgetItemIterator
{
%TypeHeaderCode
#include <qtreewidgetitemiterator.h>
%End

public:
    enum IteratorFlag /BaseType=Flag/
    {
        All,
        Hidden,
        NotHidden,
        Selected,
        Unselected,
        Selectable,
        NotSelectable,
        DragEnabled,
        DragDisabled,
        DropEnabled,
        DropDisabled,
        HasChildren,
        NoChildren,
        Checked,
        NotChecked,
        Enabled,
        Disabled,
        Editable,
        NotEditable,
        UserFlag,
    };

    typedef QFlags<QTreeWidgetItemIterator::IteratorFlag> IteratorFlags;
    QTreeWidgetItemIterator(QTreeWidgetItem *item, QTreeWidgetItemIterator::IteratorFlags flags = QTreeWidgetItemIterator::All);
    QTreeWidgetItemIterator(QTreeWidget *widget, QTreeWidgetItemIterator::IteratorFlags flags = QTreeWidgetItemIterator::All);
    QTreeWidgetItemIterator(const QTreeWidgetItemIterator &it);
    ~QTreeWidgetItemIterator();
    QTreeWidgetItem *value() const;
%MethodCode
        // SIP doesn't support operator* so this is a thin wrapper around it.
        sipRes = sipCpp->operator*();
%End

    QTreeWidgetItemIterator &operator+=(int n);
    QTreeWidgetItemIterator &operator-=(int n);
};
