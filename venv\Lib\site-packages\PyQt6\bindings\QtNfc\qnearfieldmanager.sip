// qnearfieldmanager.sip generated by MetaSIP
//
// This file is part of the QtNfc Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QNearFieldManager : public QObject
{
%TypeHeaderCode
#include <qnearfieldmanager.h>
%End

%ConvertToSubClassCode
    static struct class_graph {
        const char *name;
        sipTypeDef **type;
        int yes, no;
    } graph[] = {
        {sipName_QNearFieldManager, &sipType_QNearFieldManager, -1, 1},
        {sipName_QNearFieldTarget, &sipType_QNearFieldTarget, -1, -1},
    };
    
    int i = 0;
    
    sipType = NULL;
    
    do
    {
        struct class_graph *cg = &graph[i];
    
        if (cg->name != NULL && sipCpp->inherits(cg->name))
        {
            sipType = *cg->type;
            i = cg->yes;
        }
        else
            i = cg->no;
    }
    while (i >= 0);
%End

public:
    explicit QNearFieldManager(QObject *parent /TransferThis/ = 0);
    virtual ~QNearFieldManager();
    bool startTargetDetection(QNearFieldTarget::AccessMethod accessMethod);
    void stopTargetDetection(const QString &errorMessage = QString());

signals:
    void targetDetected(QNearFieldTarget *target);
    void targetLost(QNearFieldTarget *target);
    void targetDetectionStopped();

public:
    enum class AdapterState
    {
        Offline,
        TurningOn,
        Online,
        TurningOff,
    };

    bool isSupported(QNearFieldTarget::AccessMethod accessMethod = QNearFieldTarget::AnyAccess) const;

signals:
    void adapterStateChanged(QNearFieldManager::AdapterState state);

public:
    bool isEnabled() const;
    void setUserInformation(const QString &message);
};

%End

%ModuleHeaderCode
// Imports from QtCore.
typedef sipErrorState (*pyqt6_qtnfc_get_pyqtslot_parts_t)(PyObject *, QObject **, QByteArray &);
extern pyqt6_qtnfc_get_pyqtslot_parts_t pyqt6_qtnfc_get_pyqtslot_parts;
%End

%ModuleCode
// Imports from QtCore.
pyqt6_qtnfc_get_pyqtslot_parts_t pyqt6_qtnfc_get_pyqtslot_parts;
%End

%PostInitialisationCode
// Imports from QtCore.
pyqt6_qtnfc_get_pyqtslot_parts = (pyqt6_qtnfc_get_pyqtslot_parts_t)sipImportSymbol("pyqt6_get_pyqtslot_parts");
Q_ASSERT(pyqt6_qtnfc_get_pyqtslot_parts);
%End
