oem_dycnvbk
keystore
opproduct_b
sdc
devcfg_a
vbmeta_a
mdm_oem_stanvbk
uefisecapp_a
vbmeta_system_b
dip
cmnlib64_a
fw_ufs5_b
qupfw_a
vbmeta_system_a
aging
misc
op1
config
storsec_a
xbl_b
fw_ufs4_b
secdata
dtbo_a
multiimgoem_b
catecontentfv
aging_mod
reserve2
reserve4
modem_b
imagefv_a
catefv
frp
devinfo
reserve1
recovery_b
reserve3
userdata
xbl_config_b
keymaster_a
qupfw_b
fw_ufs2_b
rawdump
dtbo_b
imagefv_b
core_nhlos
recovery_a
fw_ufs1_b
fw_ufs3_a
devcfg_b
cmnlib_a
vbmeta_b
fw_ufs5_a
uefisecapp_b
fw_ufs1_a
aop_b
LOGO_b
boot_a
tz_a
mdm1oemnvbktmp
fw_ufs2_a
ssd
fw_ufs3_b
sdd
fw_ufs4_a
multiimgoem_a
mdm1m9kefs2
splash
tz_b
toolsfv
ALIGN_TO_128K_2
mdm1m9kefs3
cateloader
LOGO_a
spunvm
modemst2
limits
logfs
logdump
opproduct_a
uefivarstore
mdm_oem_dycnvbk
sde
sdb
param
apdp
oem_stanvbk
msadp
modem_a
mdtpsecapp_a
mdtp_b
xbl_a
abl_a
mdmddr
dsp_b
storsec_b
boot_b
fsc
hyp_a
aop_a
bluetooth_a
mdtpsecapp_b
mdtp_a
abl_b
dsp_a
keymaster_b
hyp_b
cmnlib_b
bluetooth_b
cmnlib64_b
xbl_config_a
ddr
cdt
ALIGN_TO_128K_1
modemst1
fsg
mdm1m9kefsc
mdm1m9kefs1
sdf
super
metadata
ADB
adl
my_carrier
1
my_heytap
my_bigball_a
my_carrier_a
my_company_a
my_engineering_a
my_heytap_a
my_manifest_a
my_preload_a
my_product_a
my_region_a
my_stock_a
odm_a
product_a
system_a
system_ext_a
vendor_a