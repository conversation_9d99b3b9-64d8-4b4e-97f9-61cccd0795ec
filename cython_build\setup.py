# -*- coding: utf-8 -*-
"""
Cython编译配置 - 强制编译所有核心算法
"""
import os
import sys
from setuptools import setup, Extension
from Cython.Build import cythonize

# 尝试导入numpy
try:
    import numpy
    include_dirs = [numpy.get_include()]
    define_macros = [('NPY_NO_DEPRECATED_API', 'NPY_1_7_API_VERSION')]
    print("✅ 使用numpy支持")
except ImportError:
    include_dirs = []
    define_macros = []
    print("⚠️ numpy未安装，使用基础配置")

# 核心模块列表
CORE_MODULES = [
    "coloros.py",
    "coloros15.py", 
    "utils.py",
    "fastboodt.py",
    "zhidinyishuaxie.py",
    "payload_extractor.py",
    "custom_messagebox.py",
    "genduodakhd.py",
    "font_extractor.py",
]

# 创建扩展模块
extensions = []
for module_file in CORE_MODULES:
    if os.path.exists(module_file):
        # 生成模块名
        module_name = module_file.replace('.py', '_cython')
        
        # 编译选项
        extra_compile_args = []
        extra_link_args = []
        
        if sys.platform == 'win32':
            extra_compile_args = ['/O2', '/DNDEBUG']
        else:
            extra_compile_args = ['-O3', '-DNDEBUG']
        
        # 创建扩展
        ext = Extension(
            module_name,
            [module_file],
            include_dirs=include_dirs,
            define_macros=define_macros,
            extra_compile_args=extra_compile_args,
            extra_link_args=extra_link_args,
            language='c++'
        )
        extensions.append(ext)
        print(f"✅ 添加编译目标: {module_name} <- {module_file}")

if not extensions:
    print("❌ 错误: 没有找到任何核心模块")
    sys.exit(1)

print(f"📦 总计 {len(extensions)} 个模块将被编译")

# 编译指令
compiler_directives = {
    'language_level': 3,
    'boundscheck': False,
    'wraparound': False,
    'initializedcheck': False,
    'cdivision': True,
    'embedsignature': False,
    'optimize.use_switch': True,
    'optimize.unpack_method_calls': True,
}

# 执行编译
setup(
    name="CoreAlgorithms",
    ext_modules=cythonize(
        extensions,
        compiler_directives=compiler_directives,
        build_dir="build",
        annotate=False,
        nthreads=4,
        force=True
    ),
    zip_safe=False,
)
