// qdatastream.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDataStream : public QIODeviceBase
{
%TypeHeaderCode
#include <qdatastream.h>
%End

public:
    enum Version /BaseType=IntEnum/
    {
        Qt_1_0,
        Qt_2_0,
        Qt_2_1,
        Qt_3_0,
        Qt_3_1,
        Qt_3_3,
        Qt_4_0,
        Qt_4_1,
        Qt_4_2,
        Qt_4_3,
        Qt_4_4,
        Qt_4_5,
        Qt_4_6,
        Qt_4_7,
        Qt_4_8,
        Qt_4_9,
        Qt_5_0,
        Qt_5_1,
        Qt_5_2,
        Qt_5_3,
        Qt_5_4,
        Qt_5_5,
        Qt_5_6,
        Qt_5_7,
        Qt_5_8,
        Qt_5_9,
        Qt_5_10,
        Qt_5_11,
        Qt_5_12,
        Qt_5_13,
        Qt_5_14,
        Qt_5_15,
        Qt_6_0,
%If (Qt_6_1_0 -)
        Qt_6_1,
%End
%If (Qt_6_2_0 -)
        Qt_6_2,
%End
%If (Qt_6_3_0 -)
        Qt_6_3,
%End
%If (Qt_6_4_0 -)
        Qt_6_4,
%End
%If (Qt_6_5_0 -)
        Qt_6_5,
%End
%If (Qt_6_6_0 -)
        Qt_6_6,
%End
%If (Qt_6_7_0 -)
        Qt_6_7,
%End
    };

    enum ByteOrder
    {
        BigEndian,
        LittleEndian,
    };

    enum Status
    {
        Ok,
        ReadPastEnd,
        ReadCorruptData,
        WriteFailed,
%If (Qt_6_7_0 -)
        SizeLimitExceeded,
%End
    };

    enum FloatingPointPrecision
    {
        SinglePrecision,
        DoublePrecision,
    };

    QDataStream();
    explicit QDataStream(QIODevice *);
    QDataStream(QByteArray * /Constrained/, QIODeviceBase::OpenMode flags);
    QDataStream(const QByteArray &);
    ~QDataStream();
    QIODevice *device() const;
    void setDevice(QIODevice *);
    bool atEnd() const;
    QDataStream::Status status() const;
    void setStatus(QDataStream::Status status);
    void resetStatus();
    QDataStream::FloatingPointPrecision floatingPointPrecision() const;
    void setFloatingPointPrecision(QDataStream::FloatingPointPrecision precision);
    QDataStream::ByteOrder byteOrder() const;
    void setByteOrder(QDataStream::ByteOrder);
    int version() const;
    void setVersion(int);
%If (Qt_6_7_0 -)
    SIP_PYOBJECT readBytes() /ReleaseGIL,TypeHint="bytes"/;
%MethodCode
        char *s;
        qint64 l;
        
        Py_BEGIN_ALLOW_THREADS
        sipCpp->readBytes(s, l);
        Py_END_ALLOW_THREADS
        
        if ((sipRes = PyBytes_FromStringAndSize(s, l)) == NULL)
            sipIsErr = 1;
        
        if (s)
            delete[] s;
%End

%End
%If (- Qt_6_7_0)
    SIP_PYOBJECT readBytes() /ReleaseGIL,TypeHint="bytes"/;
%MethodCode
        char *s;
        uint l;
        
        Py_BEGIN_ALLOW_THREADS
        sipCpp->readBytes(s, l);
        Py_END_ALLOW_THREADS
        
        if ((sipRes = PyBytes_FromStringAndSize(s, l)) == NULL)
            sipIsErr = 1;
        
        if (s)
            delete[] s;
%End

%End
%If (Qt_6_7_0 -)
    SIP_PYOBJECT readRawData(qint64 len) /Encoding="None",ReleaseGIL,TypeHint="bytes"/;
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
            len = sipCpp->readRawData(s, a0);
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = PyBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

%End
%If (- Qt_6_7_0)
    SIP_PYOBJECT readRawData(int len) /Encoding="None",ReleaseGIL,TypeHint="bytes"/;
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            int len;
        
            Py_BEGIN_ALLOW_THREADS
            len = sipCpp->readRawData(s, a0);
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = PyBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

%End
%If (Qt_6_7_0 -)
    QDataStream &writeBytes(SIP_PYBUFFER) /ReleaseGIL/;
%MethodCode
        sipBufferInfoDef bi;
        
        if (sipGetBufferInfo(a0, &bi) > 0)
        {
            Py_BEGIN_ALLOW_THREADS
            sipRes = &sipCpp->writeBytes(reinterpret_cast<char *>(bi.bi_buf),
                    bi.bi_len);
            Py_END_ALLOW_THREADS
            
            sipReleaseBufferInfo(&bi);
        }
        else
        {
            sipIsErr = 1;
        }
%End

%End
%If (- Qt_6_7_0)
    QDataStream &writeBytes(SIP_PYBUFFER) /ReleaseGIL/;
%MethodCode
        sipBufferInfoDef bi;
        
        if (sipGetBufferInfo(a0, &bi) > 0)
        {
            Py_BEGIN_ALLOW_THREADS
            sipRes = &sipCpp->writeBytes(reinterpret_cast<char *>(bi.bi_buf),
                    bi.bi_len);
            Py_END_ALLOW_THREADS
            
            sipReleaseBufferInfo(&bi);
        }
        else
        {
            sipIsErr = 1;
        }
%End

%End
%If (Qt_6_7_0 -)
    qint64 writeRawData(SIP_PYBUFFER) /ReleaseGIL/;
%MethodCode
        sipBufferInfoDef bi;
        
        if (sipGetBufferInfo(a0, &bi) > 0)
        {
            Py_BEGIN_ALLOW_THREADS
            sipRes = sipCpp->writeRawData(reinterpret_cast<char *>(bi.bi_buf),
                    bi.bi_len);
            Py_END_ALLOW_THREADS
            
            sipReleaseBufferInfo(&bi);
        }
        else
        {
            sipIsErr = 1;
        }
%End

%End
%If (- Qt_6_7_0)
    int writeRawData(SIP_PYBUFFER) /ReleaseGIL/;
%MethodCode
        sipBufferInfoDef bi;
        
        if (sipGetBufferInfo(a0, &bi) > 0)
        {
            Py_BEGIN_ALLOW_THREADS
            sipRes = sipCpp->writeRawData(reinterpret_cast<char *>(bi.bi_buf),
                    bi.bi_len);
            Py_END_ALLOW_THREADS
            
            sipReleaseBufferInfo(&bi);
        }
        else
        {
            sipIsErr = 1;
        }
%End

%End
%If (Qt_6_7_0 -)
    qint64 skipRawData(qint64 len) /ReleaseGIL/;
%End
%If (- Qt_6_7_0)
    int skipRawData(int len) /ReleaseGIL/;
%End
    void startTransaction();
    bool commitTransaction();
    void rollbackTransaction();
    void abortTransaction();
// Extra methods to give explicit control over the simple data types being read and written.
int readInt() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

qint8 readInt8() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

quint8 readUInt8() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

qint16 readInt16() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

quint16 readUInt16() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

qint32 readInt32() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

quint32 readUInt32() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

qint64 readInt64() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

quint64 readUInt64() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

bool readBool() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

float readFloat() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

double readDouble() /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> sipRes;
    Py_END_ALLOW_THREADS
%End

SIP_PYOBJECT readString() /ReleaseGIL,TypeHint="bytes"/;
%MethodCode
    // Note that this should really be called readCString().

    char *s;

    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> s;
    Py_END_ALLOW_THREADS

    if (s)
    {
        sipRes = PyBytes_FromString(s);
        delete[] s;
    }
    else
    {
        sipRes = Py_None;
        Py_INCREF(Py_None);
    }
%End
    
void writeInt(int i) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeInt8(qint8 i) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeUInt8(quint8 i) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeInt16(qint16 i) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeUInt16(quint16 i) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeInt32(qint32 i) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeUInt32(quint32 i) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeInt64(qint64 i) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeUInt64(quint64 i) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeBool(bool i) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeFloat(float f) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeDouble(double f) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

void writeString(const char *str /Encoding="None"/) /ReleaseGIL/;
%MethodCode
    // Note that this should really be called writeCString().
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << a0;
    Py_END_ALLOW_THREADS
%End

QString readQString() /ReleaseGIL/;
%MethodCode
    sipRes = new QString;

    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> *sipRes;
    Py_END_ALLOW_THREADS
%End

void writeQString(const QString &qstr) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << *a0;
    Py_END_ALLOW_THREADS
%End

QStringList readQStringList() /ReleaseGIL/;
%MethodCode
    sipRes = new QStringList;

    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> *sipRes;
    Py_END_ALLOW_THREADS
%End

void writeQStringList(const QStringList &qstrlst) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << *a0;
    Py_END_ALLOW_THREADS
%End

QVariant readQVariant() /ReleaseGIL/;
%MethodCode
    sipRes = new QVariant;

    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> *sipRes;
    Py_END_ALLOW_THREADS
%End

void writeQVariant(const QVariant &qvar) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << *a0;
    Py_END_ALLOW_THREADS
%End

QVariantList readQVariantList() /ReleaseGIL/;
%MethodCode
    sipRes = new QVariantList;

    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> *sipRes;
    Py_END_ALLOW_THREADS
%End

void writeQVariantList(const QVariantList &qvarlst) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << *a0;
    Py_END_ALLOW_THREADS
%End

QVariantMap readQVariantMap() /ReleaseGIL/;
%MethodCode
    sipRes = new QVariantMap;

    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> *sipRes;
    Py_END_ALLOW_THREADS
%End

void writeQVariantMap(const QVariantMap &qvarmap) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << *a0;
    Py_END_ALLOW_THREADS
%End

QVariantHash readQVariantHash() /ReleaseGIL/;
%MethodCode
    sipRes = new QVariantHash;

    Py_BEGIN_ALLOW_THREADS
    *sipCpp >> *sipRes;
    Py_END_ALLOW_THREADS
%End

void writeQVariantHash(const QVariantHash &qvarhash) /ReleaseGIL/;
%MethodCode
    Py_BEGIN_ALLOW_THREADS
    *sipCpp << *a0;
    Py_END_ALLOW_THREADS
%End

private:
    QDataStream(const QDataStream &);
};

QDataStream &operator>>(QDataStream &s, QKeyCombination &combination /Constrained/) /ReleaseGIL/;
QDataStream &operator<<(QDataStream &s, QKeyCombination combination) /ReleaseGIL/;
