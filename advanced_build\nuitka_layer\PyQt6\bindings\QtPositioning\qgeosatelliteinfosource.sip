// qgeosatelliteinfosource.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QGeoSatelliteInfoSource : public QObject
{
%TypeHeaderCode
#include <qgeosatelliteinfosource.h>
%End

public:
    enum Error
    {
        AccessError,
        ClosedError,
        NoError,
        UnknownSourceError,
        UpdateTimeoutError,
    };

    explicit QGeoSatelliteInfoSource(QObject *parent /TransferThis/);
    virtual ~QGeoSatelliteInfoSource();
    static QGeoSatelliteInfoSource *createDefaultSource(QObject *parent /TransferThis/) /Factory/;
    static QGeoSatelliteInfoSource *createDefaultSource(const QVariantMap &parameters, QObject *parent /TransferThis/) /Factory/;
    static QGeoSatelliteInfoSource *createSource(const QString &sourceName, QObject *parent /TransferThis/) /Factory/;
    static QGeoSatelliteInfoSource *createSource(const QString &sourceName, const QVariantMap &parameters, QObject *parent /TransferThis/) /Factory/;
    static QStringList availableSources();
    QString sourceName() const;
    virtual void setUpdateInterval(int msec);
    int updateInterval() const;
    virtual int minimumUpdateInterval() const = 0;
    virtual QGeoSatelliteInfoSource::Error error() const = 0;

public slots:
    virtual void startUpdates() = 0;
    virtual void stopUpdates() = 0;
    virtual void requestUpdate(int timeout = 0) = 0;

signals:
    void satellitesInViewUpdated(const QList<QGeoSatelliteInfo> &satellites);
    void satellitesInUseUpdated(const QList<QGeoSatelliteInfo> &satellites);
    void errorOccurred(QGeoSatelliteInfoSource::Error);

public:
    virtual bool setBackendProperty(const QString &name, const QVariant &value);
    virtual QVariant backendProperty(const QString &name) const;
};

%End
