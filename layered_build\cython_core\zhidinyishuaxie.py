import os
import subprocess
import sys
import threading
import random
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton, 
                           QLabel, QFileDialog, QMessageBox, QTextEdit)
from PyQt6.QtCore import Qt, pyqtSignal, QObject, QPoint
from utils import ADBTools
from custom_messagebox import CustomMessageBox

class FlashWorker(QObject):
    finished = pyqtSignal()
    error = pyqtSignal(str)
    log = pyqtSignal(str)
    
    def __init__(self, fastboot_path, partition, file_path):
        super().__init__()
        self.fastboot_path = fastboot_path
        self.partition = partition
        self.file_path = file_path
        
    def run(self):
        try:
            # 创建 startupinfo 对象来隐藏黑框（Windows特定）
            startupinfo = None
            if os.name == 'nt':  # Windows系统
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                
            # 执行刷写命令
            cmd = [self.fastboot_path, "flash", self.partition, self.file_path]
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                startupinfo=startupinfo,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            # 读取输出
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    self.log.emit(output.strip())
                    
            # 检查返回码
            if process.returncode == 0:
                self.log.emit("刷写成功")
            else:
                error = process.stderr.read()
                self.error.emit(f"刷写失败: {error}")
                
            self.finished.emit()
                
        except Exception as e:
            self.error.emit(f"发生错误: {str(e)}")

class CustomFlashDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("自定义刷写")
        self.setFixedSize(750, 650)
        # 设置为无边框+顶层弹窗+模态
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.Dialog)
        self.setWindowModality(Qt.WindowModality.ApplicationModal)
        # 拖动相关变量
        self.draggable = True
        self.dragging = False
        self.offset = QPoint()
        
        # 创建临时目录
        self.temp_dir = self._get_temp_dir()
        if not os.path.exists(self.temp_dir):
            os.makedirs(self.temp_dir)
            
        self.setStyleSheet("""
            QDialog {
                background-color: #2E4F7C;
                border-radius: 12px;
                border: 2px solid #1E3F6C;
            }
            QLabel {
                color: white;
                font-size: 14px;
                margin: 5px;
                font-weight: bold;
            }
            QPushButton {
                background-color: #ff3333;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 12px;
                font-size: 14px;
                min-width: 100px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #cc2929;
            }
            QPushButton:pressed {
                background-color: #b32424;
            }
            QPushButton:disabled {
                background-color: #ff9999;
            }
            QTextEdit {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 12px;
                padding: 8px;
                font-family: "Microsoft YaHei UI";
                font-size: 12px;
                font-weight: bold;
            }
        """)
        
        self.initUI()
        
    def initUI(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(10)  # 增加整体间距
        layout.setContentsMargins(20, 20, 20, 20)  # 增加边距
        
        # 分区选择区域
        partition_frame = QVBoxLayout()
        partition_frame.setSpacing(13)  # 增加分区区域的间距
        partition_label = QLabel("选择要刷写的分区:")
        partition_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        partition_frame.addWidget(partition_label)
        
        # 分区按钮布局
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)  # 增加按钮之间的间距
        
        # 常用分区按钮（多行，顺序随机）
        common_partitions = [
            "boot", "recovery", "init_boot", "vendor", "modem",
            "dtbo", "vbmeta", "system", "abl", "odm"
        ]
        random.shuffle(common_partitions)  # 随机顺序

        # 分两行
        row_count = 2
        per_row = (len(common_partitions) + row_count - 1) // row_count
        for i in range(row_count):
            row_layout = QHBoxLayout()
            row_layout.setSpacing(15)
            for partition in common_partitions[i*per_row:(i+1)*per_row]:
                btn = QPushButton(partition)
                btn.setMinimumHeight(40)
                btn.setMinimumWidth(30)
                btn.setMaximumWidth(80)
                btn.clicked.connect(lambda checked, p=partition: self.select_partition(p))
                row_layout.addWidget(btn)
            partition_frame.addLayout(row_layout)
        
        partition_frame.addLayout(button_layout)
        
        # 添加手动输入分区名的区域
        custom_partition_layout = QHBoxLayout()
        custom_partition_layout.setSpacing(20)  # 增加自定义分区区域的间距
        custom_partition_label = QLabel("自定义分区名:")
        self.custom_partition_input = QTextEdit()
        self.custom_partition_input.setMinimumHeight(40)  # 增加输入框高度
        self.custom_partition_input.setMaximumHeight(40)  # 限制输入框高度
        self.custom_partition_input.setMinimumWidth(150)  # 设置最小宽度
        self.custom_partition_input.setMaximumWidth(200)  # 设置最大宽度
        self.custom_partition_input.setPlaceholderText("输入分区名称")
        self.custom_partition_input.setLineWrapMode(QTextEdit.LineWrapMode.NoWrap)  # 禁用自动换行
        self.custom_partition_input.setStyleSheet("font-size: 12px;")  # 设置字体大小
        self.custom_partition_input.textChanged.connect(self.validate_partition_input)  # 添加输入验证
        custom_partition_layout.addWidget(custom_partition_label)
        custom_partition_layout.addWidget(self.custom_partition_input)
        
        # 添加自定义分区按钮
        custom_partition_btn = QPushButton("使用自定义分区")
        custom_partition_btn.setMinimumHeight(30)  # 增加按钮高度
        custom_partition_btn.clicked.connect(self.use_custom_partition)
        custom_partition_layout.addWidget(custom_partition_btn)
        
        partition_frame.addLayout(custom_partition_layout)
        layout.addLayout(partition_frame)
        
        # 文件选择区域
        file_frame = QVBoxLayout()
        file_frame.setSpacing(13)  # 增加文件选择区域的间距
        
        # 创建水平布局来居中按钮
        file_button_layout = QHBoxLayout()
        file_button_layout.addStretch()  # 添加弹性空间
        
        select_file_btn = QPushButton("选择文件")
        select_file_btn.setMinimumHeight(40)  # 增加按钮高度
        select_file_btn.setMinimumWidth(100)  # 设置最小宽度
        select_file_btn.setMaximumWidth(120)  # 设置最大宽度
        select_file_btn.clicked.connect(self.select_file)
        file_button_layout.addWidget(select_file_btn)
        
        file_button_layout.addStretch()  # 添加弹性空间
        file_frame.addLayout(file_button_layout)
        
        layout.addLayout(file_frame)
        
        # 日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMinimumHeight(100)  # 增加日志区域高度
        layout.addWidget(self.log_text)
        
        # 操作按钮
        button_frame = QHBoxLayout()
        button_frame.setSpacing(30)  # 增加操作按钮之间的间距
        
        flash_btn = QPushButton("开始刷写")
        flash_btn.setMinimumHeight(40)  # 增加按钮高度
        flash_btn.clicked.connect(self.start_flash)
        button_frame.addWidget(flash_btn)
        
        close_btn = QPushButton("关闭")
        close_btn.setMinimumHeight(40)  # 增加按钮高度
        close_btn.clicked.connect(self.close)
        button_frame.addWidget(close_btn)
        
        layout.addLayout(button_frame)
        
    def select_partition(self, partition):
        self.selected_partition = partition
        self.add_log(f"已选择目标分区: {partition}")
        
    def select_file(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择刷写文件",
            "",
            "镜像文件 (*.img);;所有文件 (*)"
        )
        if file_path:
            self.selected_file = file_path
            self.add_log(f"已选择文件: {os.path.basename(file_path)}")
            
    def start_flash(self):
        if not hasattr(self, 'selected_partition'):
            CustomMessageBox("警告", "请先选择要刷写的分区", self).exec()
            return
            
        if not hasattr(self, 'selected_file'):
            CustomMessageBox("警告", "请先选择刷写文件", self).exec()
            return
            
        # 禁用所有按钮
        self.set_buttons_enabled(False)
        self.add_log(f"{self.selected_partition}刷写中，请等待完成...")
        
        try:
            # 获取 fastboot 路径
            adb_tools = ADBTools()
            adb_path, fastboot_path = adb_tools.get_adb_path()
            if not os.path.exists(fastboot_path):
                self.add_log("错误: 未找到 fastboot 工具")
                self.set_buttons_enabled(True)
                return
                
            # 创建并启动工作线程
            self.worker = FlashWorker(fastboot_path, self.selected_partition, self.selected_file)
            self.worker_thread = threading.Thread(target=self.worker.run)
            
            # 连接信号
            self.worker.log.connect(self.add_log)
            self.worker.error.connect(self.handle_error)
            self.worker.finished.connect(self.flash_finished)
            
            # 启动线程
            self.worker_thread.start()
                
        except Exception as e:
            self.add_log(f"发生错误: {str(e)}")
            CustomMessageBox("错误", f"发生错误: {str(e)}", self).exec()
            self.set_buttons_enabled(True)
            
    def handle_error(self, error_msg):
        self.add_log(error_msg)
        CustomMessageBox("错误", error_msg, self).exec()
        self.set_buttons_enabled(True)
        
    def flash_finished(self):
        CustomMessageBox("成功", "刷写完成", self).exec()
        self.set_buttons_enabled(True)
        
    def set_buttons_enabled(self, enabled):
        # 禁用/启用所有按钮
        for child in self.findChildren(QPushButton):
            child.setEnabled(enabled)

    def add_log(self, message):
        self.log_text.append(message)
        # 滚动到底部
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )

    def _get_temp_dir(self):
        """获取临时目录路径"""
        if getattr(sys, 'frozen', False):
            # 打包后的环境
            base_path = sys._MEIPASS
        else:
            # 开发环境
            base_path = os.path.dirname(os.path.abspath(__file__))
            
        temp_dir = os.path.join(base_path, "temp")
        return temp_dir

    def use_custom_partition(self):
        custom_partition = self.custom_partition_input.toPlainText().strip()
        if custom_partition:
            self.select_partition(custom_partition)
        else:
            CustomMessageBox("警告", "请输入分区名称", self).exec()

    def validate_partition_input(self):
        """验证分区输入"""
        text = self.custom_partition_input.toPlainText()
        # 如果包含换行符，只保留第一行
        if '\n' in text:
            lines = text.split('\n')
            self.custom_partition_input.setPlainText(lines[0])

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton and self.draggable:
            self.dragging = True
            self.offset = event.position().toPoint()

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.dragging and self.draggable:
            self.move(self.mapToGlobal(event.position().toPoint() - self.offset))

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.dragging = False
            
    def showEvent(self, event):
        """窗口显示事件"""
        super().showEvent(event)
        # 获取主窗口的位置
        parent = self.parent()
        if parent:
            # 计算居中位置
            x = parent.x() + (parent.width() - self.width()) // 2
            y = parent.y() + (parent.height() - self.height()) // 2
            self.move(x, y)
