// qmetaobject.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QMetaMethod
{
%TypeHeaderCode
#include <qmetaobject.h>
%End

%TypeCode
// Raise an exception when QMetaMethod::invoke() returns false.
static void qtcore_invoke_exception()
{
    PyErr_SetString(PyExc_RuntimeError, "QMetaMethod.invoke() call failed");
}
%End

public:
    QMetaMethod();
    const char *typeName() const;
    QList<QByteArray> parameterTypes() const;
    QList<QByteArray> parameterNames() const;
    const char *tag() const;

    enum Access
    {
        Private,
        Protected,
        Public,
    };

    QMetaMethod::Access access() const;

    enum MethodType
    {
        Method,
        Signal,
        Slot,
        Constructor,
    };

    QMetaMethod::MethodType methodType() const;
    SIP_PYOBJECT invoke(QObject *object, QGenericArgument value0 = QGenericArgument(nullptr), QGenericArgument value1 = QGenericArgument(), QGenericArgument value2 = QGenericArgument(), QGenericArgument value3 = QGenericArgument(), QGenericArgument value4 = QGenericArgument(), QGenericArgument value5 = QGenericArgument(), QGenericArgument value6 = QGenericArgument(), QGenericArgument value7 = QGenericArgument(), QGenericArgument value8 = QGenericArgument(), QGenericArgument value9 = QGenericArgument()) const;
%MethodCode
        // Raise an exception if the call failed.
        bool ok;
        
        Py_BEGIN_ALLOW_THREADS
        ok = sipCpp->invoke(a0, *a1, *a2, *a3, *a4, *a5, *a6, *a7, *a8, *a9, *a10);
        Py_END_ALLOW_THREADS
        
        if (ok)
        {
            Py_INCREF(Py_None);
            sipRes = Py_None;
        }
        else
        {
            qtcore_invoke_exception();
        }
%End

    SIP_PYOBJECT invoke(QObject *object, Qt::ConnectionType connectionType, QGenericArgument value0 = QGenericArgument(nullptr), QGenericArgument value1 = QGenericArgument(), QGenericArgument value2 = QGenericArgument(), QGenericArgument value3 = QGenericArgument(), QGenericArgument value4 = QGenericArgument(), QGenericArgument value5 = QGenericArgument(), QGenericArgument value6 = QGenericArgument(), QGenericArgument value7 = QGenericArgument(), QGenericArgument value8 = QGenericArgument(), QGenericArgument value9 = QGenericArgument()) const;
%MethodCode
        // Raise an exception if the call failed.
        bool ok;
        
        Py_BEGIN_ALLOW_THREADS
        ok = sipCpp->invoke(a0, a1, *a2, *a3, *a4, *a5, *a6, *a7, *a8, *a9, *a10, *a11);
        Py_END_ALLOW_THREADS
        
        if (ok)
        {
            Py_INCREF(Py_None);
            sipRes = Py_None;
        }
        else
        {
            qtcore_invoke_exception();
        }
%End

    SIP_PYOBJECT invoke(QObject *object, QGenericReturnArgument returnValue /GetWrapper/, QGenericArgument value0 = QGenericArgument(nullptr), QGenericArgument value1 = QGenericArgument(), QGenericArgument value2 = QGenericArgument(), QGenericArgument value3 = QGenericArgument(), QGenericArgument value4 = QGenericArgument(), QGenericArgument value5 = QGenericArgument(), QGenericArgument value6 = QGenericArgument(), QGenericArgument value7 = QGenericArgument(), QGenericArgument value8 = QGenericArgument(), QGenericArgument value9 = QGenericArgument()) const;
%MethodCode
        // Raise an exception if the call failed.
        bool ok;
        
        Py_BEGIN_ALLOW_THREADS
        ok = sipCpp->invoke(a0, *a1, *a2, *a3, *a4, *a5, *a6, *a7, *a8, *a9, *a10,
                *a11);
        Py_END_ALLOW_THREADS
        
        if (ok)
            sipRes = qpycore_ReturnValue(a1Wrapper);
        else
        
            qtcore_invoke_exception();
%End

    SIP_PYOBJECT invoke(QObject *object, Qt::ConnectionType connectionType, QGenericReturnArgument returnValue /GetWrapper/, QGenericArgument value0 = QGenericArgument(nullptr), QGenericArgument value1 = QGenericArgument(), QGenericArgument value2 = QGenericArgument(), QGenericArgument value3 = QGenericArgument(), QGenericArgument value4 = QGenericArgument(), QGenericArgument value5 = QGenericArgument(), QGenericArgument value6 = QGenericArgument(), QGenericArgument value7 = QGenericArgument(), QGenericArgument value8 = QGenericArgument(), QGenericArgument value9 = QGenericArgument()) const;
%MethodCode
        // Raise an exception if the call failed.
        bool ok;
        
        Py_BEGIN_ALLOW_THREADS
        ok = sipCpp->invoke(a0, a1, *a2, *a3, *a4, *a5, *a6, *a7, *a8, *a9, *a10, *a11,
                *a12);
        Py_END_ALLOW_THREADS
        
        if (ok)
            sipRes = qpycore_ReturnValue(a2Wrapper);
        else
            qtcore_invoke_exception();
%End

    int methodIndex() const;
    int revision() const;
    bool isValid() const;
    QByteArray methodSignature() const;
    QByteArray name() const;
    int returnType() const;
    int parameterCount() const;
    int parameterType(int index) const;
    QMetaType returnMetaType() const;
    QMetaType parameterMetaType(int index) const;
    QByteArray parameterTypeName(int index) const;
    int relativeMethodIndex() const;
%If (Qt_6_2_0 -)
    bool isConst() const;
%End
};

class QMetaEnum
{
%TypeHeaderCode
#include <qmetaobject.h>
%End

public:
    QMetaEnum();
    const char *name() const;
    bool isFlag() const;
    int keyCount() const;
    const char *key(int index) const;
    int value(int index) const;
    const char *scope() const;
    int keyToValue(const char *key, bool *ok = 0) const;
    const char *valueToKey(int value) const;
    int keysToValue(const char *keys, bool *ok = 0) const;
    QByteArray valueToKeys(int value) const;
    bool isValid() const;
    bool isScoped() const;
    const char *enumName() const;
%If (Qt_6_6_0 -)
    QMetaType metaType() const;
%End
};

class QMetaProperty
{
%TypeHeaderCode
#include <qmetaobject.h>
%End

public:
    QMetaProperty();
    const char *name() const;
    const char *typeName() const;
    bool isReadable() const;
    bool isWritable() const;
    bool isDesignable() const;
    bool isScriptable() const;
    bool isStored() const;
    bool isFlagType() const;
    bool isEnumType() const;
    QMetaEnum enumerator() const;
    SIP_PYOBJECT read(const QObject *obj) const /TypeHint="Any"/;
%MethodCode
        // Make use of the QMetaProperty to provide the extra information to correctly
        // handle enums.
        sipRes = qpycore_PyObject_FromQVariant(*sipCpp, sipCpp->read(a0));
%End

    bool write(QObject *obj, const QVariant &value) const;
%MethodCode
        // If it looks like we are trying to write an int to an enum then we are
        // probably trying to write to an unregistered enum.  Converting the int to the
        // name of the corresponding key should work (although this isn't a documented
        // feature).
        
        QMetaEnum me = sipCpp->enumerator();
        if (me.isValid() && a1->typeId() == QMetaType::Int)
        {
            QString key;
        
            if (me.isFlag())
            {
                key = QString(me.valueToKeys(a1->toInt()));
            }
            else
            {
                const char *key_s = me.valueToKey(a1->toInt());
                
                if (key_s)
                {
                    key = QString(key_s);
                }
                else
                {
                    PyErr_Format(PyExc_ValueError, "%S is not a valid enum member", a1);
                    sipIsErr = 1;
                }
            }
            
            if (!sipIsErr)
                *const_cast<QVariant *>(a1) = QVariant(key);
        }
        
        if (!sipIsErr)
            sipRes = sipCpp->write(a0, *a1);
%End

    bool reset(QObject *obj) const;
    bool hasStdCppSet() const;
    bool isValid() const;
    bool isResettable() const;
    bool isUser() const;
    int userType() const;
    bool hasNotifySignal() const;
    QMetaMethod notifySignal() const;
    int notifySignalIndex() const;
    int propertyIndex() const;
    bool isConstant() const;
    bool isFinal() const;
    int revision() const;
    int relativePropertyIndex() const;
    bool isRequired() const;
    QMetaType metaType() const;
    bool isBindable() const;
    int typeId() const;
};

class QMetaClassInfo
{
%TypeHeaderCode
#include <qmetaobject.h>
%End

public:
    QMetaClassInfo();
    const char *name() const;
    const char *value() const;
};

bool operator==(const QMetaMethod &m1, const QMetaMethod &m2);
bool operator!=(const QMetaMethod &m1, const QMetaMethod &m2);
