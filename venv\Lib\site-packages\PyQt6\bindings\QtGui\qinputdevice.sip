// qinputdevice.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QInputDevice : public QObject
{
%TypeHeaderCode
#include <qinputdevice.h>
%End

public:
    enum class DeviceType /BaseType=Flag/
    {
        Unknown,
        Mouse,
        TouchScreen,
        <PERSON><PERSON>,
        Puck,
        <PERSON>ylus,
        Airbrush,
        Keyboard,
        AllDevices,
    };

    typedef QFlags<QInputDevice::DeviceType> DeviceTypes;

    enum class Capability /BaseType=Flag/
    {
        None,
        Position,
        Area,
        Pressure,
        Velocity,
        NormalizedPosition,
        MouseEmulation,
%If (Qt_6_2_0 -)
        PixelScroll,
%End
        Scroll,
        Hover,
        Rotation,
        XTilt,
        YTilt,
        TangentialPressure,
        ZPosition,
        All,
    };

    typedef QFlags<QInputDevice::Capability> Capabilities;
    QInputDevice(const QString &name, qint64 systemId, QInputDevice::DeviceType type, const QString &seatName = QString(), QObject *parent /TransferThis/ = 0);
    QInputDevice(QObject *parent /TransferThis/ = 0);
    virtual ~QInputDevice();
    QString name() const;
    QInputDevice::DeviceType type() const;
    QInputDevice::Capabilities capabilities() const;
    bool hasCapability(QInputDevice::Capability cap) const;
    qint64 systemId() const;
    QString seatName() const;
    QRect availableVirtualGeometry() const;
    static QList<const QInputDevice *> devices();
    static const QInputDevice *primaryKeyboard(const QString &seatName = QString());
    bool operator==(const QInputDevice &other) const;

signals:
    void availableVirtualGeometryChanged(QRect area);

public:
%If (Qt_6_3_0 -)
    static QStringList seatNames();
%End
};
