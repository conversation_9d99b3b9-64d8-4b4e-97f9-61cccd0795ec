import os
import sys
import platform
import subprocess
import time
from shutil import which

class ADBTools:
    def __init__(self):
        """初始化ADB工具类"""
        self.adb_path, self.fastboot_path = self.get_adb_path()

        # 确保工具存在
        if not os.path.exists(self.adb_path) or not os.path.exists(self.fastboot_path):
            print(f"ADB工具路径: {self.adb_path}")
            print(f"Fastboot工具路径: {self.fastboot_path}")
            print("警告：未找到ADB工具")

    @staticmethod
    def get_adb_path():
        # 兼容开发和PyInstaller打包环境
        if getattr(sys, 'frozen', False):
            base_dir = sys._MEIPASS
        else:
            base_dir = os.path.dirname(os.path.abspath(__file__))
        adb_dir = os.path.join(base_dir, 'ADBTools')
        adb_exe = 'adb.exe' if os.name == 'nt' else 'adb'
        fastboot_exe = 'fastboot.exe' if os.name == 'nt' else 'fastboot'
        adb_path = os.path.join(adb_dir, adb_exe)
        fastboot_path = os.path.join(adb_dir, fastboot_exe)
        if os.path.exists(adb_path) and os.path.exists(fastboot_path):
            return adb_path, fastboot_path
        # 兜底查环境变量
        adb_path_env = which('adb')
        fastboot_path_env = which('fastboot')
        if adb_path_env and fastboot_path_env:
            return adb_path_env, fastboot_path_env
        raise FileNotFoundError("未找到工具。")

    @staticmethod
    def _get_subprocess_config():
        """获取subprocess的配置，用于隐藏命令行窗口"""
        startupinfo = None
        creationflags = 0
        if os.name == 'nt':  # Windows系统
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            creationflags = subprocess.CREATE_NO_WINDOW
        return startupinfo, creationflags

    def get_devices(self):
        """获取已连接的设备列表
        返回格式: [(mode, device_id), ...]
        mode 可能是: device, recovery, fastboot, sideload
        """
        devices = []
        try:
            # 检查 adb 设备
            adb_result = subprocess.run(
                [self.adb_path, "devices"],
                capture_output=True,
                text=True,
                encoding='utf-8',
                startupinfo=self._get_startupinfo(),
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            if adb_result.stdout:
                for line in adb_result.stdout.split('\n')[1:]:  # 跳过第一行的 "List of devices attached"
                    if '\t' in line:
                        device, status = line.strip().split('\t')
                        if status in ['device', 'recovery', 'sideload']:
                            devices.append((status, device))

            # 检查 fastboot 设备
            fastboot_result = subprocess.run(
                [self.fastboot_path, "devices"],
                capture_output=True,
                text=True,
                encoding='utf-8',
                startupinfo=self._get_startupinfo(),
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            if fastboot_result.stdout:
                for line in fastboot_result.stdout.split('\n'):
                    if line.strip() and not line.startswith('List'):
                        parts = line.strip().split()
                        if len(parts) >= 1:
                            devices.append(('fastboot', parts[0]))

            return devices

        except Exception as e:
            print(f"获取设备列表出错: {str(e)}")
            return []

    def _get_startupinfo(self):
        """获取 startupinfo 对象，用于隐藏命令行窗口"""
        if os.name == 'nt':  # Windows系统
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            return startupinfo
        return None

    @staticmethod
    def execute_command(command):
        """使用 subprocess 执行命令"""
        try:
            startupinfo, creationflags = ADBTools._get_subprocess_config()
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                encoding='utf-8',
                startupinfo=startupinfo,
                creationflags=creationflags
            )
            return result.stdout, result.stderr
        except Exception as e:
            return None, str(e)

    @staticmethod
    def check_adb_exists():
        """检查adb工具是否存在"""
        adb_path, fastboot_path = ADBTools.get_adb_path()
        return os.path.exists(adb_path) and os.path.exists(fastboot_path)

    def check_device_status(self):
        """检查设备连接状态"""
        try:
            adb_path, fastboot_path = self.get_adb_path()
            if not os.path.exists(adb_path) or not os.path.exists(fastboot_path):
                return "ADB工具未找到", "red"
                
            devices = []
            startupinfo, creationflags = self._get_subprocess_config()

            # 检查adb设备
            try:
                adb_process = subprocess.run(
                    [adb_path, "devices"],
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    timeout=5,  # 设置超时时间为5秒
                    startupinfo=startupinfo,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )
                if adb_process.stdout:
                    for line in adb_process.stdout.split('\n')[1:]:
                        if '\t' in line:
                            device, status = line.split('\t')
                            if status.strip() in ['device', 'recovery', 'sideload']:
                                devices.append((status.strip(), device))
            except subprocess.TimeoutExpired:
                print("ADB 命令执行超时")
            except Exception as e:
                print(f"ADB 命令执行错误: {str(e)}")

            # 检查fastboot设备
            try:
                fastboot_process = subprocess.run(
                    [fastboot_path, "devices"],
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    timeout=5,  # 设置超时时间为5秒
                    startupinfo=startupinfo,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                )
                if fastboot_process.stdout:
                    for line in fastboot_process.stdout.split('\n'):
                        if line.strip() and not line.startswith('List'):
                            parts = line.split()
                            if len(parts) >= 1:
                                device = parts[0]
                                devices.append(('fastboot', device))
            except subprocess.TimeoutExpired:
                print("Fastboot 命令执行超时")
            except Exception as e:
                print(f"Fastboot 命令执行错误: {str(e)}")
            
            if not devices:
                return "未连接设备", "red"
            else:
                device_info = []
                for mode, device in devices:
                    if mode == 'device':
                        device_info.append(f"正常模式: {device}")
                    elif mode == 'recovery':
                        device_info.append(f"恢复模式: {device}")
                    elif mode == 'fastboot':
                        device_info.append(f"Fastboot模式: {device}")
                    elif mode == 'sideload':
                        device_info.append(f"Sideload模式: {device}")
                return "已连接设备:\n" + "\n".join(device_info), "green"

        except Exception as e:
            return f"检测出错: {str(e)}", "red" 