@echo off
echo 正在创建依赖下载目录...
if not exist "dependencies" mkdir dependencies

echo 正在下载matplotlib依赖到本地目录...
pip download matplotlib -d dependencies
pip download numpy -d dependencies
pip download cycler -d dependencies
pip download kiwisolver -d dependencies
pip download packaging -d dependencies
pip download pillow -d dependencies
pip download pyparsing -d dependencies
pip download python-dateutil -d dependencies
pip download six -d dependencies

echo 正在从本地安装matplotlib...
pip install --no-index --find-links dependencies matplotlib

echo.
echo matplotlib依赖下载和安装完成！
pause 