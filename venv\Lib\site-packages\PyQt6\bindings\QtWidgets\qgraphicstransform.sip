// qgraphicstransform.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QGraphicsTransform : public QObject
{
%TypeHeaderCode
#include <qgraphicstransform.h>
%End

public:
    QGraphicsTransform(QObject *parent /TransferThis/ = 0);
    virtual ~QGraphicsTransform();
    virtual void applyTo(QMatrix4x4 *matrix) const = 0;

protected slots:
    void update();
};

class QGraphicsScale : public QGraphicsTransform
{
%TypeHeaderCode
#include <qgraphicstransform.h>
%End

public:
    QGraphicsScale(QObject *parent /TransferThis/ = 0);
    virtual ~QGraphicsScale();
    QVector3D origin() const;
    void setOrigin(const QVector3D &point);
    qreal xScale() const;
    void setXScale(qreal);
    qreal yScale() const;
    void setYScale(qreal);
    qreal zScale() const;
    void setZScale(qreal);
    virtual void applyTo(QMatrix4x4 *matrix) const;

signals:
    void originChanged();
    void scaleChanged();
    void xScaleChanged();
    void yScaleChanged();
    void zScaleChanged();
};

class QGraphicsRotation : public QGraphicsTransform
{
%TypeHeaderCode
#include <qgraphicstransform.h>
%End

public:
    QGraphicsRotation(QObject *parent /TransferThis/ = 0);
    virtual ~QGraphicsRotation();
    QVector3D origin() const;
    void setOrigin(const QVector3D &point);
    qreal angle() const;
    void setAngle(qreal);
    QVector3D axis() const;
    void setAxis(const QVector3D &axis);
    void setAxis(Qt::Axis axis);
    virtual void applyTo(QMatrix4x4 *matrix) const;

signals:
    void originChanged();
    void angleChanged();
    void axisChanged();
};
