import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "qquickattachedpropertypropagator.h"
        name: "QQuickAttachedPropertyPropagator"
        accessSemantics: "reference"
        prototype: "QObject"
    }
    Component {
        file: "private/qquickuniversalstyle_p.h"
        name: "QQuickUniversalStyle"
        accessSemantics: "reference"
        prototype: "QQuickAttachedPropertyPropagator"
        exports: [
            "QtQuick.Controls.Universal/Universal 2.0",
            "QtQuick.Controls.Universal/Universal 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [512, 1536]
        attachedType: "QQuickUniversalStyle"
        Enum {
            name: "Theme"
            values: ["Light", "Dark", "System"]
        }
        Enum {
            name: "Color"
            values: [
                "<PERSON>e",
                "<PERSON>",
                "<PERSON>",
                "<PERSON><PERSON>",
                "<PERSON><PERSON>",
                "Cobalt",
                "<PERSON><PERSON>",
                "<PERSON>",
                "<PERSON>",
                "<PERSON>gent<PERSON>",
                "<PERSON>",
                "<PERSON>",
                "<PERSON>",
                "<PERSON>",
                "<PERSON>",
                "<PERSON>",
                "<PERSON>",
                "<PERSON>",
                "Mauve",
                "Taupe"
            ]
        }
        Property {
            name: "theme"
            type: "Theme"
            read: "theme"
            write: "setTheme"
            reset: "resetTheme"
            notify: "themeChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "accent"
            type: "QVariant"
            read: "accent"
            write: "setAccent"
            reset: "resetAccent"
            notify: "accentChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "foreground"
            type: "QVariant"
            read: "foreground"
            write: "setForeground"
            reset: "resetForeground"
            notify: "foregroundChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "background"
            type: "QVariant"
            read: "background"
            write: "setBackground"
            reset: "resetBackground"
            notify: "backgroundChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "altHighColor"
            type: "QColor"
            read: "altHighColor"
            notify: "paletteChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "altLowColor"
            type: "QColor"
            read: "altLowColor"
            notify: "paletteChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "altMediumColor"
            type: "QColor"
            read: "altMediumColor"
            notify: "paletteChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "altMediumHighColor"
            type: "QColor"
            read: "altMediumHighColor"
            notify: "paletteChanged"
            index: 7
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "altMediumLowColor"
            type: "QColor"
            read: "altMediumLowColor"
            notify: "paletteChanged"
            index: 8
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "baseHighColor"
            type: "QColor"
            read: "baseHighColor"
            notify: "paletteChanged"
            index: 9
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "baseLowColor"
            type: "QColor"
            read: "baseLowColor"
            notify: "paletteChanged"
            index: 10
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "baseMediumColor"
            type: "QColor"
            read: "baseMediumColor"
            notify: "paletteChanged"
            index: 11
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "baseMediumHighColor"
            type: "QColor"
            read: "baseMediumHighColor"
            notify: "paletteChanged"
            index: 12
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "baseMediumLowColor"
            type: "QColor"
            read: "baseMediumLowColor"
            notify: "paletteChanged"
            index: 13
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "chromeAltLowColor"
            type: "QColor"
            read: "chromeAltLowColor"
            notify: "paletteChanged"
            index: 14
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "chromeBlackHighColor"
            type: "QColor"
            read: "chromeBlackHighColor"
            notify: "paletteChanged"
            index: 15
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "chromeBlackLowColor"
            type: "QColor"
            read: "chromeBlackLowColor"
            notify: "paletteChanged"
            index: 16
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "chromeBlackMediumLowColor"
            type: "QColor"
            read: "chromeBlackMediumLowColor"
            notify: "paletteChanged"
            index: 17
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "chromeBlackMediumColor"
            type: "QColor"
            read: "chromeBlackMediumColor"
            notify: "paletteChanged"
            index: 18
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "chromeDisabledHighColor"
            type: "QColor"
            read: "chromeDisabledHighColor"
            notify: "paletteChanged"
            index: 19
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "chromeDisabledLowColor"
            type: "QColor"
            read: "chromeDisabledLowColor"
            notify: "paletteChanged"
            index: 20
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "chromeHighColor"
            type: "QColor"
            read: "chromeHighColor"
            notify: "paletteChanged"
            index: 21
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "chromeLowColor"
            type: "QColor"
            read: "chromeLowColor"
            notify: "paletteChanged"
            index: 22
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "chromeMediumColor"
            type: "QColor"
            read: "chromeMediumColor"
            notify: "paletteChanged"
            index: 23
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "chromeMediumLowColor"
            type: "QColor"
            read: "chromeMediumLowColor"
            notify: "paletteChanged"
            index: 24
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "chromeWhiteColor"
            type: "QColor"
            read: "chromeWhiteColor"
            notify: "paletteChanged"
            index: 25
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "listLowColor"
            type: "QColor"
            read: "listLowColor"
            notify: "paletteChanged"
            index: 26
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "listMediumColor"
            type: "QColor"
            read: "listMediumColor"
            notify: "paletteChanged"
            index: 27
            isReadonly: true
            isFinal: true
        }
        Signal { name: "themeChanged" }
        Signal { name: "accentChanged" }
        Signal { name: "foregroundChanged" }
        Signal { name: "backgroundChanged" }
        Signal { name: "paletteChanged" }
        Method {
            name: "color"
            type: "QColor"
            Parameter { name: "color"; type: "Color" }
        }
    }
}
