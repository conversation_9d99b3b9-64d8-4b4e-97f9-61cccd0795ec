// qgeorectangle.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_6_2_0 -)

class QGeoRectangle : public QGeoShape
{
%TypeHeaderCode
#include <qgeorectangle.h>
%End

public:
    QGeoRectangle();
    QGeoRectangle(const QGeoCoordinate &center, double degreesWidth, double degreesHeight);
    QGeoRectangle(const QGeoCoordinate &topLeft, const QGeoCoordinate &bottomRight);
    QGeoRectangle(const QList<QGeoCoordinate> &coordinates);
    QGeoRectangle(const QGeoRectangle &other);
    QGeoRectangle(const QGeoShape &other);
    ~QGeoRectangle();
    void setTopLeft(const QGeoCoordinate &topLeft);
    QGeoCoordinate topLeft() const;
    void setTopRight(const QGeoCoordinate &topRight);
    QGeoCoordinate topRight() const;
    void setBottomLeft(const QGeoCoordinate &bottomLeft);
    QGeoCoordinate bottomLeft() const;
    void setBottomRight(const QGeoCoordinate &bottomRight);
    QGeoCoordinate bottomRight() const;
    void setCenter(const QGeoCoordinate &center);
    QGeoCoordinate center() const;
    void setWidth(double degreesWidth);
    double width() const;
    void setHeight(double degreesHeight);
    double height() const;
    bool contains(const QGeoRectangle &rectangle) const;
    bool intersects(const QGeoRectangle &rectangle) const;
    void translate(double degreesLatitude, double degreesLongitude);
    QGeoRectangle translated(double degreesLatitude, double degreesLongitude) const;
    QGeoRectangle united(const QGeoRectangle &rectangle) const;
    QGeoRectangle &operator|=(const QGeoRectangle &rectangle);
    QGeoRectangle operator|(const QGeoRectangle &rectangle) const;
    QString toString() const;
    void extendRectangle(const QGeoCoordinate &coordinate);
};

%End
%If (Qt_6_5_0 -)
QDataStream &operator<<(QDataStream &stream, const QGeoRectangle &rectangle) /ReleaseGIL/;
%End
%If (Qt_6_5_0 -)
QDataStream &operator>>(QDataStream &stream, QGeoRectangle &rectangle /Constrained/) /ReleaseGIL/;
%End
