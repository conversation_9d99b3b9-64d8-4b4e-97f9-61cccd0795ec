frp
recovery_b
userdata
xbl_config_b
qupfw_b
dtbo_b
mdm_oem_stanvbk
vbmeta_system_b
imagefv_b
fw_ufs1_b
fw_ufs5_b
devcfg_b
aging
op1
vbmeta_b
storsec_a
fw_ufs4_b
oem_dycnvbk
multiimgoem_b
keystore
aging_mod
opproduct_b
uefisecapp_b
sdc
devcfg_a
vbmeta_a
vbmeta_system_a
uefisecapp_a
misc
LOGO_b
devinfo
dip
config
cmnlib64_a
xbl_b
qupfw_a
secdata
dtbo_a
rawdump
catecontentfv
imagefv_a
mdm1oemnvbktmp
reserve2
fw_ufs2_b
core_nhlos
recovery_a
reserve4
fw_ufs3_a
modem_b
fw_ufs5_a
catefv
aop_b
reserve1
fw_ufs3_b
boot_a
fw_ufs1_a
reserve3
fw_ufs2_a
tz_a
keymaster_a
fw_ufs4_a
cmnlib_a
multiimgoem_a
sdd
tz_b
opproduct_a
apdp
mdm_oem_dycnvbk
mdm1m9kefs3
msadp
ALIGN_TO_128K_2
param
mdm1m9kefs2
modem_a
mdtpsecapp_a
modemst2
abl_a
storsec_b
hyp_a
bluetooth_a
ssd
mdtp_a
dsp_a
hyp_b
splash
bluetooth_b
oem_stanvbk
toolsfv
xbl_a
cateloader
mdmddr
mdtp_b
LOGO_a
dsp_b
fsc
spunvm
boot_b
aop_a
limits
mdtpsecapp_b
logfs
abl_b
logdump
keymaster_b
uefivarstore
cmnlib_b
sde
cmnlib64_b
sdb
xbl_config_a
ddr
cdt
ALIGN_TO_128K_1
modemst1
fsg
mdm1m9kefsc
mdm1m9kefs1
sdf
super
metadata
ADB
adl
my_carrier
1
my_heytap
my_bigball_a
my_carrier_a
my_company_a
my_engineering_a
my_heytap_a
my_manifest_a
my_preload_a
my_product_a
my_region_a
my_stock_a
odm_a
product_a
system_a
system_ext_a
vendor_a