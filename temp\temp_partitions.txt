mdm_oem_stanvbk
vbmeta_system_b
fw_ufs5_b
frp
recovery_b
aging
userdata
op1
xbl_config_b
storsec_a
qupfw_b
fw_ufs4_b
dtbo_b
multiimgoem_b
imagefv_b
fw_ufs1_b
devcfg_b
vbmeta_b
aging_mod
uefisecapp_b
oem_dycnvbk
LOGO_b
keystore
opproduct_b
vbmeta_system_a
sdc
devinfo
devcfg_a
misc
vbmeta_a
config
mdm1oemnvbktmp
uefisecapp_a
xbl_b
dip
secdata
rawdump
cmnlib64_a
catecontentfv
qupfw_a
core_nhlos
reserve2
dtbo_a
recovery_a
reserve4
fw_ufs3_b
imagefv_a
fw_ufs3_a
modem_b
fw_ufs2_b
fw_ufs5_a
catefv
fw_ufs1_a
aop_b
reserve1
boot_a
reserve3
tz_a
fw_ufs2_a
keymaster_a
fw_ufs4_a
cmnlib_a
sdd
multiimgoem_a
mdm1m9kefs2
ALIGN_TO_128K_2
tz_b
apdp
msadp
mdm1m9kefs3
xbl_a
mdmddr
modemst2
fsc
opproduct_a
ssd
mdm_oem_dycnvbk
param
splash
oem_stanvbk
modem_a
toolsfv
mdtpsecapp_a
mdtp_b
cateloader
abl_a
dsp_b
LOGO_a
storsec_b
boot_b
spunvm
hyp_a
aop_a
limits
bluetooth_a
mdtpsecapp_b
logfs
mdtp_a
abl_b
logdump
dsp_a
keymaster_b
uefivarstore
hyp_b
cmnlib_b
bluetooth_b
sde
cmnlib64_b
sdb
xbl_config_a
ddr
cdt
ALIGN_TO_128K_1
modemst1
fsg
mdm1m9kefsc
mdm1m9kefs1
sdf
super
metadata
ADB
adl
my_carrier
1
my_heytap
my_bigball_a
my_carrier_a
my_company_a
my_engineering_a
my_heytap_a
my_manifest_a
my_preload_a
my_product_a
my_region_a
my_stock_a
odm_a
product_a
system_a
system_ext_a
vendor_a