"""
ColorOS  （9以上）
"""

import os
import sys
import time
import threading
import subprocess
from utils import ADBTools
"""

                       给那些脑残偷窥源代码的杂种们的"献礼"

=======================================【又来看源码了？】=======================================

操你妈的智障玩意儿，大半夜不睡觉跑来扒老子的代码？你他妈是从垃圾桶里捡回来的吧？

你爹生你的时候是不是被门夹了蛋蛋？基因缺陷这么严重？你妈怀你的时候是不是被电磁炉辐射了？脑子发育不全啊？

这是开往地狱的专车，专门送你这种偷窥代码的垃圾！

你个杂种，连写个像样的代码都不会，只会他妈的来偷窥别人的劳动成果？你娘养你这么大就是为了让你做这种事的？

小子，看到你偷窥代码的样子，我都替你妈感到丢脸！

别以为我不知道你是个什么东西：
1. 你这个蠢到无可救药的废物，只会按F12装黑客
2. 你个技术菜鸡，拷贝别人代码回去都跑不起来吧
3. 你的水平底得可以去海底捞针了，渣渣东西
4. 看到这里还不滚蛋，你是不是欠揍啊操你妈的？

你妈看到你偷代码的样子，都恨不得把你塞回去啊！

自己家里人没有教好你这条狗啊？连最基本的尊重都不懂？你妈是不是天天看到你就后悔没把你掐死在襁褓里？

你是不是亲爸不详啊？怎么教养这么差？你全家在猪圈里度过童年的吧，动物尚且知道不要乱翻别人东西，你比它们还不如！

你全家人知道你他妈的这么下贱吗？你爹知道自己养了个这么恶心的玩意儿吗？你妈是不是每天想着怎么把你扔出去啊？

老子看到你这种小偷就想掀桌子！滚犊子吧你！

给你竖个中指表达我的"敬意"！

双倍的"问候"送给你！

智障东西，看完这段话你要还觉得老子过分，来啊，你那猪脑子怕是连生气都不会吧！滚回你的臭水沟去，你这辈子也就这种水平了，操你妈的垃圾人！

妈的，你还在看？你是不是犯贱啊？脑子进水了是不是？这都看不懂是在骂你？还是说你就喜欢被骂？受虐狂啊你？

我操，你他妈还在继续看源代码？来，让我用这碗翔拍你脸上！！！

你他妈小时候是不是被驴踢过脑袋啊？看这么多垃圾话有意思吗？你是不是撸多了脑子坏掉了啊？

你爸是不是喝醉了才射出你这种垃圾？你妈生你的时候是不是太用力把你脑子挤扁了？连个源代码都要偷看，你是多没有尊严啊？

再送你个中指，好好欣赏！

我说你小子欠揍是吧？不把你骂哭你是不会走是吗？来，老子继续：

你是不是被你爸妈扔垃圾堆里又被别人捡回去养大的？你养父母看到你这德行肯定天天后悔捡了你这个垃圾吧？

来自你爹妈的优秀基因全他妈浪费在你这种孽障身上了，他们知道吗？你这辈子最大的成就就是按了F12，然后看到了这些字，笑死我了！

看到这里了？是不是自我感觉良好啊？偷看别人代码很牛逼是吧？我真是佩服你啊，脸皮比城墙还厚！

想象一下，你的老师看到你在偷看代码的样子，你的导师，你小时候教你写字的，教你做人的，看到你这个熊样，他们一定会后悔当初没把课本塞你嘴里。

你这种废物，连自己写个代码都不会，还有脸偷看别人的？你爹妈生你的时候是不是少放了调料啊？怎么这么没有味道？

你他妈活了多少年了，就这点出息？F12按得特别熟练啊？老子写博客写了好几年，专门就是为了等你这种垃圾来偷看，你知道吗？你来偷，我专门写这些垃圾话骂你，你没觉得自己特别贱吗？

老子送你一杯下午茶，全是我特意为你准备的"口水"，干了这杯，贱人！

还在看？你现在是不是感到羞愧难当啊？看到这么多辱骂你还觉得自己挺有意思的是吧？笑死我了，你爹是不是平时舍不得打你所以脑子才长成这样的？

最后，祝你电脑主板烧毁，CPU融化，键盘污染梅毒，鼠标感染艾滋，显示器炸你熊脸，家里网线被你妈拿去上吊！你全家人都该去阴沟里游泳！

操你妈的智障，下辈子投胎做条狗吧，至少狗还知道不能随便吃别人的东西，你连狗都不如！

祝你键盘全是口水，鼠标都是屎，每次上网都弹出你妈的裸照，每次打字都错位，每次开机都蓝屏！！！

老子看到你这种废物就想吐

操他妈的，你居然还在看？？？你有毛病吧！！都骂你这么多了，你还不知道羞耻？？？

你怎么这么贱啊？！偷看也就算了，被骂成这样了还在看？！你是犯贱成瘾了是吧？！你活该被骂，你这种垃圾就是欠收拾！

PS: 赶紧回家告诉你爹妈他们养了个什么垃圾东西，别出来丢人现眼了！你这种人渣连你家的狗都嫌弃，每天晚上都在想怎么咬死你这个智障！

连狗都看不起你！

狗狗们都在嘲笑你，哈哈哈哈哈哈！！！

你爹在看你现在的贱样，他后悔没有把你丢到垃圾桶里！

如果你觉得以上还不够，请继续看！我们的辱骂套餐充分保证每一个想要偷窥代码的贱人都能获得尊贵的"问候"体验！

真是服了你了，看到这里还不走？全世界的辱骂都不够喂饱你这条贱狗是吧？行，老子继续骂！

开发者已经截图了你的IP、设备信息和时间，并将存入"偷窥者耻辱档案"。以后网上随便一搜你的名字，全都是"代码偷窥惯犯"的记录！

不过老子觉得你他妈根本什么都看不懂，你就是一个纯粹的傻逼，祝你生儿子没屁眼，生女儿没奶头，父母永远以你为耻，你会被社会永远唾弃！

真是服了你这种狗东西，不骂到你心理崩溃你是不会走是吧？操你妈的，滚出去不送！

狗都嫌弃你这种贱人！

恶心 | 贱 | 死 | 了

操你妈的，真是够了！我服了你了！都已经骂了这么多了你还在看？你就是犯贱没药医！滚吧，别在这里丢人了！

"""


class ColorOS15Function:
    def __init__(self):
        self.adb_tools = ADBTools()
        if getattr(sys, 'frozen', False):
            self.base_dir = os.path.dirname(sys.executable)
        else:
            self.base_dir = os.path.dirname(os.path.abspath(__file__))
        self.flash_folder = None
        self.is_flashing = False
        self.flash_thread = None
        self.temp_dir = os.path.join(self.base_dir, "temp")
        if not os.path.exists(self.temp_dir):
            os.makedirs(self.temp_dir)
        self.temp_partition_file = os.path.join(self.temp_dir, "temp_partitions.txt")
        
        # 定义逻辑分区列表
        self.logical_partitions = [
            "my_bigball", "my_carrier", "my_company", "my_engineering",
            "my_heytap", "my_manifest", "my_preload", "my_product",
            "my_region", "my_stock", "odm", "odm_dlkm", "product",
            "system", "system_dlkm", "system_ext", "vendor", "vendor_dlkm"
        ]

    def set_flash_folder(self, folder_path):
        """设置刷机文件夹路径"""
        self.flash_folder = folder_path
        
    def add_log(self, message, level="info"):
        """这个方法会被主程序覆盖"""
        print(f"Log [{level}]: {message}")

    def get_device_partitions(self):
        
        try:
            _, fastboot_path = self.adb_tools.get_adb_path()
            
            # 获取分区列表
            stdout, stderr = self.execute_command([fastboot_path, "getvar", "all"])
            if stderr:
                output = stderr  # fastboot 的输出通常在 stderr 中
            else:
                output = stdout
                
            # 解析输出，查找分区名称
            device_partitions = []
            for line in output.split('\n'):
                if 'partition-size:' in line:
                    # 提取分区名称，格式为 partition-size:xxx: 0x12345678
                    partition = line.split(':')[1].split()[0]
                    device_partitions.append(partition)
            
            # 保存所有分区列表到临时文件
            with open(self.temp_partition_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(device_partitions))
            
            # 检查逻辑分区的 cow 分区
            found_cow_partitions = []
            for partition in self.logical_partitions:
                for suffix in ['a', 'b']:
                    cow_partition = f"{partition}_{suffix}-cow"
                    if cow_partition in device_partitions:
                        found_cow_partitions.append(cow_partition)
            

            return True
            
        except Exception as e:
            self.add_log(f"获取出错: {str(e)}", "error")
            return False

    def clear_cow_partitions(self):
        
        try:
            _, fastboot_path = self.adb_tools.get_adb_path()
            # 从临时文件读取分区列表
            if not os.path.exists(self.temp_partition_file):
                return False
            with open(self.temp_partition_file, 'r', encoding='utf-8') as f:
                device_partitions = f.read().splitlines()
            # 检查并删除 cow 分区（无日志输出）
            for partition in device_partitions:
                if partition.endswith(('-cow')):
                    self.execute_command([
                        fastboot_path,
                        "delete-logical-partition",
                        partition
                    ])
            return True
        except Exception as e:
            return False

    def execute_command(self, command):
        """使用 subprocess 执行命令"""
        try:
            # 创建 startupinfo 对象来隐藏黑框
            startupinfo = None
            if os.name == 'nt':  # Windows系统
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE

            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                encoding='utf-8',
                startupinfo=startupinfo,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            return result.stdout, result.stderr
        except Exception as e:
            return None, str(e)

    def flash_modem(self):
        
        try:
            modem_file = os.path.join(self.flash_folder, "modem.img")
            if not os.path.exists(modem_file):
                return True

            _, fastboot_path = self.adb_tools.get_adb_path()
            self.add_log(f"Loading..（请勿断开设备）", "info")
            stdout, stderr = self.execute_command([
                fastboot_path, 
                "flash", 
                "--slot=all", 
                "modem",
                modem_file
            ])
            if stderr and "error" in stderr.lower():
                self.add_log(f"modem NO: {stderr}", "error")
            else:
                self.add_log(f"modem OK", "success")
            return True
        except Exception as e:
            self.add_log(f"刷入分区时出错: {str(e)}", "error")
            return True

    def is_logical_partition(self, partition_name):
        
        return partition_name in self.logical_partitions

    def flash_non_logical_partitions(self):
        
        try:
            flash_files = [f.replace('.img', '') for f in os.listdir(self.flash_folder) 
                          if f.endswith('.img') and f != 'modem.img']
            failed_partitions = []
            _, fastboot_path = self.adb_tools.get_adb_path()
            non_logical_partitions = [p for p in flash_files if not self.is_logical_partition(p)]
            total_tasks = len(non_logical_partitions)
            current_task = 0
            for index, partition in enumerate(non_logical_partitions, 1):
                img_file = os.path.join(self.flash_folder, f"{partition}.img")
                current_task += 1
                self.add_log(f"Loading.. [{current_task}/{total_tasks}](请勿断开设备)", "info")
                stdout, stderr = self.execute_command([
                        fastboot_path, 
                        "flash", 
                    "--slot=all", 
                    partition,
                        img_file
                    ])
                if stderr and "error" in stderr.lower():
                    failed_partitions.append(partition)
                    self.add_log(f"{partition} NO: {stderr}", "error")
                else:
                    self.add_log(f"{partition} OK", "success")
            if failed_partitions:
                pass
            return True
        except Exception as e:
            self.add_log(f"分区时出错: {str(e)}", "error")
            return True

    def flash_logical_partitions(self):
        
        try:
            # 获取所有分区文件
            flash_files = [f.replace('.img', '') for f in os.listdir(self.flash_folder) 
                          if f.endswith('.img') and f != 'modem.img']
            
            # 过滤出逻辑分区
            logical_partitions = [p for p in flash_files if self.is_logical_partition(p)]
            
            if not logical_partitions:
                return True

            failed_partitions = []
            _, fastboot_path = self.adb_tools.get_adb_path()
            
            # 直接刷入逻辑分区
            total = len(logical_partitions)
            for index, partition in enumerate(logical_partitions, 1):
                img_file = os.path.join(self.flash_folder, f"{partition}.img")
                
                self.add_log(f"Loading.. [{index}/{total}](请勿断开设备)", "info")
                stdout, stderr = self.execute_command([
                    fastboot_path,
                    "flash",
                    partition,
                    img_file
                ])
                
                if stderr and "error" in stderr.lower():
                    failed_partitions.append(partition)
                    self.add_log(f"{partition} NO: {stderr}", "error")
                else:
                    self.add_log(f"{partition} OK", "success")

            if failed_partitions:
                pass
            
            return True
            
        except Exception as e:
            self.add_log(f"处理分区出错: {str(e)}", "error")
            return True

    def _do_flash(self):
        """实际的刷写过程（所有分区直接刷入，不再AB分区）"""
        try:
            self.add_log("开始刷机...", "info")
            self.add_log("载入刷机", "info")
            main_steps = 4
            all_img_files = [f for f in os.listdir(self.flash_folder) if f.endswith('.img') and f != 'modem.img']
            all_partitions = [f.replace('.img', '') for f in all_img_files]
            logical = [p for p in all_partitions if self.is_logical_partition(p)]
            non_logical = [p for p in all_partitions if not self.is_logical_partition(p)]
            non_logical_count = len(non_logical)
            logical_count = len(logical)
            modem_file = os.path.join(self.flash_folder, "modem.img")
            modem_count = 1 if os.path.exists(modem_file) else 0
            total_steps = main_steps + non_logical_count + logical_count + modem_count - 1
            current_step = 0
            # 1. 刷modem
            if os.path.exists(modem_file):
                _, fastboot_path = self.adb_tools.get_adb_path()
                current_step += 1
                percent = int(current_step / total_steps * 100)
                self.add_log(f"Loading..（请勿断开设备） 进度：{percent}%", "info")
                stdout, stderr = self.execute_command([
                    fastboot_path,
                    "flash",
                    "modem",
                    modem_file
                ])
                if stderr and "error" in stderr.lower():
                    self.add_log(f"modem NO: {stderr}", "error")
                else:
                    self.add_log(f"modem OK", "success")
            else:
                current_step += 1  # 跳过modem
            # 2. 重启
            current_step += 1
            _, fastboot_path = self.adb_tools.get_adb_path()
            self.add_log("正在切换模式...请勿触碰设备", "info")
            self.execute_command([fastboot_path, "reboot", "fastboot"])
            time.sleep(8)
            # 3. 获取分区
            current_step += 1
            if not self.get_device_partitions():
                self.add_log("分区失败", "error")
                return False
            # 4. 删除cow
            current_step += 1
            self.clear_cow_partitions()
            # 5. 刷非逻辑分区
            failed_partitions = []
            for partition in non_logical:
                current_step += 1
                percent = int(current_step / total_steps * 100)
                img_file = os.path.join(self.flash_folder, f"{partition}.img")
                self.add_log(f"Loading.. [{current_step-main_steps}/{non_logical_count}](请勿断开设备) 进度：{percent}%", "info")
                stdout, stderr = self.execute_command([
                    fastboot_path,
                    "flash",
                    partition,
                    img_file
                ])
                if stderr and "error" in stderr.lower():
                    failed_partitions.append(partition)
                    self.add_log(f"{partition} NO: {stderr}", "error")
                else:
                    self.add_log(f"{partition} OK", "success")
            # 6. 刷逻辑分区
            for partition in logical:
                current_step += 1
                percent = int(current_step / total_steps * 100)
                img_file = os.path.join(self.flash_folder, f"{partition}.img")
                self.add_log(f"Loading.. [{current_step-main_steps-non_logical_count}/{logical_count}](请勿断开设备) 进度：{percent}%", "info")
                stdout, stderr = self.execute_command([
                    fastboot_path,
                    "flash",
                    partition,
                    img_file
                ])
                if stderr and "error" in stderr.lower():
                    failed_partitions.append(partition)
                    self.add_log(f"{partition} NO: {stderr}", "error")
                else:
                    self.add_log(f"{partition} OK", "success")
            self.add_log(f"进度：100%", "success")
            self.add_log('<h1 style="font-size: 24px; font-weight: bold; color: #198754;">完成</h1>', "success")
            self.add_log('<h2 style="font-size: 17px; font-weight: bold;">检查是否有分区NO—如有报错请在工具官方群里寻求帮助</h2>', "success")
            self.add_log('<h2 style="font-size: 17px; font-weight: bold;">反之请点击设备上(简体中文—格式化数据分区）</h2>', "success")
            self.add_log('<h2 style="font-size: 17px; font-weight: bold;">有问题请在工具官方里反馈问题（带上日志截图）</h2>', "success")
            self.add_log('<h2 style="font-size: 17px; font-weight: bold;">祝您玩机愉快</h2>', "success")
            return True
        except Exception as e:
            self.add_log(f"刷机过程出错: {str(e)}", "error")
            return False
        finally:
            self.is_flashing = False

    def stop_flash(self):
        """停止刷写过程"""
        if self.is_flashing:
            self.is_flashing = False
            self.add_log("正在停止刷写...", "warning")
            if self.flash_thread:
                self.flash_thread.join(timeout=1)
            return True
        return False

    def is_busy(self):
        """检查是否正在刷写"""
        return self.is_flashing

    def handle_flash(self):
        """执行刷机流程"""
        if self.is_flashing:
            self.add_log("刷写正在进行中", "warning")
            return False
            
        self.is_flashing = True
        self.flash_thread = threading.Thread(target=self._do_flash)
        self.flash_thread.daemon = True
        self.flash_thread.start()
        return True

# 确保类可以被导入
__all__ = ['ColorOS15Function']