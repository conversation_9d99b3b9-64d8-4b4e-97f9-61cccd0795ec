[2025-07-16 22:21:35] [SUCCESS] ✅ 验证目录: ADBTools (6 个文件)
[2025-07-16 22:21:35] [INFO] ℹ️   ✓ adb.exe (5,920,544 字节)
[2025-07-16 22:21:35] [INFO] ℹ️   ✓ fastboot.exe (2,050,144 字节)
[2025-07-16 22:21:35] [SUCCESS] ✅ 验证目录: ico (1 个文件)
[2025-07-16 22:21:35] [SUCCESS] ✅ 验证目录: tup (2 个文件)
[2025-07-16 22:21:35] [SUCCESS] ✅ 验证目录: PyQt6 (2502 个文件)
[2025-07-16 22:21:35] [SUCCESS] ✅ 找到图标文件: ico/icon.ico
[2025-07-16 22:21:35] [INFO] ℹ️ 添加ADBTools文件: adb.exe
[2025-07-16 22:21:35] [INFO] ℹ️ 添加ADBTools文件: AdbWinApi.dll
[2025-07-16 22:21:35] [INFO] ℹ️ 添加ADBTools文件: AdbWinUsbApi.dll
[2025-07-16 22:21:35] [INFO] ℹ️ 添加ADBTools文件: fastboot.exe
[2025-07-16 22:21:35] [INFO] ℹ️ 添加ADBTools文件: libwinpthread-1.dll
[2025-07-16 22:21:35] [INFO] ℹ️ 添加ADBTools文件: pay.exe
[2025-07-16 22:21:35] [INFO] ℹ️ 开始Nuitka编译...
[2025-07-16 22:21:35] [INFO] ℹ️ 编译命令: D:\py\shuajishangyong\shuaji\venv\Scripts\python.exe -m nuitka --standalone --onefile --onefile-tempdir-spec={TEMP}\syiming\onefile_{PID}_{TIME} --windows-console-mode=disable --enable-plugin=pyqt6 --assume-yes-for-downloads --show-progress --show-memory --remove-output --output-filename=益民欧加真固件刷写工具_加密版.exe --include-data-file=ADBTools\adb.exe=ADBTools/adb.exe --include-data-file=ADBTools\AdbWinApi.dll=ADBTools/AdbWinApi.dll --include-data-file=ADBTools\AdbWinUsbApi.dll=ADBTools/AdbWinUsbApi.dll --include-data-file=ADBTools\fastboot.exe=ADBTools/fastboot.exe --include-data-file=ADBTools\libwinpthread-1.dll=ADBTools/libwinpthread-1.dll --include-data-file=ADBTools\pay.exe=ADBTools/pay.exe --include-data-dir=ico=ico --include-data-dir=tup=tup --include-data-dir=PyQt6=PyQt6 --windows-icon-from-ico=ico/icon.ico main.py
