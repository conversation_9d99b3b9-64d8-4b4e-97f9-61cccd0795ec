{#     Copyright 2025, <PERSON>, mailto:<EMAIL> find license text at end of file #}

PyObject *MAKE_LIST{{args_count}}(PyThreadState *tstate,
{% for i in range(args_count) %}
{% if i != 0 %}
    ,
{% endif %}
    PyObject *arg{{i}}
{% endfor %}
) {

    PyObject *result = MAKE_LIST_EMPTY(tstate, {{args_count}});

    if (unlikely(result == NULL)) {
        return NULL;
    }

{% for i in range(args_count) %}
    CHECK_OBJECT(arg{{i}});
    Py_INCREF(arg{{i}});
    PyList_SET_ITEM(result, {{i}}, arg{{i}});

{% endfor %}

    return result;
}

{#     Part of "Nuitka", an optimizing Python compiler that is compatible and   #}
{#     integrates with CPython, but also works on its own.                      #}
{#                                                                              #}
{#     Licensed under the Apache License, Version 2.0 (the "License");          #}
{#     you may not use this file except in compliance with the License.         #}
{#     You may obtain a copy of the License at                                  #}
{#                                                                              #}
{#        http://www.apache.org/licenses/LICENSE-2.0                            #}
{#                                                                              #}
{#     Unless required by applicable law or agreed to in writing, software      #}
{#     distributed under the License is distributed on an "AS IS" BASIS,        #}
{#     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. #}
{#     See the License for the specific language governing permissions and      #}
{#     limitations under the License.                                           #}
