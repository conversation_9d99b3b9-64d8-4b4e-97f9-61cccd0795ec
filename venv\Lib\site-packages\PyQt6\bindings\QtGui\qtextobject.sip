// qtextobject.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTextObject : public QObject
{
%TypeHeaderCode
#include <qtextobject.h>
%End

protected:
    explicit QTextObject(QTextDocument *doc);
    virtual ~QTextObject();
    void setFormat(const QTextFormat &format);

public:
    QTextFormat format() const;
    int formatIndex() const;
    QTextDocument *document() const;
    int objectIndex() const;
};

class QTextBlockGroup : public QTextObject
{
%TypeHeaderCode
#include <qtextobject.h>
%End

protected:
    explicit QTextBlockGroup(QTextDocument *doc);
    virtual ~QTextBlockGroup();
    virtual void blockInserted(const QTextBlock &block);
    virtual void blockRemoved(const QTextBlock &block);
    virtual void blockFormatChanged(const QTextBlock &block);
    QList<QTextBlock> blockList() const;
};

class QTextFrame : public QTextObject
{
%TypeHeaderCode
#include <qtextobject.h>
%End

public:
    explicit QTextFrame(QTextDocument *doc);
    virtual ~QTextFrame();
    QTextFrameFormat frameFormat() const;
    QTextCursor firstCursorPosition() const;
    QTextCursor lastCursorPosition() const;
    int firstPosition() const;
    int lastPosition() const;
    QList<QTextFrame *> childFrames() const;
    QTextFrame *parentFrame() const;

    class iterator
    {
%TypeHeaderCode
#include <qtextobject.h>
%End

    public:
        iterator();
        QTextFrame *parentFrame() const;
        QTextFrame *currentFrame() const;
        QTextBlock currentBlock() const;
        bool atEnd() const;
        bool operator==(const QTextFrame::iterator &o) const;
        bool operator!=(const QTextFrame::iterator &o) const;
        QTextFrame::iterator &operator+=(int);
%MethodCode
            if (a0 > 0)
                while (a0--)
                    (*sipCpp)++;
            else if (a0 < 0)
                while (a0++)
                    (*sipCpp)--;
%End

        QTextFrame::iterator &operator-=(int);
%MethodCode
            if (a0 > 0)
                while (a0--)
                    (*sipCpp)--;
            else if (a0 < 0)
                while (a0++)
                    (*sipCpp)++;
%End
    };

    typedef QTextFrame::iterator Iterator;
    QTextFrame::iterator begin() const;
    QTextFrame::iterator end() const;
    void setFrameFormat(const QTextFrameFormat &aformat);
};

class QTextBlock /Supertype=PyQt6.sip.wrapper/
{
%TypeHeaderCode
#include <qtextobject.h>
%End

public:
    QTextBlock();
    QTextBlock(const QTextBlock &o);
    bool isValid() const;
    bool operator==(const QTextBlock &o) const;
    bool operator!=(const QTextBlock &o) const;
    bool operator<(const QTextBlock &o) const;
    int position() const;
    int length() const;
    bool contains(int position) const;
    QTextLayout *layout() const;
    QTextBlockFormat blockFormat() const;
    int blockFormatIndex() const;
    QTextCharFormat charFormat() const;
    int charFormatIndex() const;
    QString text() const;
    const QTextDocument *document() const;
    QTextList *textList() const;

    class iterator
    {
%TypeHeaderCode
#include <qtextobject.h>
%End

    public:
        iterator();
        QTextFragment fragment() const;
        bool atEnd() const;
        bool operator==(const QTextBlock::iterator &o) const;
        bool operator!=(const QTextBlock::iterator &o) const;
        QTextBlock::iterator &operator+=(int);
%MethodCode
            if (a0 > 0)
                while (a0--)
                    (*sipCpp)++;
            else if (a0 < 0)
                while (a0++)
                    (*sipCpp)--;
%End

        QTextBlock::iterator &operator-=(int);
%MethodCode
            if (a0 > 0)
                while (a0--)
                    (*sipCpp)--;
            else if (a0 < 0)
                while (a0++)
                    (*sipCpp)++;
%End
    };

    typedef QTextBlock::iterator Iterator;
    QTextBlock::iterator begin() const;
    QTextBlock::iterator end() const;
    QTextBlock next() const;
    QTextBlock previous() const;
    QTextBlockUserData *userData() const;
    void setUserData(QTextBlockUserData *data /GetWrapper/);
%MethodCode
        // Ownership of the user data is with the document not the text block.
        const QTextDocument *td = sipCpp->document();
        
        if (td)
        {
            PyObject *py_td = qtgui_wrap_ancestors(const_cast<QTextDocument *>(td),
                    sipType_QTextDocument);
        
            if (!py_td)
            {
                sipIsErr = 1;
            }
            else
            {
                sipTransferTo(a0Wrapper, py_td);
                Py_DECREF(py_td);
            }
        }
        
        sipCpp->setUserData(a0);
%End

    int userState() const;
    void setUserState(int state);
    void clearLayout();
    int revision() const;
    void setRevision(int rev);
    bool isVisible() const;
    void setVisible(bool visible);
    int blockNumber() const;
    int firstLineNumber() const;
    void setLineCount(int count);
    int lineCount() const;
    Qt::LayoutDirection textDirection() const;
    QList<QTextLayout::FormatRange> textFormats() const;
};

class QTextFragment
{
%TypeHeaderCode
#include <qtextobject.h>
%End

public:
    QTextFragment();
    QTextFragment(const QTextFragment &o);
    bool isValid() const;
    bool operator==(const QTextFragment &o) const;
    bool operator!=(const QTextFragment &o) const;
    bool operator<(const QTextFragment &o) const;
    int position() const;
    int length() const;
    bool contains(int position) const;
    QTextCharFormat charFormat() const;
    int charFormatIndex() const;
    QString text() const;
%If (PyQt_RawFont)
    QList<QGlyphRun> glyphRuns(int from = -1, int length = -1) const;
%End
};

class QTextBlockUserData /Supertype=PyQt6.sip.wrapper/
{
%TypeHeaderCode
#include <qtextobject.h>
%End

public:
    virtual ~QTextBlockUserData();
};

%ModuleHeaderCode
PyObject *qtgui_wrap_ancestors(QObject *obj, const sipTypeDef *td);
%End

%ModuleCode
// Wrap a QObject and ensure that it's ancestors are all wrapped with the
// correct ownerships.
static PyObject *qtgui_wrap_ancestors_worker(QObject *obj)
{
    if (!obj)
    {
        Py_INCREF(Py_None);
        return Py_None;
    }

    PyObject *py_parent = qtgui_wrap_ancestors_worker(obj->parent());

    if (!py_parent)
        return 0;

    PyObject *py_obj = sipConvertFromType(obj, sipType_QObject,
            (py_parent != Py_None ? py_parent : 0));

    Py_DECREF(py_parent);
    return py_obj;
}

PyObject *qtgui_wrap_ancestors(QObject *obj, const sipTypeDef *td)
{
    PyObject *py_parent = qtgui_wrap_ancestors_worker(obj->parent());

    if (!py_parent)
        return 0;

    PyObject *py_obj = sipConvertFromType(obj, td,
            (py_parent != Py_None ? py_parent : 0));

    Py_DECREF(py_parent);

    return py_obj;
}
%End
