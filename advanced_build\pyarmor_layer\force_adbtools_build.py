# -*- coding: utf-8 -*-
"""
强制ADBTools打包脚本
使用多种方法确保ADBTools被正确打包
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def create_adbtools_resource_file():
    """创建ADBTools资源文件"""
    print("🔧 创建ADBTools资源文件...")
    
    adbtools_path = "ADBTools"
    if not os.path.exists(adbtools_path):
        print("❌ ADBTools目录不存在")
        return False
    
    # 创建资源文件内容
    resource_content = '''# -*- coding: utf-8 -*-
"""
ADBTools资源管理器
确保ADBTools文件在打包后可用
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# ADBTools文件列表
ADBTOOLS_FILES = {
    "adb.exe": None,
    "AdbWinApi.dll": None,
    "AdbWinUsbApi.dll": None,
    "fastboot.exe": None,
    "libwinpthread-1.dll": None,
    "pay.exe": None,
}

def get_resource_path(relative_path):
    """获取资源文件路径"""
    if hasattr(sys, '_MEIPASS'):
        # 打包后的环境
        base_path = sys._MEIPASS
    else:
        # 开发环境
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

def extract_adbtools():
    """提取ADBTools到临时目录"""
    temp_dir = tempfile.mkdtemp(prefix="adbtools_")
    adbtools_temp = os.path.join(temp_dir, "ADBTools")
    os.makedirs(adbtools_temp, exist_ok=True)
    
    success_count = 0
    for filename in ADBTOOLS_FILES:
        src_path = get_resource_path(f"ADBTools/{filename}")
        dst_path = os.path.join(adbtools_temp, filename)
        
        if os.path.exists(src_path):
            try:
                shutil.copy2(src_path, dst_path)
                if os.path.exists(dst_path):
                    success_count += 1
                    print(f"✅ 提取成功: {filename}")
                else:
                    print(f"❌ 提取失败: {filename}")
            except Exception as e:
                print(f"❌ 提取异常 {filename}: {e}")
        else:
            print(f"❌ 源文件不存在: {filename}")
    
    if success_count > 0:
        print(f"✅ 成功提取 {success_count}/{len(ADBTOOLS_FILES)} 个ADBTools文件")
        return adbtools_temp
    else:
        print("❌ 没有成功提取任何ADBTools文件")
        return None

def get_adbtools_path():
    """获取ADBTools路径"""
    # 首先尝试当前目录
    local_path = "ADBTools"
    if os.path.exists(local_path):
        return local_path
    
    # 然后尝试从资源中提取
    return extract_adbtools()

# 自动初始化
_adbtools_path = None

def init_adbtools():
    """初始化ADBTools"""
    global _adbtools_path
    if _adbtools_path is None:
        _adbtools_path = get_adbtools_path()
    return _adbtools_path

def get_adb_exe():
    """获取adb.exe路径"""
    adbtools_path = init_adbtools()
    if adbtools_path:
        adb_path = os.path.join(adbtools_path, "adb.exe")
        if os.path.exists(adb_path):
            return adb_path
    return None

def get_fastboot_exe():
    """获取fastboot.exe路径"""
    adbtools_path = init_adbtools()
    if adbtools_path:
        fastboot_path = os.path.join(adbtools_path, "fastboot.exe")
        if os.path.exists(fastboot_path):
            return fastboot_path
    return None

# 在模块加载时自动初始化
init_adbtools()
'''
    
    with open("adbtools_manager.py", "w", encoding="utf-8") as f:
        f.write(resource_content)
    
    print("✅ ADBTools资源管理器已创建: adbtools_manager.py")
    return True

def create_enhanced_nuitka_build():
    """创建增强的Nuitka构建脚本"""
    print("🚀 开始增强Nuitka构建...")
    
    # 检查ADBTools
    adbtools_path = "ADBTools"
    if not os.path.exists(adbtools_path):
        print("❌ ADBTools目录不存在")
        return False
    
    files = os.listdir(adbtools_path)
    print(f"📦 ADBTools包含 {len(files)} 个文件:")
    for file in files:
        file_path = os.path.join(adbtools_path, file)
        if os.path.isfile(file_path):
            size = os.path.getsize(file_path)
            print(f"  📄 {file} ({size:,} 字节)")
    
    # 构建Nuitka命令 - 使用多种方法确保包含
    nuitka_cmd = [
        sys.executable, "-m", "nuitka",
        "--standalone",
        "--onefile",
        "--onefile-tempdir-spec={TEMP}\\syiming\\onefile_{PID}_{TIME}",
        "--windows-console-mode=disable",
        "--enable-plugin=pyqt6",
        "--assume-yes-for-downloads",
        "--show-progress",
        "--show-memory",
        "--remove-output",
        "--output-filename=益民欧加真固件刷写工具_强制ADBTools版.exe",
    ]
    
    # 添加图标
    if os.path.exists("ico/icon.ico"):
        nuitka_cmd.append("--windows-icon-from-ico=ico/icon.ico")
    
    # 方法1: 使用 --include-data-dir (整个目录)
    nuitka_cmd.append(f"--include-data-dir={adbtools_path}={adbtools_path}")
    print(f"✅ 方法1: 添加整个目录 --include-data-dir={adbtools_path}={adbtools_path}")
    
    # 方法2: 逐个添加文件 (双重保险)
    print("✅ 方法2: 逐个添加ADBTools文件:")
    for file in files:
        file_path = os.path.join(adbtools_path, file)
        if os.path.isfile(file_path):
            nuitka_cmd.append(f"--include-data-file={file_path}={adbtools_path}/{file}")
            print(f"  ✅ 添加: {file}")
    
    # 方法3: 添加资源管理器
    if os.path.exists("adbtools_manager.py"):
        nuitka_cmd.append("--include-module=adbtools_manager")
        print("✅ 方法3: 添加ADBTools资源管理器模块")
    
    # 添加其他资源目录
    other_dirs = ["ico", "tup"]
    for dir_name in other_dirs:
        if os.path.exists(dir_name):
            nuitka_cmd.append(f"--include-data-dir={dir_name}={dir_name}")
            print(f"✅ 添加目录: {dir_name}")
    
    # 添加主脚本
    nuitka_cmd.append("main.py")
    
    print("\n🚀 执行增强Nuitka编译...")
    print(f"命令长度: {len(' '.join(nuitka_cmd))} 字符")
    
    try:
        result = subprocess.run(nuitka_cmd, timeout=1800)  # 30分钟超时
        
        if result.returncode == 0:
            output_file = "益民欧加真固件刷写工具_强制ADBTools版.exe"
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"\n✅ 强制ADBTools版构建成功!")
                print(f"📁 输出文件: {output_file}")
                print(f"📦 文件大小: {file_size:,} 字节 ({file_size/1024/1024:.1f} MB)")
                
                # 移动到输出目录
                final_path = "advanced_build/final_output/益民欧加真固件刷写工具_强制ADBTools版.exe"
                os.makedirs(os.path.dirname(final_path), exist_ok=True)
                shutil.move(output_file, final_path)
                print(f"📁 已移动到: {final_path}")
                
                return True
            else:
                print("❌ 编译完成但未找到输出文件")
                return False
        else:
            print(f"❌ Nuitka编译失败，返回码: {result.returncode}")
            return False
    
    except Exception as e:
        print(f"❌ 编译过程异常: {e}")
        return False

def create_adbtools_test():
    """创建ADBTools测试程序"""
    test_content = '''# -*- coding: utf-8 -*-
"""
ADBTools测试程序 - 强制版本
"""

import os
import sys
import subprocess

def test_adbtools_in_exe():
    """测试打包后的ADBTools"""
    print("🔍 测试强制ADBTools版本...")
    
    # 方法1: 检查_MEIPASS目录
    if hasattr(sys, '_MEIPASS'):
        print(f"📁 _MEIPASS目录: {sys._MEIPASS}")
        adbtools_path = os.path.join(sys._MEIPASS, "ADBTools")
        if os.path.exists(adbtools_path):
            print("✅ 在_MEIPASS中找到ADBTools目录")
            files = os.listdir(adbtools_path)
            print(f"包含 {len(files)} 个文件:")
            for file in files:
                file_path = os.path.join(adbtools_path, file)
                if os.path.exists(file_path):
                    size = os.path.getsize(file_path)
                    print(f"  ✅ {file} ({size:,} 字节)")
                else:
                    print(f"  ❌ {file} (不存在)")
        else:
            print("❌ 在_MEIPASS中未找到ADBTools目录")
    
    # 方法2: 检查当前目录
    local_adbtools = "ADBTools"
    if os.path.exists(local_adbtools):
        print("✅ 在当前目录找到ADBTools")
    else:
        print("❌ 在当前目录未找到ADBTools")
    
    # 方法3: 尝试导入资源管理器
    try:
        import adbtools_manager
        adb_path = adbtools_manager.get_adb_exe()
        if adb_path and os.path.exists(adb_path):
            print(f"✅ 通过资源管理器找到adb.exe: {adb_path}")
            # 测试运行
            try:
                result = subprocess.run([adb_path, "version"], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print("✅ adb.exe 可以正常运行")
                    print(f"版本信息: {result.stdout.strip()}")
                else:
                    print("⚠️ adb.exe 运行失败")
            except Exception as e:
                print(f"⚠️ adb.exe 测试异常: {e}")
        else:
            print("❌ 资源管理器无法找到adb.exe")
    except ImportError:
        print("❌ 无法导入adbtools_manager模块")

if __name__ == "__main__":
    test_adbtools_in_exe()
    input("按回车键退出...")
'''
    
    with open("test_force_adbtools.py", "w", encoding="utf-8") as f:
        f.write(test_content)
    
    print("✅ 强制ADBTools测试程序已创建: test_force_adbtools.py")

def main():
    """主函数"""
    print("🔧 强制ADBTools打包工具")
    print("=" * 50)
    
    # 步骤1: 创建资源管理器
    if not create_adbtools_resource_file():
        print("❌ 资源管理器创建失败")
        return False
    
    # 步骤2: 创建测试程序
    create_adbtools_test()
    
    # 步骤3: 执行强制构建
    success = create_enhanced_nuitka_build()
    
    if success:
        print("\n🎉 强制ADBTools版构建完成！")
        print("💡 建议运行以下命令测试:")
        print("   python test_force_adbtools.py")
        print("   或直接运行生成的exe文件")
    else:
        print("\n❌ 强制ADBTools版构建失败")
    
    return success

if __name__ == "__main__":
    main()
