// qfontdatabase.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QFontDatabase
{
%TypeHeaderCode
#include <qfontdatabase.h>
%End

public:
    enum WritingSystem
    {
        Any,
        Latin,
        Greek,
        Cyrillic,
        Armenian,
        Hebrew,
        Arabic,
        Syriac,
        Thaana,
        Devanagari,
        Bengali,
        Gurmukhi,
        Gujarati,
        Oriya,
        Tamil,
        Telugu,
        Kannada,
        Malayalam,
        Sinhala,
        Thai,
        Lao,
        Tibetan,
        Myanmar,
        Georgian,
        Khmer,
        SimplifiedChinese,
        TraditionalChinese,
        Japanese,
        Korean,
        Vietnamese,
        Other,
        Symbol,
        Ogham,
        Runic,
        Nko,
    };

    static QList<int> standardSizes();
    static QList<QFontDatabase::WritingSystem> writingSystems(const QString &family);
    static QList<QFontDatabase::WritingSystem> writingSystems();
    static QStringList families(QFontDatabase::WritingSystem writingSystem = QFontDatabase::Any);
    static QStringList styles(const QString &family);
    static QList<int> pointSizes(const QString &family, const QString &style = QString());
    static QList<int> smoothSizes(const QString &family, const QString &style);
    static QString styleString(const QFontInfo &fontInfo);
    static QString styleString(const QFont &font);
    static QFont font(const QString &family, const QString &style, int pointSize);
    static bool isBitmapScalable(const QString &family, const QString &style = QString());
    static bool isSmoothlyScalable(const QString &family, const QString &style = QString());
    static bool isScalable(const QString &family, const QString &style = QString());
    static bool isFixedPitch(const QString &family, const QString &style = QString());
    static bool italic(const QString &family, const QString &style);
    static bool bold(const QString &family, const QString &style);
    static int weight(const QString &family, const QString &style);
    static QString writingSystemName(QFontDatabase::WritingSystem writingSystem);
    static QString writingSystemSample(QFontDatabase::WritingSystem writingSystem);
    static int addApplicationFont(const QString &fileName);
    static int addApplicationFontFromData(const QByteArray &fontData);
    static QStringList applicationFontFamilies(int id);
    static bool removeApplicationFont(int id);
    static bool removeAllApplicationFonts();

    enum SystemFont
    {
        GeneralFont,
        FixedFont,
        TitleFont,
        SmallestReadableFont,
    };

    static QFont systemFont(QFontDatabase::SystemFont type);
    static bool isPrivateFamily(const QString &family);

private:
    QFontDatabase();
};
