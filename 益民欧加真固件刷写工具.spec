# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['temp_build\\main.py'],
    pathex=[],
    binaries=[],
    datas=[('temp_build/ADBTools', 'ADBTools'), ('temp_build/ico', 'ico'), ('temp_build/tup', 'tup')],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=['C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpc2k5exf9.py'],
    excludes=['tkinter', 'unittest', 'test', 'distutils', 'email', 'html', 'http', 'xml', 'pydoc', 'doctest', 'pickle', 'argparse', 'PyQt6.QtWebEngineCore', 'PyQt6.QtWebEngineWidgets', 'PyQt6.QtWebEngine', 'PyQt6.QtWebChannel', 'PyQt6.QtNetwork', 'PyQt6.QtMultimedia', 'PyQt6.QtMultimediaWidgets', 'PyQt6.QtOpenGL', 'PyQt6.QtOpenGLWidgets', 'PyQt6.QtPositioning', 'PyQt6.QtQml', 'PyQt6.QtQuick', 'PyQt6.QtQuickWidgets', 'PyQt6.QtRemoteObjects', 'PyQt6.QtSensors', 'PyQt6.QtSerialPort', 'PyQt6.QtSql', 'PyQt6.QtSvg', 'PyQt6.QtTest', 'PyQt6.QtWebSockets', 'PyQt6.QtXml', 'PyQt6.QtXmlPatterns'],
    noarchive=False,
    optimize=2,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [('O', None, 'OPTION'), ('O', None, 'OPTION')],
    name='益民欧加真固件刷写工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['ico\\icon.ico'],
)
