import os
from mx9p import ADBTools
import subprocess
import sys
import time

class FastbooDTFunction:
    def __init__(self):
        self.adb_tools = ADBTools()
        self.flash_folder = None
        self.flash_failed = False
        self.is_unlocking = False
        if getattr(sys, 'frozen', False):
            self.base_dir = os.path.dirname(sys.executable)
        else:
            self.base_dir = os.path.dirname(os.path.abspath(__file__))

    def add_log(self, message, level="info"):
        print(f"Log [{level}]: {message}")

    def set_flash_folder(self, folder_path):
        self.flash_folder = folder_path

    def execute_command(self, command):
        """执行命令，使用ADBTools的通用方法"""
        return self.adb_tools.execute_command(command)

    def check_fastboot_type(self, fastboot_path):
        """检查fastboot模式类型（后台执行，避免UI卡顿）"""
        try:
            import subprocess

            # 使用ADBTools的统一subprocess配置
            from mx9p import ADBTools
            startupinfo, creationflags = ADBTools._get_subprocess_config()

            # 执行fastboot命令
            result = subprocess.run(
                [fastboot_path, "getvar", "is-userspace"],
                capture_output=True,
                text=True,
                timeout=3,
                startupinfo=startupinfo,
                creationflags=creationflags,
                encoding='utf-8',
                errors='ignore'
            )

            # fastboot getvar 的输出通常在stderr中
            output = result.stderr.lower() if result.stderr else ""

            if "is-userspace: yes" in output:
                return "Fastboot"
            elif "is-userspace: no" in output:
                return "Bootloader"
            else:
                return "Unknown"

        except Exception:
            return "Unknown"

    def handle_flash(self):
        self.add_log("fastbooDT刷机流程开始...", "info")
        preset_partitions = [
            "vbmeta", "vbmeta_system", "vbmeta_vendor",  # vbmeta 相关分区
            "vendor_boot", "init_boot", "boot",
            "recovery", "dtbo", "lk", "md1img", "modem"
        ]
        try:
            img_files = [f for f in os.listdir(self.flash_folder) if f.endswith('.img')]
            if not img_files:
                self.add_log("未找到任何.img文件", "error")
                self.flash_failed = True
                return False
            _, fastboot_path = self.adb_tools.get_adb_path()
            total = len(preset_partitions)
            success_count = 0
            for idx, partition in enumerate(preset_partitions, 1):
                img_file = f"{partition}.img"
                img_path = os.path.join(self.flash_folder, img_file)
                progress_percent = int((idx / total) * 100)
                if os.path.exists(img_path):
                    self.add_log(f"Loading.. （请勿断开设备/正在修复） 进度：{progress_percent}%", "info")
                    stdout, stderr = self.execute_command([
                        fastboot_path,
                        "flash",
                        partition,
                        img_path
                    ])
                    if stderr and "error" in stderr.lower():
                        self.add_log(f" NO: ", "error")
                    else:
                        self.add_log(f" OK", "success")
                        success_count += 1
            self.add_log(f"进度：100%", "success")
            self.add_log("修复完成，检查设备是否正常进入fastbooDt模式", "success")
            # 刷写完成后自动重启到 fastboot
            try:
                self.add_log("正在测试重启", "info")
                stdout, stderr = self.execute_command([
                    fastboot_path, "reboot", "fastboot"
                ])

                # 检查设备模式
                import time
                time.sleep(3)  # 等待设备稳定
                current_mode = self.check_fastboot_type(fastboot_path)
                if current_mode == "Fastboot":
                    self.add_log("✅ 设备在Fastboot模式，可以继续操作", "success")

                self.add_log("设备已重启到 fastboot 模式，请使用高级重启到bootloader模式，使用colorOS 执行刷机", "success")
            except Exception as e:
                self.add_log(f"重启失败: {str(e)}", "error")
            return True
        except Exception as e:
            self.add_log(f"fastbooDT出错: {str(e)}", "error")
            return False 