// qcborstreamreader.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCborStreamReader
{
%TypeHeaderCode
#include <qcborstreamreader.h>
%End

public:
    enum Type
    {
        UnsignedInteger,
        NegativeInteger,
        ByteString,
        ByteArray,
        TextString,
        String,
        Array,
        Map,
        Tag,
        SimpleType,
        HalfFloat,
        Float16,
        Float,
        Double,
        Invalid,
    };

    enum StringResultCode
    {
        EndOfString,
        Ok,
        Error,
    };

    QCborStreamReader();
    explicit QCborStreamReader(const QByteArray &data);
    explicit QCborStreamReader(QIODevice *device);
    ~QCborStreamReader();
    void setDevice(QIODevice *device);
    QIODevice *device() const;
    void addData(const QByteArray &data);
    void reparse();
    void clear();
    void reset();
%If (Qt_6_7_0 -)
    QCborError lastError() const;
%End
%If (- Qt_6_7_0)
    QCborError lastError();
%End
    qint64 currentOffset() const;
    bool isValid() const;
    int containerDepth() const;
    QCborStreamReader::Type parentContainerType() const;
    bool hasNext() const;
    bool next(int maxRecursion = 10000);
    QCborStreamReader::Type type() const;
    bool isUnsignedInteger() const;
    bool isNegativeInteger() const;
    bool isInteger() const;
    bool isByteArray() const;
    bool isString() const;
    bool isArray() const;
    bool isMap() const;
    bool isTag() const;
    bool isSimpleType() const;
    bool isFloat16() const;
    bool isFloat() const;
    bool isDouble() const;
    bool isInvalid() const;
    bool isSimpleType(QCborSimpleType st) const;
    bool isFalse() const;
    bool isTrue() const;
    bool isBool() const;
    bool isNull() const;
    bool isUndefined() const;
    bool isLengthKnown() const;
    quint64 length() const /__len__/;
    bool isContainer() const;
    bool enterContainer();
    bool leaveContainer();
    SIP_PYTUPLE readString() /TypeHint="Tuple[str, QCborStreamReader.StringResultCode]"/;
%MethodCode
        QCborStreamReader::StringResult<QString> res = sipCpp->readString();
        
        QString *qs = new QString;
        if (res.status != QCborStreamReader::Error)
            *qs = res.data;
        
        sipRes = sipBuildResult(NULL, "NF", qs, sipType_QString, NULL, res.status, sipType_QCborStreamReader_StringResultCode);
%End

    SIP_PYTUPLE readByteArray() /TypeHint="Tuple[QByteArray, QCborStreamReader.StringResultCode]"/;
%MethodCode
        QCborStreamReader::StringResult<QByteArray> res = sipCpp->readByteArray();
        
        QByteArray *qba = new QByteArray;
        if (res.status != QCborStreamReader::Error)
            *qba = res.data;
        
        sipRes = sipBuildResult(NULL, "NF", qba, sipType_QByteArray, NULL, res.status, sipType_QCborStreamReader_StringResultCode);
%End

%If (Qt_6_7_0 -)
    SIP_PYTUPLE readUtf8String() /TypeHint="Tuple[QByteArray, QCborStreamReader.StringResultCode]"/;
%MethodCode
        QCborStreamReader::StringResult<QByteArray> res = sipCpp->readUtf8String();
        
        QByteArray *qba = new QByteArray;
        if (res.status != QCborStreamReader::Error)
            *qba = res.data;
        
        sipRes = sipBuildResult(NULL, "NF", qba, sipType_QByteArray, NULL, res.status, sipType_QCborStreamReader_StringResultCode);
%End

%End
    bool toBool() const;
    quint64 toUnsignedInteger() const;
    QCborSimpleType toSimpleType() const;
    double toDouble() const;
    qint64 toInteger() const;
%If (Qt_6_7_0 -)
    bool readAndAppendToString(QString &dst);
%End
%If (Qt_6_7_0 -)
    bool readAndAppendToUtf8String(QByteArray &dst);
%End
%If (Qt_6_7_0 -)
    bool readAndAppendToByteArray(QByteArray &dst);
%End
%If (Qt_6_7_0 -)
    QString readAllString();
%End
%If (Qt_6_7_0 -)
    QByteArray readAllUtf8String();
%End
%If (Qt_6_7_0 -)
    QByteArray readAllByteArray();
%End

private:
    QCborStreamReader(const QCborStreamReader &);
};
