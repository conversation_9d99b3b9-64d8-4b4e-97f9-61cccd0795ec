@echo off
chcp 65001 >nul
title 三合一加密打包工具

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🔒 三合一加密打包工具 v1.0.0                    ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║  分层保护架构:                                                  ║
echo ║  ├── 外层: Nuitka编译 + VMProtect加壳 + 自签名证书                ║
echo ║  ├── 中层: PyArmor混淆非核心代码 (免费版)                         ║
echo ║  └── 内层: Cython编译核心算法                                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:MENU
echo 请选择操作:
echo [1] 完整构建 (推荐)
echo [2] 仅Cython编译
echo [3] 仅PyArmor混淆
echo [4] 仅Nuitka编译
echo [5] 清理构建文件
echo [6] 查看帮助
echo [0] 退出
echo.
set /p choice=请输入选择 (0-6):

if "%choice%"=="1" goto FULL_BUILD
if "%choice%"=="2" goto CYTHON_ONLY
if "%choice%"=="3" goto PYARMOR_ONLY
if "%choice%"=="4" goto NUITKA_ONLY
if "%choice%"=="5" goto CLEAN
if "%choice%"=="6" goto HELP
if "%choice%"=="0" goto EXIT
echo 无效选择，请重新输入
goto MENU

:FULL_BUILD
echo.
echo 🚀 开始完整构建...
echo ⚠️  注意: 此过程可能需要较长时间，请耐心等待
echo.
python build.py
goto END

:CYTHON_ONLY
echo.
echo 🔧 开始Cython编译...
python build.py --cython-only
goto END

:PYARMOR_ONLY
echo.
echo 🛡️  开始PyArmor混淆...
python build.py --pyarmor-only
goto END

:NUITKA_ONLY
echo.
echo ⚡ 开始Nuitka编译...
python build.py --nuitka-only
goto END

:CLEAN
echo.
echo 🧹 清理构建文件...
python build.py --clean
goto END

:HELP
echo.
python build.py --help
goto END

:END
echo.
echo 按任意键返回主菜单...
pause >nul
goto MENU

:EXIT
echo.
echo 👋 感谢使用三合一加密打包工具！
echo.
pause