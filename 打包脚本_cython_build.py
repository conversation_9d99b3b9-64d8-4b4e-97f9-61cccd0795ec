# -*- coding: utf-8 -*-
"""
可靠的Cython编译脚本 - 逐个编译核心算法
基于测试结果，确保每个模块都能成功编译
"""

import os
import sys
import shutil
import subprocess
import time
import json
from pathlib import Path

# 核心算法模块（必须全部编译）
CORE_MODULES = [
    "coloros.py",
    "coloros15.py", 
    "utils.py",
    "fastboodt.py",
    "zhidinyishuaxie.py",
    "payload_extractor.py",
    "custom_messagebox.py",
    "genduodakhd.py",
    "font_extractor.py",
    "flash_tool.py",
    
]

def log(message):
    """日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def compile_single_module(module_file, output_dir):
    """编译单个模块"""
    log(f"🔧 编译模块: {module_file}")
    
    if not os.path.exists(module_file):
        log(f"❌ 文件不存在: {module_file}")
        return False, None
    
    # 模块名
    module_name = module_file.replace('.py', '_cython')
    
    # 创建专用的setup.py
    setup_content = f'''
from setuptools import setup
from Cython.Build import cythonize

setup(
    ext_modules = cythonize("{module_file}", language_level=3),
    zip_safe=False,
)
'''
    
    setup_file = f"setup_{module_name}.py"
    with open(setup_file, "w", encoding="utf-8") as f:
        f.write(setup_content)
    
    try:
        # 执行编译
        cmd = [sys.executable, setup_file, "build_ext", "--inplace"]
        log(f"执行: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=90)
        
        # 清理setup文件
        if os.path.exists(setup_file):
            os.remove(setup_file)
        
        if result.returncode == 0:
            # 查找生成的.pyd文件
            pyd_files = [f for f in os.listdir('.') if f.startswith(module_file.replace('.py', '')) and f.endswith('.pyd')]
            
            if pyd_files:
                pyd_file = pyd_files[0]
                size = os.path.getsize(pyd_file)
                log(f"✅ 编译成功: {pyd_file} ({size:,} 字节)")
                
                # 移动到输出目录
                output_path = os.path.join(output_dir, pyd_file)
                shutil.move(pyd_file, output_path)
                
                # 清理.c文件
                c_files = [f for f in os.listdir('.') if f.startswith(module_file.replace('.py', '')) and f.endswith('.c')]
                for c_file in c_files:
                    try:
                        os.remove(c_file)
                    except:
                        pass
                
                return True, pyd_file
            else:
                log(f"⚠️ 编译完成但未找到.pyd文件")
        else:
            log(f"❌ 编译失败:")
            if result.stderr:
                log(f"错误: {result.stderr[:300]}")
        
        return False, None
        
    except subprocess.TimeoutExpired:
        log(f"❌ 编译超时: {module_file}")
        return False, None
    except Exception as e:
        log(f"❌ 编译异常: {e}")
        return False, None
    finally:
        # 清理临时文件
        if os.path.exists(setup_file):
            try:
                os.remove(setup_file)
            except:
                pass

def compile_all_modules():
    """编译所有核心模块"""
    log("🚀 开始逐个编译核心算法模块")
    log("=" * 60)
    
    # 准备输出目录
    output_dir = "cython_compiled"
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    os.makedirs(output_dir)
    
    compiled_modules = {}
    failed_modules = []
    
    # 逐个编译
    for i, module in enumerate(CORE_MODULES, 1):
        log(f"\n📦 [{i}/{len(CORE_MODULES)}] 处理模块: {module}")
        
        if os.path.exists(module):
            file_size = os.path.getsize(module)
            log(f"源文件大小: {file_size:,} 字节")
            
            success, pyd_file = compile_single_module(module, output_dir)
            
            if success and pyd_file:
                compiled_modules[module] = pyd_file
                log(f"✅ 模块 {module} 编译成功")
            else:
                failed_modules.append(module)
                log(f"❌ 模块 {module} 编译失败")
        else:
            failed_modules.append(module)
            log(f"❌ 模块 {module} 文件不存在")
        
        # 短暂暂停，避免资源冲突
        time.sleep(0.5)
    
    # 生成报告
    log("\n" + "=" * 60)
    log("📊 编译结果统计")
    log(f"总模块数: {len(CORE_MODULES)}")
    log(f"成功编译: {len(compiled_modules)}")
    log(f"编译失败: {len(failed_modules)}")
    log(f"成功率: {len(compiled_modules)/len(CORE_MODULES)*100:.1f}%")
    
    if compiled_modules:
        log("\n✅ 成功编译的模块:")
        for original, compiled in compiled_modules.items():
            compiled_path = os.path.join(output_dir, compiled)
            if os.path.exists(compiled_path):
                size = os.path.getsize(compiled_path)
                log(f"  {original} -> {compiled} ({size:,} 字节)")
    
    if failed_modules:
        log("\n❌ 编译失败的模块:")
        for module in failed_modules:
            log(f"  {module}")
    
    # 保存编译映射
    if compiled_modules:
        mapping_data = {
            "modules": compiled_modules,
            "total_modules": len(compiled_modules),
            "failed_modules": failed_modules,
            "success_rate": f"{len(compiled_modules)/len(CORE_MODULES)*100:.1f}%",
            "compile_time": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        mapping_file = os.path.join(output_dir, "cython_mapping.json")
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(mapping_data, f, indent=2, ensure_ascii=False)
        
        log(f"\n✅ 编译映射已保存: {mapping_file}")
        
        return True, output_dir, compiled_modules
    else:
        log("\n❌ 没有成功编译任何模块")
        return False, None, {}

def create_final_build(output_dir, compiled_modules):
    """创建最终的分层保护版本"""
    log("\n🔧 创建Cython分层保护版")
    log("-" * 40)
    
    if not compiled_modules:
        log("❌ 没有可用的Cython模块")
        return False
    
    # 构建Nuitka命令
    nuitka_cmd = [
        sys.executable, "-m", "nuitka",
        "--standalone",
        "--onefile",
        "--onefile-tempdir-spec=\\syiming\\cython_{PID}_{TIME}",
        "--windows-console-mode=disable",
        "--enable-plugin=pyqt6",
        "--assume-yes-for-downloads",
        "--show-progress",
        "--show-memory",
        "--remove-output",
        "--output-filename=yinming.exe",
    ]
    
    # 添加图标
    if os.path.exists("ico/icon.ico"):
        nuitka_cmd.append("--windows-icon-from-ico=ico/icon.ico")
        log("✅ 添加图标文件")
    
    # 添加所有Cython编译模块
    for original, compiled in compiled_modules.items():
        compiled_path = os.path.join(output_dir, compiled)
        if os.path.exists(compiled_path):
            nuitka_cmd.append(f"--include-data-file={compiled_path}={compiled}")
            log(f"包含Cython模块: {compiled}")
    
    # 添加映射文件
    mapping_file = os.path.join(output_dir, "cython_mapping.json")
    if os.path.exists(mapping_file):
        nuitka_cmd.append(f"--include-data-file={mapping_file}=cython_mapping.json")
        log("包含Cython映射文件")
    
    # 添加ADBTools（逐个文件）
    if os.path.exists("ADBTools"):
        adb_files = os.listdir("ADBTools")
        for file in adb_files:
            file_path = os.path.join("ADBTools", file)
            if os.path.isfile(file_path):
                nuitka_cmd.append(f"--include-data-file={file_path}=ADBTools/{file}")
        log(f"✅ 包含ADBTools目录 ({len(adb_files)} 个文件)")
    
    # 添加其他资源
    for dir_name in ["ico", "tup"]:
        if os.path.exists(dir_name):
            nuitka_cmd.append(f"--include-data-dir={dir_name}={dir_name}")
            log(f"包含目录: {dir_name}")
    
    # 添加主脚本
    nuitka_cmd.append("main.py")
    
    log("\n🚀 开始Nuitka编译...")
    try:
        result = subprocess.run(nuitka_cmd, timeout=1800)
        
        if result.returncode == 0:
            output_file = "益民欧加真固件刷写工具_Cython分层保护版.exe"
            if os.path.exists(output_file):
                size = os.path.getsize(output_file)
                log(f"\n🎉 Cython分层保护版构建成功!")
                log(f"📁 输出文件: {output_file}")
                log(f"📦 文件大小: {size:,} 字节 ({size/1024/1024:.1f} MB)")
                
                # 移动到最终输出目录
                final_dir = "advanced_build/final_output"
                os.makedirs(final_dir, exist_ok=True)
                final_path = os.path.join(final_dir, output_file)
                shutil.move(output_file, final_path)
                log(f"📁 已移动到: {final_path}")
                
                return True
        
        log("❌ Nuitka编译失败")
        return False
        
    except Exception as e:
        log(f"❌ Nuitka编译异常: {e}")
        return False

def main():
    """主函数"""
    log("🔒 可靠的Cython编译工具")
    log("确保核心算法编译成功")
    log("=" * 60)
    
    # 步骤1: 编译所有核心模块
    success, output_dir, compiled_modules = compile_all_modules()
    
    if not success:
        log("\n❌ Cython编译失败")
        return False
    
    # 检查编译成功率
    success_rate = len(compiled_modules) / len(CORE_MODULES) * 100
    
    if success_rate < 50:
        log(f"\n⚠️ 编译成功率过低 ({success_rate:.1f}%)，建议检查环境")
        choice = input("是否继续构建？(y/n): ").lower()
        if choice != 'y':
            return False
    
    log(f"\n🎉 Cython编译完成! 成功率: {success_rate:.1f}%")
    log(f"成功编译 {len(compiled_modules)}/{len(CORE_MODULES)} 个核心算法模块")
    
    # 步骤2: 询问是否继续最终构建
    choice = input("\n是否继续创建Cython分层保护版exe？(y/n): ").lower()
    if choice == 'y':
        if create_final_build(output_dir, compiled_modules):
            log("\n🎉 Cython分层保护版构建完成!")
            log("🔒 实现的保护层级:")
            log(f"   内层: {len(compiled_modules)} 个核心算法Cython编译")
            log("   外层: Nuitka编译 + ADBTools完整包含")
            log("   准备: VMProtect加壳配置")
        else:
            log("\n❌ 最终构建失败")
    
    return True

if __name__ == "__main__":
    main()
