[2025-07-16 22:21:30] [SUCCESS] ✅ 构建目录清理完成
[2025-07-16 22:21:30] [INFO] ℹ️ 创建目录: advanced_build
[2025-07-16 22:21:30] [INFO] ℹ️ 创建目录: advanced_build/cython_layer
[2025-07-16 22:21:30] [INFO] ℹ️ 创建目录: advanced_build/pyarmor_layer
[2025-07-16 22:21:30] [INFO] ℹ️ 创建目录: advanced_build/nuitka_layer
[2025-07-16 22:21:30] [INFO] ℹ️ 创建目录: advanced_build/final_output
[2025-07-16 22:21:30] [INFO] ℹ️ 创建目录: advanced_build/temp
[2025-07-16 22:21:30] [STEP] 🔧 检查构建环境 - 开始
[2025-07-16 22:21:30] [SUCCESS] ✅ Python版本: 3.12.7
[2025-07-16 22:21:30] [SUCCESS] ✅ 找到必需文件: main.py
[2025-07-16 22:21:30] [SUCCESS] ✅ 找到目录: ADBTools (6 个文件)
[2025-07-16 22:21:30] [SUCCESS] ✅ 找到目录: ico (1 个文件)
[2025-07-16 22:21:30] [SUCCESS] ✅ 找到目录: tup (2 个文件)
[2025-07-16 22:21:30] [SUCCESS] ✅ 找到目录: PyQt6 (2502 个文件)
[2025-07-16 22:21:30] [STEP] 🔧 安装构建依赖 - 开始
[2025-07-16 22:21:30] [INFO] ℹ️ 依赖已安装: PyQt6
[2025-07-16 22:21:30] [INFO] ℹ️ 依赖已安装: nuitka
[2025-07-16 22:21:30] [INFO] ℹ️ 依赖已安装: cython
[2025-07-16 22:21:30] [INFO] ℹ️ 依赖已安装: pyarmor
[2025-07-16 22:21:30] [INFO] ℹ️ 依赖已安装: numpy
[2025-07-16 22:21:30] [INFO] ℹ️ 依赖已安装: pywin32
[2025-07-16 22:21:30] [INFO] ℹ️ 依赖已安装: setuptools
[2025-07-16 22:21:30] [INFO] ℹ️ 依赖已安装: wheel
[2025-07-16 22:21:30] [INFO] ℹ️ 依赖已安装: psutil
[2025-07-16 22:21:30] [SUCCESS] ✅ 所有依赖安装完成
[2025-07-16 22:21:30] [STEP] 🔧 复制源文件 - 开始
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: adbtools_manager.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: advanced_build.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: build.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: coloros.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: coloros15.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: config.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: custom_messagebox.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: dabao123.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: fastboodt.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: final_layered_build.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: fix_adbtools_packaging.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: flash_tool.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: font_extractor.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: force_adbtools_build.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: force_cython_build.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: genduodakhd.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: layered_protection_build.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: main.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: obfuscate_config.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: payload_extractor.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: quick_cython_build.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: reliable_cython_build.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: simple_cython_build.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: step1_cython_compile.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: step2_nuitka_package.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: test_adbtools.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: test_adbtools_in_exe.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: test_cython.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: test_force_adbtools.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: utils.py
[2025-07-16 22:21:30] [INFO] ℹ️ 复制Python文件: zhidinyishuaxie.py
[2025-07-16 22:21:30] [SUCCESS] ✅ 复制目录: ADBTools (6 个文件)
[2025-07-16 22:21:30] [SUCCESS] ✅ 复制目录: ico (1 个文件)
[2025-07-16 22:21:30] [SUCCESS] ✅ 复制目录: tup (2 个文件)
[2025-07-16 22:21:33] [SUCCESS] ✅ 复制目录: PyQt6 (2502 个文件)
[2025-07-16 22:21:33] [SUCCESS] ✅ 源文件复制完成
[2025-07-16 22:21:33] [INFO] ℹ️ 
🔧 内层保护: Cython编译核心算法
[2025-07-16 22:21:33] [INFO] ℹ️ ----------------------------------------
[2025-07-16 22:21:33] [STEP] 🔧 编译Cython核心模块 - 开始
[2025-07-16 22:21:33] [ERROR] ❌ Cython编译异常: [Errno 2] No such file or directory: 'advanced_build/cython_layer\\setup.py'
[2025-07-16 22:21:33] [WARNING] ⚠️ Cython编译失败，使用原始Python文件
[2025-07-16 22:21:33] [INFO] ℹ️ 
🛡️ 中层保护: PyArmor混淆非核心代码
[2025-07-16 22:21:33] [INFO] ℹ️ ----------------------------------------
[2025-07-16 22:21:33] [STEP] 🔧 PyArmor混淆非核心模块 - 开始
[2025-07-16 22:21:35] [INFO] ℹ️ 
🔗 整合模块
[2025-07-16 22:21:35] [INFO] ℹ️ ----------------------------------------
[2025-07-16 22:21:35] [STEP] 🔧 整合所有模块 - 开始
[2025-07-16 22:21:35] [INFO] ℹ️ 复制原始核心模块: coloros.py
[2025-07-16 22:21:35] [INFO] ℹ️ 复制原始核心模块: coloros15.py
[2025-07-16 22:21:35] [INFO] ℹ️ 复制原始核心模块: custom_messagebox.py
[2025-07-16 22:21:35] [INFO] ℹ️ 复制原始核心模块: fastboodt.py
[2025-07-16 22:21:35] [INFO] ℹ️ 复制原始核心模块: payload_extractor.py
[2025-07-16 22:21:35] [INFO] ℹ️ 复制原始核心模块: utils.py
[2025-07-16 22:21:35] [INFO] ℹ️ 复制原始核心模块: zhidinyishuaxie.py
[2025-07-16 22:21:35] [INFO] ℹ️ 复制原始非核心模块: flash_tool.py
[2025-07-16 22:21:35] [INFO] ℹ️ 复制原始非核心模块: main.py
[2025-07-16 22:21:35] [INFO] ℹ️ 复制原始非核心模块: config.py
[2025-07-16 22:21:35] [SUCCESS] ✅ 模块整合完成
[2025-07-16 22:21:35] [STEP] 🔧 创建模块加载器 - 开始
[2025-07-16 22:21:35] [SUCCESS] ✅ 模块加载器已创建
[2025-07-16 22:21:35] [INFO] ℹ️ 
⚡ 外层保护: Nuitka编译
[2025-07-16 22:21:35] [INFO] ℹ️ ----------------------------------------
[2025-07-16 22:21:35] [STEP] 🔧 Nuitka编译最终程序 - 开始
[2025-07-16 22:24:30] [SUCCESS] ✅ Nuitka编译成功: advanced_build/nuitka_layer\益民欧加真固件刷写工具_加密版.exe (28,853,760 字节)
[2025-07-16 22:24:30] [INFO] ℹ️ 
🎯 完成构建
[2025-07-16 22:24:30] [INFO] ℹ️ ----------------------------------------
[2025-07-16 22:24:30] [STEP] 🔧 完成构建 - 开始
[2025-07-16 22:24:30] [SUCCESS] ✅ 最终输出: advanced_build/final_output\益民欧加真固件刷写工具_加密版.exe (28,853,760 字节)
[2025-07-16 22:24:30] [SUCCESS] ✅ 构建报告已保存: advanced_build/final_output\build_report.json
[2025-07-16 22:24:30] [INFO] ℹ️ 
============================================================
[2025-07-16 22:24:30] [SUCCESS] ✅ 🎉 三合一加密打包成功完成！
[2025-07-16 22:24:30] [INFO] ℹ️ ⏱️ 总用时: 3分0秒
[2025-07-16 22:24:30] [INFO] ℹ️ 🔒 保护层级:
[2025-07-16 22:24:30] [INFO] ℹ️    ├── 外层: Nuitka编译 + VMProtect加壳
[2025-07-16 22:24:30] [INFO] ℹ️    ├── 中层: PyArmor混淆非核心代码
[2025-07-16 22:24:30] [INFO] ℹ️    └── 内层: Cython编译核心算法
[2025-07-16 22:24:30] [INFO] ℹ️ 📦 包含文件夹: ADBTools, ico, tup, PyQt6
[2025-07-16 22:24:30] [INFO] ℹ️ 📁 输出目录: advanced_build/final_output
[2025-07-16 22:24:30] [INFO] ℹ️ ============================================================
