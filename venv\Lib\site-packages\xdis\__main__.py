"""
  Copyright (c) 2015-2017, 2020-2021, 2024 by <PERSON>
  Copyright (c) 2000 by hart<PERSON><PERSON> <<EMAIL>>

  This program is free software; you can redistribute it and/or
  modify it under the terms of the GNU General Public License
  as published by the Free Software Foundation; either version 2
  of the License, or (at your option) any later version.

  This program is distributed in the hope that it will be useful,
  but WITHOUT ANY WARRANTY; without even the implied warranty of
  MERCHANT<PERSON>ILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  GNU General Public License for more details.

  You should have received a copy of the GNU General Public License
  along with this program; if not, write to the Free Software
  Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.

  NB. This is not a masterpiece of software, but became more like a hack.
  Probably a complete rewrite would be sensefull. hG/2000-12-27
"""
import sys

from xdis.disasm import disassemble_file

if __name__ == "__main__" and len(sys.argv) > 1:
    for path in sys.argv[1:]:
        disassemble_file(path, sys.stdout)
